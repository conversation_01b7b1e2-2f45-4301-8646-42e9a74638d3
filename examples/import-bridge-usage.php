<?php
/**
 * SquareKit Import Bridge Usage Examples
 *
 * Demonstrates how to use the new Import Bridge to resolve conflicts
 * between existing and SWEVER import architectures.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// This file is for documentation purposes only
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * EXAMPLE 1: Basic Import Manager Usage (Recommended)
 * 
 * The Import Manager provides the simplest interface and automatically
 * chooses the best import method.
 */
function example_basic_import_manager() {
    // Initialize the import manager
    $import_manager = new SquareKit_Import_Manager();
    
    // Example 1a: Import by Square item ID (SWEVER-style)
    $square_item_id = 'ABCD1234567890';
    $result = $import_manager->import_product( $square_item_id );
    
    if ( is_wp_error( $result ) ) {
        echo "Import failed: " . $result->get_error_message();
    } else {
        echo "Import successful! Product ID: " . $result['product_id'];
        echo "Method used: " . $result['method']; // 'SWEVER' or 'Legacy'
        echo "Action: " . ($result['created'] ? 'Created' : 'Updated');
    }
    
    // Example 1b: Import by Square item data (Legacy-style)
    $square_item_data = array(
        'id' => 'ABCD1234567890',
        'item_data' => array(
            'name' => 'Test Product',
            'variations' => array(/* ... */)
        )
    );
    
    $result = $import_manager->import_product( $square_item_data );
    // Handle result same as above
}

/**
 * EXAMPLE 2: Import Bridge Direct Usage
 * 
 * For more control, you can use the Import Bridge directly.
 */
function example_import_bridge_direct() {
    // Initialize the import bridge
    $import_bridge = new SquareKit_Import_Bridge();
    
    // Example 2a: Import with specific options
    $square_item_id = 'ABCD1234567890';
    $options = array(
        'import_name' => true,
        'import_description' => true,
        'import_price' => true,
        'import_images' => false, // Skip images
        'update_only' => false    // Allow creation
    );
    
    $result = $import_bridge->import_product( $square_item_id, $options );
    
    // Example 2b: Check which method would be recommended
    $recommended = $import_bridge->get_recommended_method( $square_item_id );
    echo "Recommended method: " . $recommended; // 'SWEVER' or 'Legacy'
    
    // Example 2c: Check if SWEVER is available
    if ( $import_bridge->is_swever_available() ) {
        echo "SWEVER architecture is available";
    } else {
        echo "Only legacy import is available";
    }
}

/**
 * EXAMPLE 3: Batch Import with Import Manager
 * 
 * Import multiple products efficiently.
 */
function example_batch_import() {
    $import_manager = new SquareKit_Import_Manager();
    
    // Array of Square item IDs
    $square_item_ids = array(
        'ABCD1234567890',
        'EFGH0987654321',
        'IJKL1122334455'
    );
    
    $options = array(
        'batch_size' => 5,
        'import_images' => true,
        'import_categories' => true
    );
    
    $batch_result = $import_manager->import_products_batch( $square_item_ids, $options );
    
    echo "Batch import completed:";
    echo "Total: " . $batch_result['total'];
    echo "Successful: " . $batch_result['successful'];
    echo "Failed: " . $batch_result['failed'];
    
    // Handle individual results
    foreach ( $batch_result['results'] as $individual_result ) {
        $input = $individual_result['input'];
        $result = $individual_result['result'];
        
        if ( is_wp_error( $result ) ) {
            echo "Failed to import {$input}: " . $result->get_error_message();
        } else {
            echo "Successfully imported {$input} as product " . $result['product_id'];
        }
    }
}

/**
 * EXAMPLE 4: Configuring Import Preferences
 * 
 * Customize how the Import Manager behaves.
 */
function example_configure_preferences() {
    $import_manager = new SquareKit_Import_Manager();
    
    // Update preferences
    $new_preferences = array(
        'preferred_method' => 'swever',  // Force SWEVER method
        'fallback_enabled' => true,      // Enable fallback to legacy if SWEVER fails
        'validation_enabled' => true,    // Enable input validation
        'logging_enabled' => true        // Enable detailed logging
    );
    
    $import_manager->update_preferences( $new_preferences );
    
    // Get current preferences
    $current_preferences = $import_manager->get_preferences();
    print_r( $current_preferences );
}

/**
 * EXAMPLE 5: Testing Import Methods
 * 
 * Test which import methods are available.
 */
function example_test_import_methods() {
    $import_manager = new SquareKit_Import_Manager();
    
    $test_results = $import_manager->test_import_methods();
    
    echo "Legacy available: " . ($test_results['legacy_available'] ? 'Yes' : 'No');
    echo "SWEVER available: " . ($test_results['swever_available'] ? 'Yes' : 'No');
    echo "Bridge available: " . ($test_results['bridge_available'] ? 'Yes' : 'No');
    echo "Recommended method: " . $test_results['recommended_method'];
}

/**
 * EXAMPLE 6: Backward Compatibility
 * 
 * Existing code continues to work unchanged.
 */
function example_backward_compatibility() {
    // This existing code still works exactly as before
    $wc_integration = new SquareKit_WooCommerce();
    
    $square_item_data = array(
        'id' => 'ABCD1234567890',
        'item_data' => array(/* ... */)
    );
    
    $image_map = array(/* ... */);
    
    // Legacy method still works
    $result = $wc_integration->import_product_from_square( $square_item_data, $image_map );
    
    // But now you can also use the new unified approach
    $import_manager = new SquareKit_Import_Manager();
    $unified_result = $import_manager->import_product( $square_item_data );
    
    // Both approaches work and return compatible results
}

/**
 * EXAMPLE 7: Error Handling and Debugging
 * 
 * Comprehensive error handling with the new bridge.
 */
function example_error_handling() {
    $import_manager = new SquareKit_Import_Manager();
    
    $square_item_id = 'INVALID_ID';
    $result = $import_manager->import_product( $square_item_id );
    
    if ( is_wp_error( $result ) ) {
        echo "Error Code: " . $result->get_error_code();
        echo "Error Message: " . $result->get_error_message();
        
        // Get detailed statistics
        $stats = $import_manager->get_import_statistics();
        print_r( $stats );
    } else {
        echo "Import successful!";
        echo "Method used: " . $result['method'];
        echo "Product ID: " . $result['product_id'];
        echo "Created: " . ($result['created'] ? 'Yes' : 'No');
        
        // Access detailed stats
        if ( isset( $result['stats'] ) ) {
            print_r( $result['stats'] );
        }
    }
}

/**
 * MIGRATION GUIDE
 * 
 * How to migrate from existing import code to the new bridge:
 * 
 * BEFORE (Legacy approach):
 * ```php
 * $wc = new SquareKit_WooCommerce();
 * $result = $wc->import_product_from_square($item_data, $image_map);
 * ```
 * 
 * AFTER (Unified approach):
 * ```php
 * $manager = new SquareKit_Import_Manager();
 * $result = $manager->import_product($item_data); // or $square_item_id
 * ```
 * 
 * BENEFITS:
 * - Automatic method selection (SWEVER vs Legacy)
 * - Unified result format
 * - Built-in error handling and fallback
 * - Batch processing capabilities
 * - Comprehensive logging and statistics
 * - Backward compatibility maintained
 */
