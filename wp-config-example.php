<?php
/**
 * SquareKit Secure Configuration Example
 * 
 * Add these constants to your wp-config.php file for secure credential storage.
 * This keeps sensitive data out of your database and version control.
 */

// ===================================================================
// SQUAREKIT SECURE CONFIGURATION
// ===================================================================

/**
 * Sandbox Environment Credentials
 * Get these from your Square Developer Console -> Sandbox
 */
define( 'SQUAREKIT_SANDBOX_APPLICATION_ID', 'sandbox-sq0idb-YOUR_SANDBOX_APP_ID_HERE' );
define( 'SQUAREKIT_SANDBOX_CLIENT_SECRET', 'sandbox-sq0csp-YOUR_SANDBOX_CLIENT_SECRET_HERE' );

/**
 * Production Environment Credentials
 * Get these from your Square Developer Console -> Production
 */
define( 'SQUAREKIT_PRODUCTION_APPLICATION_ID', 'sq0idb-YOUR_PRODUCTION_APP_ID_HERE' );
define( 'SQUAREKIT_PRODUCTION_CLIENT_SECRET', 'sq0csp-YOUR_PRODUCTION_CLIENT_SECRET_HERE' );

/**
 * Default Environment
 * Set to 'sandbox' for development, 'production' for live sites
 */
define( 'SQUAREKIT_DEFAULT_ENVIRONMENT', 'sandbox' );

/**
 * Optional: Webhook Configuration
 */
// define( 'SQUAREKIT_WEBHOOK_URL', 'https://yoursite.com/wp-json/squarekit/v1/webhook' );
// define( 'SQUAREKIT_WEBHOOK_SIGNATURE_KEY', 'your-webhook-signature-key' );

/**
 * Optional: Debug and Performance Settings
 */
// define( 'SQUAREKIT_DEBUG', true );
// define( 'SQUAREKIT_API_TIMEOUT', 30 );
// define( 'SQUAREKIT_SYNC_BATCH_SIZE', 100 );

// ===================================================================
// SECURITY NOTES:
// ===================================================================
// 
// 1. NEVER commit wp-config.php to version control
// 2. Use different credentials for sandbox vs production
// 3. Regularly rotate your client secrets
// 4. Monitor your Square Developer Console for unauthorized access
// 5. Use HTTPS in production
// 
// ===================================================================
