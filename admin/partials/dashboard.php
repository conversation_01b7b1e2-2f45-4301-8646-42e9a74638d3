<?php
// Initialize settings and API
$settings = new SquareKit_Settings();
$is_connected = $settings->is_connected();
$webhook_status = $settings->get('webhook_status', false);
$cron_schedule = $settings->get('cron_schedule', 'hourly');
$last_sync = get_option('squarekit_last_sync', 'Never');
$logs = (new SquareKit_DB())->get_logs(['number' => 5]);

// Get comprehensive statistics
global $wpdb;

// WooCommerce counts
$wc_product_count = wp_count_posts('product')->publish;
$wc_order_count = wp_count_posts('shop_order')->publish;
$wc_customer_count = count(get_users(['role__in' => ['customer']]));

// Square synced products count
$synced_products_count = $wpdb->get_var(
    "SELECT COUNT(DISTINCT post_id) FROM {$wpdb->postmeta} WHERE meta_key = '_square_id' AND meta_value != ''"
);

// Products with sync issues (out of sync)
$out_of_sync_count = $wpdb->get_var(
    "SELECT COUNT(DISTINCT p1.post_id) FROM {$wpdb->postmeta} p1
     INNER JOIN {$wpdb->postmeta} p2 ON p1.post_id = p2.post_id
     INNER JOIN {$wpdb->postmeta} p3 ON p1.post_id = p3.post_id
     WHERE p1.meta_key = '_square_id' AND p1.meta_value != ''
     AND p2.meta_key = '_square_last_sync'
     AND p3.meta_key = '_square_updated_at'
     AND p2.meta_value < p3.meta_value"
);

// Square inventory count (if connected)
$square_inventory_count = 0;
$square_product_count = 0;
if ($is_connected) {
    try {
        $square_api = new SquareKit_Square_API();
        $catalog_items = $square_api->get_catalog_items();
        if (!is_wp_error($catalog_items)) {
            $square_product_count = count($catalog_items);
            // Count inventory items
            $location_id = $settings->get('location_id');
            if ($location_id) {
                $inventory = $square_api->get_inventory([], [$location_id]);
                if (!is_wp_error($inventory)) {
                    $square_inventory_count = count($inventory);
                }
            }
        }
    } catch (Exception $e) {
        // Silently handle API errors
    }
}

// Calculate sync percentage
$sync_percentage = $square_product_count > 0 ? round(($synced_products_count / $square_product_count) * 100, 1) : 0;

// Recent sync activity
$recent_sync_logs = $wpdb->get_var(
    "SELECT COUNT(*) FROM {$wpdb->prefix}square_kit_sync_logs
     WHERE log_type IN ('sync', 'import', 'export')
     AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
);

// Format last sync time
$last_sync_formatted = 'Never';
if ($last_sync && $last_sync !== 'Never') {
    $last_sync_time = strtotime($last_sync);
    if ($last_sync_time) {
        $time_diff = time() - $last_sync_time;
        if ($time_diff < 3600) {
            $last_sync_formatted = sprintf(__('%d minutes ago', 'squarekit'), floor($time_diff / 60));
        } elseif ($time_diff < 86400) {
            $last_sync_formatted = sprintf(__('%d hours ago', 'squarekit'), floor($time_diff / 3600));
        } else {
            $last_sync_formatted = sprintf(__('%d days ago', 'squarekit'), floor($time_diff / 86400));
        }
    }
}
?>

<div class="wrap squarekit-admin-wrap">
    <!-- Modern Header -->
    <div class="squarekit-admin-header">
        <h1><?php esc_html_e('Square Kit Dashboard', 'squarekit'); ?></h1>
        <p><?php esc_html_e('Monitor your Square and WooCommerce integration status, sync statistics, and recent activity.', 'squarekit'); ?></p>
    </div>

    <!-- Connection Status Banner -->
    <div class="squarekit-connection-banner <?php echo $is_connected ? 'connected' : 'disconnected'; ?>">
        <div class="squarekit-connection-status">
            <div class="status-indicator">
                <?php if ($is_connected): ?>
                    <div class="status-icon success">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                        </svg>
                    </div>
                <?php else: ?>
                    <div class="status-icon error">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18.3 5.71L12 12.01L5.7 5.71L4.29 7.12L10.59 13.42L4.29 19.72L5.7 21.13L12 14.83L18.3 21.13L19.71 19.72L13.41 13.42L19.71 7.12L18.3 5.71Z" fill="currentColor"/>
                        </svg>
                    </div>
                <?php endif; ?>
                <div class="status-text">
                    <strong><?php echo $is_connected ? esc_html__('Connected to Square', 'squarekit') : esc_html__('Not Connected', 'squarekit'); ?></strong>
                    <?php if ($is_connected): ?>
                        <span class="status-subtitle"><?php esc_html_e('Your Square account is successfully connected and ready to sync', 'squarekit'); ?></span>
                    <?php else: ?>
                        <span class="status-subtitle"><?php esc_html_e('Connect your Square account to start syncing products and orders', 'squarekit'); ?></span>
                    <?php endif; ?>
                </div>
            </div>
            <?php if (!$is_connected): ?>
                <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings')); ?>" class="squarekit-admin-btn-primary">
                    <?php esc_html_e('Connect Now', 'squarekit'); ?>
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="squarekit-stats-grid">
        <!-- Square Inventory Stats -->
        <div class="squarekit-stat-card primary">
            <div class="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 7H4V5C4 3.9 4.9 3 6 3H18C19.1 3 20 3.9 20 5V7Z" fill="currentColor"/>
                    <path d="M20 7V19C20 20.1 19.1 21 18 21H6C4.9 21 4 20.1 4 19V7H20Z" fill="currentColor" opacity="0.7"/>
                </svg>
            </div>
            <div class="stat-content">
                <h3><?php esc_html_e('Square Inventory', 'squarekit'); ?></h3>
                <div class="stat-number"><?php echo esc_html(number_format($square_inventory_count)); ?></div>
                <div class="stat-label"><?php esc_html_e('Items in Square', 'squarekit'); ?></div>
            </div>
        </div>

        <!-- Synced Products -->
        <div class="squarekit-stat-card success">
            <div class="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                </svg>
            </div>
            <div class="stat-content">
                <h3><?php esc_html_e('Synced Products', 'squarekit'); ?></h3>
                <div class="stat-number"><?php echo esc_html(number_format($synced_products_count)); ?></div>
                <div class="stat-label">
                    <?php echo esc_html(sprintf(__('of %s WooCommerce products', 'squarekit'), number_format($wc_product_count))); ?>
                </div>
                <div class="stat-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo esc_attr($sync_percentage); ?>%"></div>
                    </div>
                    <span class="progress-text"><?php echo esc_html($sync_percentage); ?>% synced</span>
                </div>
            </div>
        </div>

        <!-- Sync Issues -->
        <div class="squarekit-stat-card <?php echo $out_of_sync_count > 0 ? 'warning' : 'info'; ?>">
            <div class="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor"/>
                </svg>
            </div>
            <div class="stat-content">
                <h3><?php esc_html_e('Sync Status', 'squarekit'); ?></h3>
                <div class="stat-number"><?php echo esc_html(number_format($out_of_sync_count)); ?></div>
                <div class="stat-label">
                    <?php echo $out_of_sync_count > 0 ? esc_html__('Products need sync', 'squarekit') : esc_html__('All products in sync', 'squarekit'); ?>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="squarekit-stat-card info">
            <div class="stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor"/>
                </svg>
            </div>
            <div class="stat-content">
                <h3><?php esc_html_e('Recent Activity', 'squarekit'); ?></h3>
                <div class="stat-number"><?php echo esc_html(number_format($recent_sync_logs)); ?></div>
                <div class="stat-label"><?php esc_html_e('Sync operations (24h)', 'squarekit'); ?></div>
            </div>
        </div>
    </div>

    <!-- System Status & Quick Actions -->
    <div class="squarekit-dashboard-grid">
        <!-- System Status -->
        <div class="squarekit-card">
            <h3><?php esc_html_e('System Status', 'squarekit'); ?></h3>
            <div class="squarekit-status-list">
                <div class="status-item">
                    <span class="status-label"><?php esc_html_e('Square Connection:', 'squarekit'); ?></span>
                    <span class="squarekit-status <?php echo $is_connected ? 'success' : 'error'; ?>">
                        <?php echo $is_connected ? esc_html__('Connected', 'squarekit') : esc_html__('Disconnected', 'squarekit'); ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label"><?php esc_html_e('Webhooks:', 'squarekit'); ?></span>
                    <span class="squarekit-status <?php echo $webhook_status ? 'success' : 'warning'; ?>">
                        <?php echo $webhook_status ? esc_html__('Active', 'squarekit') : esc_html__('Inactive', 'squarekit'); ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label"><?php esc_html_e('Auto Sync:', 'squarekit'); ?></span>
                    <span class="squarekit-status info">
                        <?php echo esc_html(ucfirst($cron_schedule)); ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label"><?php esc_html_e('Last Sync:', 'squarekit'); ?></span>
                    <span class="status-value"><?php echo esc_html($last_sync_formatted); ?></span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="squarekit-card">
            <h3><?php esc_html_e('Quick Actions', 'squarekit'); ?></h3>

            <!-- Sync Now Section -->
            <?php if ($is_connected): ?>
                <div class="squarekit-sync-section">
                    <div class="sync-header">
                        <h4><?php esc_html_e('Manual Sync', 'squarekit'); ?></h4>
                        <p><?php esc_html_e('Trigger immediate synchronization based on your sync preferences.', 'squarekit'); ?></p>
                    </div>
                    <div class="sync-actions">
                        <button type="button" id="squarekit-sync-now" class="squarekit-admin-btn-primary sync-btn" data-sync-type="all">
                            <div class="btn-icon">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 4V1L8 5L12 9V6C15.31 6 18 8.69 18 12C18 13.01 17.75 13.97 17.3 14.8L18.76 16.26C19.54 15.03 20 13.57 20 12C20 7.58 16.42 4 12 4Z" fill="currentColor"/>
                                    <path d="M12 18C8.69 18 6 15.31 6 12C6 10.99 6.25 10.03 6.7 9.2L5.24 7.74C4.46 8.97 4 10.43 4 12C4 16.42 7.58 20 12 20V23L16 19L12 15V18Z" fill="currentColor"/>
                                </svg>
                            </div>
                            <span class="btn-text"><?php esc_html_e('Sync Now', 'squarekit'); ?></span>
                            <span class="btn-loading" style="display: none;">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </span>
                        </button>

                        <div class="sync-options">
                            <button type="button" class="sync-option-btn" data-sync-type="products">
                                <div class="option-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19 7H5V5C5 3.9 5.9 3 7 3H17C18.1 3 19 3.9 19 5V7Z" fill="currentColor"/>
                                        <path d="M19 7V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V7H19Z" fill="currentColor" opacity="0.7"/>
                                    </svg>
                                </div>
                                <div class="option-content">
                                    <span class="option-title"><?php esc_html_e('Products', 'squarekit'); ?></span>
                                    <span class="option-subtitle"><?php esc_html_e('Sync product data', 'squarekit'); ?></span>
                                </div>
                                <div class="option-check">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </button>

                            <button type="button" class="sync-option-btn" data-sync-type="customers">
                                <div class="option-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="option-content">
                                    <span class="option-title"><?php esc_html_e('Customers', 'squarekit'); ?></span>
                                    <span class="option-subtitle"><?php esc_html_e('Sync customer data', 'squarekit'); ?></span>
                                </div>
                                <div class="option-check">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </button>

                            <button type="button" class="sync-option-btn" data-sync-type="inventory">
                                <div class="option-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="option-content">
                                    <span class="option-title"><?php esc_html_e('Inventory', 'squarekit'); ?></span>
                                    <span class="option-subtitle"><?php esc_html_e('Sync stock levels', 'squarekit'); ?></span>
                                </div>
                                <div class="option-check">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </button>
                        </div>
                    </div>

                    <div id="sync-progress" class="sync-progress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="progress-text">
                            <span class="progress-status"><?php esc_html_e('Initializing...', 'squarekit'); ?></span>
                            <span class="progress-percentage">0%</span>
                        </div>
                    </div>
                </div>

                <div class="action-divider"></div>
            <?php endif; ?>

            <div class="squarekit-action-grid">
                <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-products')); ?>" class="squarekit-action-btn primary">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 7H5V5C5 3.9 5.9 3 7 3H17C18.1 3 19 3.9 19 5V7Z" fill="currentColor"/>
                            <path d="M19 7V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V7H19Z" fill="currentColor" opacity="0.7"/>
                        </svg>
                    </div>
                    <div class="action-content">
                        <span class="action-title"><?php esc_html_e('Products', 'squarekit'); ?></span>
                        <span class="action-subtitle"><?php echo esc_html(sprintf(__('%s items', 'squarekit'), number_format($wc_product_count))); ?></span>
                    </div>
                </a>

                <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-orders')); ?>" class="squarekit-action-btn">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="action-content">
                        <span class="action-title"><?php esc_html_e('Orders', 'squarekit'); ?></span>
                        <span class="action-subtitle"><?php echo esc_html(sprintf(__('%s orders', 'squarekit'), number_format($wc_order_count))); ?></span>
                    </div>
                </a>

                <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-customers')); ?>" class="squarekit-action-btn">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="action-content">
                        <span class="action-title"><?php esc_html_e('Customers', 'squarekit'); ?></span>
                        <span class="action-subtitle"><?php echo esc_html(sprintf(__('%s customers', 'squarekit'), number_format($wc_customer_count))); ?></span>
                    </div>
                </a>

                <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-tools')); ?>" class="squarekit-action-btn">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22.7 19L13.6 9.9C14.5 7.6 14 4.9 12.1 3C10.1 1 7.1 1 5.1 3S3.1 7.9 5.1 9.9C7 11.8 9.6 12.3 11.9 11.4L21 20.6C21.3 20.9 21.7 20.9 22 20.6L22.6 20C22.9 19.7 22.9 19.3 22.6 19L22.7 19ZM7.5 8.5C6.1 7.1 6.1 4.9 7.5 3.5S10.9 2.1 12.3 3.5 13.7 6.9 12.3 8.3 8.9 9.7 7.5 8.3V8.5Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="action-content">
                        <span class="action-title"><?php esc_html_e('Tools', 'squarekit'); ?></span>
                        <span class="action-subtitle"><?php esc_html_e('Sync & manage', 'squarekit'); ?></span>
                    </div>
                </a>

                <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings')); ?>" class="squarekit-action-btn">
                    <div class="action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19.14 12.94C19.18 12.64 19.2 12.33 19.2 12S19.18 11.36 19.14 11.06L21.16 9.48C21.34 9.34 21.39 9.07 21.28 8.87L19.36 5.55C19.24 5.33 18.99 5.26 18.77 5.33L16.38 6.29C15.88 5.91 15.35 5.59 14.76 5.35L14.4 2.81C14.36 2.57 14.16 2.4 13.92 2.4H10.08C9.84 2.4 9.64 2.57 9.6 2.81L9.24 5.35C8.65 5.59 8.12 5.92 7.62 6.29L5.23 5.33C5.01 5.25 4.76 5.33 4.64 5.55L2.72 8.87C2.61 9.08 2.66 9.34 2.84 9.48L4.86 11.06C4.82 11.36 4.8 11.69 4.8 12S4.82 12.64 4.86 12.94L2.84 14.52C2.66 14.66 2.61 14.93 2.72 15.13L4.64 18.45C4.76 18.67 5.01 18.74 5.23 18.67L7.62 17.71C8.12 18.09 8.65 18.41 9.24 18.65L9.6 21.19C9.64 21.43 9.84 21.6 10.08 21.6H13.92C14.16 21.6 14.36 21.43 14.4 21.19L14.76 18.65C15.35 18.41 15.88 18.09 16.38 17.71L18.77 18.67C18.99 18.75 19.24 18.67 19.36 18.45L21.28 15.13C21.39 14.93 21.34 14.66 21.16 14.52L19.14 12.94ZM12 15.6C10.02 15.6 8.4 13.98 8.4 12S10.02 8.4 12 8.4S15.6 10.02 15.6 12S13.98 15.6 12 15.6Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="action-content">
                        <span class="action-title"><?php esc_html_e('Settings', 'squarekit'); ?></span>
                        <span class="action-subtitle"><?php esc_html_e('Configure sync', 'squarekit'); ?></span>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity Log -->
    <div class="squarekit-card">
        <div class="card-header">
            <h3><?php esc_html_e('Recent Activity', 'squarekit'); ?></h3>
            <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-debug-logs')); ?>" class="squarekit-admin-btn">
                <?php esc_html_e('View All Logs', 'squarekit'); ?>
            </a>
        </div>

        <?php if ($logs): ?>
            <div class="squarekit-activity-log">
                <?php foreach ($logs as $log): ?>
                    <div class="activity-item">
                        <div class="activity-icon <?php echo esc_attr(strtolower($log->log_type)); ?>">
                            <?php if ($log->log_type === 'error'): ?>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor"/>
                                </svg>
                            <?php elseif ($log->log_type === 'success'): ?>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 16.17L4.83 12L3.41 13.41L9 19L21 7L19.59 5.59L9 16.17Z" fill="currentColor"/>
                                </svg>
                            <?php else: ?>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor"/>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div class="activity-content">
                            <div class="activity-message"><?php echo esc_html($log->log_message); ?></div>
                            <div class="activity-meta">
                                <span class="activity-type"><?php echo esc_html(ucfirst($log->log_type)); ?></span>
                                <span class="activity-time"><?php echo esc_html(human_time_diff(strtotime($log->created_at), current_time('timestamp'))); ?> ago</span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="squarekit-empty-state">
                <div class="empty-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill="currentColor" opacity="0.3"/>
                    </svg>
                </div>
                <h4><?php esc_html_e('No Recent Activity', 'squarekit'); ?></h4>
                <p><?php esc_html_e('Sync activity and system events will appear here.', 'squarekit'); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>