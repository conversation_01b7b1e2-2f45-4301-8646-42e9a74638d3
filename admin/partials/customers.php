<?php
// Robust Customers Admin UI for Square Kit
if ( ! defined( 'ABSPATH' ) ) exit;
?>
<div class="wrap squarekit-customers">
    <h1><?php esc_html_e('Square Kit Customers', 'squarekit'); ?></h1>
    <form id="squarekit-customers-filter" method="get" action="" style="margin-bottom:1em;display:flex;gap:1em;align-items:flex-end;">
        <input type="hidden" name="page" value="squarekit-customers" />
        <input type="search" name="s" id="squarekit-customers-search" placeholder="<?php esc_attr_e('Search customers...', 'squarekit'); ?>" />
        <select name="sync_status" id="squarekit-customers-sync-status">
            <option value=""><?php esc_html_e('All Sync Statuses', 'squarekit'); ?></option>
            <option value="synced"><?php esc_html_e('Synced', 'squarekit'); ?></option>
            <option value="not_synced"><?php esc_html_e('Not Synced', 'squarekit'); ?></option>
        </select>
        <label for="squarekit-customers-per-page" style="margin-bottom:0;">
            <?php esc_html_e('Per page:', 'squarekit'); ?>
            <select name="per_page" id="squarekit-customers-per-page">
                <option value="20">20</option>
                <option value="50" selected>50</option>
                <option value="100">100</option>
            </select>
        </label>
        <button class="button" id="squarekit-customers-filter-btn" type="submit"><?php esc_html_e('Filter', 'squarekit'); ?></button>
    </form>
    <div id="squarekit-customers-feedback" role="status" aria-live="polite" style="margin-bottom:1em;"></div>
    <div id="squarekit-customers-table-wrapper" aria-busy="true">
        <!-- Table will be loaded here via AJAX -->
    </div>
    <div id="squarekit-customer-modal" style="display:none;" role="dialog" aria-modal="true" aria-labelledby="squarekit-customer-modal-title">
        <div class="squarekit-modal-content">
            <button class="squarekit-modal-close" aria-label="Close">&times;</button>
            <div id="squarekit-customer-modal-body">
                <!-- Customer details will be loaded here -->
            </div>
        </div>
    </div>
</div>
<script>
jQuery(function($){
    function loadCustomers(page) {
        var data = $('#squarekit-customers-filter').serializeArray();
        data.push({name:'action',value:'squarekit_customers_table_ajax'});
        data.push({name:'nonce',value:squarekit_admin.nonce});
        if(page) data.push({name:'paged',value:page});
        $('#squarekit-customers-table-wrapper').attr('aria-busy','true').html('<div class="spinner is-active"></div>');
        $.post(squarekit_admin.ajax_url, data, function(resp){
            if(resp.success) {
                $('#squarekit-customers-table-wrapper').html(resp.data.table);
            } else {
                $('#squarekit-customers-table-wrapper').html('<div class="notice notice-error">'+(resp.data && resp.data.message ? resp.data.message : 'Error loading customers')+'</div>');
            }
            $('#squarekit-customers-table-wrapper').attr('aria-busy','false');
        }).fail(function(){
            $('#squarekit-customers-table-wrapper').html('<div class="notice notice-error">AJAX error.</div>').attr('aria-busy','false');
        });
    }
    $('#squarekit-customers-filter').on('submit', function(e){ e.preventDefault(); loadCustomers(1); });
    $(document).on('click', '.squarekit-customers-pagination a', function(e){
        e.preventDefault();
        var page = $(this).data('page');
        loadCustomers(page);
    });
    $(document).on('click', '.sync-customer, .export-customer, .remove-meta-customer', function(e){
        e.preventDefault();
        var btn = $(this), id = btn.data('id'), action = btn.hasClass('sync-customer') ? 'sync' : btn.hasClass('export-customer') ? 'export' : 'remove_meta';
        btn.prop('disabled', true).attr('aria-busy','true');
        $('#squarekit-customers-feedback').html('<span class="spinner is-active"></span>');
        $.post(squarekit_admin.ajax_url, {
            action: 'squarekit_customer_action',
            nonce: squarekit_admin.nonce,
            customer_id: id,
            customer_action: action
        }, function(resp){
            if(resp.success) {
                $('#squarekit-customers-feedback').html('<div class="notice notice-success">'+resp.data.message+'</div>');
                loadCustomers();
            } else {
                $('#squarekit-customers-feedback').html('<div class="notice notice-error">'+(resp.data && resp.data.message ? resp.data.message : 'Action failed')+'</div>');
                btn.prop('disabled', false).attr('aria-busy','false');
            }
        }).fail(function(){
            $('#squarekit-customers-feedback').html('<div class="notice notice-error">AJAX error.</div>');
            btn.prop('disabled', false).attr('aria-busy','false');
        });
    });
    $(document).on('click', '.view-customer-details', function(e){
        e.preventDefault();
        var id = $(this).data('id');
        $('#squarekit-customer-modal').show().attr('aria-busy','true');
        $('#squarekit-customer-modal-body').html('<div class="spinner is-active"></div>');
        $.post(squarekit_admin.ajax_url, {
            action: 'squarekit_customer_details',
            nonce: squarekit_admin.nonce,
            customer_id: id
        }, function(resp){
            if(resp.success) {
                $('#squarekit-customer-modal-body').html(resp.data.html);
            } else {
                $('#squarekit-customer-modal-body').html('<div class="notice notice-error">'+(resp.data && resp.data.message ? resp.data.message : 'Error loading details')+'</div>');
            }
            $('#squarekit-customer-modal').attr('aria-busy','false');
        }).fail(function(){
            $('#squarekit-customer-modal-body').html('<div class="notice notice-error">AJAX error.</div>');
            $('#squarekit-customer-modal').attr('aria-busy','false');
        });
    });
    $('.squarekit-modal-close').on('click', function(){
        $('#squarekit-customer-modal').hide();
        $('#squarekit-customer-modal-body').html('');
    });
    // Accessibility: close modal on ESC
    $(document).on('keydown', function(e){
        if(e.key === 'Escape') $('#squarekit-customer-modal').hide();
    });
    // Initial load
    loadCustomers(1);
});
</script>
<style>
.squarekit-modal-content { background:#fff; padding:2em; border-radius:8px; max-width:600px; margin:2em auto; position:relative; }
.squarekit-modal-close { position:absolute; top:1em; right:1em; background:none; border:none; font-size:2em; cursor:pointer; }
.spinner.is-active { display:inline-block; width:24px; height:24px; border:4px solid #ccc; border-top:4px solid #0073aa; border-radius:50%; animation:spin 1s linear infinite; vertical-align:middle; }
@keyframes spin { 100% { transform:rotate(360deg); } }
</style> 