<?php
/**
 * Conflicts Management Tab - Modern Design
 *
 * @package SquareKit
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Helper function to format conflict values for display
 */
function format_conflict_value( $value, $type ) {
    switch ( $type ) {
        case 'inventory':
            return is_numeric( $value ) ? number_format( $value ) . ' units' : $value;
        case 'price':
            return is_numeric( $value ) ? '$' . number_format( $value, 2 ) : $value;
        default:
            return is_array( $value ) || is_object( $value ) ? wp_json_encode( $value ) : $value;
    }
}

// Initialize conflict manager
$conflict_manager = new SquareKit_Conflict_Manager();

// Handle conflict resolution
if ( isset( $_POST['resolve_conflict'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'resolve_conflict' ) ) {
    $conflict_id = intval( $_POST['conflict_id'] );
    $resolution = sanitize_text_field( $_POST['resolution'] );
    $custom_value = isset( $_POST['custom_value'] ) ? sanitize_text_field( $_POST['custom_value'] ) : null;
    $notes = sanitize_textarea_field( $_POST['notes'] );

    if ( $conflict_manager->resolve_conflict( $conflict_id, $resolution, $custom_value, $notes ) ) {
        echo '<div class="squarekit-notice squarekit-notice-success">
                <div class="squarekit-notice-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="squarekit-notice-content">
                    <h4>' . esc_html__( 'Success!', 'squarekit' ) . '</h4>
                    <p>' . esc_html__( 'Conflict resolved successfully!', 'squarekit' ) . '</p>
                </div>
              </div>';
    } else {
        echo '<div class="squarekit-notice squarekit-notice-error">
                <div class="squarekit-notice-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="squarekit-notice-content">
                    <h4>' . esc_html__( 'Error', 'squarekit' ) . '</h4>
                    <p>' . esc_html__( 'Failed to resolve conflict. Please try again.', 'squarekit' ) . '</p>
                </div>
              </div>';
    }
}

// Get conflicts
$pending_conflicts = $conflict_manager->get_pending_conflicts();
$conflict_count = $conflict_manager->get_conflict_count();
$resolved_today = $conflict_manager->get_conflict_count( 'resolved' );
?>

<!-- Conflicts Tab Content -->
<div class="squarekit-conflicts-wrapper">
<div class="squarekit-section">
    <div class="squarekit-section-header">
        <div class="squarekit-header-content">
            <div class="squarekit-header-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div>
                <h2><?php esc_html_e( 'Sync Conflicts', 'squarekit' ); ?></h2>
                <p class="squarekit-section-description">
                    <?php esc_html_e( 'Review and resolve conflicts when the same data is changed in both Square and WooCommerce.', 'squarekit' ); ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="squarekit-stats-grid">
        <div class="squarekit-stat-card warning">
            <div class="squarekit-stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="squarekit-stat-content">
                <div class="squarekit-stat-number"><?php echo esc_html( $conflict_count ); ?></div>
                <div class="squarekit-stat-label"><?php esc_html_e( 'Pending Conflicts', 'squarekit' ); ?></div>
                <div class="squarekit-stat-description"><?php esc_html_e( 'Require your attention', 'squarekit' ); ?></div>
            </div>
        </div>

        <div class="squarekit-stat-card success">
            <div class="squarekit-stat-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="squarekit-stat-content">
                <div class="squarekit-stat-number"><?php echo esc_html( $resolved_today ); ?></div>
                <div class="squarekit-stat-label"><?php esc_html_e( 'Resolved Today', 'squarekit' ); ?></div>
                <div class="squarekit-stat-description"><?php esc_html_e( 'Successfully handled', 'squarekit' ); ?></div>
            </div>
        </div>
    </div>

    <?php if ( empty( $pending_conflicts ) ) : ?>
        <!-- No Conflicts State -->
        <div class="squarekit-empty-state">
            <div class="squarekit-empty-state-icon">
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div class="squarekit-empty-state-content">
                <h3><?php esc_html_e( 'No Conflicts Found', 'squarekit' ); ?></h3>
                <p><?php esc_html_e( 'All your Square and WooCommerce data is in sync! Your systems are working together perfectly.', 'squarekit' ); ?></p>
                <div class="squarekit-empty-state-actions">
                    <button type="button" class="squarekit-btn-secondary" onclick="location.reload();">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 4V10H7M23 20V14H17M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <?php esc_html_e( 'Refresh Status', 'squarekit' ); ?>
                    </button>
                </div>
            </div>
        </div>
    <?php else : ?>
        <!-- Conflicts List -->
        <div class="squarekit-conflicts-container">
            <div class="squarekit-conflicts-header">
                <h3><?php esc_html_e( 'Pending Conflicts', 'squarekit' ); ?></h3>
                <p class="squarekit-conflicts-description">
                    <?php esc_html_e( 'Review each conflict and choose how to resolve the data differences.', 'squarekit' ); ?>
                </p>
            </div>

            <div class="squarekit-conflicts-list">
                <?php foreach ( $pending_conflicts as $conflict ) : ?>
                    <div class="squarekit-conflict-card" data-conflict-id="<?php echo esc_attr( $conflict->id ); ?>">
                        <div class="squarekit-conflict-header">
                            <div class="squarekit-conflict-info">
                                <div class="squarekit-conflict-type">
                                    <span class="squarekit-conflict-badge squarekit-conflict-badge-<?php echo esc_attr( strtolower( $conflict->conflict_type ) ); ?>">
                                        <?php echo esc_html( ucfirst( $conflict->conflict_type ) ); ?>
                                    </span>
                                </div>
                                <div class="squarekit-conflict-title">
                                    <h4><?php echo esc_html( ucfirst( $conflict->object_type ) ); ?> Conflict</h4>
                                    <div class="squarekit-conflict-meta">
                                        <span class="squarekit-conflict-id">ID: <?php echo esc_html( $conflict->object_id ); ?></span>
                                        <span class="squarekit-conflict-time">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            <?php echo esc_html( human_time_diff( strtotime( $conflict->detected_at ), current_time( 'timestamp' ) ) ); ?> ago
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="squarekit-conflict-actions">
                                <button type="button" class="squarekit-btn-primary resolve-conflict-btn" data-conflict-id="<?php echo esc_attr( $conflict->id ); ?>">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                    <?php esc_html_e( 'Resolve', 'squarekit' ); ?>
                                </button>
                            </div>
                        </div>

                        <div class="squarekit-conflict-details">
                            <div class="squarekit-value-comparison">
                                <div class="squarekit-value-item squarekit-value-square">
                                    <div class="squarekit-value-header">
                                        <div class="squarekit-value-platform">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                                <rect x="9" y="9" width="6" height="6" stroke="currentColor" stroke-width="2"/>
                                            </svg>
                                            <strong><?php esc_html_e( 'Square Value', 'squarekit' ); ?></strong>
                                        </div>
                                        <?php if ( $conflict->square_last_modified ) : ?>
                                            <div class="squarekit-value-timestamp">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                                    <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <?php echo esc_html( date( 'M j, Y g:i A', strtotime( $conflict->square_last_modified ) ) ); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="squarekit-value-content">
                                        <div class="squarekit-value-display">
                                            <?php echo esc_html( format_conflict_value( $conflict->square_value, $conflict->conflict_type ) ); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="squarekit-vs-divider">
                                    <div class="squarekit-vs-icon">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8 12H16M12 8V16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <span><?php esc_html_e( 'VS', 'squarekit' ); ?></span>
                                </div>

                                <div class="squarekit-value-item squarekit-value-woocommerce">
                                    <div class="squarekit-value-header">
                                        <div class="squarekit-value-platform">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M3.055 11H5a2 2 0 0 1 2 2v1a2 2 0 0 0 2 2 2 2 0 0 0 2-2v-1a2 2 0 0 1 2-2h1.945M12 7v4M12 21v-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M3 15h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            <strong><?php esc_html_e( 'WooCommerce Value', 'squarekit' ); ?></strong>
                                        </div>
                                        <?php if ( $conflict->woocommerce_last_modified ) : ?>
                                            <div class="squarekit-value-timestamp">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                                    <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                                <?php echo esc_html( date( 'M j, Y g:i A', strtotime( $conflict->woocommerce_last_modified ) ) ); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="squarekit-value-content">
                                        <div class="squarekit-value-display">
                                            <?php echo esc_html( format_conflict_value( $conflict->woocommerce_value, $conflict->conflict_type ) ); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="squarekit-resolution-form" id="resolution-form-<?php echo esc_attr( $conflict->id ); ?>" style="display: none;">
                                <form method="post" action="" class="squarekit-form">
                                    <?php wp_nonce_field( 'resolve_conflict' ); ?>
                                    <input type="hidden" name="conflict_id" value="<?php echo esc_attr( $conflict->id ); ?>">
                                    <input type="hidden" name="resolve_conflict" value="1">

                                    <div class="squarekit-resolution-header">
                                        <h4><?php esc_html_e( 'Choose Resolution Method', 'squarekit' ); ?></h4>
                                        <p><?php esc_html_e( 'Select how you want to resolve this conflict:', 'squarekit' ); ?></p>
                                    </div>

                                    <div class="squarekit-resolution-options">
                                        <label class="squarekit-resolution-option">
                                            <input type="radio" name="resolution" value="square_wins" checked>
                                            <div class="squarekit-option-card">
                                                <div class="squarekit-option-icon">
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                                        <rect x="9" y="9" width="6" height="6" stroke="currentColor" stroke-width="2"/>
                                                    </svg>
                                                </div>
                                                <div class="squarekit-option-content">
                                                    <strong><?php esc_html_e( 'Use Square Value', 'squarekit' ); ?></strong>
                                                    <span class="squarekit-option-description"><?php esc_html_e( 'Square data will overwrite WooCommerce', 'squarekit' ); ?></span>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="squarekit-resolution-option">
                                            <input type="radio" name="resolution" value="woocommerce_wins">
                                            <div class="squarekit-option-card">
                                                <div class="squarekit-option-icon">
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M3.055 11H5a2 2 0 0 1 2 2v1a2 2 0 0 0 2 2 2 2 0 0 0 2-2v-1a2 2 0 0 1 2-2h1.945M12 7v4M12 21v-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                        <path d="M3 15h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                                <div class="squarekit-option-content">
                                                    <strong><?php esc_html_e( 'Use WooCommerce Value', 'squarekit' ); ?></strong>
                                                    <span class="squarekit-option-description"><?php esc_html_e( 'WooCommerce data will overwrite Square', 'squarekit' ); ?></span>
                                                </div>
                                            </div>
                                        </label>

                                        <label class="squarekit-resolution-option">
                                            <input type="radio" name="resolution" value="custom">
                                            <div class="squarekit-option-card">
                                                <div class="squarekit-option-icon">
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                                <div class="squarekit-option-content">
                                                    <strong><?php esc_html_e( 'Custom Value', 'squarekit' ); ?></strong>
                                                    <span class="squarekit-option-description"><?php esc_html_e( 'Enter a custom value to use instead', 'squarekit' ); ?></span>
                                                </div>
                                            </div>
                                        </label>

                                        <div class="squarekit-custom-value-input" style="display: none;">
                                            <div class="squarekit-form-group">
                                                <label for="custom_value_<?php echo esc_attr( $conflict->id ); ?>" class="squarekit-form-label">
                                                    <?php esc_html_e( 'Custom Value:', 'squarekit' ); ?>
                                                </label>
                                                <input type="text"
                                                       id="custom_value_<?php echo esc_attr( $conflict->id ); ?>"
                                                       name="custom_value"
                                                       class="squarekit-form-input"
                                                       placeholder="<?php esc_attr_e( 'Enter custom value', 'squarekit' ); ?>">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="squarekit-resolution-notes">
                                        <div class="squarekit-form-group">
                                            <label for="notes_<?php echo esc_attr( $conflict->id ); ?>" class="squarekit-form-label">
                                                <?php esc_html_e( 'Resolution Notes', 'squarekit' ); ?>
                                                <span class="squarekit-form-optional"><?php esc_html_e( '(Optional)', 'squarekit' ); ?></span>
                                            </label>
                                            <textarea id="notes_<?php echo esc_attr( $conflict->id ); ?>"
                                                      name="notes"
                                                      class="squarekit-form-textarea"
                                                      rows="3"
                                                      placeholder="<?php esc_attr_e( 'Add notes about why you chose this resolution...', 'squarekit' ); ?>"></textarea>
                                        </div>
                                    </div>

                                    <div class="squarekit-resolution-actions">
                                        <button type="submit" class="squarekit-btn-primary">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            <?php esc_html_e( 'Apply Resolution', 'squarekit' ); ?>
                                        </button>
                                        <button type="button" class="squarekit-btn-secondary cancel-resolution">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                            <?php esc_html_e( 'Cancel', 'squarekit' ); ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>
</div>

<script>
jQuery(document).ready(function($) {
    // Show/hide resolution form with improved animations
    $('.resolve-conflict-btn').on('click', function() {
        var conflictId = $(this).data('conflict-id');
        var form = $('#resolution-form-' + conflictId);
        var $btn = $(this);
        var $icon = $btn.find('svg');

        if (form.is(':visible')) {
            form.slideUp(300, function() {
                $btn.removeClass('active');
            });
            $btn.html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg><?php esc_html_e( 'Resolve', 'squarekit' ); ?>');
        } else {
            // Hide other open forms first
            $('.squarekit-resolution-form:visible').slideUp(200);
            $('.resolve-conflict-btn.active').removeClass('active').html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg><?php esc_html_e( 'Resolve', 'squarekit' ); ?>');

            setTimeout(function() {
                form.slideDown(300);
                $btn.addClass('active');
                $btn.html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg><?php esc_html_e( 'Cancel', 'squarekit' ); ?>');
            }, 200);
        }
    });

    // Cancel resolution
    $('.cancel-resolution').on('click', function() {
        var form = $(this).closest('.squarekit-resolution-form');
        var conflictId = form.attr('id').replace('resolution-form-', '');
        var btn = $('.resolve-conflict-btn[data-conflict-id="' + conflictId + '"]');

        form.slideUp(300, function() {
            btn.removeClass('active');
        });
        btn.html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg><?php esc_html_e( 'Resolve', 'squarekit' ); ?>');
    });

    // Show/hide custom value input with smooth animation
    $('input[name="resolution"]').on('change', function() {
        var customInput = $(this).closest('.squarekit-resolution-options').find('.squarekit-custom-value-input');

        if ($(this).val() === 'custom') {
            customInput.slideDown(300);
            customInput.find('input').focus();
        } else {
            customInput.slideUp(300);
        }
    });

    // Add hover effects to conflict cards
    $('.squarekit-conflict-card').hover(
        function() {
            $(this).addClass('hover');
        },
        function() {
            $(this).removeClass('hover');
        }
    );

    // Form validation
    $('.squarekit-form').on('submit', function(e) {
        var resolution = $(this).find('input[name="resolution"]:checked').val();
        var customValue = $(this).find('input[name="custom_value"]').val();

        if (resolution === 'custom' && !customValue.trim()) {
            e.preventDefault();
            alert('<?php esc_html_e( 'Please enter a custom value.', 'squarekit' ); ?>');
            $(this).find('input[name="custom_value"]').focus();
            return false;
        }

        // Show loading state
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        submitBtn.prop('disabled', true).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 12 12" to="360 12 12" dur="1s" repeatCount="indefinite"/></svg><?php esc_html_e( 'Applying...', 'squarekit' ); ?>');
    });
});
</script>
