<?php
/**
 * Conflicts Management Tab
 *
 * @package SquareKit
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Helper function to format conflict values for display
 */
function format_conflict_value( $value, $type ) {
    switch ( $type ) {
        case 'inventory':
            return is_numeric( $value ) ? number_format( $value ) . ' units' : $value;
        case 'price':
            return is_numeric( $value ) ? '$' . number_format( $value, 2 ) : $value;
        default:
            return is_array( $value ) || is_object( $value ) ? wp_json_encode( $value ) : $value;
    }
}

// Initialize conflict manager
$conflict_manager = new SquareKit_Conflict_Manager();

// Handle conflict resolution
if ( isset( $_POST['resolve_conflict'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'resolve_conflict' ) ) {
    $conflict_id = intval( $_POST['conflict_id'] );
    $resolution = sanitize_text_field( $_POST['resolution'] );
    $custom_value = isset( $_POST['custom_value'] ) ? sanitize_text_field( $_POST['custom_value'] ) : null;
    $notes = sanitize_textarea_field( $_POST['notes'] );
    
    if ( $conflict_manager->resolve_conflict( $conflict_id, $resolution, $custom_value, $notes ) ) {
        echo '<div class="notice notice-success"><p>' . esc_html__( 'Conflict resolved successfully!', 'squarekit' ) . '</p></div>';
    } else {
        echo '<div class="notice notice-error"><p>' . esc_html__( 'Failed to resolve conflict.', 'squarekit' ) . '</p></div>';
    }
}

// Get conflicts
$pending_conflicts = $conflict_manager->get_pending_conflicts();
$conflict_count = $conflict_manager->get_conflict_count();
?>

<div class="squarekit-conflicts-tab">
    <div class="squarekit-section">
        <div class="squarekit-section-header">
            <h2><?php esc_html_e( 'Sync Conflicts', 'squarekit' ); ?></h2>
            <p class="description">
                <?php esc_html_e( 'Review and resolve conflicts when the same data is changed in both Square and WooCommerce.', 'squarekit' ); ?>
            </p>
        </div>

        <div class="conflict-stats">
            <div class="stat-card pending">
                <div class="stat-number"><?php echo esc_html( $conflict_count ); ?></div>
                <div class="stat-label"><?php esc_html_e( 'Pending Conflicts', 'squarekit' ); ?></div>
            </div>
            <div class="stat-card resolved">
                <div class="stat-number"><?php echo esc_html( $conflict_manager->get_conflict_count( 'resolved' ) ); ?></div>
                <div class="stat-label"><?php esc_html_e( 'Resolved Today', 'squarekit' ); ?></div>
            </div>
        </div>

        <?php if ( empty( $pending_conflicts ) ) : ?>
            <div class="no-conflicts">
                <div class="no-conflicts-icon">✅</div>
                <h3><?php esc_html_e( 'No Conflicts Found', 'squarekit' ); ?></h3>
                <p><?php esc_html_e( 'All your Square and WooCommerce data is in sync!', 'squarekit' ); ?></p>
            </div>
        <?php else : ?>
            <div class="conflicts-list">
                <?php foreach ( $pending_conflicts as $conflict ) : ?>
                    <div class="conflict-item" data-conflict-id="<?php echo esc_attr( $conflict->id ); ?>">
                        <div class="conflict-header">
                            <div class="conflict-title">
                                <h4><?php echo esc_html( ucfirst( $conflict->conflict_type ) ); ?> Conflict</h4>
                                <span class="conflict-meta">
                                    <?php echo esc_html( ucfirst( $conflict->object_type ) ); ?> #<?php echo esc_html( $conflict->object_id ); ?>
                                    • <?php echo esc_html( human_time_diff( strtotime( $conflict->detected_at ), current_time( 'timestamp' ) ) ); ?> ago
                                </span>
                            </div>
                            <div class="conflict-actions">
                                <button type="button" class="button resolve-conflict-btn" data-conflict-id="<?php echo esc_attr( $conflict->id ); ?>">
                                    <?php esc_html_e( 'Resolve', 'squarekit' ); ?>
                                </button>
                            </div>
                        </div>

                        <div class="conflict-details">
                            <div class="conflict-values">
                                <div class="value-comparison">
                                    <div class="value-item square">
                                        <div class="value-header">
                                            <strong><?php esc_html_e( 'Square Value', 'squarekit' ); ?></strong>
                                            <?php if ( $conflict->square_last_modified ) : ?>
                                                <span class="timestamp"><?php echo esc_html( date( 'M j, Y g:i A', strtotime( $conflict->square_last_modified ) ) ); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="value-content">
                                            <?php echo esc_html( format_conflict_value( $conflict->square_value, $conflict->conflict_type ) ); ?>
                                        </div>
                                    </div>

                                    <div class="vs-divider">VS</div>

                                    <div class="value-item woocommerce">
                                        <div class="value-header">
                                            <strong><?php esc_html_e( 'WooCommerce Value', 'squarekit' ); ?></strong>
                                            <?php if ( $conflict->woocommerce_last_modified ) : ?>
                                                <span class="timestamp"><?php echo esc_html( date( 'M j, Y g:i A', strtotime( $conflict->woocommerce_last_modified ) ) ); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="value-content">
                                            <?php echo esc_html( format_conflict_value( $conflict->woocommerce_value, $conflict->conflict_type ) ); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="resolution-form" id="resolution-form-<?php echo esc_attr( $conflict->id ); ?>" style="display: none;">
                                <form method="post" action="">
                                    <?php wp_nonce_field( 'resolve_conflict' ); ?>
                                    <input type="hidden" name="conflict_id" value="<?php echo esc_attr( $conflict->id ); ?>">
                                    <input type="hidden" name="resolve_conflict" value="1">

                                    <div class="resolution-options">
                                        <h4><?php esc_html_e( 'Choose Resolution', 'squarekit' ); ?></h4>
                                        
                                        <label class="resolution-option">
                                            <input type="radio" name="resolution" value="square_wins" checked>
                                            <div class="option-content">
                                                <strong><?php esc_html_e( 'Use Square Value', 'squarekit' ); ?></strong>
                                                <span class="option-description"><?php esc_html_e( 'Square data will overwrite WooCommerce', 'squarekit' ); ?></span>
                                            </div>
                                        </label>

                                        <label class="resolution-option">
                                            <input type="radio" name="resolution" value="woocommerce_wins">
                                            <div class="option-content">
                                                <strong><?php esc_html_e( 'Use WooCommerce Value', 'squarekit' ); ?></strong>
                                                <span class="option-description"><?php esc_html_e( 'WooCommerce data will overwrite Square', 'squarekit' ); ?></span>
                                            </div>
                                        </label>

                                        <label class="resolution-option">
                                            <input type="radio" name="resolution" value="custom">
                                            <div class="option-content">
                                                <strong><?php esc_html_e( 'Custom Value', 'squarekit' ); ?></strong>
                                                <span class="option-description"><?php esc_html_e( 'Enter a custom value', 'squarekit' ); ?></span>
                                            </div>
                                        </label>

                                        <div class="custom-value-input" style="display: none;">
                                            <label for="custom_value_<?php echo esc_attr( $conflict->id ); ?>">
                                                <?php esc_html_e( 'Custom Value:', 'squarekit' ); ?>
                                            </label>
                                            <input type="text" 
                                                   id="custom_value_<?php echo esc_attr( $conflict->id ); ?>" 
                                                   name="custom_value" 
                                                   placeholder="<?php esc_attr_e( 'Enter custom value', 'squarekit' ); ?>">
                                        </div>
                                    </div>

                                    <div class="resolution-notes">
                                        <label for="notes_<?php echo esc_attr( $conflict->id ); ?>">
                                            <?php esc_html_e( 'Resolution Notes (Optional):', 'squarekit' ); ?>
                                        </label>
                                        <textarea id="notes_<?php echo esc_attr( $conflict->id ); ?>" 
                                                  name="notes" 
                                                  rows="3" 
                                                  placeholder="<?php esc_attr_e( 'Add notes about why you chose this resolution...', 'squarekit' ); ?>"></textarea>
                                    </div>

                                    <div class="resolution-actions">
                                        <button type="submit" class="button button-primary">
                                            <?php esc_html_e( 'Apply Resolution', 'squarekit' ); ?>
                                        </button>
                                        <button type="button" class="button cancel-resolution">
                                            <?php esc_html_e( 'Cancel', 'squarekit' ); ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Show/hide resolution form
    $('.resolve-conflict-btn').on('click', function() {
        var conflictId = $(this).data('conflict-id');
        var form = $('#resolution-form-' + conflictId);
        
        if (form.is(':visible')) {
            form.slideUp();
            $(this).text('<?php esc_html_e( 'Resolve', 'squarekit' ); ?>');
        } else {
            form.slideDown();
            $(this).text('<?php esc_html_e( 'Cancel', 'squarekit' ); ?>');
        }
    });

    // Cancel resolution
    $('.cancel-resolution').on('click', function() {
        var form = $(this).closest('.resolution-form');
        var conflictId = form.attr('id').replace('resolution-form-', '');
        var btn = $('.resolve-conflict-btn[data-conflict-id="' + conflictId + '"]');
        
        form.slideUp();
        btn.text('<?php esc_html_e( 'Resolve', 'squarekit' ); ?>');
    });

    // Show/hide custom value input
    $('input[name="resolution"]').on('change', function() {
        var customInput = $(this).closest('.resolution-options').find('.custom-value-input');
        
        if ($(this).val() === 'custom') {
            customInput.slideDown();
        } else {
            customInput.slideUp();
        }
    });
});
</script>


