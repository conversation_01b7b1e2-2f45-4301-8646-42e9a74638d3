<?php
// Mapping Settings Tab
if ( ! defined( 'ABSPATH' ) ) exit;

// Get mapping data
$customer_role_mapping = $settings->get_customer_role_mapping();
$fulfillment_mapping = $settings->get_fulfillment_mapping();
$pickup_location_mapping = $settings->get_pickup_location_mapping();
$sku_mapping_rules = $settings->get_sku_mapping_rules();

// Get available WooCommerce roles
$wc_roles = wp_roles()->get_names();

// Get available shipping methods
$shipping_methods = array();
if (class_exists('WC_Shipping')) {
    $shipping_zones = WC_Shipping_Zones::get_zones();
    foreach ($shipping_zones as $zone) {
        foreach ($zone['shipping_methods'] as $method) {
            $shipping_methods[$method->id] = $method->get_title();
        }
    }
}
?>

<form method="post" action="" class="squarekit-settings-form">

    <!-- Attribute Mapping Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Product Attribute Mapping', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Map Square product attributes to WooCommerce product attributes.', 'squarekit'); ?></p>

        <form method="post" action="">
            <?php wp_nonce_field('squarekit_save_attribute_mapping', 'squarekit_attribute_mapping_nonce'); ?>

            <div class="squarekit-mapping-container">
                <div class="mapping-header">
                    <div class="mapping-col"><?php esc_html_e('Square Attribute', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('WooCommerce Attribute', 'squarekit'); ?></div>
                    <div class="mapping-actions"><?php esc_html_e('Actions', 'squarekit'); ?></div>
                </div>

                <div id="attribute-mapping-rows">
                    <?php if (!empty($attribute_mapping)): ?>
                        <?php foreach ($attribute_mapping as $square_attr => $wc_attr): ?>
                            <div class="mapping-row">
                                <div class="mapping-col">
                                    <input type="text" name="square_attribute[]" value="<?php echo esc_attr($square_attr); ?>" placeholder="<?php esc_attr_e('Square attribute name', 'squarekit'); ?>" />
                                </div>
                                <div class="mapping-col">
                                    <select name="wc_attribute[]">
                                        <option value=""><?php esc_html_e('Select WooCommerce attribute', 'squarekit'); ?></option>
                                        <?php foreach ($wc_attributes as $attr_key => $attr_label): ?>
                                            <option value="<?php echo esc_attr($attr_key); ?>" <?php selected($wc_attr, $attr_key); ?>><?php echo esc_html($attr_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mapping-actions">
                                    <button type="button" class="squarekit-btn-remove" onclick="removeAttributeRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="mapping-row">
                            <div class="mapping-col">
                                <input type="text" name="square_attribute[]" placeholder="<?php esc_attr_e('Square attribute name', 'squarekit'); ?>" />
                            </div>
                            <div class="mapping-col">
                                <select name="wc_attribute[]">
                                    <option value=""><?php esc_html_e('Select WooCommerce attribute', 'squarekit'); ?></option>
                                    <?php foreach ($wc_attributes as $attr_key => $attr_label): ?>
                                        <option value="<?php echo esc_attr($attr_key); ?>"><?php echo esc_html($attr_label); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mapping-actions">
                                <button type="button" class="squarekit-btn-remove" onclick="removeAttributeRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mapping-footer">
                    <button type="button" class="squarekit-admin-btn" onclick="addAttributeRow()"><?php esc_html_e('Add Mapping', 'squarekit'); ?></button>
                    <button type="submit" name="save_attribute_mapping" class="squarekit-admin-btn-primary"><?php esc_html_e('Save Attribute Mapping', 'squarekit'); ?></button>
                </div>
            </div>
        </form>
    </div>

    <!-- Order Status Mapping Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Order Status Mapping', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Map WooCommerce order statuses to Square order statuses.', 'squarekit'); ?></p>

        <form method="post" action="">
            <?php wp_nonce_field('squarekit_save_status_mapping', 'squarekit_status_mapping_nonce'); ?>

            <div class="squarekit-mapping-container">
                <div class="mapping-header">
                    <div class="mapping-col"><?php esc_html_e('WooCommerce Status', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('Square Status', 'squarekit'); ?></div>
                    <div class="mapping-actions"><?php esc_html_e('Actions', 'squarekit'); ?></div>
                </div>

                <div id="status-mapping-rows">
                    <?php if (!empty($status_mapping)): ?>
                        <?php foreach ($status_mapping as $wc_status => $square_status): ?>
                            <div class="mapping-row">
                                <div class="mapping-col">
                                    <select name="wc_status[]">
                                        <option value=""><?php esc_html_e('Select WooCommerce status', 'squarekit'); ?></option>
                                        <?php foreach ($wc_statuses as $status_key => $status_label): ?>
                                            <option value="<?php echo esc_attr($status_key); ?>" <?php selected($wc_status, $status_key); ?>><?php echo esc_html($status_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mapping-col">
                                    <select name="square_status[]">
                                        <option value=""><?php esc_html_e('Select Square status', 'squarekit'); ?></option>
                                        <?php foreach ($square_statuses as $status_key => $status_label): ?>
                                            <option value="<?php echo esc_attr($status_key); ?>" <?php selected($square_status, $status_key); ?>><?php echo esc_html($status_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mapping-actions">
                                    <button type="button" class="squarekit-btn-remove" onclick="removeStatusRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="mapping-row">
                            <div class="mapping-col">
                                <select name="wc_status[]">
                                    <option value=""><?php esc_html_e('Select WooCommerce status', 'squarekit'); ?></option>
                                    <?php foreach ($wc_statuses as $status_key => $status_label): ?>
                                        <option value="<?php echo esc_attr($status_key); ?>"><?php echo esc_html($status_label); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mapping-col">
                                <select name="square_status[]">
                                    <option value=""><?php esc_html_e('Select Square status', 'squarekit'); ?></option>
                                    <?php foreach ($square_statuses as $status_key => $status_label): ?>
                                        <option value="<?php echo esc_attr($status_key); ?>"><?php echo esc_html($status_label); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mapping-actions">
                                <button type="button" class="squarekit-btn-remove" onclick="removeStatusRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mapping-footer">
                    <button type="button" class="squarekit-admin-btn" onclick="addStatusRow()"><?php esc_html_e('Add Mapping', 'squarekit'); ?></button>
                    <button type="submit" name="save_status_mapping" class="squarekit-admin-btn-primary"><?php esc_html_e('Save Status Mapping', 'squarekit'); ?></button>
                </div>
            </div>
        </form>
    </div>

    <!-- Customer Role Mapping Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Customer Role Mapping', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Automatically assign WooCommerce roles to customers based on Square customer data.', 'squarekit'); ?></p>

        <form method="post" action="">
            <?php wp_nonce_field('squarekit_save_customer_role_mapping', 'squarekit_customer_role_mapping_nonce'); ?>

            <div class="squarekit-mapping-container">
                <div class="mapping-header">
                    <div class="mapping-col"><?php esc_html_e('Criteria', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('Condition', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('Value', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('WooCommerce Role', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('Priority', 'squarekit'); ?></div>
                    <div class="mapping-actions"><?php esc_html_e('Actions', 'squarekit'); ?></div>
                </div>

                <div id="customer-role-mapping-rows">
                    <?php if (!empty($customer_role_mapping)): ?>
                        <?php foreach ($customer_role_mapping as $mapping): ?>
                            <div class="mapping-row">
                                <div class="mapping-col">
                                    <select name="mapping_criteria[]">
                                        <option value="total_spent" <?php selected($mapping['criteria'], 'total_spent'); ?>><?php esc_html_e('Total Spent', 'squarekit'); ?></option>
                                        <option value="order_count" <?php selected($mapping['criteria'], 'order_count'); ?>><?php esc_html_e('Order Count', 'squarekit'); ?></option>
                                        <option value="customer_group" <?php selected($mapping['criteria'], 'customer_group'); ?>><?php esc_html_e('Customer Group', 'squarekit'); ?></option>
                                        <option value="email_domain" <?php selected($mapping['criteria'], 'email_domain'); ?>><?php esc_html_e('Email Domain', 'squarekit'); ?></option>
                                    </select>
                                </div>
                                <div class="mapping-col">
                                    <select name="mapping_condition[]">
                                        <option value="greater_than" <?php selected($mapping['condition'], 'greater_than'); ?>><?php esc_html_e('Greater Than', 'squarekit'); ?></option>
                                        <option value="less_than" <?php selected($mapping['condition'], 'less_than'); ?>><?php esc_html_e('Less Than', 'squarekit'); ?></option>
                                        <option value="equals" <?php selected($mapping['condition'], 'equals'); ?>><?php esc_html_e('Equals', 'squarekit'); ?></option>
                                        <option value="contains" <?php selected($mapping['condition'], 'contains'); ?>><?php esc_html_e('Contains', 'squarekit'); ?></option>
                                    </select>
                                </div>
                                <div class="mapping-col">
                                    <input type="text" name="mapping_value[]" value="<?php echo esc_attr($mapping['value']); ?>" placeholder="<?php esc_attr_e('Value', 'squarekit'); ?>" />
                                </div>
                                <div class="mapping-col">
                                    <select name="mapping_role[]">
                                        <option value=""><?php esc_html_e('Select role', 'squarekit'); ?></option>
                                        <?php foreach ($wc_roles as $role_key => $role_name): ?>
                                            <option value="<?php echo esc_attr($role_key); ?>" <?php selected($mapping['role'], $role_key); ?>><?php echo esc_html($role_name); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mapping-col">
                                    <input type="number" name="mapping_priority[]" value="<?php echo esc_attr($mapping['priority']); ?>" min="1" max="100" />
                                </div>
                                <div class="mapping-actions">
                                    <button type="button" class="squarekit-btn-remove" onclick="removeCustomerRoleRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="mapping-row">
                            <div class="mapping-col">
                                <select name="mapping_criteria[]">
                                    <option value="total_spent"><?php esc_html_e('Total Spent', 'squarekit'); ?></option>
                                    <option value="order_count"><?php esc_html_e('Order Count', 'squarekit'); ?></option>
                                    <option value="customer_group"><?php esc_html_e('Customer Group', 'squarekit'); ?></option>
                                    <option value="email_domain"><?php esc_html_e('Email Domain', 'squarekit'); ?></option>
                                </select>
                            </div>
                            <div class="mapping-col">
                                <select name="mapping_condition[]">
                                    <option value="greater_than"><?php esc_html_e('Greater Than', 'squarekit'); ?></option>
                                    <option value="less_than"><?php esc_html_e('Less Than', 'squarekit'); ?></option>
                                    <option value="equals"><?php esc_html_e('Equals', 'squarekit'); ?></option>
                                    <option value="contains"><?php esc_html_e('Contains', 'squarekit'); ?></option>
                                </select>
                            </div>
                            <div class="mapping-col">
                                <input type="text" name="mapping_value[]" placeholder="<?php esc_attr_e('Value', 'squarekit'); ?>" />
                            </div>
                            <div class="mapping-col">
                                <select name="mapping_role[]">
                                    <option value=""><?php esc_html_e('Select role', 'squarekit'); ?></option>
                                    <?php foreach ($wc_roles as $role_key => $role_name): ?>
                                        <option value="<?php echo esc_attr($role_key); ?>"><?php echo esc_html($role_name); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mapping-col">
                                <input type="number" name="mapping_priority[]" value="10" min="1" max="100" />
                            </div>
                            <div class="mapping-actions">
                                <button type="button" class="squarekit-btn-remove" onclick="removeCustomerRoleRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mapping-footer">
                    <button type="button" class="squarekit-admin-btn" onclick="addCustomerRoleRow()"><?php esc_html_e('Add Mapping', 'squarekit'); ?></button>
                    <button type="submit" name="save_customer_role_mapping" class="squarekit-admin-btn-primary"><?php esc_html_e('Save Customer Role Mapping', 'squarekit'); ?></button>
                </div>
            </div>
        </form>
    </div>

    <!-- Fulfillment Mapping Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Fulfillment & Pickup Mapping', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Map Square fulfillment types to WooCommerce shipping methods and pickup locations.', 'squarekit'); ?></p>

        <form method="post" action="">
            <?php wp_nonce_field('squarekit_save_fulfillment_mapping', 'squarekit_fulfillment_mapping_nonce'); ?>

            <h4><?php esc_html_e('Fulfillment Type Mapping', 'squarekit'); ?></h4>
            <div class="squarekit-mapping-container">
                <div class="mapping-header">
                    <div class="mapping-col"><?php esc_html_e('Square Fulfillment Type', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('WooCommerce Shipping Method', 'squarekit'); ?></div>
                    <div class="mapping-actions"><?php esc_html_e('Actions', 'squarekit'); ?></div>
                </div>

                <div id="fulfillment-mapping-rows">
                    <?php if (!empty($fulfillment_mapping)): ?>
                        <?php foreach ($fulfillment_mapping as $mapping): ?>
                            <div class="mapping-row">
                                <div class="mapping-col">
                                    <select name="fulfillment_type[]">
                                        <option value="PICKUP" <?php selected($mapping['fulfillment_type'], 'PICKUP'); ?>><?php esc_html_e('Pickup', 'squarekit'); ?></option>
                                        <option value="SHIPMENT" <?php selected($mapping['fulfillment_type'], 'SHIPMENT'); ?>><?php esc_html_e('Shipment', 'squarekit'); ?></option>
                                        <option value="DELIVERY" <?php selected($mapping['fulfillment_type'], 'DELIVERY'); ?>><?php esc_html_e('Delivery', 'squarekit'); ?></option>
                                    </select>
                                </div>
                                <div class="mapping-col">
                                    <select name="shipping_method[]">
                                        <option value=""><?php esc_html_e('Select shipping method', 'squarekit'); ?></option>
                                        <?php foreach ($shipping_methods as $method_id => $method_name): ?>
                                            <option value="<?php echo esc_attr($method_id); ?>" <?php selected($mapping['shipping_method'], $method_id); ?>><?php echo esc_html($method_name); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mapping-actions">
                                    <button type="button" class="squarekit-btn-remove" onclick="removeFulfillmentRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="mapping-row">
                            <div class="mapping-col">
                                <select name="fulfillment_type[]">
                                    <option value="PICKUP"><?php esc_html_e('Pickup', 'squarekit'); ?></option>
                                    <option value="SHIPMENT"><?php esc_html_e('Shipment', 'squarekit'); ?></option>
                                    <option value="DELIVERY"><?php esc_html_e('Delivery', 'squarekit'); ?></option>
                                </select>
                            </div>
                            <div class="mapping-col">
                                <select name="shipping_method[]">
                                    <option value=""><?php esc_html_e('Select shipping method', 'squarekit'); ?></option>
                                    <?php foreach ($shipping_methods as $method_id => $method_name): ?>
                                        <option value="<?php echo esc_attr($method_id); ?>"><?php echo esc_html($method_name); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mapping-actions">
                                <button type="button" class="squarekit-btn-remove" onclick="removeFulfillmentRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mapping-footer">
                    <button type="button" class="squarekit-admin-btn" onclick="addFulfillmentRow()"><?php esc_html_e('Add Mapping', 'squarekit'); ?></button>
                </div>
            </div>

            <h4><?php esc_html_e('Pickup Location Mapping', 'squarekit'); ?></h4>
            <div class="squarekit-mapping-container">
                <div class="mapping-header">
                    <div class="mapping-col"><?php esc_html_e('Square Location', 'squarekit'); ?></div>
                    <div class="mapping-col"><?php esc_html_e('WooCommerce Pickup Location', 'squarekit'); ?></div>
                    <div class="mapping-actions"><?php esc_html_e('Actions', 'squarekit'); ?></div>
                </div>

                <div id="pickup-location-mapping-rows">
                    <?php if (!empty($pickup_location_mapping)): ?>
                        <?php foreach ($pickup_location_mapping as $mapping): ?>
                            <div class="mapping-row">
                                <div class="mapping-col">
                                    <input type="text" name="square_location[]" value="<?php echo esc_attr($mapping['square_location']); ?>" placeholder="<?php esc_attr_e('Square location ID', 'squarekit'); ?>" />
                                </div>
                                <div class="mapping-col">
                                    <input type="text" name="wc_pickup_location[]" value="<?php echo esc_attr($mapping['wc_pickup_location']); ?>" placeholder="<?php esc_attr_e('WooCommerce pickup location', 'squarekit'); ?>" />
                                </div>
                                <div class="mapping-actions">
                                    <button type="button" class="squarekit-btn-remove" onclick="removePickupLocationRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="mapping-row">
                            <div class="mapping-col">
                                <input type="text" name="square_location[]" placeholder="<?php esc_attr_e('Square location ID', 'squarekit'); ?>" />
                            </div>
                            <div class="mapping-col">
                                <input type="text" name="wc_pickup_location[]" placeholder="<?php esc_attr_e('WooCommerce pickup location', 'squarekit'); ?>" />
                            </div>
                            <div class="mapping-actions">
                                <button type="button" class="squarekit-btn-remove" onclick="removePickupLocationRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="mapping-footer">
                    <button type="button" class="squarekit-admin-btn" onclick="addPickupLocationRow()"><?php esc_html_e('Add Mapping', 'squarekit'); ?></button>
                    <button type="submit" name="save_fulfillment_mapping" class="squarekit-admin-btn-primary"><?php esc_html_e('Save Fulfillment Mapping', 'squarekit'); ?></button>
                </div>
            </div>
        </form>
    </div>
</form>

<script>
// Attribute Mapping Functions
function addAttributeRow() {
    const container = document.getElementById('attribute-mapping-rows');
    const newRow = document.createElement('div');
    newRow.className = 'mapping-row';
    newRow.innerHTML = `
        <div class="mapping-col">
            <input type="text" name="square_attribute[]" placeholder="<?php esc_attr_e('Square attribute name', 'squarekit'); ?>" />
        </div>
        <div class="mapping-col">
            <select name="wc_attribute[]">
                <option value=""><?php esc_html_e('Select WooCommerce attribute', 'squarekit'); ?></option>
                <?php foreach ($wc_attributes as $attr_key => $attr_label): ?>
                    <option value="<?php echo esc_attr($attr_key); ?>"><?php echo esc_html($attr_label); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mapping-actions">
            <button type="button" class="squarekit-btn-remove" onclick="removeAttributeRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
        </div>
    `;
    container.appendChild(newRow);
}

function removeAttributeRow(button) {
    button.closest('.mapping-row').remove();
}

// Status Mapping Functions
function addStatusRow() {
    const container = document.getElementById('status-mapping-rows');
    const newRow = document.createElement('div');
    newRow.className = 'mapping-row';
    newRow.innerHTML = `
        <div class="mapping-col">
            <select name="wc_status[]">
                <option value=""><?php esc_html_e('Select WooCommerce status', 'squarekit'); ?></option>
                <?php foreach ($wc_statuses as $status_key => $status_label): ?>
                    <option value="<?php echo esc_attr($status_key); ?>"><?php echo esc_html($status_label); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mapping-col">
            <select name="square_status[]">
                <option value=""><?php esc_html_e('Select Square status', 'squarekit'); ?></option>
                <?php foreach ($square_statuses as $status_key => $status_label): ?>
                    <option value="<?php echo esc_attr($status_key); ?>"><?php echo esc_html($status_label); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mapping-actions">
            <button type="button" class="squarekit-btn-remove" onclick="removeStatusRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
        </div>
    `;
    container.appendChild(newRow);
}

function removeStatusRow(button) {
    button.closest('.mapping-row').remove();
}

// Customer Role Mapping Functions
function addCustomerRoleRow() {
    const container = document.getElementById('customer-role-mapping-rows');
    const newRow = document.createElement('div');
    newRow.className = 'mapping-row';
    newRow.innerHTML = `
        <div class="mapping-col">
            <select name="mapping_criteria[]">
                <option value="total_spent"><?php esc_html_e('Total Spent', 'squarekit'); ?></option>
                <option value="order_count"><?php esc_html_e('Order Count', 'squarekit'); ?></option>
                <option value="customer_group"><?php esc_html_e('Customer Group', 'squarekit'); ?></option>
                <option value="email_domain"><?php esc_html_e('Email Domain', 'squarekit'); ?></option>
            </select>
        </div>
        <div class="mapping-col">
            <select name="mapping_condition[]">
                <option value="greater_than"><?php esc_html_e('Greater Than', 'squarekit'); ?></option>
                <option value="less_than"><?php esc_html_e('Less Than', 'squarekit'); ?></option>
                <option value="equals"><?php esc_html_e('Equals', 'squarekit'); ?></option>
                <option value="contains"><?php esc_html_e('Contains', 'squarekit'); ?></option>
            </select>
        </div>
        <div class="mapping-col">
            <input type="text" name="mapping_value[]" placeholder="<?php esc_attr_e('Value', 'squarekit'); ?>" />
        </div>
        <div class="mapping-col">
            <select name="mapping_role[]">
                <option value=""><?php esc_html_e('Select role', 'squarekit'); ?></option>
                <?php foreach ($wc_roles as $role_key => $role_name): ?>
                    <option value="<?php echo esc_attr($role_key); ?>"><?php echo esc_html($role_name); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mapping-col">
            <input type="number" name="mapping_priority[]" value="10" min="1" max="100" />
        </div>
        <div class="mapping-actions">
            <button type="button" class="squarekit-btn-remove" onclick="removeCustomerRoleRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
        </div>
    `;
    container.appendChild(newRow);
}

function removeCustomerRoleRow(button) {
    button.closest('.mapping-row').remove();
}

// Fulfillment Mapping Functions
function addFulfillmentRow() {
    const container = document.getElementById('fulfillment-mapping-rows');
    const newRow = document.createElement('div');
    newRow.className = 'mapping-row';
    newRow.innerHTML = `
        <div class="mapping-col">
            <select name="fulfillment_type[]">
                <option value="PICKUP"><?php esc_html_e('Pickup', 'squarekit'); ?></option>
                <option value="SHIPMENT"><?php esc_html_e('Shipment', 'squarekit'); ?></option>
                <option value="DELIVERY"><?php esc_html_e('Delivery', 'squarekit'); ?></option>
            </select>
        </div>
        <div class="mapping-col">
            <select name="shipping_method[]">
                <option value=""><?php esc_html_e('Select shipping method', 'squarekit'); ?></option>
                <?php foreach ($shipping_methods as $method_id => $method_name): ?>
                    <option value="<?php echo esc_attr($method_id); ?>"><?php echo esc_html($method_name); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mapping-actions">
            <button type="button" class="squarekit-btn-remove" onclick="removeFulfillmentRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
        </div>
    `;
    container.appendChild(newRow);
}

function removeFulfillmentRow(button) {
    button.closest('.mapping-row').remove();
}

// Pickup Location Mapping Functions
function addPickupLocationRow() {
    const container = document.getElementById('pickup-location-mapping-rows');
    const newRow = document.createElement('div');
    newRow.className = 'mapping-row';
    newRow.innerHTML = `
        <div class="mapping-col">
            <input type="text" name="square_location[]" placeholder="<?php esc_attr_e('Square location ID', 'squarekit'); ?>" />
        </div>
        <div class="mapping-col">
            <input type="text" name="wc_pickup_location[]" placeholder="<?php esc_attr_e('WooCommerce pickup location', 'squarekit'); ?>" />
        </div>
        <div class="mapping-actions">
            <button type="button" class="squarekit-btn-remove" onclick="removePickupLocationRow(this)"><?php esc_html_e('Remove', 'squarekit'); ?></button>
        </div>
    `;
    container.appendChild(newRow);
}

function removePickupLocationRow(button) {
    button.closest('.mapping-row').remove();
}
</script>
