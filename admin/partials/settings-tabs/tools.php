<?php
// Tools Settings Tab
if ( ! defined( 'ABSPATH' ) ) exit;

// Get system information
$php_version = phpversion();
$wp_version = get_bloginfo('version');
$wc_version = defined('WC_VERSION') ? WC_VERSION : 'Not installed';
$plugin_version = defined('SQUAREKIT_VERSION') ? SQUAREKIT_VERSION : '1.0.0';
$memory_limit = ini_get('memory_limit');
$max_execution_time = ini_get('max_execution_time');
$upload_max_filesize = ini_get('upload_max_filesize');

// Get log files
$log_files = array();
$log_dir = SQUAREKIT_PLUGIN_DIR . 'logs/';
if (is_dir($log_dir)) {
    $files = scandir($log_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'log') {
            $log_files[] = array(
                'name' => $file,
                'size' => filesize($log_dir . $file),
                'modified' => filemtime($log_dir . $file)
            );
        }
    }
}
?>

<div class="squarekit-settings-form">

    <!-- System Status -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('System Status', 'squarekit'); ?></h3>
        <p><?php esc_html_e('View system information and compatibility status.', 'squarekit'); ?></p>

        <div class="squarekit-system-status-grid">
            <div class="system-status-card">
                <h4><?php esc_html_e('Environment Information', 'squarekit'); ?></h4>

                <div class="status-items">
                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('PHP Version:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo esc_html($php_version); ?></span>
                        <span class="status-indicator <?php echo version_compare($php_version, '7.4', '>=') ? 'enabled' : 'disabled'; ?>">
                            <?php echo version_compare($php_version, '7.4', '>=') ? '✓' : '✗'; ?>
                        </span>
                    </div>

                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('WordPress Version:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo esc_html($wp_version); ?></span>
                        <span class="status-indicator <?php echo version_compare($wp_version, '5.0', '>=') ? 'enabled' : 'disabled'; ?>">
                            <?php echo version_compare($wp_version, '5.0', '>=') ? '✓' : '✗'; ?>
                        </span>
                    </div>

                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('WooCommerce Version:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo esc_html($wc_version); ?></span>
                        <span class="status-indicator <?php echo ($wc_version !== 'Not installed' && version_compare($wc_version, '4.0', '>=')) ? 'enabled' : 'disabled'; ?>">
                            <?php echo ($wc_version !== 'Not installed' && version_compare($wc_version, '4.0', '>=')) ? '✓' : '✗'; ?>
                        </span>
                    </div>

                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('Square Kit Version:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo esc_html($plugin_version); ?></span>
                        <span class="status-indicator enabled">✓</span>
                    </div>
                </div>
            </div>

            <div class="system-status-card">
                <h4><?php esc_html_e('Server Configuration', 'squarekit'); ?></h4>

                <div class="status-items">
                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('Memory Limit:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo esc_html($memory_limit); ?></span>
                        <span class="status-indicator <?php echo (int)$memory_limit >= 128 ? 'enabled' : 'disabled'; ?>">
                            <?php echo (int)$memory_limit >= 128 ? '✓' : '✗'; ?>
                        </span>
                    </div>

                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('Max Execution Time:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo esc_html($max_execution_time . 's'); ?></span>
                        <span class="status-indicator <?php echo (int)$max_execution_time >= 30 ? 'enabled' : 'disabled'; ?>">
                            <?php echo (int)$max_execution_time >= 30 ? '✓' : '✗'; ?>
                        </span>
                    </div>

                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('Upload Max Filesize:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo esc_html($upload_max_filesize); ?></span>
                        <span class="status-indicator enabled">✓</span>
                    </div>

                    <div class="status-item">
                        <span class="status-label"><?php esc_html_e('SSL Enabled:', 'squarekit'); ?></span>
                        <span class="status-value"><?php echo is_ssl() ? esc_html__('Yes', 'squarekit') : esc_html__('No', 'squarekit'); ?></span>
                        <span class="status-indicator <?php echo is_ssl() ? 'enabled' : 'disabled'; ?>">
                            <?php echo is_ssl() ? '✓' : '✗'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diagnostic Tools -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Diagnostic Tools', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Run diagnostic tests and troubleshoot connection issues.', 'squarekit'); ?></p>

        <div class="squarekit-diagnostic-tools-grid">
            <div class="diagnostic-tool-card">
                <div class="tool-header">
                    <div class="tool-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Connection Test', 'squarekit'); ?></h4>
                </div>
                <p><?php esc_html_e('Test the connection to Square API and verify credentials.', 'squarekit'); ?></p>
                <button type="button" id="run-connection-test" class="squarekit-admin-btn">
                    <?php esc_html_e('Run Connection Test', 'squarekit'); ?>
                </button>
            </div>

            <div class="diagnostic-tool-card">
                <div class="tool-header">
                    <div class="tool-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Sync Test', 'squarekit'); ?></h4>
                </div>
                <p><?php esc_html_e('Test sync operations and verify data flow between systems.', 'squarekit'); ?></p>
                <button type="button" id="run-sync-test" class="squarekit-admin-btn">
                    <?php esc_html_e('Run Sync Test', 'squarekit'); ?>
                </button>
            </div>

            <div class="diagnostic-tool-card">
                <div class="tool-header">
                    <div class="tool-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13 3L4 14H11L10 21L19 10H12L13 3Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Webhook Test', 'squarekit'); ?></h4>
                </div>
                <p><?php esc_html_e('Test webhook endpoints and verify real-time sync functionality.', 'squarekit'); ?></p>
                <button type="button" id="run-webhook-test" class="squarekit-admin-btn">
                    <?php esc_html_e('Run Webhook Test', 'squarekit'); ?>
                </button>
            </div>
        </div>

        <div id="diagnostic-results" style="display:none; margin-top: 2em; padding: 1.5em; border-radius: 12px; border: 2px solid #e2e8f0; background: #f9f9f9;">
            <h4><?php esc_html_e('Diagnostic Results', 'squarekit'); ?></h4>
            <div id="diagnostic-content"></div>
        </div>
    </div>

    <!-- Log Management -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Log Management', 'squarekit'); ?></h3>
        <p><?php esc_html_e('View, download, and manage plugin log files.', 'squarekit'); ?></p>

        <div class="squarekit-log-management-container">
            <?php if (!empty($log_files)): ?>
                <div class="log-files-grid">
                    <?php foreach ($log_files as $log_file): ?>
                        <div class="log-file-card">
                            <div class="log-file-header">
                                <div class="log-file-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" fill="currentColor"/>
                                        <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M16 13H8M16 17H8M10 9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="log-file-info">
                                    <h4><?php echo esc_html($log_file['name']); ?></h4>
                                    <div class="log-file-meta">
                                        <span class="file-size"><?php echo esc_html(size_format($log_file['size'])); ?></span>
                                        <span class="file-date"><?php echo esc_html(date('Y-m-d H:i:s', $log_file['modified'])); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="log-file-actions">
                                <button type="button" class="squarekit-admin-btn view-log" data-file="<?php echo esc_attr($log_file['name']); ?>">
                                    <?php esc_html_e('View', 'squarekit'); ?>
                                </button>
                                <button type="button" class="squarekit-admin-btn download-log" data-file="<?php echo esc_attr($log_file['name']); ?>">
                                    <?php esc_html_e('Download', 'squarekit'); ?>
                                </button>
                                <button type="button" class="squarekit-btn-remove clear-log" data-file="<?php echo esc_attr($log_file['name']); ?>">
                                    <?php esc_html_e('Clear', 'squarekit'); ?>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="log-files-empty">
                    <div class="empty-icon">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" fill="currentColor" opacity="0.3"/>
                            <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" opacity="0.3"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('No Log Files Found', 'squarekit'); ?></h4>
                    <p><?php esc_html_e('Log files will appear here once the plugin starts logging activities.', 'squarekit'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Maintenance Tools -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Maintenance Tools', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Perform maintenance tasks and data cleanup operations.', 'squarekit'); ?></p>

        <div class="squarekit-maintenance-tools-grid">
            <div class="maintenance-tool-card">
                <div class="tool-header">
                    <div class="tool-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 6H21M8 6V4C8 3.45 8.45 3 9 3H15C15.55 3 16 3.45 16 4V6M19 6V20C19 21.1 18.1 22 17 22H7C5.9 22 5 21.1 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Clear Cache', 'squarekit'); ?></h4>
                </div>
                <p><?php esc_html_e('Clear all cached data and force fresh sync on next operation.', 'squarekit'); ?></p>
                <button type="button" id="clear-cache" class="squarekit-admin-btn">
                    <?php esc_html_e('Clear Cache', 'squarekit'); ?>
                </button>
            </div>

            <div class="maintenance-tool-card">
                <div class="tool-header">
                    <div class="tool-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 4V10H7M23 20V14H17M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Reset Sync Status', 'squarekit'); ?></h4>
                </div>
                <p><?php esc_html_e('Reset all sync status flags and clear any stuck operations.', 'squarekit'); ?></p>
                <button type="button" id="reset-sync-status" class="squarekit-admin-btn">
                    <?php esc_html_e('Reset Sync Status', 'squarekit'); ?>
                </button>
            </div>

            <div class="maintenance-tool-card">
                <div class="tool-header">
                    <div class="tool-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15V19C21 20.1 20.1 21 19 21H5C3.9 21 3 20.1 3 19V15M7 10L12 15L17 10M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Export Configuration', 'squarekit'); ?></h4>
                </div>
                <p><?php esc_html_e('Export plugin settings and configuration for backup or migration.', 'squarekit'); ?></p>
                <button type="button" id="export-config" class="squarekit-admin-btn">
                    <?php esc_html_e('Export Configuration', 'squarekit'); ?>
                </button>
            </div>

            <div class="maintenance-tool-card">
                <div class="tool-header">
                    <div class="tool-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15V19C21 20.1 20.1 21 19 21H5C3.9 21 3 20.1 3 19V15M17 8L12 3L7 8M12 3V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Import Configuration', 'squarekit'); ?></h4>
                </div>
                <p><?php esc_html_e('Import plugin settings and configuration from a backup file.', 'squarekit'); ?></p>
                <div class="import-config-container">
                    <input type="file" id="import-config-file" accept=".json" style="display: none;" />
                    <button type="button" id="import-config" class="squarekit-admin-btn">
                        <?php esc_html_e('Import Configuration', 'squarekit'); ?>
                    </button>
                </div>
            </div>
        </div>

        <div id="maintenance-results" style="display:none; margin-top: 2em; padding: 1.5em; border-radius: 12px; border: 2px solid #e2e8f0; background: #f9f9f9;">
            <h4><?php esc_html_e('Maintenance Results', 'squarekit'); ?></h4>
            <div id="maintenance-content"></div>
        </div>
    </div>
</div>

<script>
jQuery(function($){
    // Diagnostic Tools
    $('#run-connection-test').on('click', function(){
        runDiagnostic('connection', $(this));
    });

    $('#run-sync-test').on('click', function(){
        runDiagnostic('sync', $(this));
    });

    $('#run-webhook-test').on('click', function(){
        runDiagnostic('webhook', $(this));
    });

    function runDiagnostic(type, $btn) {
        var $results = $('#diagnostic-results');
        var $content = $('#diagnostic-content');

        $btn.prop('disabled', true).text('<?php esc_html_e('Running...', 'squarekit'); ?>');
        $results.show();
        $content.html('<div class="diagnostic-loading"><?php esc_html_e('Running diagnostic test...', 'squarekit'); ?></div>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_run_diagnostic',
                type: type,
                nonce: '<?php echo wp_create_nonce('squarekit_diagnostic'); ?>'
            },
            success: function(response){
                if (response.success) {
                    $content.html('<div class="diagnostic-success">' + response.data.message + '</div>');
                    $results.css({'background': '#d1fae5', 'border-color': '#10b981'});
                } else {
                    $content.html('<div class="diagnostic-error">' + response.data.message + '</div>');
                    $results.css({'background': '#fee2e2', 'border-color': '#ef4444'});
                }
            },
            error: function(){
                $content.html('<div class="diagnostic-error"><?php esc_html_e('Diagnostic test failed. Please try again.', 'squarekit'); ?></div>');
                $results.css({'background': '#fee2e2', 'border-color': '#ef4444'});
            },
            complete: function(){
                $btn.prop('disabled', false).text($btn.data('original-text') || '<?php esc_html_e('Run Test', 'squarekit'); ?>');
            }
        });
    }

    // Log Management
    $('.view-log').on('click', function(){
        var fileName = $(this).data('file');
        window.open('<?php echo admin_url('admin.php?page=squarekit&tab=logs&action=view&file='); ?>' + fileName, '_blank');
    });

    $('.download-log').on('click', function(){
        var fileName = $(this).data('file');
        window.location.href = '<?php echo admin_url('admin.php?page=squarekit&tab=logs&action=download&file='); ?>' + fileName;
    });

    $('.clear-log').on('click', function(){
        var fileName = $(this).data('file');
        var $btn = $(this);

        if (!confirm('<?php esc_html_e('Are you sure you want to clear this log file?', 'squarekit'); ?>')) {
            return;
        }

        $btn.prop('disabled', true).text('<?php esc_html_e('Clearing...', 'squarekit'); ?>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_clear_log',
                file: fileName,
                nonce: '<?php echo wp_create_nonce('squarekit_clear_log'); ?>'
            },
            success: function(response){
                if (response.success) {
                    $btn.closest('.log-file-card').fadeOut();
                } else {
                    alert('<?php esc_html_e('Failed to clear log file.', 'squarekit'); ?>');
                }
            },
            error: function(){
                alert('<?php esc_html_e('Failed to clear log file.', 'squarekit'); ?>');
            },
            complete: function(){
                $btn.prop('disabled', false).text('<?php esc_html_e('Clear', 'squarekit'); ?>');
            }
        });
    });

    // Maintenance Tools
    $('#clear-cache').on('click', function(){
        runMaintenance('clear_cache', $(this));
    });

    $('#reset-sync-status').on('click', function(){
        if (confirm('<?php esc_html_e('Are you sure you want to reset all sync status flags?', 'squarekit'); ?>')) {
            runMaintenance('reset_sync_status', $(this));
        }
    });

    $('#export-config').on('click', function(){
        window.location.href = '<?php echo admin_url('admin.php?page=squarekit&tab=tools&action=export_config'); ?>';
    });

    $('#import-config').on('click', function(){
        $('#import-config-file').click();
    });

    $('#import-config-file').on('change', function(){
        var file = this.files[0];
        if (file) {
            var formData = new FormData();
            formData.append('action', 'squarekit_import_config');
            formData.append('config_file', file);
            formData.append('nonce', '<?php echo wp_create_nonce('squarekit_import_config'); ?>');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response){
                    if (response.success) {
                        alert('<?php esc_html_e('Configuration imported successfully.', 'squarekit'); ?>');
                        location.reload();
                    } else {
                        alert('<?php esc_html_e('Failed to import configuration:', 'squarekit'); ?> ' + response.data.message);
                    }
                },
                error: function(){
                    alert('<?php esc_html_e('Failed to import configuration.', 'squarekit'); ?>');
                }
            });
        }
    });

    function runMaintenance(type, $btn) {
        var $results = $('#maintenance-results');
        var $content = $('#maintenance-content');

        $btn.prop('disabled', true).text('<?php esc_html_e('Processing...', 'squarekit'); ?>');
        $results.show();
        $content.html('<div class="maintenance-loading"><?php esc_html_e('Running maintenance task...', 'squarekit'); ?></div>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_run_maintenance',
                type: type,
                nonce: '<?php echo wp_create_nonce('squarekit_maintenance'); ?>'
            },
            success: function(response){
                if (response.success) {
                    $content.html('<div class="maintenance-success">' + response.data.message + '</div>');
                    $results.css({'background': '#d1fae5', 'border-color': '#10b981'});
                } else {
                    $content.html('<div class="maintenance-error">' + response.data.message + '</div>');
                    $results.css({'background': '#fee2e2', 'border-color': '#ef4444'});
                }
            },
            error: function(){
                $content.html('<div class="maintenance-error"><?php esc_html_e('Maintenance task failed. Please try again.', 'squarekit'); ?></div>');
                $results.css({'background': '#fee2e2', 'border-color': '#ef4444'});
            },
            complete: function(){
                $btn.prop('disabled', false).text($btn.data('original-text') || '<?php esc_html_e('Run Task', 'squarekit'); ?>');
            }
        });
    }

    // Store original button text
    $('.squarekit-admin-btn').each(function(){
        $(this).data('original-text', $(this).text());
    });
});
</script>
