<?php
// Connection Settings Tab
if ( ! defined( 'ABSPATH' ) ) exit;
?>

<form method="post" action="" class="squarekit-settings-form">
    <?php wp_nonce_field('squarekit_settings_save', 'squarekit_settings_nonce'); ?>
    
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Square Environment', 'squarekit'); ?></h3>
        
        <div class="squarekit-form-group">
            <label><?php esc_html_e('Environment', 'squarekit'); ?></label>
            <div class="squarekit-radio-group">
                <label class="squarekit-radio">
                    <input type="radio" name="environment" value="sandbox" <?php checked($environment, 'sandbox'); ?> />
                    <span class="squarekit-radio-label">
                        <strong><?php esc_html_e('Sandbox (Testing)', 'squarekit'); ?></strong>
                        <br><small><?php esc_html_e('Use for development and testing', 'squarekit'); ?></small>
                    </span>
                </label>
                <label class="squarekit-radio">
                    <input type="radio" name="environment" value="production" <?php checked($environment, 'production'); ?> />
                    <span class="squarekit-radio-label">
                        <strong><?php esc_html_e('Production (Live)', 'squarekit'); ?></strong>
                        <br><small><?php esc_html_e('Use for live transactions', 'squarekit'); ?></small>
                    </span>
                </label>
            </div>
            <p class="description"><?php esc_html_e('Choose whether to connect to Square Sandbox for testing or Production for live transactions.', 'squarekit'); ?></p>
        </div>
    </div>

    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Connection Status', 'squarekit'); ?></h3>
        
        <?php
        $is_connected = $settings->is_connected();
        $access_token = $settings->get_access_token();
        $location_id = $settings->get_location_id();
        ?>
        
        <div class="squarekit-connection-cards">
            <div class="squarekit-connection-card <?php echo $is_connected ? 'active' : ''; ?>">
                <h4>
                    <?php esc_html_e('Current Environment:', 'squarekit'); ?> 
                    <span class="squarekit-status-indicator <?php echo $is_connected ? 'connected' : 'disconnected'; ?>">
                        <?php echo esc_html(ucfirst($environment)); ?>
                    </span>
                </h4>
                
                <div class="squarekit-connection-info">
                    <div class="squarekit-connection-info-item">
                        <span class="squarekit-connection-info-label"><?php esc_html_e('Status:', 'squarekit'); ?></span>
                        <span class="squarekit-status-indicator <?php echo $is_connected ? 'connected' : 'disconnected'; ?>">
                            <?php echo $is_connected ? esc_html__('Connected', 'squarekit') : esc_html__('Not Connected', 'squarekit'); ?>
                        </span>
                    </div>
                    
                    <?php if ($access_token): ?>
                        <div class="squarekit-connection-info-item">
                            <span class="squarekit-connection-info-label"><?php esc_html_e('Access Token:', 'squarekit'); ?></span>
                            <span class="squarekit-connection-info-value"><?php echo esc_html(substr($access_token, 0, 20) . '...'); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($location_id): ?>
                        <div class="squarekit-connection-info-item">
                            <span class="squarekit-connection-info-label"><?php esc_html_e('Location ID:', 'squarekit'); ?></span>
                            <span class="squarekit-connection-info-value"><?php echo esc_html($location_id); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="squarekit-form-group">
            <div style="display: flex; gap: 1em; flex-wrap: wrap;">
                <button type="button" id="test-connection" class="squarekit-admin-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php esc_html_e('Test Connection', 'squarekit'); ?>
                </button>
                
                <button type="button" id="refresh-connection" class="squarekit-admin-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 4V10H7M23 20V14H17M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php esc_html_e('Refresh Connection', 'squarekit'); ?>
                </button>
                
                <?php if (!$is_connected): ?>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-wizard')); ?>" class="squarekit-admin-btn-primary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13 3L4 14H11L10 21L19 10H12L13 3Z" fill="currentColor"/>
                        </svg>
                        <?php esc_html_e('Setup Connection', 'squarekit'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <div id="connection-test-result" style="display:none; margin-top: 1em; padding: 1em; border-radius: 8px; border: 2px solid #e2e8f0; background: #f9f9f9;"></div>
    </div>

    <div class="squarekit-form-actions">
        <button type="submit" class="squarekit-admin-btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 21H5C4.45 21 4 20.55 4 20V4C4 3.45 4.45 3 5 3H16L20 7V20C20 20.55 19.55 21 19 21Z" fill="currentColor"/>
                <path d="M17 21V13H7V21H17Z" fill="currentColor" opacity="0.7"/>
                <path d="M7 7H13V10H7V7Z" fill="currentColor" opacity="0.7"/>
            </svg>
            <?php esc_html_e('Save Connection Settings', 'squarekit'); ?>
        </button>
    </div>
</form>

<script>
jQuery(function($){
    $('#test-connection').on('click', function(){
        var $btn = $(this);
        var $result = $('#connection-test-result');
        
        $btn.prop('disabled', true).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg> <?php esc_html_e('Testing...', 'squarekit'); ?>');
        $result.hide();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_test_connection',
                nonce: '<?php echo wp_create_nonce('squarekit_test_connection'); ?>'
            },
            success: function(response){
                $result.html(response.data.message).show();
                if (response.success) {
                    $result.css({'background': '#d1fae5', 'border-color': '#10b981', 'color': '#065f46'});
                } else {
                    $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
                }
            },
            error: function(){
                $result.html('<?php esc_html_e('Test failed. Please try again.', 'squarekit'); ?>').show();
                $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
            },
            complete: function(){
                $btn.prop('disabled', false).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg> <?php esc_html_e('Test Connection', 'squarekit'); ?>');
            }
        });
    });
    
    $('#refresh-connection').on('click', function(){
        var $btn = $(this);
        var $result = $('#connection-test-result');
        
        $btn.prop('disabled', true).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg> <?php esc_html_e('Refreshing...', 'squarekit'); ?>');
        $result.hide();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_refresh_connection',
                nonce: '<?php echo wp_create_nonce('squarekit_refresh_connection'); ?>'
            },
            success: function(response){
                $result.html(response.data.message).show();
                if (response.success) {
                    $result.css({'background': '#d1fae5', 'border-color': '#10b981', 'color': '#065f46'});
                    setTimeout(function() { location.reload(); }, 1500);
                } else {
                    $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
                }
            },
            error: function(){
                $result.html('<?php esc_html_e('Refresh failed. Please try again.', 'squarekit'); ?>').show();
                $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
            },
            complete: function(){
                $btn.prop('disabled', false).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 4V10H7M23 20V14H17M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg> <?php esc_html_e('Refresh Connection', 'squarekit'); ?>');
            }
        });
    });
});
</script>
