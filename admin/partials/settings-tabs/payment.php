<?php
// Payment Settings Tab
if ( ! defined( 'ABSPATH' ) ) exit;

// Get payment settings
$payment_methods = $settings->get_payment_methods();
$enable_square_gateway = $settings->get('enable_square_gateway', false);
$gateway_title = $settings->get('square_gateway_title', 'Square');
$gateway_description = $settings->get('square_gateway_description', 'Pay securely with Square');
$enable_saved_cards = $settings->get('enable_saved_cards', true);
$enable_digital_wallet = $settings->get('enable_digital_wallet', true);
$capture_method = $settings->get('payment_capture_method', 'automatic');
$enable_logging = $settings->get('enable_payment_logging', true);
$sandbox_mode = $settings->get('payment_sandbox_mode', true);
?>

<form method="post" action="" class="squarekit-settings-form">
    <?php wp_nonce_field('squarekit_save_payment_settings', 'squarekit_payment_settings_nonce'); ?>

    <!-- Payment Gateway Configuration -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Square Payment Gateway', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure the Square payment gateway for WooCommerce checkout.', 'squarekit'); ?></p>

        <div class="squarekit-payment-gateway-card">
            <div class="gateway-header">
                <div class="gateway-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 4H4C2.89 4 2.01 4.89 2.01 6L2 18C2 19.11 2.89 20 4 20H20C21.11 20 22 19.11 22 18V6C22 4.89 21.11 4 20 4ZM20 18H4V12H20V18ZM20 8H4V6H20V8Z" fill="currentColor"/>
                    </svg>
                </div>
                <div class="gateway-info">
                    <h4><?php esc_html_e('Square Payment Gateway', 'squarekit'); ?></h4>
                    <p><?php esc_html_e('Accept credit cards, debit cards, and digital wallets through Square.', 'squarekit'); ?></p>
                </div>
                <div class="gateway-toggle">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enable_square_gateway" value="1" <?php checked($enable_square_gateway, true); ?> />
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <div class="gateway-settings">
                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Gateway Title', 'squarekit'); ?></label>
                    <input type="text" name="square_gateway_title" value="<?php echo esc_attr($gateway_title); ?>" placeholder="<?php esc_attr_e('Square', 'squarekit'); ?>" />
                    <p class="description"><?php esc_html_e('This is the title customers will see during checkout.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Gateway Description', 'squarekit'); ?></label>
                    <textarea name="square_gateway_description" rows="3" placeholder="<?php esc_attr_e('Pay securely with Square', 'squarekit'); ?>"><?php echo esc_textarea($gateway_description); ?></textarea>
                    <p class="description"><?php esc_html_e('This is the description customers will see during checkout.', 'squarekit'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Features -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Payment Features', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure advanced payment features and customer experience options.', 'squarekit'); ?></p>

        <div class="squarekit-payment-features-grid">
            <div class="payment-feature-card">
                <div class="feature-header">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 7H5V5C5 3.9 5.9 3 7 3H17C18.1 3 19 3.9 19 5V7Z" fill="currentColor"/>
                            <path d="M19 7V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V7H19Z" fill="currentColor" opacity="0.7"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Saved Cards', 'squarekit'); ?></h4>
                </div>

                <div class="feature-content">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enable_saved_cards" value="1" <?php checked($enable_saved_cards, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Saved Cards', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Allow customers to save their payment methods for future purchases.', 'squarekit'); ?></p>
                </div>
            </div>

            <div class="payment-feature-card">
                <div class="feature-header">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.9 1 3 1.9 3 3V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V9Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Digital Wallets', 'squarekit'); ?></h4>
                </div>

                <div class="feature-content">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enable_digital_wallet" value="1" <?php checked($enable_digital_wallet, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Digital Wallets', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Accept payments from Apple Pay, Google Pay, and other digital wallets.', 'squarekit'); ?></p>

                    <div class="digital-wallet-logos">
                        <img src="<?php echo esc_url(plugin_dir_url(dirname(dirname(__DIR__))) . 'assets/images/logos/Apple_Pay_logo.svg.png'); ?>"
                             alt="<?php esc_attr_e('Apple Pay', 'squarekit'); ?>"
                             class="wallet-logo" />
                        <img src="<?php echo esc_url(plugin_dir_url(dirname(dirname(__DIR__))) . 'assets/images/logos/512px-Google_Pay_Logo.svg.png'); ?>"
                             alt="<?php esc_attr_e('Google Pay', 'squarekit'); ?>"
                             class="wallet-logo" />
                    </div>
                </div>
            </div>

            <div class="payment-feature-card">
                <div class="feature-header">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Payment Logging', 'squarekit'); ?></h4>
                </div>

                <div class="feature-content">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enable_payment_logging" value="1" <?php checked($enable_logging, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Payment Logging', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Log payment transactions for debugging and audit purposes.', 'squarekit'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction Settings -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Transaction Settings', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure how payments are processed and captured.', 'squarekit'); ?></p>

        <div class="squarekit-transaction-settings-grid">
            <div class="transaction-setting-card">
                <h4><?php esc_html_e('Payment Capture', 'squarekit'); ?></h4>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Capture Method', 'squarekit'); ?></label>
                    <div class="squarekit-radio-group">
                        <label class="squarekit-radio">
                            <input type="radio" name="payment_capture_method" value="automatic" <?php checked($capture_method, 'automatic'); ?> />
                            <span class="squarekit-radio-label">
                                <strong><?php esc_html_e('Automatic Capture', 'squarekit'); ?></strong>
                                <br><small><?php esc_html_e('Capture payment immediately when order is placed', 'squarekit'); ?></small>
                            </span>
                        </label>
                        <label class="squarekit-radio">
                            <input type="radio" name="payment_capture_method" value="manual" <?php checked($capture_method, 'manual'); ?> />
                            <span class="squarekit-radio-label">
                                <strong><?php esc_html_e('Manual Capture', 'squarekit'); ?></strong>
                                <br><small><?php esc_html_e('Authorize payment and capture manually later', 'squarekit'); ?></small>
                            </span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="transaction-setting-card">
                <h4><?php esc_html_e('Environment Settings', 'squarekit'); ?></h4>

                <div class="squarekit-form-group">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="payment_sandbox_mode" value="1" <?php checked($sandbox_mode, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Sandbox Mode', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Use Square sandbox for testing. Disable for live transactions.', 'squarekit'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Mapping -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Payment Method Mapping', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Map Square payment methods to WooCommerce payment gateways.', 'squarekit'); ?></p>

        <div class="squarekit-payment-mapping-container">
            <div class="payment-mapping-header">
                <div class="mapping-col"><?php esc_html_e('Square Payment Method', 'squarekit'); ?></div>
                <div class="mapping-col"><?php esc_html_e('WooCommerce Gateway', 'squarekit'); ?></div>
                <div class="mapping-col"><?php esc_html_e('Status', 'squarekit'); ?></div>
                <div class="mapping-actions"><?php esc_html_e('Actions', 'squarekit'); ?></div>
            </div>

            <div id="payment-mapping-rows">
                <?php if (!empty($payment_methods)): ?>
                    <?php foreach ($payment_methods as $method): ?>
                        <div class="payment-mapping-row">
                            <div class="mapping-col">
                                <div class="payment-method-info">
                                    <span class="method-name"><?php echo esc_html($method['name']); ?></span>
                                    <span class="method-type"><?php echo esc_html($method['type']); ?></span>
                                </div>
                            </div>
                            <div class="mapping-col">
                                <select name="payment_gateway_mapping[<?php echo esc_attr($method['id']); ?>]">
                                    <option value=""><?php esc_html_e('Select gateway', 'squarekit'); ?></option>
                                    <option value="square" <?php selected($method['gateway'], 'square'); ?>><?php esc_html_e('Square Gateway', 'squarekit'); ?></option>
                                    <option value="stripe" <?php selected($method['gateway'], 'stripe'); ?>><?php esc_html_e('Stripe', 'squarekit'); ?></option>
                                    <option value="paypal" <?php selected($method['gateway'], 'paypal'); ?>><?php esc_html_e('PayPal', 'squarekit'); ?></option>
                                    <option value="cod" <?php selected($method['gateway'], 'cod'); ?>><?php esc_html_e('Cash on Delivery', 'squarekit'); ?></option>
                                </select>
                            </div>
                            <div class="mapping-col">
                                <span class="squarekit-status-indicator <?php echo $method['enabled'] ? 'enabled' : 'disabled'; ?>">
                                    <?php echo $method['enabled'] ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?>
                                </span>
                            </div>
                            <div class="mapping-actions">
                                <button type="button" class="squarekit-btn-toggle" onclick="togglePaymentMethod('<?php echo esc_attr($method['id']); ?>')">
                                    <?php echo $method['enabled'] ? esc_html__('Disable', 'squarekit') : esc_html__('Enable', 'squarekit'); ?>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="payment-mapping-empty">
                        <div class="empty-icon">
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 4H4C2.89 4 2.01 4.89 2.01 6L2 18C2 19.11 2.89 20 4 20H20C21.11 20 22 19.11 22 18V6C22 4.89 21.11 4 20 4ZM20 18H4V12H20V18ZM20 8H4V6H20V8Z" fill="currentColor" opacity="0.3"/>
                            </svg>
                        </div>
                        <h4><?php esc_html_e('No Payment Methods Found', 'squarekit'); ?></h4>
                        <p><?php esc_html_e('Connect to Square to see available payment methods.', 'squarekit'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Security & Compliance -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Security & Compliance', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure security settings and compliance options for payment processing.', 'squarekit'); ?></p>

        <div class="squarekit-security-grid">
            <div class="security-card">
                <div class="security-header">
                    <div class="security-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('PCI Compliance', 'squarekit'); ?></h4>
                </div>
                <div class="security-content">
                    <p><?php esc_html_e('Square handles PCI compliance automatically. Your site never stores sensitive card data.', 'squarekit'); ?></p>
                    <div class="compliance-status">
                        <span class="squarekit-status-indicator enabled"><?php esc_html_e('PCI DSS Compliant', 'squarekit'); ?></span>
                    </div>
                </div>
            </div>

            <div class="security-card">
                <div class="security-header">
                    <div class="security-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 8H20C21.1 8 22 8.9 22 10V20C22 21.1 21.1 22 20 22H4C2.9 22 2 21.1 2 20V10C2 8.9 2.9 8 4 8H6V6C6 3.8 7.8 2 10 2H14C16.2 2 18 3.8 18 6V8ZM10 4C8.9 4 8 4.9 8 6V8H16V6C16 4.9 15.1 4 14 4H10Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('SSL Encryption', 'squarekit'); ?></h4>
                </div>
                <div class="security-content">
                    <p><?php esc_html_e('All payment data is encrypted using industry-standard SSL/TLS protocols.', 'squarekit'); ?></p>
                    <div class="compliance-status">
                        <?php if (is_ssl()): ?>
                            <span class="squarekit-status-indicator enabled"><?php esc_html_e('SSL Active', 'squarekit'); ?></span>
                        <?php else: ?>
                            <span class="squarekit-status-indicator disabled"><?php esc_html_e('SSL Required', 'squarekit'); ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="squarekit-form-actions">
        <button type="submit" name="save_payment_settings" class="squarekit-admin-btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 21H5C4.45 21 4 20.55 4 20V4C4 3.45 4.45 3 5 3H16L20 7V20C20 20.55 19.55 21 19 21Z" fill="currentColor"/>
                <path d="M17 21V13H7V21H17Z" fill="currentColor" opacity="0.7"/>
                <path d="M7 7H13V10H7V7Z" fill="currentColor" opacity="0.7"/>
            </svg>
            <?php esc_html_e('Save Payment Settings', 'squarekit'); ?>
        </button>
    </div>
</form>

<script>
function togglePaymentMethod(methodId) {
    // Implementation for toggling payment method status
    console.log('Toggle payment method:', methodId);
    // This would typically make an AJAX call to update the method status
}
</script>
