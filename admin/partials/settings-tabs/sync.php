<?php
// Sync Settings Tab
if ( ! defined( 'ABSPATH' ) ) exit;

// Get current sync direction settings
$sync_direction_settings = $settings->get_sync_direction_settings();
$sync_direction_descriptions = $settings->get_sync_direction_descriptions();
?>

<form method="post" action="" class="squarekit-settings-form">
    <?php wp_nonce_field('squarekit_settings_save', 'squarekit_settings_nonce'); ?>
    
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Granular Sync Controls', 'squarekit'); ?></h3>
        <p class="description"><?php esc_html_e('Configure exactly what data syncs in each direction. You can enable different data types for each sync direction independently.', 'squarekit'); ?></p>

        <?php
        // Get granular sync settings
        $granular_settings = $settings->get_granular_sync_settings();
        ?>

        <div class="squarekit-granular-sync-grid">
            <!-- WooCommerce to Square Sync -->
            <div class="granular-sync-card woo-to-square">
                <div class="sync-card-header">
                    <div class="sync-card-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7 11L12 6L17 11M12 18V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('WooCommerce → Square', 'squarekit'); ?></h4>
                </div>

                <p class="sync-card-description"><?php esc_html_e('Push changes from WooCommerce to Square. Use with caution as this can overwrite Square data.', 'squarekit'); ?></p>

                <div class="sync-master-toggle">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="sync_woo_to_square_enabled" value="1" <?php checked($granular_settings['woo_to_square']['enabled'], true); ?> class="master-toggle" data-direction="woo_to_square" />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable WooCommerce → Square Sync', 'squarekit'); ?></span>
                    </label>
                </div>

                <div class="sync-data-types" data-direction="woo_to_square">
                    <h5><?php esc_html_e('Data Types to Sync:', 'squarekit'); ?></h5>

                    <div class="data-type-toggles">
                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_woo_to_square_products" value="1" <?php checked($granular_settings['woo_to_square']['data_types']['products'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Products', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Product names, descriptions, prices, variations', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_woo_to_square_inventory" value="1" <?php checked($granular_settings['woo_to_square']['data_types']['inventory'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Inventory', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Stock quantities and availability', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_woo_to_square_categories" value="1" <?php checked($granular_settings['woo_to_square']['data_types']['categories'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Categories', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Product categories and organization', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_woo_to_square_orders" value="1" <?php checked($granular_settings['woo_to_square']['data_types']['orders'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Orders', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Order data and transaction records', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_woo_to_square_customers" value="1" <?php checked($granular_settings['woo_to_square']['data_types']['customers'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Customers', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Customer profiles and contact information', 'squarekit'); ?></small>
                        </label>
                    </div>
                </div>

                <div class="sync-card-warning">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 9V13M12 17H12.01M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span><?php esc_html_e('⚠️ Use with caution: This can overwrite Square data', 'squarekit'); ?></span>
                </div>
            </div>

            <!-- Square to WooCommerce Sync -->
            <div class="granular-sync-card square-to-woo">
                <div class="sync-card-header">
                    <div class="sync-card-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17 13L12 18L7 13M12 6V18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php esc_html_e('Square → WooCommerce', 'squarekit'); ?></h4>
                </div>

                <p class="sync-card-description"><?php esc_html_e('Pull changes from Square to WooCommerce. Square is the authoritative source for inventory and product data.', 'squarekit'); ?></p>

                <div class="sync-master-toggle">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="sync_square_to_woo_enabled" value="1" <?php checked($granular_settings['square_to_woo']['enabled'], true); ?> class="master-toggle" data-direction="square_to_woo" />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Square → WooCommerce Sync', 'squarekit'); ?></span>
                    </label>
                </div>

                <div class="sync-data-types" data-direction="square_to_woo">
                    <h5><?php esc_html_e('Data Types to Sync:', 'squarekit'); ?></h5>

                    <div class="data-type-toggles">
                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_square_to_woo_products" value="1" <?php checked($granular_settings['square_to_woo']['data_types']['products'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Products', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Product names, descriptions, prices, variations', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox recommended">
                            <input type="checkbox" name="sync_square_to_woo_inventory" value="1" <?php checked($granular_settings['square_to_woo']['data_types']['inventory'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Inventory', 'squarekit'); ?> <span class="recommended-badge"><?php esc_html_e('Recommended', 'squarekit'); ?></span></span>
                            <small class="data-type-description"><?php esc_html_e('Stock quantities and availability (Square is authoritative)', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_square_to_woo_categories" value="1" <?php checked($granular_settings['square_to_woo']['data_types']['categories'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Categories', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Product categories and organization', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_square_to_woo_orders" value="1" <?php checked($granular_settings['square_to_woo']['data_types']['orders'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Orders', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Order data from Square POS', 'squarekit'); ?></small>
                        </label>

                        <label class="squarekit-checkbox">
                            <input type="checkbox" name="sync_square_to_woo_customers" value="1" <?php checked($granular_settings['square_to_woo']['data_types']['customers'], true); ?> />
                            <span class="checkbox-label"><?php esc_html_e('Customers', 'squarekit'); ?></span>
                            <small class="data-type-description"><?php esc_html_e('Customer profiles from Square', 'squarekit'); ?></small>
                        </label>
                    </div>
                </div>

                <div class="sync-card-recommendation">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span><?php esc_html_e('✅ Recommended: Square as authoritative source', 'squarekit'); ?></span>
                </div>
            </div>
        </div>
        
        <div class="squarekit-sync-notes">
            <div class="sync-notes-card">
                <h4><?php esc_html_e('Important Notes:', 'squarekit'); ?></h4>
                <ul>
                    <li><?php esc_html_e('You can enable both sync directions simultaneously for bidirectional synchronization.', 'squarekit'); ?></li>
                    <li><?php esc_html_e('Each sync direction operates independently and can be enabled/disabled separately.', 'squarekit'); ?></li>
                    <li><?php esc_html_e('Changes made in one system will be reflected in the other based on your sync direction settings.', 'squarekit'); ?></li>
                    <li><?php esc_html_e('Automatic sync runs according to your cron schedule settings below.', 'squarekit'); ?></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Sync Automation & Performance', 'squarekit'); ?></h3>
        <p class="description"><?php esc_html_e('Configure how and when synchronization occurs automatically between Square and WooCommerce.', 'squarekit'); ?></p>

        <div class="squarekit-sync-automation-grid">
            <div class="sync-automation-card">
                <h4><?php esc_html_e('Automatic Sync Schedule', 'squarekit'); ?></h4>
                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Sync Frequency', 'squarekit'); ?></label>
                    <select name="cron_schedule" class="squarekit-select">
                        <option value="disabled" <?php selected($interval, 'disabled'); ?>><?php esc_html_e('Disabled (Manual Only)', 'squarekit'); ?></option>
                        <option value="every_five_minutes" <?php selected($interval, 'every_five_minutes'); ?>><?php esc_html_e('Every 5 Minutes', 'squarekit'); ?></option>
                        <option value="every_fifteen_minutes" <?php selected($interval, 'every_fifteen_minutes'); ?>><?php esc_html_e('Every 15 Minutes', 'squarekit'); ?></option>
                        <option value="every_thirty_minutes" <?php selected($interval, 'every_thirty_minutes'); ?>><?php esc_html_e('Every 30 Minutes', 'squarekit'); ?></option>
                        <option value="hourly" <?php selected($interval, 'hourly'); ?>><?php esc_html_e('Hourly (Recommended)', 'squarekit'); ?></option>
                        <option value="twicedaily" <?php selected($interval, 'twicedaily'); ?>><?php esc_html_e('Twice Daily', 'squarekit'); ?></option>
                        <option value="daily" <?php selected($interval, 'daily'); ?>><?php esc_html_e('Daily', 'squarekit'); ?></option>
                        <option value="weekly" <?php selected($interval, 'weekly'); ?>><?php esc_html_e('Weekly', 'squarekit'); ?></option>
                    </select>
                    <p class="description"><?php esc_html_e('How often should automatic synchronization run? More frequent sync provides better real-time accuracy but uses more server resources.', 'squarekit'); ?></p>
                </div>
            </div>

            <div class="sync-automation-card">
                <h4><?php esc_html_e('Performance Settings', 'squarekit'); ?></h4>
                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Batch Size', 'squarekit'); ?></label>
                    <select name="sync_batch_size" class="squarekit-select">
                        <option value="25" <?php selected($settings->get('sync_batch_size', 50), 25); ?>><?php esc_html_e('25 items (Conservative)', 'squarekit'); ?></option>
                        <option value="50" <?php selected($settings->get('sync_batch_size', 50), 50); ?>><?php esc_html_e('50 items (Recommended)', 'squarekit'); ?></option>
                        <option value="100" <?php selected($settings->get('sync_batch_size', 50), 100); ?>><?php esc_html_e('100 items (Aggressive)', 'squarekit'); ?></option>
                        <option value="200" <?php selected($settings->get('sync_batch_size', 50), 200); ?>><?php esc_html_e('200 items (High Performance)', 'squarekit'); ?></option>
                    </select>
                    <p class="description"><?php esc_html_e('Number of items to process in each sync batch. Higher values are faster but use more memory.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Timeout (seconds)', 'squarekit'); ?></label>
                    <select name="sync_timeout" class="squarekit-select">
                        <option value="30" <?php selected($settings->get('sync_timeout', 60), 30); ?>><?php esc_html_e('30 seconds', 'squarekit'); ?></option>
                        <option value="60" <?php selected($settings->get('sync_timeout', 60), 60); ?>><?php esc_html_e('60 seconds (Recommended)', 'squarekit'); ?></option>
                        <option value="120" <?php selected($settings->get('sync_timeout', 60), 120); ?>><?php esc_html_e('2 minutes', 'squarekit'); ?></option>
                        <option value="300" <?php selected($settings->get('sync_timeout', 60), 300); ?>><?php esc_html_e('5 minutes', 'squarekit'); ?></option>
                    </select>
                    <p class="description"><?php esc_html_e('Maximum time to wait for Square API responses.', 'squarekit'); ?></p>
                </div>
            </div>

            <div class="sync-automation-card">
                <h4><?php esc_html_e('Conflict Resolution', 'squarekit'); ?></h4>
                <p class="description"><?php esc_html_e('How to handle conflicts when the same data is changed in both systems.', 'squarekit'); ?></p>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Inventory Conflicts', 'squarekit'); ?></label>
                    <div class="squarekit-radio-group">
                        <label class="squarekit-radio">
                            <input type="radio" name="inventory_conflict_resolution" value="square_wins" <?php checked($settings->get('inventory_conflict_resolution', 'square_wins'), 'square_wins'); ?> />
                            <span class="radio-label"><?php esc_html_e('Square Wins (Recommended)', 'squarekit'); ?></span>
                        </label>
                        <label class="squarekit-radio">
                            <input type="radio" name="inventory_conflict_resolution" value="wc_wins" <?php checked($settings->get('inventory_conflict_resolution', 'square_wins'), 'wc_wins'); ?> />
                            <span class="radio-label"><?php esc_html_e('WooCommerce Wins', 'squarekit'); ?></span>
                        </label>
                        <label class="squarekit-radio">
                            <input type="radio" name="inventory_conflict_resolution" value="manual" <?php checked($settings->get('inventory_conflict_resolution', 'square_wins'), 'manual'); ?> />
                            <span class="radio-label"><?php esc_html_e('Manual Review', 'squarekit'); ?></span>
                        </label>
                    </div>
                    <p class="description"><?php esc_html_e('Square is recommended as the authoritative source for inventory data.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Product Conflicts', 'squarekit'); ?></label>
                    <div class="squarekit-radio-group">
                        <label class="squarekit-radio">
                            <input type="radio" name="product_conflict_resolution" value="square_wins" <?php checked($settings->get('product_conflict_resolution', 'square_wins'), 'square_wins'); ?> />
                            <span class="radio-label"><?php esc_html_e('Square Wins (Recommended)', 'squarekit'); ?></span>
                        </label>
                        <label class="squarekit-radio">
                            <input type="radio" name="product_conflict_resolution" value="wc_wins" <?php checked($settings->get('product_conflict_resolution', 'square_wins'), 'wc_wins'); ?> />
                            <span class="radio-label"><?php esc_html_e('WooCommerce Wins', 'squarekit'); ?></span>
                        </label>
                        <label class="squarekit-radio">
                            <input type="radio" name="product_conflict_resolution" value="manual" <?php checked($settings->get('product_conflict_resolution', 'square_wins'), 'manual'); ?> />
                            <span class="radio-label"><?php esc_html_e('Manual Review', 'squarekit'); ?></span>
                        </label>
                    </div>
                    <p class="description"><?php esc_html_e('Square is recommended as the authoritative source for product data.', 'squarekit'); ?></p>
                </div>
            </div>
        </div>
    </div>



    <div class="squarekit-form-actions">
        <button type="submit" class="squarekit-admin-btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 21H5C4.45 21 4 20.55 4 20V4C4 3.45 4.45 3 5 3H16L20 7V20C20 20.55 19.55 21 19 21Z" fill="currentColor"/>
                <path d="M17 21V13H7V21H17Z" fill="currentColor" opacity="0.7"/>
                <path d="M7 7H13V10H7V7Z" fill="currentColor" opacity="0.7"/>
            </svg>
            <?php esc_html_e('Save Sync Settings', 'squarekit'); ?>
        </button>
    </div>
</form>
