<?php
// Sync Settings Tab
if ( ! defined( 'ABSPATH' ) ) exit;

// Get current sync direction settings
$sync_direction_settings = $settings->get_sync_direction_settings();
$sync_direction_descriptions = $settings->get_sync_direction_descriptions();
?>

<form method="post" action="" class="squarekit-settings-form">
    <?php wp_nonce_field('squarekit_settings_save', 'squarekit_settings_nonce'); ?>
    
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Sync Direction Controls', 'squarekit'); ?></h3>
        
        <div class="squarekit-sync-direction-grid">
            <!-- WooCommerce to Square Sync -->
            <div class="squarekit-sync-direction-card woo-to-square <?php echo $sync_direction_settings['woo_to_square'] ? 'enabled' : 'disabled'; ?>">
                <div class="sync-card-header">
                    <div class="sync-card-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7 11L12 6L17 11M12 18V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php echo esc_html($sync_direction_descriptions['woo_to_square']['title']); ?></h4>
                </div>
                
                <p class="sync-card-description"><?php echo esc_html($sync_direction_descriptions['woo_to_square']['description']); ?></p>
                
                <div class="sync-card-toggle">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="sync_woo_to_square" value="1" <?php checked($sync_direction_settings['woo_to_square'], true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable WooCommerce → Square Sync', 'squarekit'); ?></span>
                    </label>
                </div>
                
                <div class="sync-card-features">
                    <h5><?php esc_html_e('Features:', 'squarekit'); ?></h5>
                    <ul>
                        <?php foreach ($sync_direction_descriptions['woo_to_square']['features'] as $feature) : ?>
                            <li><?php echo esc_html($feature); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="sync-card-status">
                    <span class="status-label"><?php esc_html_e('Status:', 'squarekit'); ?></span>
                    <span class="status-indicator <?php echo $sync_direction_settings['woo_to_square'] ? 'enabled' : 'disabled'; ?>">
                        <?php echo $sync_direction_settings['woo_to_square'] ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?>
                    </span>
                </div>
            </div>
            
            <!-- Square to WooCommerce Sync -->
            <div class="squarekit-sync-direction-card square-to-woo <?php echo $sync_direction_settings['square_to_woo'] ? 'enabled' : 'disabled'; ?>">
                <div class="sync-card-header">
                    <div class="sync-card-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17 13L12 18L7 13M12 6V18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4><?php echo esc_html($sync_direction_descriptions['square_to_woo']['title']); ?></h4>
                </div>
                
                <p class="sync-card-description"><?php echo esc_html($sync_direction_descriptions['square_to_woo']['description']); ?></p>
                
                <div class="sync-card-toggle">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="sync_square_to_woo" value="1" <?php checked($sync_direction_settings['square_to_woo'], true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Square → WooCommerce Sync', 'squarekit'); ?></span>
                    </label>
                </div>
                
                <div class="sync-card-features">
                    <h5><?php esc_html_e('Features:', 'squarekit'); ?></h5>
                    <ul>
                        <?php foreach ($sync_direction_descriptions['square_to_woo']['features'] as $feature) : ?>
                            <li><?php echo esc_html($feature); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="sync-card-status">
                    <span class="status-label"><?php esc_html_e('Status:', 'squarekit'); ?></span>
                    <span class="status-indicator <?php echo $sync_direction_settings['square_to_woo'] ? 'enabled' : 'disabled'; ?>">
                        <?php echo $sync_direction_settings['square_to_woo'] ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?>
                    </span>
                </div>
            </div>
        </div>
        
        <div class="squarekit-sync-notes">
            <div class="sync-notes-card">
                <h4><?php esc_html_e('Important Notes:', 'squarekit'); ?></h4>
                <ul>
                    <li><?php esc_html_e('You can enable both sync directions simultaneously for bidirectional synchronization.', 'squarekit'); ?></li>
                    <li><?php esc_html_e('Each sync direction operates independently and can be enabled/disabled separately.', 'squarekit'); ?></li>
                    <li><?php esc_html_e('Changes made in one system will be reflected in the other based on your sync direction settings.', 'squarekit'); ?></li>
                    <li><?php esc_html_e('Automatic sync runs according to your cron schedule settings below.', 'squarekit'); ?></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Automatic Sync Schedule', 'squarekit'); ?></h3>
        
        <div class="squarekit-form-group">
            <label><?php esc_html_e('Sync Frequency', 'squarekit'); ?></label>
            <select name="cron_schedule">
                <option value="every_minute" <?php selected($interval, 'every_minute'); ?>><?php esc_html_e('Every Minute', 'squarekit'); ?></option>
                <option value="every_five_minutes" <?php selected($interval, 'every_five_minutes'); ?>><?php esc_html_e('Every 5 Minutes', 'squarekit'); ?></option>
                <option value="every_fifteen_minutes" <?php selected($interval, 'every_fifteen_minutes'); ?>><?php esc_html_e('Every 15 Minutes', 'squarekit'); ?></option>
                <option value="every_thirty_minutes" <?php selected($interval, 'every_thirty_minutes'); ?>><?php esc_html_e('Every 30 Minutes', 'squarekit'); ?></option>
                <option value="hourly" <?php selected($interval, 'hourly'); ?>><?php esc_html_e('Hourly', 'squarekit'); ?></option>
                <option value="twicedaily" <?php selected($interval, 'twicedaily'); ?>><?php esc_html_e('Twice Daily', 'squarekit'); ?></option>
                <option value="daily" <?php selected($interval, 'daily'); ?>><?php esc_html_e('Daily', 'squarekit'); ?></option>
                <option value="weekly" <?php selected($interval, 'weekly'); ?>><?php esc_html_e('Weekly', 'squarekit'); ?></option>
            </select>
            <p class="description"><?php esc_html_e('How often should automatic synchronization run? More frequent sync provides better real-time accuracy but uses more server resources.', 'squarekit'); ?></p>
        </div>
    </div>

    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Legacy Sync Settings', 'squarekit'); ?></h3>
        <p class="description"><?php esc_html_e('These settings are maintained for backward compatibility. Use the Sync Direction Controls above for new configurations.', 'squarekit'); ?></p>

        <?php if ( $sync_inventory || $sync_orders || $sync_customers ): ?>
        <div class="notice notice-warning inline">
            <p><strong><?php esc_html_e('⚠️ Warning:', 'squarekit'); ?></strong> <?php esc_html_e('Legacy sync settings are enabled. These may cause automatic syncing from WooCommerce to Square, including product deletions. Consider using the Sync Direction Controls above instead.', 'squarekit'); ?></p>
        </div>
        <?php endif; ?>
        
        <div class="squarekit-form-group">
            <label class="squarekit-checkbox">
                <input type="checkbox" name="sync_inventory" value="1" <?php checked($sync_inventory, true); ?> />
                <span class="squarekit-checkbox-label"><?php esc_html_e('Enable Inventory Sync', 'squarekit'); ?></span>
            </label>
        </div>
        
        <div class="squarekit-form-group">
            <label class="squarekit-checkbox">
                <input type="checkbox" name="sync_orders" value="1" <?php checked($sync_orders, true); ?> />
                <span class="squarekit-checkbox-label"><?php esc_html_e('Enable Order Sync', 'squarekit'); ?></span>
            </label>
        </div>
        
        <div class="squarekit-form-group">
            <label class="squarekit-checkbox">
                <input type="checkbox" name="sync_customers" value="1" <?php checked($sync_customers, true); ?> />
                <span class="squarekit-checkbox-label"><?php esc_html_e('Enable Customer Sync', 'squarekit'); ?></span>
            </label>
        </div>
    </div>

    <div class="squarekit-form-actions">
        <button type="submit" class="squarekit-admin-btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 21H5C4.45 21 4 20.55 4 20V4C4 3.45 4.45 3 5 3H16L20 7V20C20 20.55 19.55 21 19 21Z" fill="currentColor"/>
                <path d="M17 21V13H7V21H17Z" fill="currentColor" opacity="0.7"/>
                <path d="M7 7H13V10H7V7Z" fill="currentColor" opacity="0.7"/>
            </svg>
            <?php esc_html_e('Save Sync Settings', 'squarekit'); ?>
        </button>
    </div>
</form>
