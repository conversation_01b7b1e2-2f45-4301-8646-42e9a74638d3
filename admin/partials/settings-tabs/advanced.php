<?php
// Advanced Settings Tab
if ( ! defined( 'ABSPATH' ) ) exit;

// Get advanced settings
$webhook_url = $settings->get('webhook_url', '');
$webhook_events = $settings->get('webhook_events', array());
$webhook_id = $settings->get('webhook_id', '');
$webhook_status = $settings->get('webhook_enabled', false);
$webhook_last_event = $settings->get('webhook_last_event', '');
$signature_key = method_exists($settings, 'get_webhook_signature_key') ? $settings->get_webhook_signature_key() : '';

// Get advanced cron settings
$selective_sync_enabled = $settings->get('enable_selective_sync', false);
$enable_cron_retry = $settings->get('enable_cron_retry', true);
$max_retry_attempts = $settings->get('max_cron_retry_attempts', 3);
$cron_retry_delay = $settings->get('cron_retry_delay', 300);
$enable_parallel_processing = $settings->get('enable_parallel_processing', false);
$max_parallel_jobs = $settings->get('max_parallel_jobs', 2);
$max_execution_time = $settings->get('max_sync_execution_time', 300);
$batch_size = $settings->get('sync_batch_size', 50);

// Get image optimization settings
$optimize_images = $settings->get('optimize_images', false);
$max_image_width = $settings->get('max_image_width', 800);
$max_image_height = $settings->get('max_image_height', 600);
$image_quality = $settings->get('image_quality', 85);

// Get SKU validation settings
$sku_validation_settings = $settings->get('sku_validation_settings', array(
    'enforce_unique_skus' => false,
    'auto_generate_skus' => false,
    'prefix' => '',
    'suffix' => '',
    'max_length' => 50,
    'allowed_characters' => 'alphanumeric_dash'
));
$sku_mapping_rules = method_exists($settings, 'get_sku_mapping_rules') ? $settings->get_sku_mapping_rules() : array();
?>

<form method="post" action="" class="squarekit-settings-form">
    <?php wp_nonce_field('squarekit_settings_save', 'squarekit_settings_nonce'); ?>

    <!-- Webhooks Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Webhook Configuration', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure real-time synchronization with Square using webhooks.', 'squarekit'); ?></p>

        <div class="squarekit-form-group">
            <label class="squarekit-toggle">
                <input type="checkbox" name="webhook_status" value="1" <?php checked($webhook_status, true); ?> />
                <span class="toggle-slider"></span>
                <span class="toggle-label"><?php esc_html_e('Enable real-time sync with Square (webhooks)', 'squarekit'); ?></span>
            </label>
            <p class="description"><?php esc_html_e('When enabled, Square will notify your site of changes in real time.', 'squarekit'); ?></p>
        </div>

        <div class="squarekit-webhook-status-card">
            <h4><?php esc_html_e('Webhook Status', 'squarekit'); ?></h4>
            <div class="webhook-info-grid">
                <div class="webhook-info-item">
                    <span class="info-label"><?php esc_html_e('Status:', 'squarekit'); ?></span>
                    <span class="squarekit-status-indicator <?php echo $webhook_status ? 'enabled' : 'disabled'; ?>">
                        <?php echo $webhook_status ? esc_html__('Active', 'squarekit') : esc_html__('Inactive', 'squarekit'); ?>
                    </span>
                </div>

                <?php if ($webhook_url): ?>
                    <div class="webhook-info-item">
                        <span class="info-label"><?php esc_html_e('Webhook URL:', 'squarekit'); ?></span>
                        <code class="info-value"><?php echo esc_html($webhook_url); ?></code>
                    </div>
                <?php endif; ?>

                <?php if ($webhook_id): ?>
                    <div class="webhook-info-item">
                        <span class="info-label"><?php esc_html_e('Webhook ID:', 'squarekit'); ?></span>
                        <code class="info-value"><?php echo esc_html($webhook_id); ?></code>
                    </div>
                <?php endif; ?>

                <?php if (!empty($webhook_events)): ?>
                    <div class="webhook-info-item">
                        <span class="info-label"><?php esc_html_e('Event Types:', 'squarekit'); ?></span>
                        <div class="webhook-events">
                            <?php foreach ($webhook_events as $event): ?>
                                <span class="webhook-event-tag"><?php echo esc_html($event); ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($signature_key): ?>
                    <div class="webhook-info-item">
                        <span class="info-label"><?php esc_html_e('Signature Key:', 'squarekit'); ?></span>
                        <code class="info-value"><?php echo esc_html(substr($signature_key, 0, 10) . '...'); ?></code>
                    </div>
                <?php else: ?>
                    <div class="webhook-info-item">
                        <span class="info-label"><?php esc_html_e('Security:', 'squarekit'); ?></span>
                        <span class="squarekit-status-indicator disabled"><?php esc_html_e('No signature key configured', 'squarekit'); ?></span>
                    </div>
                <?php endif; ?>

                <?php if ($webhook_last_event): ?>
                    <div class="webhook-info-item">
                        <span class="info-label"><?php esc_html_e('Last Event:', 'squarekit'); ?></span>
                        <code class="info-value"><?php echo esc_html($webhook_last_event); ?></code>
                    </div>
                <?php endif; ?>
            </div>

            <div class="webhook-actions">
                <button type="button" id="test-webhook" class="squarekit-admin-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php esc_html_e('Test Webhook', 'squarekit'); ?>
                </button>

                <button type="button" id="refresh-webhook" class="squarekit-admin-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 4V10H7M23 20V14H17M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php esc_html_e('Refresh Registration', 'squarekit'); ?>
                </button>

                <button type="button" id="view-webhook-logs" class="squarekit-admin-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" fill="currentColor"/>
                        <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 13H8M16 17H8M10 9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php esc_html_e('View Logs', 'squarekit'); ?>
                </button>
            </div>

            <div id="webhook-test-result" style="display:none; margin-top: 1em; padding: 1em; border-radius: 8px; border: 2px solid #e2e8f0; background: #f9f9f9;"></div>
        </div>
    </div>

    <!-- Performance Settings Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Performance & Cron Settings', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure advanced performance and scheduling options for optimal sync operations.', 'squarekit'); ?></p>

        <div class="squarekit-performance-grid">
            <div class="performance-card">
                <h4><?php esc_html_e('Sync Performance', 'squarekit'); ?></h4>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Batch Size', 'squarekit'); ?></label>
                    <input type="number" name="sync_batch_size" value="<?php echo esc_attr($batch_size); ?>" min="10" max="200" step="10" />
                    <p class="description"><?php esc_html_e('Number of items to process in each batch. Lower values use less memory but take longer.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Max Execution Time (seconds)', 'squarekit'); ?></label>
                    <input type="number" name="max_sync_execution_time" value="<?php echo esc_attr($max_execution_time); ?>" min="60" max="1800" step="60" />
                    <p class="description"><?php esc_html_e('Maximum time allowed for sync operations before timeout.', 'squarekit'); ?></p>
                </div>
            </div>

            <div class="performance-card">
                <h4><?php esc_html_e('Advanced Cron Options', 'squarekit'); ?></h4>

                <div class="squarekit-form-group">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enable_selective_sync" value="1" <?php checked($selective_sync_enabled, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Selective Sync', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('When enabled, different data types can be synced at different intervals.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enable_cron_retry" value="1" <?php checked($enable_cron_retry, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Retry on Failure', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Automatically retry failed sync operations.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Max Retry Attempts', 'squarekit'); ?></label>
                    <input type="number" name="max_cron_retry_attempts" value="<?php echo esc_attr($max_retry_attempts); ?>" min="1" max="10" step="1" />
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Retry Delay (seconds)', 'squarekit'); ?></label>
                    <input type="number" name="cron_retry_delay" value="<?php echo esc_attr($cron_retry_delay); ?>" min="60" max="3600" step="60" />
                </div>
            </div>

            <div class="performance-card">
                <h4><?php esc_html_e('Parallel Processing', 'squarekit'); ?></h4>

                <div class="squarekit-form-group">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enable_parallel_processing" value="1" <?php checked($enable_parallel_processing, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Parallel Processing', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Process multiple sync operations simultaneously (experimental).', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Max Parallel Jobs', 'squarekit'); ?></label>
                    <input type="number" name="max_parallel_jobs" value="<?php echo esc_attr($max_parallel_jobs); ?>" min="1" max="5" step="1" />
                    <p class="description"><?php esc_html_e('Maximum number of simultaneous sync operations.', 'squarekit'); ?></p>
                </div>
            </div>
        </div>

        <?php
        // Get cron status
        if ( ! class_exists('SquareKit_Loader') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
        }
        $loader = new SquareKit_Loader();
        $cron_status = $loader->get_cron_status();
        ?>

        <div class="squarekit-cron-status-card">
            <h4><?php esc_html_e('Cron Status', 'squarekit'); ?></h4>
            <div class="cron-status-grid">
                <div class="cron-status-item">
                    <span class="status-label"><?php esc_html_e('Status:', 'squarekit'); ?></span>
                    <span class="squarekit-status-indicator <?php echo $cron_status['enabled'] ? 'enabled' : 'disabled'; ?>">
                        <?php echo $cron_status['enabled'] ? esc_html__('Active', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?>
                    </span>
                </div>

                <div class="cron-status-item">
                    <span class="status-label"><?php esc_html_e('Schedule:', 'squarekit'); ?></span>
                    <span class="status-value"><?php echo esc_html(ucfirst($cron_status['schedule'])); ?></span>
                </div>

                <div class="cron-status-item">
                    <span class="status-label"><?php esc_html_e('Next Run:', 'squarekit'); ?></span>
                    <span class="status-value"><?php echo $cron_status['next_run'] ? esc_html(date('Y-m-d H:i:s', $cron_status['next_run'])) : esc_html__('Not scheduled', 'squarekit'); ?></span>
                </div>

                <div class="cron-status-item">
                    <span class="status-label"><?php esc_html_e('Last Run:', 'squarekit'); ?></span>
                    <span class="status-value"><?php echo $cron_status['last_run'] ? esc_html(date('Y-m-d H:i:s', $cron_status['last_run'])) : esc_html__('Never', 'squarekit'); ?></span>
                </div>

                <div class="cron-status-item">
                    <span class="status-label"><?php esc_html_e('Selective Sync:', 'squarekit'); ?></span>
                    <span class="squarekit-status-indicator <?php echo $cron_status['selective_sync'] ? 'enabled' : 'disabled'; ?>">
                        <?php echo $cron_status['selective_sync'] ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?>
                    </span>
                </div>
            </div>

            <div class="cron-actions">
                <button type="button" id="test-cron" class="squarekit-admin-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php esc_html_e('Test Cron', 'squarekit'); ?>
                </button>

                <button type="button" id="view-cron-logs" class="squarekit-admin-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" fill="currentColor"/>
                        <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M16 13H8M16 17H8M10 9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php esc_html_e('View Logs', 'squarekit'); ?>
                </button>
            </div>

            <div id="cron-test-result" style="display:none; margin-top: 1em; padding: 1em; border-radius: 8px; border: 2px solid #e2e8f0; background: #f9f9f9;"></div>
        </div>
    </div>

    <!-- Image Optimization Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('Image Optimization', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure how product images are processed during sync operations.', 'squarekit'); ?></p>

        <div class="squarekit-image-optimization-grid">
            <div class="optimization-card">
                <h4><?php esc_html_e('Image Processing', 'squarekit'); ?></h4>

                <div class="squarekit-form-group">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="optimize_images" value="1" <?php checked($optimize_images, true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enable Image Optimization', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Automatically optimize images during sync for better performance.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Max Image Width (px)', 'squarekit'); ?></label>
                    <input type="number" name="max_image_width" value="<?php echo esc_attr($max_image_width); ?>" min="200" max="2000" step="50" />
                    <p class="description"><?php esc_html_e('Maximum width for product images. Images will be resized if larger.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Max Image Height (px)', 'squarekit'); ?></label>
                    <input type="number" name="max_image_height" value="<?php echo esc_attr($max_image_height); ?>" min="200" max="2000" step="50" />
                    <p class="description"><?php esc_html_e('Maximum height for product images. Images will be resized if larger.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Image Quality (%)', 'squarekit'); ?></label>
                    <input type="number" name="image_quality" value="<?php echo esc_attr($image_quality); ?>" min="50" max="100" step="5" />
                    <p class="description"><?php esc_html_e('JPEG compression quality. Higher values mean better quality but larger file sizes.', 'squarekit'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- SKU Validation Section -->
    <div class="squarekit-settings-section">
        <h3><?php esc_html_e('SKU Validation & Generation', 'squarekit'); ?></h3>
        <p><?php esc_html_e('Configure SKU validation rules and automatic generation patterns.', 'squarekit'); ?></p>

        <div class="squarekit-sku-validation-grid">
            <div class="sku-validation-card">
                <h4><?php esc_html_e('Validation Rules', 'squarekit'); ?></h4>

                <div class="squarekit-form-group">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="enforce_unique_skus" value="1" <?php checked($sku_validation_settings['enforce_unique_skus'], true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Enforce Unique SKUs', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Prevent duplicate SKUs across products.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label class="squarekit-toggle">
                        <input type="checkbox" name="auto_generate_skus" value="1" <?php checked($sku_validation_settings['auto_generate_skus'], true); ?> />
                        <span class="toggle-slider"></span>
                        <span class="toggle-label"><?php esc_html_e('Auto-Generate Missing SKUs', 'squarekit'); ?></span>
                    </label>
                    <p class="description"><?php esc_html_e('Automatically generate SKUs for products that don\'t have them.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('SKU Prefix', 'squarekit'); ?></label>
                    <input type="text" name="sku_prefix" value="<?php echo esc_attr($sku_validation_settings['prefix']); ?>" placeholder="<?php esc_attr_e('e.g., PROD-', 'squarekit'); ?>" />
                    <p class="description"><?php esc_html_e('Prefix to add to auto-generated SKUs.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('SKU Suffix', 'squarekit'); ?></label>
                    <input type="text" name="sku_suffix" value="<?php echo esc_attr($sku_validation_settings['suffix']); ?>" placeholder="<?php esc_attr_e('e.g., -SQ', 'squarekit'); ?>" />
                    <p class="description"><?php esc_html_e('Suffix to add to auto-generated SKUs.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Max SKU Length', 'squarekit'); ?></label>
                    <input type="number" name="sku_max_length" value="<?php echo esc_attr($sku_validation_settings['max_length']); ?>" min="8" max="128" step="1" />
                    <p class="description"><?php esc_html_e('Maximum allowed length for SKUs.', 'squarekit'); ?></p>
                </div>

                <div class="squarekit-form-group">
                    <label><?php esc_html_e('Allowed Characters', 'squarekit'); ?></label>
                    <select name="sku_allowed_characters">
                        <option value="alphanumeric" <?php selected($sku_validation_settings['allowed_characters'], 'alphanumeric'); ?>><?php esc_html_e('Alphanumeric only', 'squarekit'); ?></option>
                        <option value="alphanumeric_dash" <?php selected($sku_validation_settings['allowed_characters'], 'alphanumeric_dash'); ?>><?php esc_html_e('Alphanumeric + dash', 'squarekit'); ?></option>
                        <option value="alphanumeric_dash_underscore" <?php selected($sku_validation_settings['allowed_characters'], 'alphanumeric_dash_underscore'); ?>><?php esc_html_e('Alphanumeric + dash + underscore', 'squarekit'); ?></option>
                        <option value="all_printable" <?php selected($sku_validation_settings['allowed_characters'], 'all_printable'); ?>><?php esc_html_e('All printable characters', 'squarekit'); ?></option>
                    </select>
                    <p class="description"><?php esc_html_e('Which characters are allowed in SKUs.', 'squarekit'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <div class="squarekit-form-actions">
        <button type="submit" class="squarekit-admin-btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 21H5C4.45 21 4 20.55 4 20V4C4 3.45 4.45 3 5 3H16L20 7V20C20 20.55 19.55 21 19 21Z" fill="currentColor"/>
                <path d="M17 21V13H7V21H17Z" fill="currentColor" opacity="0.7"/>
                <path d="M7 7H13V10H7V7Z" fill="currentColor" opacity="0.7"/>
            </svg>
            <?php esc_html_e('Save Advanced Settings', 'squarekit'); ?>
        </button>
    </div>
</form>

<script>
jQuery(function($){
    // Webhook testing
    $('#test-webhook').on('click', function(){
        var $btn = $(this);
        var $result = $('#webhook-test-result');

        $btn.prop('disabled', true).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg> <?php esc_html_e('Testing...', 'squarekit'); ?>');
        $result.hide();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_test_webhook',
                nonce: '<?php echo wp_create_nonce('squarekit_test_webhook'); ?>'
            },
            success: function(response){
                $result.html(response.data.message).show();
                if (response.success) {
                    $result.css({'background': '#d1fae5', 'border-color': '#10b981', 'color': '#065f46'});
                } else {
                    $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
                }
            },
            error: function(){
                $result.html('<?php esc_html_e('Test failed. Please try again.', 'squarekit'); ?>').show();
                $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
            },
            complete: function(){
                $btn.prop('disabled', false).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg> <?php esc_html_e('Test Webhook', 'squarekit'); ?>');
            }
        });
    });

    // Webhook refresh
    $('#refresh-webhook').on('click', function(){
        var $btn = $(this);
        var $result = $('#webhook-test-result');

        $btn.prop('disabled', true).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg> <?php esc_html_e('Refreshing...', 'squarekit'); ?>');
        $result.hide();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_refresh_webhook',
                nonce: '<?php echo wp_create_nonce('squarekit_refresh_webhook'); ?>'
            },
            success: function(response){
                $result.html(response.data.message).show();
                if (response.success) {
                    $result.css({'background': '#d1fae5', 'border-color': '#10b981', 'color': '#065f46'});
                    setTimeout(function(){ location.reload(); }, 2000);
                } else {
                    $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
                }
            },
            error: function(){
                $result.html('<?php esc_html_e('Refresh failed. Please try again.', 'squarekit'); ?>').show();
                $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
            },
            complete: function(){
                $btn.prop('disabled', false).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 4V10H7M23 20V14H17M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg> <?php esc_html_e('Refresh Registration', 'squarekit'); ?>');
            }
        });
    });

    // View webhook logs
    $('#view-webhook-logs').on('click', function(){
        window.open('<?php echo admin_url('admin.php?page=squarekit&tab=logs&type=webhook'); ?>', '_blank');
    });

    // Cron testing
    $('#test-cron').on('click', function(){
        var $btn = $(this);
        var $result = $('#cron-test-result');

        $btn.prop('disabled', true).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg> <?php esc_html_e('Testing...', 'squarekit'); ?>');
        $result.hide();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_test_cron',
                nonce: '<?php echo wp_create_nonce('squarekit_test_cron'); ?>'
            },
            success: function(response){
                $result.html(response.data.message).show();
                if (response.success) {
                    $result.css({'background': '#d1fae5', 'border-color': '#10b981', 'color': '#065f46'});
                } else {
                    $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
                }
            },
            error: function(){
                $result.html('<?php esc_html_e('Test failed. Please try again.', 'squarekit'); ?>').show();
                $result.css({'background': '#fee2e2', 'border-color': '#ef4444', 'color': '#991b1b'});
            },
            complete: function(){
                $btn.prop('disabled', false).html('<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 12L11 14L15 10M21 12C21 16.97 16.97 21 12 21C7.03 21 3 16.97 3 12C3 7.03 7.03 3 12 3C16.97 3 21 7.03 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg> <?php esc_html_e('Test Cron', 'squarekit'); ?>');
            }
        });
    });

    // View cron logs
    $('#view-cron-logs').on('click', function(){
        window.open('<?php echo admin_url('admin.php?page=squarekit&tab=logs&type=cron'); ?>', '_blank');
    });
});
</script>
