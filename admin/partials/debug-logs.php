<?php
/**
 * Debug Logs Admin Page
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Initialize logger
$logger = SquareKit_Logger::get_instance();

// Handle actions
if ( isset( $_POST['action'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'squarekit_debug_logs' ) ) {
    switch ( $_POST['action'] ) {
        case 'clear_logs':
            if ( $logger->clear_logs() ) {
                echo '<div class="notice notice-success"><p>' . esc_html__( 'Debug logs cleared successfully.', 'squarekit' ) . '</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>' . esc_html__( 'Failed to clear debug logs.', 'squarekit' ) . '</p></div>';
            }
            break;
    }
}

// Get recent logs
$recent_logs = $logger->get_recent_logs( 200 );
$log_file_size = $logger->get_log_file_size();
$log_file_path = $logger->get_log_file_path();
?>

<div class="wrap">
    <h1><?php esc_html_e( 'Debug Logs', 'squarekit' ); ?></h1>

    <!-- Security Status -->
    <div class="squarekit-security-status">
        <h2><?php esc_html_e( 'Security Status', 'squarekit' ); ?></h2>
        <?php
        $storage_info = SquareKit_Secure_Storage::get_storage_info();
        $settings = new SquareKit_Settings();
        $current_env = $settings->get_environment();
        ?>

        <table class="widefat">
            <thead>
                <tr>
                    <th><?php esc_html_e( 'Security Check', 'squarekit' ); ?></th>
                    <th><?php esc_html_e( 'Status', 'squarekit' ); ?></th>
                    <th><?php esc_html_e( 'Details', 'squarekit' ); ?></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><?php esc_html_e( 'Encryption Key', 'squarekit' ); ?></td>
                    <td>
                        <?php if ( $storage_info['encryption_key_exists'] ) : ?>
                            <span style="color: green;">✓ <?php esc_html_e( 'Generated', 'squarekit' ); ?></span>
                        <?php else : ?>
                            <span style="color: red;">✗ <?php esc_html_e( 'Missing', 'squarekit' ); ?></span>
                        <?php endif; ?>
                    </td>
                    <td><?php esc_html_e( 'Site-specific encryption key for token storage', 'squarekit' ); ?></td>
                </tr>

                <?php foreach ( array( 'sandbox', 'production' ) as $env ) : ?>
                <tr>
                    <td><?php echo esc_html( ucfirst( $env ) . ' Credentials' ); ?></td>
                    <td>
                        <?php if ( $storage_info['environments'][ $env ]['has_secure_credentials'] ) : ?>
                            <span style="color: green;">✓ <?php esc_html_e( 'Secure', 'squarekit' ); ?></span>
                        <?php else : ?>
                            <span style="color: orange;">⚠ <?php esc_html_e( 'Database', 'squarekit' ); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ( $storage_info['environments'][ $env ]['has_secure_credentials'] ) : ?>
                            <?php esc_html_e( 'Using constants/environment variables', 'squarekit' ); ?>
                        <?php else : ?>
                            <?php esc_html_e( 'Stored in database (not recommended)', 'squarekit' ); ?>
                        <?php endif; ?>
                    </td>
                </tr>

                <tr>
                    <td><?php echo esc_html( ucfirst( $env ) . ' Tokens' ); ?></td>
                    <td>
                        <?php if ( $storage_info['environments'][ $env ]['has_access_token'] ) : ?>
                            <span style="color: green;">✓ <?php esc_html_e( 'Encrypted', 'squarekit' ); ?></span>
                        <?php else : ?>
                            <span style="color: gray;">- <?php esc_html_e( 'None', 'squarekit' ); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ( $storage_info['environments'][ $env ]['has_access_token'] ) : ?>
                            <?php
                            $stored_at = $storage_info['environments'][ $env ]['stored_at'];
                            echo esc_html( sprintf(
                                __( 'Stored securely on %s', 'squarekit' ),
                                $stored_at ? date( 'Y-m-d H:i:s', $stored_at ) : 'unknown date'
                            ) );
                            ?>
                        <?php else : ?>
                            <?php esc_html_e( 'No OAuth tokens stored', 'squarekit' ); ?>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="squarekit-security-recommendations" style="margin-top: 20px; padding: 15px; background: #f0f8ff; border-left: 4px solid #0073aa;">
            <h3><?php esc_html_e( 'Security Recommendations', 'squarekit' ); ?></h3>
            <ul>
                <li><?php esc_html_e( 'Store client credentials in wp-config.php using constants (see wp-config-example.php)', 'squarekit' ); ?></li>
                <li><?php esc_html_e( 'Never commit wp-config.php to version control', 'squarekit' ); ?></li>
                <li><?php esc_html_e( 'Use different credentials for sandbox vs production', 'squarekit' ); ?></li>
                <li><?php esc_html_e( 'Regularly rotate your client secrets in Square Developer Console', 'squarekit' ); ?></li>
                <li><?php esc_html_e( 'Always use HTTPS in production', 'squarekit' ); ?></li>
            </ul>
        </div>
    </div>

    <hr style="margin: 30px 0;">
    
    <div class="squarekit-debug-logs">
        <div class="squarekit-logs-header">
            <div class="squarekit-logs-info">
                <p>
                    <strong><?php esc_html_e( 'Log File:', 'squarekit' ); ?></strong> 
                    <code><?php echo esc_html( $log_file_path ); ?></code>
                </p>
                <p>
                    <strong><?php esc_html_e( 'File Size:', 'squarekit' ); ?></strong> 
                    <?php echo esc_html( $log_file_size ); ?>
                </p>
                <p>
                    <strong><?php esc_html_e( 'Showing:', 'squarekit' ); ?></strong> 
                    <?php echo esc_html( sprintf( __( 'Last %d log entries', 'squarekit' ), count( $recent_logs ) ) ); ?>
                </p>
            </div>
            
            <div class="squarekit-logs-actions">
                <form method="post" style="display: inline;">
                    <?php wp_nonce_field( 'squarekit_debug_logs' ); ?>
                    <input type="hidden" name="action" value="clear_logs">
                    <button type="submit" class="button button-secondary" 
                            onclick="return confirm('<?php esc_attr_e( 'Are you sure you want to clear all debug logs?', 'squarekit' ); ?>')">
                        <?php esc_html_e( 'Clear Logs', 'squarekit' ); ?>
                    </button>
                </form>
                
                <button type="button" class="button button-primary" onclick="location.reload()">
                    <?php esc_html_e( 'Refresh', 'squarekit' ); ?>
                </button>
            </div>
        </div>

        <div class="squarekit-logs-content">
            <?php if ( empty( $recent_logs ) ) : ?>
                <div class="notice notice-info">
                    <p><?php esc_html_e( 'No debug logs found. Try performing an OAuth connection to generate logs.', 'squarekit' ); ?></p>
                </div>
            <?php else : ?>
                <div class="squarekit-logs-container">
                    <pre class="squarekit-logs-display"><?php
                        foreach ( $recent_logs as $log_line ) {
                            // Color code different log levels
                            $line = esc_html( $log_line );
                            if ( strpos( $line, '[ERROR]' ) !== false ) {
                                echo '<span style="color: #d63638;">' . $line . '</span>' . "\n";
                            } elseif ( strpos( $line, '[WARNING]' ) !== false ) {
                                echo '<span style="color: #dba617;">' . $line . '</span>' . "\n";
                            } elseif ( strpos( $line, '[INFO]' ) !== false ) {
                                echo '<span style="color: #00a32a;">' . $line . '</span>' . "\n";
                            } elseif ( strpos( $line, '[DEBUG]' ) !== false ) {
                                echo '<span style="color: #646970;">' . $line . '</span>' . "\n";
                            } else {
                                echo $line . "\n";
                            }
                        }
                    ?></pre>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.squarekit-debug-logs {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-top: 20px;
}

.squarekit-logs-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    border-bottom: 1px solid #c3c4c7;
    background: #f6f7f7;
}

.squarekit-logs-info p {
    margin: 0 0 8px 0;
    font-size: 13px;
}

.squarekit-logs-info code {
    background: #f0f0f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.squarekit-logs-actions {
    display: flex;
    gap: 10px;
}

.squarekit-logs-content {
    padding: 20px;
}

.squarekit-logs-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

.squarekit-logs-display {
    margin: 0;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    background: transparent;
    border: none;
}

.squarekit-logs-display::-webkit-scrollbar {
    width: 8px;
}

.squarekit-logs-display::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.squarekit-logs-display::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.squarekit-logs-display::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
// Auto-scroll to bottom of logs
document.addEventListener('DOMContentLoaded', function() {
    const logsContainer = document.querySelector('.squarekit-logs-container');
    if (logsContainer) {
        logsContainer.scrollTop = logsContainer.scrollHeight;
    }
});
</script>
