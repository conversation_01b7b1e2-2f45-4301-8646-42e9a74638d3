<?php
// Robust Orders Admin UI for Square Kit
if ( ! defined( 'ABSPATH' ) ) exit;
?>
<div class="wrap squarekit-orders">
    <h1><?php esc_html_e('Square Kit Orders', 'squarekit'); ?></h1>
    <form id="squarekit-orders-filter" method="get" action="" style="margin-bottom:1em;display:flex;gap:1em;align-items:flex-end;">
        <input type="hidden" name="page" value="squarekit-orders" />
        <input type="search" name="s" id="squarekit-orders-search" placeholder="<?php esc_attr_e('Search orders...', 'squarekit'); ?>" />
        <select name="sync_status" id="squarekit-orders-sync-status">
            <option value=""><?php esc_html_e('All Sync Statuses', 'squarekit'); ?></option>
            <option value="synced"><?php esc_html_e('Synced', 'squarekit'); ?></option>
            <option value="not_synced"><?php esc_html_e('Not Synced', 'squarekit'); ?></option>
        </select>
        <select name="order_status" id="squarekit-orders-status">
            <option value=""><?php esc_html_e('All Statuses', 'squarekit'); ?></option>
            <?php foreach (wc_get_order_statuses() as $status_key => $status_label): ?>
                <option value="<?php echo esc_attr($status_key); ?>"><?php echo esc_html($status_label); ?></option>
            <?php endforeach; ?>
        </select>
        <label for="squarekit-orders-per-page" style="margin-bottom:0;">
            <?php esc_html_e('Per page:', 'squarekit'); ?>
            <select name="per_page" id="squarekit-orders-per-page">
                <option value="20">20</option>
                <option value="50" selected>50</option>
                <option value="100">100</option>
            </select>
        </label>
        <button class="button" id="squarekit-orders-filter-btn" type="submit"><?php esc_html_e('Filter', 'squarekit'); ?></button>
    </form>
    <div id="squarekit-orders-feedback" role="status" aria-live="polite" style="margin-bottom:1em;"></div>
    <div id="squarekit-orders-table-wrapper" aria-busy="true">
        <!-- Table will be loaded here via AJAX -->
    </div>
    <div id="squarekit-order-modal" style="display:none;" role="dialog" aria-modal="true" aria-labelledby="squarekit-order-modal-title">
        <div class="squarekit-modal-content">
            <button class="squarekit-modal-close" aria-label="Close">&times;</button>
            <div id="squarekit-order-modal-body">
                <!-- Order details will be loaded here -->
            </div>
        </div>
    </div>
</div>
<script>
jQuery(function($){
    function loadOrders(page) {
        var data = $('#squarekit-orders-filter').serializeArray();
        data.push({name:'action',value:'squarekit_orders_table_ajax'});
        data.push({name:'nonce',value:squarekit_admin.nonce});
        if(page) data.push({name:'paged',value:page});
        $('#squarekit-orders-table-wrapper').attr('aria-busy','true').html('<div class="spinner is-active"></div>');
        $.post(squarekit_admin.ajax_url, data, function(resp){
            if(resp.success) {
                $('#squarekit-orders-table-wrapper').html(resp.data.table);
            } else {
                $('#squarekit-orders-table-wrapper').html('<div class="notice notice-error">'+(resp.data && resp.data.message ? resp.data.message : 'Error loading orders')+'</div>');
            }
            $('#squarekit-orders-table-wrapper').attr('aria-busy','false');
        }).fail(function(){
            $('#squarekit-orders-table-wrapper').html('<div class="notice notice-error">AJAX error.</div>').attr('aria-busy','false');
        });
    }
    $('#squarekit-orders-filter').on('submit', function(e){ e.preventDefault(); loadOrders(1); });
    $(document).on('click', '.squarekit-orders-pagination a', function(e){
        e.preventDefault();
        var page = $(this).data('page');
        loadOrders(page);
    });
    $(document).on('click', '.sync-order, .export-order, .remove-meta-order', function(e){
        e.preventDefault();
        var btn = $(this), id = btn.data('id'), action = btn.hasClass('sync-order') ? 'sync' : btn.hasClass('export-order') ? 'export' : 'remove_meta';
        btn.prop('disabled', true).attr('aria-busy','true');
        $('#squarekit-orders-feedback').html('<span class="spinner is-active"></span>');
        $.post(squarekit_admin.ajax_url, {
            action: 'squarekit_order_action',
            nonce: squarekit_admin.nonce,
            order_id: id,
            order_action: action
        }, function(resp){
            if(resp.success) {
                $('#squarekit-orders-feedback').html('<div class="notice notice-success">'+resp.data.message+'</div>');
                loadOrders();
            } else {
                $('#squarekit-orders-feedback').html('<div class="notice notice-error">'+(resp.data && resp.data.message ? resp.data.message : 'Action failed')+'</div>');
                btn.prop('disabled', false).attr('aria-busy','false');
            }
        }).fail(function(){
            $('#squarekit-orders-feedback').html('<div class="notice notice-error">AJAX error.</div>');
            btn.prop('disabled', false).attr('aria-busy','false');
        });
    });
    $(document).on('click', '.view-order-details', function(e){
        e.preventDefault();
        var id = $(this).data('id');
        $('#squarekit-order-modal').show().attr('aria-busy','true');
        $('#squarekit-order-modal-body').html('<div class="spinner is-active"></div>');
        $.post(squarekit_admin.ajax_url, {
            action: 'squarekit_order_details',
            nonce: squarekit_admin.nonce,
            order_id: id
        }, function(resp){
            if(resp.success) {
                $('#squarekit-order-modal-body').html(resp.data.html);
            } else {
                $('#squarekit-order-modal-body').html('<div class="notice notice-error">'+(resp.data && resp.data.message ? resp.data.message : 'Error loading details')+'</div>');
            }
            $('#squarekit-order-modal').attr('aria-busy','false');
        }).fail(function(){
            $('#squarekit-order-modal-body').html('<div class="notice notice-error">AJAX error.</div>');
            $('#squarekit-order-modal').attr('aria-busy','false');
        });
    });
    $('.squarekit-modal-close').on('click', function(){
        $('#squarekit-order-modal').hide();
        $('#squarekit-order-modal-body').html('');
    });
    // Accessibility: close modal on ESC
    $(document).on('keydown', function(e){
        if(e.key === 'Escape') $('#squarekit-order-modal').hide();
    });
    // Initial load
    loadOrders(1);
});
</script>
<style>
.squarekit-modal-content { background:#fff; padding:2em; border-radius:8px; max-width:600px; margin:2em auto; position:relative; }
.squarekit-modal-close { position:absolute; top:1em; right:1em; background:none; border:none; font-size:2em; cursor:pointer; }
.spinner.is-active { display:inline-block; width:24px; height:24px; border:4px solid #ccc; border-top:4px solid #0073aa; border-radius:50%; animation:spin 1s linear infinite; vertical-align:middle; }
@keyframes spin { 100% { transform:rotate(360deg); } }
</style> 