<?php
/**
 * Admin page for testing image import functionality
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

// Handle test execution
$test_results = array();
$test_executed = false;

if (isset($_POST['run_tests']) && wp_verify_nonce($_POST['_wpnonce'], 'squarekit_test_images')) {
    $test_executed = true;
    
    // Load required classes
    if (!class_exists('SquareKit_WooCommerce')) {
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
    }
    
    $wc_integration = new SquareKit_WooCommerce();
    
    // Test 1: Debug a sample image URL
    $test_url = 'https://picsum.photos/300/200';
    $debug_info = $wc_integration->debug_image_import($test_url);
    $test_results['debug'] = $debug_info;
    
    // Test 2: Get import statistics
    $stats = $wc_integration->get_image_import_stats(7);
    $test_results['stats'] = $stats;
    
    // Test 3: Test single image import
    if (isset($_POST['test_import']) && $_POST['test_import'] === '1') {
        $attachment_id = $wc_integration->import_image_from_url($test_url);
        $test_results['import'] = array(
            'success' => $attachment_id !== false,
            'attachment_id' => $attachment_id,
            'url' => $attachment_id ? wp_get_attachment_url($attachment_id) : null
        );
        
        // Clean up test image
        if ($attachment_id) {
            wp_delete_attachment($attachment_id, true);
        }
    }
}
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="notice notice-info">
        <p><strong><?php _e('Image Import Testing', 'squarekit'); ?></strong></p>
        <p><?php _e('Use this page to test and debug the Square Kit image import functionality. This will help identify any issues with image imports from Square.', 'squarekit'); ?></p>
    </div>

    <div class="squarekit-test-section">
        <h2><?php _e('Run Image Import Tests', 'squarekit'); ?></h2>
        
        <form method="post" action="">
            <?php wp_nonce_field('squarekit_test_images'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Test Options', 'squarekit'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="test_import" value="1" />
                                <?php _e('Test actual image import (will import and delete a test image)', 'squarekit'); ?>
                            </label>
                        </fieldset>
                        <p class="description">
                            <?php _e('Check this to test actual image importing. The test image will be automatically deleted after the test.', 'squarekit'); ?>
                        </p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(__('Run Tests', 'squarekit'), 'primary', 'run_tests'); ?>
        </form>
    </div>

    <?php if ($test_executed): ?>
    <div class="squarekit-test-results">
        <h2><?php _e('Test Results', 'squarekit'); ?></h2>
        
        <!-- Debug Information -->
        <div class="postbox">
            <h3 class="hndle"><?php _e('Image URL Debug Information', 'squarekit'); ?></h3>
            <div class="inside">
                <?php if (isset($test_results['debug'])): ?>
                    <p><strong><?php _e('Test URL:', 'squarekit'); ?></strong> <?php echo esc_html($test_results['debug']['url']); ?></p>
                    
                    <table class="widefat">
                        <thead>
                            <tr>
                                <th><?php _e('Check', 'squarekit'); ?></th>
                                <th><?php _e('Result', 'squarekit'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?php _e('URL Valid', 'squarekit'); ?></td>
                                <td>
                                    <?php if ($test_results['debug']['checks']['url_valid']): ?>
                                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Pass', 'squarekit'); ?>
                                    <?php else: ?>
                                        <span class="dashicons dashicons-dismiss" style="color: red;"></span> <?php _e('Fail', 'squarekit'); ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><?php _e('HTTP Accessible', 'squarekit'); ?></td>
                                <td>
                                    <?php if ($test_results['debug']['checks']['http_accessible']): ?>
                                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Pass', 'squarekit'); ?>
                                        <?php if (isset($test_results['debug']['http_status'])): ?>
                                            (<?php echo esc_html($test_results['debug']['http_status']); ?>)
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="dashicons dashicons-dismiss" style="color: red;"></span> <?php _e('Fail', 'squarekit'); ?>
                                        <?php if (isset($test_results['debug']['http_error'])): ?>
                                            <br><small><?php echo esc_html($test_results['debug']['http_error']); ?></small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php if (isset($test_results['debug']['checks']['is_image'])): ?>
                            <tr>
                                <td><?php _e('Is Image', 'squarekit'); ?></td>
                                <td>
                                    <?php if ($test_results['debug']['checks']['is_image']): ?>
                                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Pass', 'squarekit'); ?>
                                    <?php else: ?>
                                        <span class="dashicons dashicons-dismiss" style="color: red;"></span> <?php _e('Fail', 'squarekit'); ?>
                                    <?php endif; ?>
                                    <?php if (isset($test_results['debug']['content_type'])): ?>
                                        <br><small><?php echo esc_html($test_results['debug']['content_type']); ?></small>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                            <?php if (isset($test_results['debug']['checks']['size_acceptable'])): ?>
                            <tr>
                                <td><?php _e('Size Acceptable', 'squarekit'); ?></td>
                                <td>
                                    <?php if ($test_results['debug']['checks']['size_acceptable']): ?>
                                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span> <?php _e('Pass', 'squarekit'); ?>
                                    <?php else: ?>
                                        <span class="dashicons dashicons-dismiss" style="color: red;"></span> <?php _e('Fail', 'squarekit'); ?>
                                    <?php endif; ?>
                                    <?php if (isset($test_results['debug']['file_size_mb'])): ?>
                                        (<?php echo esc_html($test_results['debug']['file_size_mb']); ?> MB)
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <!-- Import Statistics -->
        <div class="postbox">
            <h3 class="hndle"><?php _e('Import Statistics (Last 7 Days)', 'squarekit'); ?></h3>
            <div class="inside">
                <?php if (isset($test_results['stats']) && !isset($test_results['stats']['error'])): ?>
                    <table class="widefat">
                        <tbody>
                            <tr>
                                <td><strong><?php _e('Total Attempts', 'squarekit'); ?></strong></td>
                                <td><?php echo esc_html($test_results['stats']['total_attempts']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php _e('Successful Imports', 'squarekit'); ?></strong></td>
                                <td><?php echo esc_html($test_results['stats']['successful_imports']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php _e('Failed Imports', 'squarekit'); ?></strong></td>
                                <td><?php echo esc_html($test_results['stats']['failed_imports']); ?></td>
                            </tr>
                            <tr>
                                <td><strong><?php _e('Success Rate', 'squarekit'); ?></strong></td>
                                <td><?php echo esc_html($test_results['stats']['success_rate_percent']); ?>%</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <?php if (!empty($test_results['stats']['recent_errors'])): ?>
                        <h4><?php _e('Recent Errors', 'squarekit'); ?></h4>
                        <ul>
                            <?php foreach (array_slice($test_results['stats']['recent_errors'], 0, 5) as $error): ?>
                                <li>
                                    <strong><?php echo esc_html(isset($error['timestamp']) ? $error['timestamp'] : 'Unknown time'); ?>:</strong>
                                    <?php echo esc_html(isset($error['error']) ? $error['error'] : 'Unknown error'); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                <?php else: ?>
                    <p><?php _e('Statistics not available:', 'squarekit'); ?> 
                        <?php echo isset($test_results['stats']['error']) ? esc_html($test_results['stats']['error']) : __('Unknown error', 'squarekit'); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Import Test Results -->
        <?php if (isset($test_results['import'])): ?>
        <div class="postbox">
            <h3 class="hndle"><?php _e('Image Import Test', 'squarekit'); ?></h3>
            <div class="inside">
                <?php if ($test_results['import']['success']): ?>
                    <p>
                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
                        <strong><?php _e('Import Successful!', 'squarekit'); ?></strong>
                    </p>
                    <p>
                        <strong><?php _e('Attachment ID:', 'squarekit'); ?></strong> <?php echo esc_html($test_results['import']['attachment_id']); ?><br>
                        <strong><?php _e('Image URL:', 'squarekit'); ?></strong> 
                        <?php if ($test_results['import']['url']): ?>
                            <a href="<?php echo esc_url($test_results['import']['url']); ?>" target="_blank">
                                <?php echo esc_html($test_results['import']['url']); ?>
                            </a>
                        <?php else: ?>
                            <?php _e('Not available', 'squarekit'); ?>
                        <?php endif; ?>
                    </p>
                    <p><em><?php _e('Note: Test image was automatically deleted after the test.', 'squarekit'); ?></em></p>
                <?php else: ?>
                    <p>
                        <span class="dashicons dashicons-dismiss" style="color: red;"></span>
                        <strong><?php _e('Import Failed!', 'squarekit'); ?></strong>
                    </p>
                    <p><?php _e('Check the error logs for more details.', 'squarekit'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <div class="squarekit-test-info">
        <h2><?php _e('Additional Information', 'squarekit'); ?></h2>
        
        <div class="postbox">
            <h3 class="hndle"><?php _e('Troubleshooting Tips', 'squarekit'); ?></h3>
            <div class="inside">
                <ul>
                    <li><?php _e('If image imports are failing, check your server\'s ability to make outbound HTTP requests.', 'squarekit'); ?></li>
                    <li><?php _e('Ensure your WordPress uploads directory is writable.', 'squarekit'); ?></li>
                    <li><?php _e('Check that your server has sufficient memory and execution time for image processing.', 'squarekit'); ?></li>
                    <li><?php _e('Review the WordPress error logs for detailed error messages.', 'squarekit'); ?></li>
                    <li><?php _e('Verify that Square image URLs are accessible from your server.', 'squarekit'); ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.squarekit-test-section,
.squarekit-test-results,
.squarekit-test-info {
    margin-top: 20px;
}

.squarekit-test-results .postbox {
    margin-bottom: 20px;
}

.squarekit-test-results .widefat th,
.squarekit-test-results .widefat td {
    padding: 8px 10px;
}

.squarekit-test-results .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}
</style>
