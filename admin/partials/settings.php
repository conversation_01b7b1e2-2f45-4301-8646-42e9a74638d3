<?php
$settings = new SquareKit_Settings();
$environment = $settings->get_environment();
$interval = $settings->get('cron_schedule', 'hourly');
$webhook_status = $settings->get('webhook_status', false);
$webhook_last_event = get_option('squarekit_webhook_last_event', '');
$sync_products = $settings->get('sync_products', 'both');
$optimize_images = $settings->get('optimize_images', true);
$max_image_width = $settings->get('max_image_width', 1200);
$max_image_height = $settings->get('max_image_height', 1200);
$image_quality = $settings->get('image_quality', 85);
$attribute_mapping = $settings->get_attribute_mapping();
$status_mapping = $settings->get_status_mapping();

// Get current tab
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'connection';

// Get conflict count for badge
$conflict_count = 0;
if ( class_exists( 'SquareKit_Conflict_Manager' ) ) {
    $conflict_manager = new SquareKit_Conflict_Manager();
    $conflict_count = $conflict_manager->get_conflict_count('pending');
}

// Fetch WooCommerce product attributes (taxonomies starting with 'pa_')
$wc_attribute_taxonomies = wc_get_attribute_taxonomies();
$wc_attributes = array();
foreach ($wc_attribute_taxonomies as $tax) {
    $wc_attributes['pa_' . $tax->attribute_name] = $tax->attribute_label;
}

// Fetch WooCommerce order statuses
$wc_statuses = wc_get_order_statuses();
// Common Square statuses (can be extended by admin)
$square_statuses = array(
    'OPEN' => 'Open',
    'COMPLETED' => 'Completed',
    'CANCELED' => 'Canceled',
    'DRAFT' => 'Draft',
    'ON_HOLD' => 'On Hold',
    'REFUNDED' => 'Refunded',
    'FAILED' => 'Failed',
);

// Fetch payment methods from settings
$payment_methods = $settings->get_payment_methods();

// Handle payment settings form submission
if (isset($_POST['save_payment_settings'])) {
    check_admin_referer('squarekit_save_payment_settings', 'squarekit_payment_settings_nonce');

    // Save payment gateway settings
    $enable_square_gateway = isset($_POST['enable_square_gateway']) ? true : false;
    $settings->set('enable_square_gateway', $enable_square_gateway);

    $gateway_title = sanitize_text_field($_POST['square_gateway_title'] ?? 'Square');
    $settings->set('square_gateway_title', $gateway_title);

    $gateway_description = sanitize_textarea_field($_POST['square_gateway_description'] ?? 'Pay securely with Square');
    $settings->set('square_gateway_description', $gateway_description);

    // Save payment features
    $enable_saved_cards = isset($_POST['enable_saved_cards']) ? true : false;
    $settings->set('enable_saved_cards', $enable_saved_cards);

    $enable_digital_wallet = isset($_POST['enable_digital_wallet']) ? true : false;
    $settings->set('enable_digital_wallet', $enable_digital_wallet);

    $enable_payment_logging = isset($_POST['enable_payment_logging']) ? true : false;
    $settings->set('enable_payment_logging', $enable_payment_logging);

    // Save transaction settings
    $payment_capture_method = sanitize_text_field($_POST['payment_capture_method'] ?? 'automatic');
    $settings->set('payment_capture_method', $payment_capture_method);

    $payment_sandbox_mode = isset($_POST['payment_sandbox_mode']) ? true : false;
    $settings->set('payment_sandbox_mode', $payment_sandbox_mode);

    // Save payment gateway mapping if provided
    if (!empty($_POST['payment_gateway_mapping'])) {
        $payment_gateway_mapping = array();
        foreach ($_POST['payment_gateway_mapping'] as $method_id => $gateway) {
            $method_id = sanitize_text_field($method_id);
            $gateway = sanitize_text_field($gateway);
            if ($method_id && $gateway) {
                $payment_gateway_mapping[$method_id] = $gateway;
            }
        }
        $settings->set('payment_gateway_mapping', $payment_gateway_mapping);
    }

    // Update WooCommerce gateway settings if gateway is enabled
    if ($enable_square_gateway) {
        $gateway_settings = get_option('woocommerce_squarekit_settings', array());
        $gateway_settings['enabled'] = 'yes';
        $gateway_settings['title'] = $gateway_title;
        $gateway_settings['description'] = $gateway_description;
        $gateway_settings['capture_method'] = $payment_capture_method;
        $gateway_settings['enable_saved_cards'] = $enable_saved_cards ? 'yes' : 'no';
        $gateway_settings['enable_digital_wallets'] = $enable_digital_wallet ? 'yes' : 'no';
        $gateway_settings['enable_logging'] = $enable_payment_logging ? 'yes' : 'no';
        update_option('woocommerce_squarekit_settings', $gateway_settings);
    } else {
        // Disable the gateway in WooCommerce
        $gateway_settings = get_option('woocommerce_squarekit_settings', array());
        $gateway_settings['enabled'] = 'no';
        update_option('woocommerce_squarekit_settings', $gateway_settings);
    }

    echo '<div class="notice notice-success"><p>' . esc_html__('Payment settings saved successfully.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_attribute_mapping'])) {
    check_admin_referer('squarekit_save_attribute_mapping', 'squarekit_attribute_mapping_nonce');
    $new_mapping = array();
    if (!empty($_POST['square_attribute']) && !empty($_POST['wc_attribute'])) {
        foreach ($_POST['square_attribute'] as $i => $sq_attr) {
            $sq_attr = sanitize_text_field($sq_attr);
            $wc_attr = sanitize_text_field($_POST['wc_attribute'][$i]);
            if ($sq_attr && $wc_attr) {
                $new_mapping[$sq_attr] = $wc_attr;
            }
        }
    }
    $settings->set_attribute_mapping($new_mapping);
    $attribute_mapping = $new_mapping;
    echo '<div class="notice notice-success"><p>' . esc_html__('Attribute mapping saved.', 'squarekit') . '</p></div>';
}

if ( isset($_POST['squarekit_settings_nonce']) && wp_verify_nonce($_POST['squarekit_settings_nonce'], 'squarekit_settings_save') ) {
    // Environment setting - only update if explicitly submitted
    if ( isset($_POST['environment']) ) {
        $environment = sanitize_text_field($_POST['environment']);
        $current_environment = $settings->get_environment();

        // Log environment changes for security
        if ( $environment !== $current_environment ) {
            if ( class_exists('SquareKit_Logger') ) {
                $logger = new SquareKit_Logger();
                $logger->log('settings', 'info', "Environment changed from {$current_environment} to {$environment} by user " . wp_get_current_user()->user_login);
            }
        }

        $settings->set('environment', $environment);
    }
    
    $interval = isset($_POST['cron_schedule']) ? sanitize_text_field($_POST['cron_schedule']) : 'hourly';
    $settings->set('cron_schedule', $interval);
    // Reschedule cron event
    if ( ! class_exists('SquareKit_Loader') ) {
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
    }
    $loader = new SquareKit_Loader();
    $loader->deactivate_cron();
    wp_clear_scheduled_hook('squarekit_scheduled_sync');
    wp_schedule_event( time(), $interval, 'squarekit_scheduled_sync' );

    $webhook_status = isset($_POST['webhook_status']) ? (bool)$_POST['webhook_status'] : false;
    $settings->set('webhook_status', $webhook_status);
    if ( $webhook_status ) {
        if ( ! class_exists('SquareKit_Loader') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
        }
        $loader = new SquareKit_Loader();
        $loader->register_square_webhooks();
    } else {
        if ( ! class_exists('SquareKit_Loader') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
        }
        $loader = new SquareKit_Loader();
        $loader->unregister_square_webhooks();
    }
    
    // Granular sync settings
    $granular_sync_settings = array(
        'woo_to_square' => array(
            'enabled' => isset($_POST['sync_woo_to_square_enabled']) ? (bool)$_POST['sync_woo_to_square_enabled'] : false,
            'data_types' => array(
                'products' => isset($_POST['sync_woo_to_square_products']) ? (bool)$_POST['sync_woo_to_square_products'] : false,
                'inventory' => isset($_POST['sync_woo_to_square_inventory']) ? (bool)$_POST['sync_woo_to_square_inventory'] : false,
                'categories' => isset($_POST['sync_woo_to_square_categories']) ? (bool)$_POST['sync_woo_to_square_categories'] : false,
                'orders' => isset($_POST['sync_woo_to_square_orders']) ? (bool)$_POST['sync_woo_to_square_orders'] : false,
                'customers' => isset($_POST['sync_woo_to_square_customers']) ? (bool)$_POST['sync_woo_to_square_customers'] : false,
            )
        ),
        'square_to_woo' => array(
            'enabled' => isset($_POST['sync_square_to_woo_enabled']) ? (bool)$_POST['sync_square_to_woo_enabled'] : false,
            'data_types' => array(
                'products' => isset($_POST['sync_square_to_woo_products']) ? (bool)$_POST['sync_square_to_woo_products'] : false,
                'inventory' => isset($_POST['sync_square_to_woo_inventory']) ? (bool)$_POST['sync_square_to_woo_inventory'] : false,
                'categories' => isset($_POST['sync_square_to_woo_categories']) ? (bool)$_POST['sync_square_to_woo_categories'] : false,
                'orders' => isset($_POST['sync_square_to_woo_orders']) ? (bool)$_POST['sync_square_to_woo_orders'] : false,
                'customers' => isset($_POST['sync_square_to_woo_customers']) ? (bool)$_POST['sync_square_to_woo_customers'] : false,
            )
        )
    );
    $settings->set_granular_sync_settings($granular_sync_settings);

    // Auto-import new products setting
    if ( isset($_POST['auto_import_new_products_from_square']) ) {
        $settings->set('auto_import_new_products_from_square', true);
    } else {
        $settings->set('auto_import_new_products_from_square', false);
    }

    // Update legacy sync direction settings for backward compatibility
    $sync_direction_settings = array(
        'woo_to_square' => $granular_sync_settings['woo_to_square']['enabled'],
        'square_to_woo' => $granular_sync_settings['square_to_woo']['enabled'],
    );
    $settings->set_sync_direction_settings($sync_direction_settings);

    // Performance and automation settings
    if ( isset($_POST['sync_batch_size']) ) {
        $sync_batch_size = intval($_POST['sync_batch_size']);
        if ( $sync_batch_size > 0 && $sync_batch_size <= 500 ) {
            $settings->set('sync_batch_size', $sync_batch_size);
        }
    }

    if ( isset($_POST['sync_timeout']) ) {
        $sync_timeout = intval($_POST['sync_timeout']);
        if ( $sync_timeout >= 30 && $sync_timeout <= 600 ) {
            $settings->set('sync_timeout', $sync_timeout);
        }
    }

    // Conflict resolution settings
    if ( isset($_POST['inventory_conflict_resolution']) ) {
        $inventory_conflict_resolution = sanitize_text_field($_POST['inventory_conflict_resolution']);
        if ( in_array($inventory_conflict_resolution, ['square_wins', 'wc_wins', 'manual']) ) {
            $settings->set('inventory_conflict_resolution', $inventory_conflict_resolution);
        }
    }

    if ( isset($_POST['product_conflict_resolution']) ) {
        $product_conflict_resolution = sanitize_text_field($_POST['product_conflict_resolution']);
        if ( in_array($product_conflict_resolution, ['square_wins', 'wc_wins', 'manual']) ) {
            $settings->set('product_conflict_resolution', $product_conflict_resolution);
        }
    }

    $sync_products = isset($_POST['sync_products']) ? sanitize_text_field($_POST['sync_products']) : 'both';
    $settings->set('sync_products', $sync_products);
    
    // Image handling settings
    $optimize_images = isset($_POST['optimize_images']) ? (bool)$_POST['optimize_images'] : false;
    $settings->set('optimize_images', $optimize_images);
    
    $max_image_width = isset($_POST['max_image_width']) ? intval($_POST['max_image_width']) : 1200;
    $settings->set('max_image_width', $max_image_width);
    
    $max_image_height = isset($_POST['max_image_height']) ? intval($_POST['max_image_height']) : 1200;
    $settings->set('max_image_height', $max_image_height);
    
    $image_quality = isset($_POST['image_quality']) ? intval($_POST['image_quality']) : 85;
    $settings->set('image_quality', $image_quality);

    // Payment methods settings
    $payment_methods = array(
        'google_pay' => isset($_POST['google_pay']),
        'apple_pay' => isset($_POST['apple_pay']),
        'afterpay' => isset($_POST['afterpay'])
    );
    $settings->set('payment_methods', $payment_methods);
    
    // Advanced cron settings
    $selective_sync_enabled = isset($_POST['enable_selective_sync']) ? (bool)$_POST['enable_selective_sync'] : false;
    $settings->set('enable_selective_sync', $selective_sync_enabled);
    
    $enable_cron_retry = isset($_POST['enable_cron_retry']) ? (bool)$_POST['enable_cron_retry'] : false;
    $settings->set('enable_cron_retry', $enable_cron_retry);
    
    $max_retry_attempts = isset($_POST['max_cron_retry_attempts']) ? intval($_POST['max_cron_retry_attempts']) : 3;
    $settings->set('max_cron_retry_attempts', $max_retry_attempts);
    
    $cron_retry_delay = isset($_POST['cron_retry_delay']) ? intval($_POST['cron_retry_delay']) : 300;
    $settings->set('cron_retry_delay', $cron_retry_delay);
    
    $enable_parallel_processing = isset($_POST['enable_parallel_processing']) ? (bool)$_POST['enable_parallel_processing'] : false;
    $settings->set('enable_parallel_processing', $enable_parallel_processing);
    
    $max_parallel_jobs = isset($_POST['max_parallel_jobs']) ? intval($_POST['max_parallel_jobs']) : 2;
    $settings->set('max_parallel_jobs', $max_parallel_jobs);
    
    $max_execution_time = isset($_POST['max_sync_execution_time']) ? intval($_POST['max_sync_execution_time']) : 300;
    $settings->set('max_sync_execution_time', $max_execution_time);
    
    $batch_size = isset($_POST['sync_batch_size']) ? intval($_POST['sync_batch_size']) : 50;
    $settings->set('sync_batch_size', $batch_size);
    
    // Real-time inventory settings
    $enable_real_time_inventory = isset($_POST['enable_real_time_inventory']) ? (bool)$_POST['enable_real_time_inventory'] : false;
    $settings->set('enable_real_time_inventory', $enable_real_time_inventory);
    
    $inventory_conflict_resolution = isset($_POST['inventory_conflict_resolution']) ? sanitize_text_field($_POST['inventory_conflict_resolution']) : 'manual';
    $settings->set('inventory_conflict_resolution', $inventory_conflict_resolution);
    
    $inventory_conflict_threshold = isset($_POST['inventory_conflict_threshold']) ? intval($_POST['inventory_conflict_threshold']) : 1;
    $settings->set('inventory_conflict_threshold', $inventory_conflict_threshold);
    
    $enable_inventory_notifications = isset($_POST['enable_inventory_notifications']) ? (bool)$_POST['enable_inventory_notifications'] : false;
    $settings->set('enable_inventory_notifications', $enable_inventory_notifications);
    
    $inventory_sync_frequency = isset($_POST['inventory_sync_frequency']) ? sanitize_text_field($_POST['inventory_sync_frequency']) : 'realtime';
    $settings->set('inventory_sync_frequency', $inventory_sync_frequency);
    
    $enable_auto_resolve_conflicts = isset($_POST['enable_auto_resolve_conflicts']) ? (bool)$_POST['enable_auto_resolve_conflicts'] : false;
    $settings->set('enable_auto_resolve_conflicts', $enable_auto_resolve_conflicts);
    
    // Customer role mapping settings
    $enable_customer_role_mapping = isset($_POST['enable_customer_role_mapping']) ? (bool)$_POST['enable_customer_role_mapping'] : false;
    $settings->set('enable_customer_role_mapping', $enable_customer_role_mapping);
    
    // Fulfillment mapping settings
    $enable_fulfillment_mapping = isset($_POST['enable_fulfillment_mapping']) ? (bool)$_POST['enable_fulfillment_mapping'] : false;
    $settings->set('enable_fulfillment_mapping', $enable_fulfillment_mapping);
    
    $enable_pickup_location_mapping = isset($_POST['enable_pickup_location_mapping']) ? (bool)$_POST['enable_pickup_location_mapping'] : false;
    $settings->set('enable_pickup_location_mapping', $enable_pickup_location_mapping);
    
    // SKU mapping settings
    $enable_sku_mapping = isset($_POST['enable_sku_mapping']) ? (bool)$_POST['enable_sku_mapping'] : false;
    $settings->set('enable_sku_mapping', $enable_sku_mapping);
    
    $sku_conflict_resolution = isset($_POST['sku_conflict_resolution']) ? sanitize_text_field($_POST['sku_conflict_resolution']) : 'skip';
    $settings->set_sku_conflict_resolution($sku_conflict_resolution);
    
    // SKU validation settings
    $sku_validation_settings = array(
        'enforce_unique' => isset($_POST['sku_enforce_unique']) ? (bool)$_POST['sku_enforce_unique'] : false,
        'allow_empty' => isset($_POST['sku_allow_empty']) ? (bool)$_POST['sku_allow_empty'] : false,
        'max_length' => isset($_POST['sku_max_length']) ? intval($_POST['sku_max_length']) : 64,
        'allowed_characters' => isset($_POST['sku_allowed_characters']) ? sanitize_text_field($_POST['sku_allowed_characters']) : 'alphanumeric_dash_underscore',
    );
    $settings->set_sku_validation_settings($sku_validation_settings);
    
    // Update cron schedule with new settings
    $loader->update_cron_schedule($interval);
    
    echo '<div class="notice notice-success"><p>' . esc_html__('Settings saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_status_mapping'])) {
    check_admin_referer('squarekit_save_status_mapping', 'squarekit_status_mapping_nonce');
    $new_mapping = array();
    if (!empty($_POST['wc_status']) && !empty($_POST['square_status'])) {
        foreach ($_POST['wc_status'] as $i => $wc_status) {
            $wc_status = sanitize_text_field($wc_status);
            $sq_status = sanitize_text_field($_POST['square_status'][$i]);
            if ($wc_status && $sq_status) {
                $new_mapping[$wc_status] = $sq_status;
            }
        }
    }
    $settings->set_status_mapping($new_mapping);
    $status_mapping = $new_mapping;
    echo '<div class="notice notice-success"><p>' . esc_html__('Order status mapping saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_customer_role_mapping'])) {
    check_admin_referer('squarekit_save_customer_role_mapping', 'squarekit_customer_role_mapping_nonce');
    $new_mapping = array();
    if (!empty($_POST['mapping_criteria']) && !empty($_POST['mapping_condition']) && !empty($_POST['mapping_value']) && !empty($_POST['mapping_role'])) {
        foreach ($_POST['mapping_criteria'] as $i => $criteria) {
            $criteria = sanitize_text_field($criteria);
            $condition = sanitize_text_field($_POST['mapping_condition'][$i]);
            $value = sanitize_text_field($_POST['mapping_value'][$i]);
            $role = sanitize_text_field($_POST['mapping_role'][$i]);
            $priority = isset($_POST['mapping_priority'][$i]) ? intval($_POST['mapping_priority'][$i]) : 10;
            
            if ($criteria && $condition && $value && $role) {
                $new_mapping[] = array(
                    'criteria' => $criteria,
                    'condition' => $condition,
                    'value' => $value,
                    'role' => $role,
                    'priority' => $priority
                );
            }
        }
    }
    $settings->set_customer_role_mapping($new_mapping);
    $customer_role_mapping = $new_mapping;
    echo '<div class="notice notice-success"><p>' . esc_html__('Customer role mapping saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_fulfillment_mapping'])) {
    check_admin_referer('squarekit_save_fulfillment_mapping', 'squarekit_fulfillment_mapping_nonce');
    
    // Process fulfillment mapping
    $new_fulfillment_mapping = array();
    if (!empty($_POST['fulfillment_type']) && !empty($_POST['shipping_method'])) {
        foreach ($_POST['fulfillment_type'] as $i => $fulfillment_type) {
            $fulfillment_type = sanitize_text_field($fulfillment_type);
            $shipping_method = sanitize_text_field($_POST['shipping_method'][$i]);
            if ($fulfillment_type && $shipping_method) {
                $new_fulfillment_mapping[] = array(
                    'fulfillment_type' => $fulfillment_type,
                    'shipping_method' => $shipping_method
                );
            }
        }
    }
    $settings->set_fulfillment_mapping($new_fulfillment_mapping);
    $fulfillment_mapping = $new_fulfillment_mapping;
    
    // Process pickup location mapping
    $new_pickup_location_mapping = array();
    if (!empty($_POST['square_location']) && !empty($_POST['wc_pickup_location'])) {
        foreach ($_POST['square_location'] as $i => $square_location) {
            $square_location = sanitize_text_field($square_location);
            $wc_pickup_location = sanitize_text_field($_POST['wc_pickup_location'][$i]);
            if ($square_location && $wc_pickup_location) {
                $new_pickup_location_mapping[] = array(
                    'square_location' => $square_location,
                    'wc_pickup_location' => $wc_pickup_location
                );
            }
        }
    }
    $settings->set_pickup_location_mapping($new_pickup_location_mapping);
    $pickup_location_mapping = $new_pickup_location_mapping;
    
    echo '<div class="notice notice-success"><p>' . esc_html__('Fulfillment and pickup mapping saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_sku_mapping'])) {
    check_admin_referer('squarekit_save_sku_mapping', 'squarekit_sku_mapping_nonce');
    
    // Process SKU mapping rules
    $new_sku_mapping_rules = array();
    if (!empty($_POST['sku_item_type']) && !empty($_POST['sku_pattern']) && !empty($_POST['sku_priority'])) {
        foreach ($_POST['sku_item_type'] as $i => $item_type) {
            $item_type = sanitize_text_field($item_type);
            $pattern = sanitize_text_field($_POST['sku_pattern'][$i]);
            $priority = intval($_POST['sku_priority'][$i]);
            if ($item_type && $pattern && $priority > 0) {
                $new_sku_mapping_rules[] = array(
                    'item_type' => $item_type,
                    'pattern' => $pattern,
                    'priority' => $priority
                );
            }
        }
    }
    $settings->set_sku_mapping_rules($new_sku_mapping_rules);
    $sku_mapping_rules = $new_sku_mapping_rules;
    
    echo '<div class="notice notice-success"><p>' . esc_html__('SKU mapping rules saved.', 'squarekit') . '</p></div>';
}
?>

<div class="wrap squarekit-admin-wrap">
    <!-- Modern Header -->
    <div class="squarekit-admin-header">
        <h1><?php esc_html_e('Square Kit Settings', 'squarekit'); ?></h1>
        <p><?php esc_html_e('Configure your Square integration, sync preferences, and advanced options.', 'squarekit'); ?></p>
    </div>

    <!-- Tab Navigation -->
    <div class="squarekit-tabs-nav">
        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=connection')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'connection' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 3L4 14H11L10 21L19 10H12L13 3Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Connection', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=sync')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'sync' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4V1L8 5L12 9V6C15.31 6 18 8.69 18 12C18 13.01 17.75 13.97 17.3 14.8L18.76 16.26C19.54 15.03 20 13.57 20 12C20 7.58 16.42 4 12 4Z" fill="currentColor"/>
                    <path d="M12 18C8.69 18 6 15.31 6 12C6 10.99 6.25 10.03 6.7 9.2L5.24 7.74C4.46 8.97 4 10.43 4 12C4 16.42 7.58 20 12 20V23L16 19L12 15V18Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Sync Settings', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=conflicts')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'conflicts' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5.12 13.5 5.34L12 6.84C11.18 5.12 9.68 4 8 4C5.24 4 3 6.24 3 9S5.24 14 8 14C9.68 14 11.18 12.88 12 11.16L13.5 12.66C14.32 12.88 15.24 12.94 16.17 12.83L13.5 15.5L15 17L21 11V9ZM8 12C6.34 12 5 10.66 5 9S6.34 6 8 6S11 7.34 11 9S9.66 12 8 12Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Conflicts', 'squarekit'); ?></span>
            <?php if ( $conflict_count > 0 ) : ?>
                <span class="conflict-count-badge"><?php echo esc_html( $conflict_count ); ?></span>
            <?php endif; ?>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=mapping')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'mapping' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 11H7L12 6L17 11H15V16H9V11Z" fill="currentColor"/>
                    <path d="M5 20V18H19V20H5Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Mapping', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=advanced')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'advanced' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19.14 12.94C19.18 12.64 19.2 12.33 19.2 12S19.18 11.36 19.14 11.06L21.16 9.48C21.34 9.34 21.39 9.07 21.28 8.87L19.36 5.55C19.24 5.33 18.99 5.26 18.77 5.33L16.38 6.29C15.88 5.91 15.35 5.59 14.76 5.35L14.4 2.81C14.36 2.57 14.16 2.4 13.92 2.4H10.08C9.84 2.4 9.64 2.57 9.6 2.81L9.24 5.35C8.65 5.59 8.12 5.92 7.62 6.29L5.23 5.33C5.01 5.25 4.76 5.33 4.64 5.55L2.72 8.87C2.61 9.08 2.66 9.34 2.84 9.48L4.86 11.06C4.82 11.36 4.8 11.69 4.8 12S4.82 12.64 4.86 12.94L2.84 14.52C2.66 14.66 2.61 14.93 2.72 15.13L4.64 18.45C4.76 18.67 5.01 18.74 5.23 18.67L7.62 17.71C8.12 18.09 8.65 18.41 9.24 18.65L9.6 21.19C9.64 21.43 9.84 21.6 10.08 21.6H13.92C14.16 21.6 14.36 21.43 14.4 21.19L14.76 18.65C15.35 18.41 15.88 18.09 16.38 17.71L18.77 18.67C18.99 18.75 19.24 18.67 19.36 18.45L21.28 15.13C21.39 14.93 21.34 14.66 21.16 14.52L19.14 12.94ZM12 15.6C10.02 15.6 8.4 13.98 8.4 12S10.02 8.4 12 8.4S15.6 10.02 15.6 12S13.98 15.6 12 15.6Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Advanced', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=payment')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'payment' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 4H4C2.89 4 2.01 4.89 2.01 6L2 18C2 19.11 2.89 20 4 20H20C21.11 20 22 19.11 22 18V6C22 4.89 21.11 4 20 4ZM20 18H4V12H20V18ZM20 8H4V6H20V8Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Payment', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=tools')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'tools' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.7 19L13.6 9.9C14.5 7.6 14 4.9 12.1 3C10.1 1 7.1 1 5.1 3S3.1 7.9 5.1 9.9C7 11.8 9.6 12.3 11.9 11.4L21 20.6C21.3 20.9 21.7 20.9 22 20.6L22.6 20C22.9 19.7 22.9 19.3 22.6 19L22.7 19ZM7.5 8.5C6.1 7.1 6.1 4.9 7.5 3.5S10.9 2.1 12.3 3.5 13.7 6.9 12.3 8.3 8.9 9.7 7.5 8.3V8.5Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Tools', 'squarekit'); ?></span>
        </a>
    </div>

    <!-- Tab Content -->
    <div class="squarekit-tab-content">
        <?php
        switch ($current_tab) {
            case 'connection':
                include 'settings-tabs/connection.php';
                break;
            case 'sync':
                include 'settings-tabs/sync.php';
                break;
            case 'conflicts':
                include 'settings-tabs/conflicts.php';
                break;
            case 'mapping':
                include 'settings-tabs/mapping.php';
                break;
            case 'advanced':
                include 'settings-tabs/advanced.php';
                break;
            case 'payment':
                include 'settings-tabs/payment.php';
                break;
            case 'tools':
                include 'settings-tabs/tools.php';
                break;
            default:
                include 'settings-tabs/connection.php';
        }
        ?>
    </div>
</div>