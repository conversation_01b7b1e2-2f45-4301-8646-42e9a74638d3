<?php
$settings = new SquareKit_Settings();
$environment = $settings->get_environment();
$interval = $settings->get('cron_schedule', 'hourly');
$webhook_status = $settings->get('webhook_status', false);
$webhook_last_event = get_option('squarekit_webhook_last_event', '');
$sync_products = $settings->get('sync_products', 'both');
$optimize_images = $settings->get('optimize_images', true);
$max_image_width = $settings->get('max_image_width', 1200);
$max_image_height = $settings->get('max_image_height', 1200);
$image_quality = $settings->get('image_quality', 85);
$attribute_mapping = $settings->get_attribute_mapping();
$status_mapping = $settings->get_status_mapping();

// Get current tab
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'connection';

// Fetch WooCommerce product attributes (taxonomies starting with 'pa_')
$wc_attribute_taxonomies = wc_get_attribute_taxonomies();
$wc_attributes = array();
foreach ($wc_attribute_taxonomies as $tax) {
    $wc_attributes['pa_' . $tax->attribute_name] = $tax->attribute_label;
}

// Fetch WooCommerce order statuses
$wc_statuses = wc_get_order_statuses();
// Common Square statuses (can be extended by admin)
$square_statuses = array(
    'OPEN' => 'Open',
    'COMPLETED' => 'Completed',
    'CANCELED' => 'Canceled',
    'DRAFT' => 'Draft',
    'ON_HOLD' => 'On Hold',
    'REFUNDED' => 'Refunded',
    'FAILED' => 'Failed',
);

// Fetch payment methods from settings
$payment_methods = $settings->get_payment_methods();

if (isset($_POST['save_attribute_mapping'])) {
    check_admin_referer('squarekit_save_attribute_mapping', 'squarekit_attribute_mapping_nonce');
    $new_mapping = array();
    if (!empty($_POST['square_attribute']) && !empty($_POST['wc_attribute'])) {
        foreach ($_POST['square_attribute'] as $i => $sq_attr) {
            $sq_attr = sanitize_text_field($sq_attr);
            $wc_attr = sanitize_text_field($_POST['wc_attribute'][$i]);
            if ($sq_attr && $wc_attr) {
                $new_mapping[$sq_attr] = $wc_attr;
            }
        }
    }
    $settings->set_attribute_mapping($new_mapping);
    $attribute_mapping = $new_mapping;
    echo '<div class="notice notice-success"><p>' . esc_html__('Attribute mapping saved.', 'squarekit') . '</p></div>';
}

if ( isset($_POST['squarekit_settings_nonce']) && wp_verify_nonce($_POST['squarekit_settings_nonce'], 'squarekit_settings_save') ) {
    // Environment setting - only update if explicitly submitted
    if ( isset($_POST['environment']) ) {
        $environment = sanitize_text_field($_POST['environment']);
        $current_environment = $settings->get_environment();

        // Log environment changes for security
        if ( $environment !== $current_environment ) {
            if ( class_exists('SquareKit_Logger') ) {
                $logger = new SquareKit_Logger();
                $logger->log('settings', 'info', "Environment changed from {$current_environment} to {$environment} by user " . wp_get_current_user()->user_login);
            }
        }

        $settings->set('environment', $environment);
    }
    
    $interval = isset($_POST['cron_schedule']) ? sanitize_text_field($_POST['cron_schedule']) : 'hourly';
    $settings->set('cron_schedule', $interval);
    // Reschedule cron event
    if ( ! class_exists('SquareKit_Loader') ) {
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
    }
    $loader = new SquareKit_Loader();
    $loader->deactivate_cron();
    wp_clear_scheduled_hook('squarekit_scheduled_sync');
    wp_schedule_event( time(), $interval, 'squarekit_scheduled_sync' );

    $webhook_status = isset($_POST['webhook_status']) ? (bool)$_POST['webhook_status'] : false;
    $settings->set('webhook_status', $webhook_status);
    if ( $webhook_status ) {
        if ( ! class_exists('SquareKit_Loader') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
        }
        $loader = new SquareKit_Loader();
        $loader->register_square_webhooks();
    } else {
        if ( ! class_exists('SquareKit_Loader') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
        }
        $loader = new SquareKit_Loader();
        $loader->unregister_square_webhooks();
    }
    
    // Granular sync settings
    $granular_sync_settings = array(
        'woo_to_square' => array(
            'enabled' => isset($_POST['sync_woo_to_square_enabled']) ? (bool)$_POST['sync_woo_to_square_enabled'] : false,
            'data_types' => array(
                'products' => isset($_POST['sync_woo_to_square_products']) ? (bool)$_POST['sync_woo_to_square_products'] : false,
                'inventory' => isset($_POST['sync_woo_to_square_inventory']) ? (bool)$_POST['sync_woo_to_square_inventory'] : false,
                'categories' => isset($_POST['sync_woo_to_square_categories']) ? (bool)$_POST['sync_woo_to_square_categories'] : false,
                'orders' => isset($_POST['sync_woo_to_square_orders']) ? (bool)$_POST['sync_woo_to_square_orders'] : false,
                'customers' => isset($_POST['sync_woo_to_square_customers']) ? (bool)$_POST['sync_woo_to_square_customers'] : false,
            )
        ),
        'square_to_woo' => array(
            'enabled' => isset($_POST['sync_square_to_woo_enabled']) ? (bool)$_POST['sync_square_to_woo_enabled'] : false,
            'data_types' => array(
                'products' => isset($_POST['sync_square_to_woo_products']) ? (bool)$_POST['sync_square_to_woo_products'] : false,
                'inventory' => isset($_POST['sync_square_to_woo_inventory']) ? (bool)$_POST['sync_square_to_woo_inventory'] : false,
                'categories' => isset($_POST['sync_square_to_woo_categories']) ? (bool)$_POST['sync_square_to_woo_categories'] : false,
                'orders' => isset($_POST['sync_square_to_woo_orders']) ? (bool)$_POST['sync_square_to_woo_orders'] : false,
                'customers' => isset($_POST['sync_square_to_woo_customers']) ? (bool)$_POST['sync_square_to_woo_customers'] : false,
            )
        )
    );
    $settings->set_granular_sync_settings($granular_sync_settings);

    // Update legacy sync direction settings for backward compatibility
    $sync_direction_settings = array(
        'woo_to_square' => $granular_sync_settings['woo_to_square']['enabled'],
        'square_to_woo' => $granular_sync_settings['square_to_woo']['enabled'],
    );
    $settings->set_sync_direction_settings($sync_direction_settings);

    // Performance and automation settings
    if ( isset($_POST['sync_batch_size']) ) {
        $sync_batch_size = intval($_POST['sync_batch_size']);
        if ( $sync_batch_size > 0 && $sync_batch_size <= 500 ) {
            $settings->set('sync_batch_size', $sync_batch_size);
        }
    }

    if ( isset($_POST['sync_timeout']) ) {
        $sync_timeout = intval($_POST['sync_timeout']);
        if ( $sync_timeout >= 30 && $sync_timeout <= 600 ) {
            $settings->set('sync_timeout', $sync_timeout);
        }
    }

    // Conflict resolution settings
    if ( isset($_POST['inventory_conflict_resolution']) ) {
        $inventory_conflict_resolution = sanitize_text_field($_POST['inventory_conflict_resolution']);
        if ( in_array($inventory_conflict_resolution, ['square_wins', 'wc_wins', 'manual']) ) {
            $settings->set('inventory_conflict_resolution', $inventory_conflict_resolution);
        }
    }

    if ( isset($_POST['product_conflict_resolution']) ) {
        $product_conflict_resolution = sanitize_text_field($_POST['product_conflict_resolution']);
        if ( in_array($product_conflict_resolution, ['square_wins', 'wc_wins', 'manual']) ) {
            $settings->set('product_conflict_resolution', $product_conflict_resolution);
        }
    }

    $sync_products = isset($_POST['sync_products']) ? sanitize_text_field($_POST['sync_products']) : 'both';
    $settings->set('sync_products', $sync_products);
    
    // Image handling settings
    $optimize_images = isset($_POST['optimize_images']) ? (bool)$_POST['optimize_images'] : false;
    $settings->set('optimize_images', $optimize_images);
    
    $max_image_width = isset($_POST['max_image_width']) ? intval($_POST['max_image_width']) : 1200;
    $settings->set('max_image_width', $max_image_width);
    
    $max_image_height = isset($_POST['max_image_height']) ? intval($_POST['max_image_height']) : 1200;
    $settings->set('max_image_height', $max_image_height);
    
    $image_quality = isset($_POST['image_quality']) ? intval($_POST['image_quality']) : 85;
    $settings->set('image_quality', $image_quality);

    // Payment methods settings
    $payment_methods = array(
        'google_pay' => isset($_POST['google_pay']),
        'apple_pay' => isset($_POST['apple_pay']),
        'afterpay' => isset($_POST['afterpay'])
    );
    $settings->set('payment_methods', $payment_methods);
    
    // Advanced cron settings
    $selective_sync_enabled = isset($_POST['enable_selective_sync']) ? (bool)$_POST['enable_selective_sync'] : false;
    $settings->set('enable_selective_sync', $selective_sync_enabled);
    
    $enable_cron_retry = isset($_POST['enable_cron_retry']) ? (bool)$_POST['enable_cron_retry'] : false;
    $settings->set('enable_cron_retry', $enable_cron_retry);
    
    $max_retry_attempts = isset($_POST['max_cron_retry_attempts']) ? intval($_POST['max_cron_retry_attempts']) : 3;
    $settings->set('max_cron_retry_attempts', $max_retry_attempts);
    
    $cron_retry_delay = isset($_POST['cron_retry_delay']) ? intval($_POST['cron_retry_delay']) : 300;
    $settings->set('cron_retry_delay', $cron_retry_delay);
    
    $enable_parallel_processing = isset($_POST['enable_parallel_processing']) ? (bool)$_POST['enable_parallel_processing'] : false;
    $settings->set('enable_parallel_processing', $enable_parallel_processing);
    
    $max_parallel_jobs = isset($_POST['max_parallel_jobs']) ? intval($_POST['max_parallel_jobs']) : 2;
    $settings->set('max_parallel_jobs', $max_parallel_jobs);
    
    $max_execution_time = isset($_POST['max_sync_execution_time']) ? intval($_POST['max_sync_execution_time']) : 300;
    $settings->set('max_sync_execution_time', $max_execution_time);
    
    $batch_size = isset($_POST['sync_batch_size']) ? intval($_POST['sync_batch_size']) : 50;
    $settings->set('sync_batch_size', $batch_size);
    
    // Real-time inventory settings
    $enable_real_time_inventory = isset($_POST['enable_real_time_inventory']) ? (bool)$_POST['enable_real_time_inventory'] : false;
    $settings->set('enable_real_time_inventory', $enable_real_time_inventory);
    
    $inventory_conflict_resolution = isset($_POST['inventory_conflict_resolution']) ? sanitize_text_field($_POST['inventory_conflict_resolution']) : 'manual';
    $settings->set('inventory_conflict_resolution', $inventory_conflict_resolution);
    
    $inventory_conflict_threshold = isset($_POST['inventory_conflict_threshold']) ? intval($_POST['inventory_conflict_threshold']) : 1;
    $settings->set('inventory_conflict_threshold', $inventory_conflict_threshold);
    
    $enable_inventory_notifications = isset($_POST['enable_inventory_notifications']) ? (bool)$_POST['enable_inventory_notifications'] : false;
    $settings->set('enable_inventory_notifications', $enable_inventory_notifications);
    
    $inventory_sync_frequency = isset($_POST['inventory_sync_frequency']) ? sanitize_text_field($_POST['inventory_sync_frequency']) : 'realtime';
    $settings->set('inventory_sync_frequency', $inventory_sync_frequency);
    
    $enable_auto_resolve_conflicts = isset($_POST['enable_auto_resolve_conflicts']) ? (bool)$_POST['enable_auto_resolve_conflicts'] : false;
    $settings->set('enable_auto_resolve_conflicts', $enable_auto_resolve_conflicts);
    
    // Customer role mapping settings
    $enable_customer_role_mapping = isset($_POST['enable_customer_role_mapping']) ? (bool)$_POST['enable_customer_role_mapping'] : false;
    $settings->set('enable_customer_role_mapping', $enable_customer_role_mapping);
    
    // Fulfillment mapping settings
    $enable_fulfillment_mapping = isset($_POST['enable_fulfillment_mapping']) ? (bool)$_POST['enable_fulfillment_mapping'] : false;
    $settings->set('enable_fulfillment_mapping', $enable_fulfillment_mapping);
    
    $enable_pickup_location_mapping = isset($_POST['enable_pickup_location_mapping']) ? (bool)$_POST['enable_pickup_location_mapping'] : false;
    $settings->set('enable_pickup_location_mapping', $enable_pickup_location_mapping);
    
    // SKU mapping settings
    $enable_sku_mapping = isset($_POST['enable_sku_mapping']) ? (bool)$_POST['enable_sku_mapping'] : false;
    $settings->set('enable_sku_mapping', $enable_sku_mapping);
    
    $sku_conflict_resolution = isset($_POST['sku_conflict_resolution']) ? sanitize_text_field($_POST['sku_conflict_resolution']) : 'skip';
    $settings->set_sku_conflict_resolution($sku_conflict_resolution);
    
    // SKU validation settings
    $sku_validation_settings = array(
        'enforce_unique' => isset($_POST['sku_enforce_unique']) ? (bool)$_POST['sku_enforce_unique'] : false,
        'allow_empty' => isset($_POST['sku_allow_empty']) ? (bool)$_POST['sku_allow_empty'] : false,
        'max_length' => isset($_POST['sku_max_length']) ? intval($_POST['sku_max_length']) : 64,
        'allowed_characters' => isset($_POST['sku_allowed_characters']) ? sanitize_text_field($_POST['sku_allowed_characters']) : 'alphanumeric_dash_underscore',
    );
    $settings->set_sku_validation_settings($sku_validation_settings);
    
    // Update cron schedule with new settings
    $loader->update_cron_schedule($interval);
    
    echo '<div class="notice notice-success"><p>' . esc_html__('Settings saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_status_mapping'])) {
    check_admin_referer('squarekit_save_status_mapping', 'squarekit_status_mapping_nonce');
    $new_mapping = array();
    if (!empty($_POST['wc_status']) && !empty($_POST['square_status'])) {
        foreach ($_POST['wc_status'] as $i => $wc_status) {
            $wc_status = sanitize_text_field($wc_status);
            $sq_status = sanitize_text_field($_POST['square_status'][$i]);
            if ($wc_status && $sq_status) {
                $new_mapping[$wc_status] = $sq_status;
            }
        }
    }
    $settings->set_status_mapping($new_mapping);
    $status_mapping = $new_mapping;
    echo '<div class="notice notice-success"><p>' . esc_html__('Order status mapping saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_customer_role_mapping'])) {
    check_admin_referer('squarekit_save_customer_role_mapping', 'squarekit_customer_role_mapping_nonce');
    $new_mapping = array();
    if (!empty($_POST['mapping_criteria']) && !empty($_POST['mapping_condition']) && !empty($_POST['mapping_value']) && !empty($_POST['mapping_role'])) {
        foreach ($_POST['mapping_criteria'] as $i => $criteria) {
            $criteria = sanitize_text_field($criteria);
            $condition = sanitize_text_field($_POST['mapping_condition'][$i]);
            $value = sanitize_text_field($_POST['mapping_value'][$i]);
            $role = sanitize_text_field($_POST['mapping_role'][$i]);
            $priority = isset($_POST['mapping_priority'][$i]) ? intval($_POST['mapping_priority'][$i]) : 10;
            
            if ($criteria && $condition && $value && $role) {
                $new_mapping[] = array(
                    'criteria' => $criteria,
                    'condition' => $condition,
                    'value' => $value,
                    'role' => $role,
                    'priority' => $priority
                );
            }
        }
    }
    $settings->set_customer_role_mapping($new_mapping);
    $customer_role_mapping = $new_mapping;
    echo '<div class="notice notice-success"><p>' . esc_html__('Customer role mapping saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_fulfillment_mapping'])) {
    check_admin_referer('squarekit_save_fulfillment_mapping', 'squarekit_fulfillment_mapping_nonce');
    
    // Process fulfillment mapping
    $new_fulfillment_mapping = array();
    if (!empty($_POST['fulfillment_type']) && !empty($_POST['shipping_method'])) {
        foreach ($_POST['fulfillment_type'] as $i => $fulfillment_type) {
            $fulfillment_type = sanitize_text_field($fulfillment_type);
            $shipping_method = sanitize_text_field($_POST['shipping_method'][$i]);
            if ($fulfillment_type && $shipping_method) {
                $new_fulfillment_mapping[] = array(
                    'fulfillment_type' => $fulfillment_type,
                    'shipping_method' => $shipping_method
                );
            }
        }
    }
    $settings->set_fulfillment_mapping($new_fulfillment_mapping);
    $fulfillment_mapping = $new_fulfillment_mapping;
    
    // Process pickup location mapping
    $new_pickup_location_mapping = array();
    if (!empty($_POST['square_location']) && !empty($_POST['wc_pickup_location'])) {
        foreach ($_POST['square_location'] as $i => $square_location) {
            $square_location = sanitize_text_field($square_location);
            $wc_pickup_location = sanitize_text_field($_POST['wc_pickup_location'][$i]);
            if ($square_location && $wc_pickup_location) {
                $new_pickup_location_mapping[] = array(
                    'square_location' => $square_location,
                    'wc_pickup_location' => $wc_pickup_location
                );
            }
        }
    }
    $settings->set_pickup_location_mapping($new_pickup_location_mapping);
    $pickup_location_mapping = $new_pickup_location_mapping;
    
    echo '<div class="notice notice-success"><p>' . esc_html__('Fulfillment and pickup mapping saved.', 'squarekit') . '</p></div>';
}

if (isset($_POST['save_sku_mapping'])) {
    check_admin_referer('squarekit_save_sku_mapping', 'squarekit_sku_mapping_nonce');
    
    // Process SKU mapping rules
    $new_sku_mapping_rules = array();
    if (!empty($_POST['sku_item_type']) && !empty($_POST['sku_pattern']) && !empty($_POST['sku_priority'])) {
        foreach ($_POST['sku_item_type'] as $i => $item_type) {
            $item_type = sanitize_text_field($item_type);
            $pattern = sanitize_text_field($_POST['sku_pattern'][$i]);
            $priority = intval($_POST['sku_priority'][$i]);
            if ($item_type && $pattern && $priority > 0) {
                $new_sku_mapping_rules[] = array(
                    'item_type' => $item_type,
                    'pattern' => $pattern,
                    'priority' => $priority
                );
            }
        }
    }
    $settings->set_sku_mapping_rules($new_sku_mapping_rules);
    $sku_mapping_rules = $new_sku_mapping_rules;
    
    echo '<div class="notice notice-success"><p>' . esc_html__('SKU mapping rules saved.', 'squarekit') . '</p></div>';
}
?>

<div class="wrap squarekit-admin-wrap">
    <!-- Modern Header -->
    <div class="squarekit-admin-header">
        <h1><?php esc_html_e('Square Kit Settings', 'squarekit'); ?></h1>
        <p><?php esc_html_e('Configure your Square integration, sync preferences, and advanced options.', 'squarekit'); ?></p>
    </div>

    <!-- Tab Navigation -->
    <div class="squarekit-tabs-nav">
        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=connection')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'connection' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 3L4 14H11L10 21L19 10H12L13 3Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Connection', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=sync')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'sync' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4V1L8 5L12 9V6C15.31 6 18 8.69 18 12C18 13.01 17.75 13.97 17.3 14.8L18.76 16.26C19.54 15.03 20 13.57 20 12C20 7.58 16.42 4 12 4Z" fill="currentColor"/>
                    <path d="M12 18C8.69 18 6 15.31 6 12C6 10.99 6.25 10.03 6.7 9.2L5.24 7.74C4.46 8.97 4 10.43 4 12C4 16.42 7.58 20 12 20V23L16 19L12 15V18Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Sync Settings', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=mapping')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'mapping' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 11H7L12 6L17 11H15V16H9V11Z" fill="currentColor"/>
                    <path d="M5 20V18H19V20H5Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Mapping', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=advanced')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'advanced' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19.14 12.94C19.18 12.64 19.2 12.33 19.2 12S19.18 11.36 19.14 11.06L21.16 9.48C21.34 9.34 21.39 9.07 21.28 8.87L19.36 5.55C19.24 5.33 18.99 5.26 18.77 5.33L16.38 6.29C15.88 5.91 15.35 5.59 14.76 5.35L14.4 2.81C14.36 2.57 14.16 2.4 13.92 2.4H10.08C9.84 2.4 9.64 2.57 9.6 2.81L9.24 5.35C8.65 5.59 8.12 5.92 7.62 6.29L5.23 5.33C5.01 5.25 4.76 5.33 4.64 5.55L2.72 8.87C2.61 9.08 2.66 9.34 2.84 9.48L4.86 11.06C4.82 11.36 4.8 11.69 4.8 12S4.82 12.64 4.86 12.94L2.84 14.52C2.66 14.66 2.61 14.93 2.72 15.13L4.64 18.45C4.76 18.67 5.01 18.74 5.23 18.67L7.62 17.71C8.12 18.09 8.65 18.41 9.24 18.65L9.6 21.19C9.64 21.43 9.84 21.6 10.08 21.6H13.92C14.16 21.6 14.36 21.43 14.4 21.19L14.76 18.65C15.35 18.41 15.88 18.09 16.38 17.71L18.77 18.67C18.99 18.75 19.24 18.67 19.36 18.45L21.28 15.13C21.39 14.93 21.34 14.66 21.16 14.52L19.14 12.94ZM12 15.6C10.02 15.6 8.4 13.98 8.4 12S10.02 8.4 12 8.4S15.6 10.02 15.6 12S13.98 15.6 12 15.6Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Advanced', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=payment')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'payment' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 4H4C2.89 4 2.01 4.89 2.01 6L2 18C2 19.11 2.89 20 4 20H20C21.11 20 22 19.11 22 18V6C22 4.89 21.11 4 20 4ZM20 18H4V12H20V18ZM20 8H4V6H20V8Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Payment', 'squarekit'); ?></span>
        </a>

        <a href="<?php echo esc_url(admin_url('admin.php?page=squarekit-settings&tab=tools')); ?>"
           class="squarekit-tab-link <?php echo $current_tab === 'tools' ? 'active' : ''; ?>">
            <div class="tab-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.7 19L13.6 9.9C14.5 7.6 14 4.9 12.1 3C10.1 1 7.1 1 5.1 3S3.1 7.9 5.1 9.9C7 11.8 9.6 12.3 11.9 11.4L21 20.6C21.3 20.9 21.7 20.9 22 20.6L22.6 20C22.9 19.7 22.9 19.3 22.6 19L22.7 19ZM7.5 8.5C6.1 7.1 6.1 4.9 7.5 3.5S10.9 2.1 12.3 3.5 13.7 6.9 12.3 8.3 8.9 9.7 7.5 8.3V8.5Z" fill="currentColor"/>
                </svg>
            </div>
            <span><?php esc_html_e('Tools', 'squarekit'); ?></span>
        </a>
    </div>

    <!-- Tab Content -->
    <div class="squarekit-tab-content">
        <?php
        switch ($current_tab) {
            case 'connection':
                include 'settings-tabs/connection.php';
                break;
            case 'sync':
                include 'settings-tabs/sync.php';
                break;
            case 'mapping':
                include 'settings-tabs/mapping.php';
                break;
            case 'advanced':
                include 'settings-tabs/advanced.php';
                break;
            case 'payment':
                include 'settings-tabs/payment.php';
                break;
            case 'tools':
                include 'settings-tabs/tools.php';
                break;
            default:
                include 'settings-tabs/connection.php';
        }
        ?>
    </div>
</div>
        <tr>
            <th><?php esc_html_e('Square Environment', 'squarekit'); ?></th>
            <td>
                <label><input type="radio" name="environment" value="sandbox" <?php checked($environment, 'sandbox'); ?> /> <?php esc_html_e('Sandbox (Testing)', 'squarekit'); ?></label><br>
                <label><input type="radio" name="environment" value="production" <?php checked($environment, 'production'); ?> /> <?php esc_html_e('Production (Live)', 'squarekit'); ?></label>
                <p class="description"><?php esc_html_e('Choose whether to connect to Square Sandbox for testing or Production for live transactions.', 'squarekit'); ?></p>
                
                <?php
                // Show current environment status
                $is_connected = $settings->is_connected();
                $access_token = $settings->get_access_token();
                $location_id = $settings->get_location_id();
                ?>
                
                <h4><?php esc_html_e('Connection Status:', 'squarekit'); ?></h4>
                <p><strong><?php esc_html_e('Environment:', 'squarekit'); ?></strong> <?php echo esc_html(ucfirst($environment)); ?></p>
                <p><strong><?php esc_html_e('Connected:', 'squarekit'); ?></strong> <?php echo $is_connected ? esc_html__('Yes', 'squarekit') : esc_html__('No', 'squarekit'); ?></p>
                
                <?php if ($access_token): ?>
                    <p><strong><?php esc_html_e('Access Token:', 'squarekit'); ?></strong> <code><?php echo esc_html(substr($access_token, 0, 20) . '...'); ?></code></p>
                <?php endif; ?>
                
                <?php if ($location_id): ?>
                    <p><strong><?php esc_html_e('Location ID:', 'squarekit'); ?></strong> <code><?php echo esc_html($location_id); ?></code></p>
                <?php endif; ?>
                
                <p>
                    <button type="button" id="test-connection" class="button"><?php esc_html_e('Test Connection', 'squarekit'); ?></button>
                    <button type="button" id="refresh-connection" class="button"><?php esc_html_e('Refresh Connection', 'squarekit'); ?></button>
                </p>
                
                <div id="connection-test-result" style="display:none; margin-top: 10px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
                
                <script>
                jQuery(function($){
                    $('#test-connection').on('click', function(){
                        var $btn = $(this);
                        var $result = $('#connection-test-result');
                        
                        $btn.prop('disabled', true).text('<?php esc_html_e('Testing...', 'squarekit'); ?>');
                        $result.hide();
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'squarekit_test_connection',
                                nonce: '<?php echo wp_create_nonce('squarekit_test_connection'); ?>'
                            },
                            success: function(response){
                                $result.html(response.message).show();
                                if (response.success) {
                                    $result.css('background', '#d4edda').css('border-color', '#c3e6cb');
                                } else {
                                    $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                                }
                            },
                            error: function(){
                                $result.html('<?php esc_html_e('Test failed. Please try again.', 'squarekit'); ?>').show();
                                $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                            },
                            complete: function(){
                                $btn.prop('disabled', false).text('<?php esc_html_e('Test Connection', 'squarekit'); ?>');
                            }
                        });
                    });
                    
                    $('#refresh-connection').on('click', function(){
                        var $btn = $(this);
                        var $result = $('#connection-test-result');
                        
                        $btn.prop('disabled', true).text('<?php esc_html_e('Refreshing...', 'squarekit'); ?>');
                        $result.hide();
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'squarekit_refresh_connection',
                                nonce: '<?php echo wp_create_nonce('squarekit_refresh_connection'); ?>'
                            },
                            success: function(response){
                                $result.html(response.message).show();
                                if (response.success) {
                                    $result.css('background', '#d4edda').css('border-color', '#c3e6cb');
                                    // Reload page to show updated status
                                    setTimeout(function(){ location.reload(); }, 2000);
                                } else {
                                    $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                                }
                            },
                            error: function(){
                                $result.html('<?php esc_html_e('Refresh failed. Please try again.', 'squarekit'); ?>').show();
                                $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                            },
                            complete: function(){
                                $btn.prop('disabled', false).text('<?php esc_html_e('Refresh Connection', 'squarekit'); ?>');
                            }
                        });
                    });
                });
                </script>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Sync Interval', 'squarekit'); ?></th>
            <td>
                <select name="cron_schedule">
                    <option value="hourly" <?php selected($interval, 'hourly'); ?>><?php esc_html_e('Hourly', 'squarekit'); ?></option>
                    <option value="twicedaily" <?php selected($interval, 'twicedaily'); ?>><?php esc_html_e('Twice Daily', 'squarekit'); ?></option>
                    <option value="daily" <?php selected($interval, 'daily'); ?>><?php esc_html_e('Daily', 'squarekit'); ?></option>
                </select>
                <p class="description"><?php esc_html_e('How often to sync with Square automatically.', 'squarekit'); ?></p>
                
                <?php
                // Get advanced cron settings
                $selective_sync_enabled = $settings->get('enable_selective_sync', false);
                $enable_cron_retry = $settings->get('enable_cron_retry', true);
                $max_retry_attempts = $settings->get('max_cron_retry_attempts', 3);
                $cron_retry_delay = $settings->get('cron_retry_delay', 300);
                $enable_parallel_processing = $settings->get('enable_parallel_processing', false);
                $max_parallel_jobs = $settings->get('max_parallel_jobs', 2);
                $max_execution_time = $settings->get('max_sync_execution_time', 300);
                $batch_size = $settings->get('sync_batch_size', 50);
                ?>
                
                <h4><?php esc_html_e('Advanced Cron Settings:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_selective_sync" value="1" <?php checked($selective_sync_enabled, true); ?> /> <?php esc_html_e('Enable selective sync (individual sync events)', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('When enabled, different data types can be synced at different intervals.', 'squarekit'); ?></p>
                
                <label><input type="checkbox" name="enable_cron_retry" value="1" <?php checked($enable_cron_retry, true); ?> /> <?php esc_html_e('Enable retry on failure', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Automatically retry failed sync operations.', 'squarekit'); ?></p>
                
                <label><?php esc_html_e('Max retry attempts:', 'squarekit'); ?> <input type="number" name="max_cron_retry_attempts" value="<?php echo esc_attr($max_retry_attempts); ?>" min="1" max="10" step="1" /></label><br>
                <label><?php esc_html_e('Retry delay (seconds):', 'squarekit'); ?> <input type="number" name="cron_retry_delay" value="<?php echo esc_attr($cron_retry_delay); ?>" min="60" max="3600" step="60" /></label><br>
                
                <label><input type="checkbox" name="enable_parallel_processing" value="1" <?php checked($enable_parallel_processing, true); ?> /> <?php esc_html_e('Enable parallel processing', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Process multiple sync operations simultaneously (experimental).', 'squarekit'); ?></p>
                
                <label><?php esc_html_e('Max parallel jobs:', 'squarekit'); ?> <input type="number" name="max_parallel_jobs" value="<?php echo esc_attr($max_parallel_jobs); ?>" min="1" max="5" step="1" /></label><br>
                
                <h4><?php esc_html_e('Performance Settings:', 'squarekit'); ?></h4>
                <label><?php esc_html_e('Max execution time (seconds):', 'squarekit'); ?> <input type="number" name="max_sync_execution_time" value="<?php echo esc_attr($max_execution_time); ?>" min="60" max="1800" step="60" /></label><br>
                <label><?php esc_html_e('Batch size:', 'squarekit'); ?> <input type="number" name="sync_batch_size" value="<?php echo esc_attr($batch_size); ?>" min="10" max="200" step="10" /></label><br>
                <p class="description"><?php esc_html_e('Number of items to process in each batch. Lower values use less memory but take longer.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Cron Status:', 'squarekit'); ?></h4>
                <?php
                if ( ! class_exists('SquareKit_Loader') ) {
                    require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
                }
                $loader = new SquareKit_Loader();
                $cron_status = $loader->get_cron_status();
                ?>
                <p><strong><?php esc_html_e('Status:', 'squarekit'); ?></strong> <?php echo $cron_status['enabled'] ? esc_html__('Active', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?></p>
                <p><strong><?php esc_html_e('Schedule:', 'squarekit'); ?></strong> <?php echo esc_html(ucfirst($cron_status['schedule'])); ?></p>
                <p><strong><?php esc_html_e('Next Run:', 'squarekit'); ?></strong> <?php echo $cron_status['next_run'] ? esc_html(date('Y-m-d H:i:s', $cron_status['next_run'])) : esc_html__('Not scheduled', 'squarekit'); ?></p>
                <p><strong><?php esc_html_e('Last Run:', 'squarekit'); ?></strong> <?php echo $cron_status['last_run'] ? esc_html(date('Y-m-d H:i:s', $cron_status['last_run'])) : esc_html__('Never', 'squarekit'); ?></p>
                <p><strong><?php esc_html_e('Selective Sync:', 'squarekit'); ?></strong> <?php echo $cron_status['selective_sync'] ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?></p>
                
                <p>
                    <button type="button" id="test-cron" class="button"><?php esc_html_e('Test Cron Connection', 'squarekit'); ?></button>
                    <button type="button" id="view-cron-logs" class="button"><?php esc_html_e('View Cron Logs', 'squarekit'); ?></button>
                </p>
                
                <div id="cron-test-result" style="display:none; margin-top: 10px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
                
                <script>
                jQuery(function($){
                    $('#test-cron').on('click', function(){
                        var $btn = $(this);
                        var $result = $('#cron-test-result');
                        
                        $btn.prop('disabled', true).text('<?php esc_html_e('Testing...', 'squarekit'); ?>');
                        $result.hide();
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'squarekit_test_cron',
                                nonce: '<?php echo wp_create_nonce('squarekit_test_cron'); ?>'
                            },
                            success: function(response){
                                $result.html(response.message).show();
                                if (response.success) {
                                    $result.css('background', '#d4edda').css('border-color', '#c3e6cb');
                                } else {
                                    $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                                }
                            },
                            error: function(){
                                $result.html('<?php esc_html_e('Test failed. Please try again.', 'squarekit'); ?>').show();
                                $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                            },
                            complete: function(){
                                $btn.prop('disabled', false).text('<?php esc_html_e('Test Cron Connection', 'squarekit'); ?>');
                            }
                        });
                    });
                    
                    $('#view-cron-logs').on('click', function(){
                        window.open('<?php echo admin_url('admin.php?page=squarekit&tab=logs&type=cron'); ?>', '_blank');
                    });
                });
                </script>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Webhooks', 'squarekit'); ?></th>
            <td>
                <label><input type="checkbox" name="webhook_status" value="1" <?php checked($webhook_status, true); ?> /> <?php esc_html_e('Enable real-time sync with Square (webhooks)', 'squarekit'); ?></label>
                <p class="description"><?php esc_html_e('When enabled, Square will notify your site of changes in real time.', 'squarekit'); ?></p>
                
                <?php
                // Get webhook details
                $webhook_url = $settings->get('webhook_url', '');
                $webhook_events = $settings->get('webhook_events', array());
                $webhook_id = $settings->get('webhook_id', '');
                $signature_key = $settings->get_webhook_signature_key();
                ?>
                
                <h4><?php esc_html_e('Webhook Status:', 'squarekit'); ?></h4>
                <p><strong><?php echo $webhook_status ? esc_html__('Active', 'squarekit') : esc_html__('Inactive', 'squarekit'); ?></strong></p>
                
                <?php if ($webhook_url): ?>
                    <p><strong><?php esc_html_e('Webhook URL:', 'squarekit'); ?></strong> <code><?php echo esc_html($webhook_url); ?></code></p>
                <?php endif; ?>
                
                <?php if ($webhook_id): ?>
                    <p><strong><?php esc_html_e('Webhook ID:', 'squarekit'); ?></strong> <code><?php echo esc_html($webhook_id); ?></code></p>
                <?php endif; ?>
                
                <?php if (!empty($webhook_events)): ?>
                    <p><strong><?php esc_html_e('Event Types:', 'squarekit'); ?></strong></p>
                    <ul style="margin-left: 20px;">
                        <?php foreach ($webhook_events as $event): ?>
                            <li><code><?php echo esc_html($event); ?></code></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
                
                <?php if ($signature_key): ?>
                    <p><strong><?php esc_html_e('Signature Key:', 'squarekit'); ?></strong> <code><?php echo esc_html(substr($signature_key, 0, 10) . '...'); ?></code></p>
                <?php else: ?>
                    <p><em><?php esc_html_e('No signature key configured. Webhook security is disabled.', 'squarekit'); ?></em></p>
                <?php endif; ?>
                
                <?php if ($webhook_last_event): ?>
                    <p><strong><?php esc_html_e('Last event received:', 'squarekit'); ?></strong> <code><?php echo esc_html($webhook_last_event); ?></code></p>
                <?php endif; ?>
                
                <h4><?php esc_html_e('Webhook Management:', 'squarekit'); ?></h4>
                <p>
                    <button type="button" id="test-webhook" class="button"><?php esc_html_e('Test Webhook Connection', 'squarekit'); ?></button>
                    <button type="button" id="refresh-webhook" class="button"><?php esc_html_e('Refresh Webhook Registration', 'squarekit'); ?></button>
                    <button type="button" id="view-webhook-logs" class="button"><?php esc_html_e('View Webhook Logs', 'squarekit'); ?></button>
                </p>
                
                <div id="webhook-test-result" style="display:none; margin-top: 10px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
                
                <script>
                jQuery(function($){
                    $('#test-webhook').on('click', function(){
                        var $btn = $(this);
                        var $result = $('#webhook-test-result');
                        
                        $btn.prop('disabled', true).text('<?php esc_html_e('Testing...', 'squarekit'); ?>');
                        $result.hide();
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'squarekit_test_webhook',
                                nonce: '<?php echo wp_create_nonce('squarekit_test_webhook'); ?>'
                            },
                            success: function(response){
                                $result.html(response.message).show();
                                if (response.success) {
                                    $result.css('background', '#d4edda').css('border-color', '#c3e6cb');
                                } else {
                                    $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                                }
                            },
                            error: function(){
                                $result.html('<?php esc_html_e('Test failed. Please try again.', 'squarekit'); ?>').show();
                                $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                            },
                            complete: function(){
                                $btn.prop('disabled', false).text('<?php esc_html_e('Test Webhook Connection', 'squarekit'); ?>');
                            }
                        });
                    });
                    
                    $('#refresh-webhook').on('click', function(){
                        var $btn = $(this);
                        var $result = $('#webhook-test-result');
                        
                        $btn.prop('disabled', true).text('<?php esc_html_e('Refreshing...', 'squarekit'); ?>');
                        $result.hide();
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'squarekit_refresh_webhook',
                                nonce: '<?php echo wp_create_nonce('squarekit_refresh_webhook'); ?>'
                            },
                            success: function(response){
                                $result.html(response.message).show();
                                if (response.success) {
                                    $result.css('background', '#d4edda').css('border-color', '#c3e6cb');
                                    setTimeout(function(){ location.reload(); }, 2000);
                                } else {
                                    $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                                }
                            },
                            error: function(){
                                $result.html('<?php esc_html_e('Refresh failed. Please try again.', 'squarekit'); ?>').show();
                                $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                            },
                            complete: function(){
                                $btn.prop('disabled', false).text('<?php esc_html_e('Refresh Webhook Registration', 'squarekit'); ?>');
                            }
                        });
                    });
                    
                    $('#view-webhook-logs').on('click', function(){
                        window.open('<?php echo admin_url('admin.php?page=squarekit&tab=logs&type=webhook'); ?>', '_blank');
                    });
                });
                </script>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Sync Direction Controls', 'squarekit'); ?></th>
            <td>
                <?php
                // Get current sync direction settings
                $sync_direction_settings = $settings->get_sync_direction_settings();
                $sync_direction_descriptions = $settings->get_sync_direction_descriptions();
                ?>
                
                <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                    <!-- WooCommerce to Square Sync -->
                    <div style="flex: 1; border: 2px solid #0073aa; border-radius: 8px; padding: 20px; background: #f9f9f9;">
                        <h3 style="margin-top: 0; color: #0073aa;"><?php echo esc_html($sync_direction_descriptions['woo_to_square']['title']); ?></h3>
                        <p><?php echo esc_html($sync_direction_descriptions['woo_to_square']['description']); ?></p>
                        
                        <label style="display: block; margin: 15px 0;">
                            <input type="checkbox" name="sync_woo_to_square" value="1" <?php checked($sync_direction_settings['woo_to_square'], true); ?> style="margin-right: 8px;" />
                            <strong><?php esc_html_e('Enable WooCommerce → Square Sync', 'squarekit'); ?></strong>
                        </label>
                        
                        <h4><?php esc_html_e('Features:', 'squarekit'); ?></h4>
                        <ul style="margin-left: 20px;">
                            <?php foreach ($sync_direction_descriptions['woo_to_square']['features'] as $feature) : ?>
                                <li><?php echo esc_html($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <div style="margin-top: 15px; padding: 10px; background: #e7f3ff; border-radius: 4px;">
                            <strong><?php esc_html_e('Status:', 'squarekit'); ?></strong>
                            <?php if ($sync_direction_settings['woo_to_square']) : ?>
                                <span style="color: #28a745;"><?php esc_html_e('Enabled', 'squarekit'); ?></span>
                            <?php else : ?>
                                <span style="color: #dc3545;"><?php esc_html_e('Disabled', 'squarekit'); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Square to WooCommerce Sync -->
                    <div style="flex: 1; border: 2px solid #28a745; border-radius: 8px; padding: 20px; background: #f9f9f9;">
                        <h3 style="margin-top: 0; color: #28a745;"><?php echo esc_html($sync_direction_descriptions['square_to_woo']['title']); ?></h3>
                        <p><?php echo esc_html($sync_direction_descriptions['square_to_woo']['description']); ?></p>
                        
                        <label style="display: block; margin: 15px 0;">
                            <input type="checkbox" name="sync_square_to_woo" value="1" <?php checked($sync_direction_settings['square_to_woo'], true); ?> style="margin-right: 8px;" />
                            <strong><?php esc_html_e('Enable Square → WooCommerce Sync', 'squarekit'); ?></strong>
                        </label>
                        
                        <h4><?php esc_html_e('Features:', 'squarekit'); ?></h4>
                        <ul style="margin-left: 20px;">
                            <?php foreach ($sync_direction_descriptions['square_to_woo']['features'] as $feature) : ?>
                                <li><?php echo esc_html($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <div style="margin-top: 15px; padding: 10px; background: #e7f3ff; border-radius: 4px;">
                            <strong><?php esc_html_e('Status:', 'squarekit'); ?></strong>
                            <?php if ($sync_direction_settings['square_to_woo']) : ?>
                                <span style="color: #28a745;"><?php esc_html_e('Enabled', 'squarekit'); ?></span>
                            <?php else : ?>
                                <span style="color: #dc3545;"><?php esc_html_e('Disabled', 'squarekit'); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin-top: 20px;">
                    <h4 style="margin-top: 0; color: #856404;"><?php esc_html_e('Important Notes:', 'squarekit'); ?></h4>
                    <ul style="margin-left: 20px;">
                        <li><?php esc_html_e('You can enable both sync directions simultaneously for bidirectional synchronization.', 'squarekit'); ?></li>
                        <li><?php esc_html_e('Each sync direction operates independently and can be enabled/disabled separately.', 'squarekit'); ?></li>
                        <li><?php esc_html_e('Changes made in one system will be reflected in the other based on your sync direction settings.', 'squarekit'); ?></li>
                        <li><?php esc_html_e('Disabling a sync direction will prevent automatic synchronization in that direction.', 'squarekit'); ?></li>
                    </ul>
                </div>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Image Handling', 'squarekit'); ?></th>
            <td>
                <h4><?php esc_html_e('Image optimization:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="optimize_images" value="1" <?php checked($optimize_images, true); ?> /> <?php esc_html_e('Optimize images during import', 'squarekit'); ?></label>
                <p class="description"><?php esc_html_e('Automatically resize and compress images to improve performance.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Maximum dimensions:', 'squarekit'); ?></h4>
                <label><?php esc_html_e('Width:', 'squarekit'); ?> <input type="number" name="max_image_width" value="<?php echo esc_attr($max_image_width); ?>" min="100" max="3000" step="50" /> px</label><br>
                <label><?php esc_html_e('Height:', 'squarekit'); ?> <input type="number" name="max_image_height" value="<?php echo esc_attr($max_image_height); ?>" min="100" max="3000" step="50" /> px</label>
                <p class="description"><?php esc_html_e('Images larger than these dimensions will be resized.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Image quality:', 'squarekit'); ?></h4>
                <label><?php esc_html_e('JPEG quality:', 'squarekit'); ?> <input type="number" name="image_quality" value="<?php echo esc_attr($image_quality); ?>" min="1" max="100" step="5" /> %</label>
                <p class="description"><?php esc_html_e('Higher quality means larger file sizes.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Gallery support:', 'squarekit'); ?></h4>
                <p class="description"><?php esc_html_e('Product galleries are automatically imported and exported between Square and WooCommerce.', 'squarekit'); ?></p>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Payment Methods', 'squarekit'); ?></th>
            <td>
                <!--
                /**
                 * Payment Methods Section
                 *
                 * Allows admin to enable/disable Google Pay, Apple Pay, and Afterpay for Square payments.
                 *
                 * @since 1.0.0
                 */
                -->
                <label><input type="checkbox" name="google_pay" <?php checked($payment_methods['google_pay']); ?> /> <?php esc_html_e('Enable Google Pay', 'squarekit'); ?></label><br>
                <label><input type="checkbox" name="apple_pay" <?php checked($payment_methods['apple_pay']); ?> /> <?php esc_html_e('Enable Apple Pay', 'squarekit'); ?></label><br>
                <label><input type="checkbox" name="afterpay" <?php checked($payment_methods['afterpay']); ?> /> <?php esc_html_e('Enable Afterpay', 'squarekit'); ?></label>
                <p class="description"><?php esc_html_e('Enable or disable additional payment methods for Square checkout. Google Pay, Apple Pay, and Afterpay require correct Square account configuration.', 'squarekit'); ?></p>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Real-Time Inventory Sync', 'squarekit'); ?></th>
            <td>
                <!--
                /**
                 * Real-Time Inventory Sync Section
                 *
                 * Allows admin to configure real-time inventory synchronization with conflict resolution.
                 *
                 * @since 1.0.0
                 */
                -->
                <?php
                // Get real-time inventory settings
                $enable_real_time_inventory = $settings->get('enable_real_time_inventory', true);
                $inventory_conflict_resolution = $settings->get('inventory_conflict_resolution', 'manual');
                $inventory_conflict_threshold = $settings->get('inventory_conflict_threshold', 1);
                $enable_inventory_notifications = $settings->get('enable_inventory_notifications', true);
                $inventory_sync_frequency = $settings->get('inventory_sync_frequency', 'realtime');
                $enable_auto_resolve_conflicts = $settings->get('enable_auto_resolve_conflicts', false);
                ?>
                
                <h4><?php esc_html_e('Real-Time Sync Settings:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_real_time_inventory" value="1" <?php checked($enable_real_time_inventory, true); ?> /> <?php esc_html_e('Enable real-time inventory sync', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Automatically sync inventory changes when orders are processed, cancelled, or refunded.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Conflict Resolution:', 'squarekit'); ?></h4>
                <select name="inventory_conflict_resolution">
                    <option value="manual" <?php selected($inventory_conflict_resolution, 'manual'); ?>><?php esc_html_e('Manual Resolution', 'squarekit'); ?></option>
                    <option value="wc_wins" <?php selected($inventory_conflict_resolution, 'wc_wins'); ?>><?php esc_html_e('WooCommerce Wins', 'squarekit'); ?></option>
                    <option value="square_wins" <?php selected($inventory_conflict_resolution, 'square_wins'); ?>><?php esc_html_e('Square Wins', 'squarekit'); ?></option>
                </select>
                <p class="description"><?php esc_html_e('How to handle inventory conflicts between WooCommerce and Square.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Conflict Threshold:', 'squarekit'); ?></h4>
                <label><?php esc_html_e('Minimum difference to trigger conflict:', 'squarekit'); ?> <input type="number" name="inventory_conflict_threshold" value="<?php echo esc_attr($inventory_conflict_threshold); ?>" min="0" max="100" step="1" /></label><br>
                <p class="description"><?php esc_html_e('Quantity difference threshold to consider as a conflict.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Notifications:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_inventory_notifications" value="1" <?php checked($enable_inventory_notifications, true); ?> /> <?php esc_html_e('Enable email notifications for conflicts', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Send email notifications when inventory conflicts are detected.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Sync Frequency:', 'squarekit'); ?></h4>
                <select name="inventory_sync_frequency">
                    <option value="realtime" <?php selected($inventory_sync_frequency, 'realtime'); ?>><?php esc_html_e('Real-time', 'squarekit'); ?></option>
                    <option value="hourly" <?php selected($inventory_sync_frequency, 'hourly'); ?>><?php esc_html_e('Hourly', 'squarekit'); ?></option>
                    <option value="daily" <?php selected($inventory_sync_frequency, 'daily'); ?>><?php esc_html_e('Daily', 'squarekit'); ?></option>
                </select>
                <p class="description"><?php esc_html_e('How often to sync inventory changes.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Auto-Resolution:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_auto_resolve_conflicts" value="1" <?php checked($enable_auto_resolve_conflicts, false); ?> /> <?php esc_html_e('Automatically resolve conflicts', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Automatically resolve conflicts based on the resolution setting above.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Inventory Conflict Management:', 'squarekit'); ?></h4>
                <p>
                    <button type="button" id="view-inventory-conflicts" class="button"><?php esc_html_e('View Inventory Conflicts', 'squarekit'); ?></button>
                    <button type="button" id="sync-inventory-manual" class="button"><?php esc_html_e('Manual Inventory Sync', 'squarekit'); ?></button>
                </p>
                
                <div id="inventory-sync-result" style="display:none; margin-top: 10px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
                
                <script>
                jQuery(function($){
                    $('#view-inventory-conflicts').on('click', function(){
                        window.open('<?php echo admin_url('admin.php?page=squarekit&tab=inventory_conflicts'); ?>', '_blank');
                    });
                    
                    $('#sync-inventory-manual').on('click', function(){
                        var $btn = $(this);
                        var $result = $('#inventory-sync-result');
                        
                        $btn.prop('disabled', true).text('<?php esc_html_e('Syncing...', 'squarekit'); ?>');
                        $result.hide();
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'squarekit_sync_inventory_manual',
                                direction: 'square_to_wc',
                                nonce: '<?php echo wp_create_nonce('squarekit_admin'); ?>'
                            },
                            success: function(response){
                                $result.html(response.message).show();
                                if (response.success) {
                                    $result.css('background', '#d4edda').css('border-color', '#c3e6cb');
                                } else {
                                    $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                                }
                            },
                            error: function(){
                                $result.html('<?php esc_html_e('Sync failed. Please try again.', 'squarekit'); ?>').show();
                                $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                            },
                            complete: function(){
                                $btn.prop('disabled', false).text('<?php esc_html_e('Manual Inventory Sync', 'squarekit'); ?>');
                            }
                        });
                    });
                });
                </script>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Attribute Mapping', 'squarekit'); ?></th>
            <td>
                <form method="post" action="">
                    <?php wp_nonce_field('squarekit_save_attribute_mapping', 'squarekit_attribute_mapping_nonce'); ?>
                    <table class="widefat" style="max-width:600px;">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('Square Attribute', 'squarekit'); ?></th>
                                <th><?php esc_html_e('WooCommerce Attribute', 'squarekit'); ?></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="squarekit-attribute-mapping-rows">
                            <?php if (!empty($attribute_mapping)) : ?>
                                <?php foreach ($attribute_mapping as $sq_attr => $wc_attr) : ?>
                                    <tr>
                                        <td><input type="text" name="square_attribute[]" value="<?php echo esc_attr($sq_attr); ?>" class="regular-text" /></td>
                                        <td>
                                            <select name="wc_attribute[]">
                                                <?php foreach ($wc_attributes as $tax => $label) : ?>
                                                    <option value="<?php echo esc_attr($tax); ?>" <?php selected($wc_attr, $tax); ?>><?php echo esc_html($label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td><button type="button" class="button remove-mapping">&times;</button></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <!-- Empty row for adding new mapping -->
                            <tr>
                                <td><input type="text" name="square_attribute[]" value="" class="regular-text" placeholder="e.g. color" /></td>
                                <td>
                                    <select name="wc_attribute[]">
                                        <option value=""><?php esc_html_e('Select attribute', 'squarekit'); ?></option>
                                        <?php foreach ($wc_attributes as $tax => $label) : ?>
                                            <option value="<?php echo esc_attr($tax); ?>"><?php echo esc_html($label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <p><button type="submit" name="save_attribute_mapping" class="button button-primary"><?php esc_html_e('Save Attribute Mapping', 'squarekit'); ?></button></p>
                </form>
                <script>
                jQuery(function($){
                    // Remove mapping row
                    $(document).on('click', '.remove-mapping', function(){
                        $(this).closest('tr').remove();
                    });
                    // Add new row if last row is filled
                    $('#squarekit-attribute-mapping-rows').on('change', 'input,select', function(){
                        var $last = $('#squarekit-attribute-mapping-rows tr:last');
                        if ($last.find('input').val() && $last.find('select').val()) {
                            var $clone = $last.clone();
                            $clone.find('input').val('');
                            $clone.find('select').val('');
                            $last.after($clone);
                        }
                    });
                });
                </script>
                <p class="description"><?php esc_html_e('Map Square attributes to WooCommerce attributes for advanced product sync. Add as many as needed.', 'squarekit'); ?></p>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Order Status Mapping', 'squarekit'); ?></th>
            <td>
                <form method="post" action="">
                    <?php wp_nonce_field('squarekit_save_status_mapping', 'squarekit_status_mapping_nonce'); ?>
                    <table class="widefat" style="max-width:600px;">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('WooCommerce Status', 'squarekit'); ?></th>
                                <th><?php esc_html_e('Square Status', 'squarekit'); ?></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="squarekit-status-mapping-rows">
                            <?php if (!empty($status_mapping)) : ?>
                                <?php foreach ($status_mapping as $wc_status => $sq_status) : ?>
                                    <tr>
                                        <td>
                                            <select name="wc_status[]">
                                                <?php foreach ($wc_statuses as $status_key => $status_label) : ?>
                                                    <option value="<?php echo esc_attr($status_key); ?>" <?php selected($wc_status, $status_key); ?>><?php echo esc_html($status_label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <select name="square_status[]" class="squarekit-square-status-select">
                                                <?php foreach ($square_statuses as $sq_key => $sq_label) : ?>
                                                    <option value="<?php echo esc_attr($sq_key); ?>" <?php selected($sq_status, $sq_key); ?>><?php echo esc_html($sq_label); ?></option>
                                                <?php endforeach; ?>
                                                <option value="custom" <?php selected(!isset($square_statuses[$sq_status]) && $sq_status, true); ?>><?php esc_html_e('Custom...', 'squarekit'); ?></option>
                                            </select>
                                            <input type="text" name="square_status[]" value="<?php echo (!isset($square_statuses[$sq_status]) && $sq_status) ? esc_attr($sq_status) : ''; ?>" class="regular-text squarekit-custom-status" style="display:<?php echo (!isset($square_statuses[$sq_status]) && $sq_status) ? 'inline-block' : 'none'; ?>;" placeholder="Custom status" />
                                        </td>
                                        <td><button type="button" class="button remove-mapping">&times;</button></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <!-- Empty row for adding new mapping -->
                            <tr>
                                <td>
                                    <select name="wc_status[]">
                                        <option value=""><?php esc_html_e('Select status', 'squarekit'); ?></option>
                                        <?php foreach ($wc_statuses as $status_key => $status_label) : ?>
                                            <option value="<?php echo esc_attr($status_key); ?>"><?php echo esc_html($status_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                                <td>
                                    <select name="square_status[]" class="squarekit-square-status-select">
                                        <option value=""><?php esc_html_e('Select status', 'squarekit'); ?></option>
                                        <?php foreach ($square_statuses as $sq_key => $sq_label) : ?>
                                            <option value="<?php echo esc_attr($sq_key); ?>"><?php echo esc_html($sq_label); ?></option>
                                        <?php endforeach; ?>
                                        <option value="custom"><?php esc_html_e('Custom...', 'squarekit'); ?></option>
                                    </select>
                                    <input type="text" name="square_status[]" value="" class="regular-text squarekit-custom-status" style="display:none;" placeholder="Custom status" />
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <p><button type="submit" name="save_status_mapping" class="button button-primary"><?php esc_html_e('Save Status Mapping', 'squarekit'); ?></button></p>
                </form>
                <script>
                jQuery(function($){
                    // Remove mapping row
                    $(document).on('click', '.remove-mapping', function(){
                        $(this).closest('tr').remove();
                    });
                    // Add new row if last row is filled
                    $('#squarekit-status-mapping-rows').on('change', 'select', function(){
                        var $last = $('#squarekit-status-mapping-rows tr:last');
                        if ($last.find('select').first().val() && $last.find('select.squarekit-square-status-select').val()) {
                            var $clone = $last.clone();
                            $clone.find('select').val('');
                            $clone.find('input').val('').hide();
                            $last.after($clone);
                        }
                    });
                    // Show/hide custom status input
                    $(document).on('change', 'select.squarekit-square-status-select', function(){
                        var $input = $(this).closest('td').find('input.squarekit-custom-status');
                        if ($(this).val() === 'custom') {
                            $input.show();
                        } else {
                            $input.hide().val('');
                        }
                    });
                });
                </script>
                <p class="description"><?php esc_html_e('Map WooCommerce order statuses to Square order statuses for two-way sync. Add as many as needed.', 'squarekit'); ?></p>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Customer Role Mapping', 'squarekit'); ?></th>
            <td>
                <!--
                /**
                 * Customer Role Mapping Section
                 *
                 * Allows admin to map Square customer attributes to WooCommerce user roles.
                 *
                 * @since 1.0.0
                 */
                -->
                <?php
                // Get customer role mapping settings
                $enable_customer_role_mapping = $settings->get('enable_customer_role_mapping', false);
                $customer_role_mapping = $settings->get_customer_role_mapping();
                $mapping_criteria = $settings->get_customer_role_mapping_criteria();
                $available_roles = $settings->get_available_wc_roles();
                ?>
                
                <h4><?php esc_html_e('Role Mapping Settings:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_customer_role_mapping" value="1" <?php checked($enable_customer_role_mapping, true); ?> /> <?php esc_html_e('Enable customer role mapping', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Automatically assign WooCommerce user roles based on Square customer data.', 'squarekit'); ?></p>
                
                <form method="post" action="">
                    <?php wp_nonce_field('squarekit_save_customer_role_mapping', 'squarekit_customer_role_mapping_nonce'); ?>
                    <table class="widefat" style="max-width:800px;">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('Criteria', 'squarekit'); ?></th>
                                <th><?php esc_html_e('Condition', 'squarekit'); ?></th>
                                <th><?php esc_html_e('Value', 'squarekit'); ?></th>
                                <th><?php esc_html_e('WooCommerce Role', 'squarekit'); ?></th>
                                <th><?php esc_html_e('Priority', 'squarekit'); ?></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="squarekit-customer-role-mapping-rows">
                            <?php if (!empty($customer_role_mapping)) : ?>
                                <?php foreach ($customer_role_mapping as $index => $mapping) : ?>
                                    <tr>
                                        <td>
                                            <select name="mapping_criteria[]">
                                                <?php foreach ($mapping_criteria as $criteria_key => $criteria_label) : ?>
                                                    <option value="<?php echo esc_attr($criteria_key); ?>" <?php selected($mapping['criteria'], $criteria_key); ?>><?php echo esc_html($criteria_label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <select name="mapping_condition[]">
                                                <option value="greater_than" <?php selected($mapping['condition'], 'greater_than'); ?>><?php esc_html_e('Greater than', 'squarekit'); ?></option>
                                                <option value="greater_than_equal" <?php selected($mapping['condition'], 'greater_than_equal'); ?>><?php esc_html_e('Greater than or equal', 'squarekit'); ?></option>
                                                <option value="less_than" <?php selected($mapping['condition'], 'less_than'); ?>><?php esc_html_e('Less than', 'squarekit'); ?></option>
                                                <option value="less_than_equal" <?php selected($mapping['condition'], 'less_than_equal'); ?>><?php esc_html_e('Less than or equal', 'squarekit'); ?></option>
                                                <option value="equals" <?php selected($mapping['condition'], 'equals'); ?>><?php esc_html_e('Equals', 'squarekit'); ?></option>
                                                <option value="not_equals" <?php selected($mapping['condition'], 'not_equals'); ?>><?php esc_html_e('Not equals', 'squarekit'); ?></option>
                                                <option value="contains" <?php selected($mapping['condition'], 'contains'); ?>><?php esc_html_e('Contains', 'squarekit'); ?></option>
                                                <option value="not_contains" <?php selected($mapping['condition'], 'not_contains'); ?>><?php esc_html_e('Not contains', 'squarekit'); ?></option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" name="mapping_value[]" value="<?php echo esc_attr($mapping['value']); ?>" class="regular-text" placeholder="Enter value" />
                                        </td>
                                        <td>
                                            <select name="mapping_role[]">
                                                <?php foreach ($available_roles as $role_key => $role_label) : ?>
                                                    <option value="<?php echo esc_attr($role_key); ?>" <?php selected($mapping['role'], $role_key); ?>><?php echo esc_html($role_label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="number" name="mapping_priority[]" value="<?php echo esc_attr($mapping['priority']); ?>" min="1" max="100" class="small-text" />
                                        </td>
                                        <td><button type="button" class="button remove-mapping">&times;</button></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <!-- Empty row for adding new mapping -->
                            <tr>
                                <td>
                                    <select name="mapping_criteria[]">
                                        <option value=""><?php esc_html_e('Select criteria', 'squarekit'); ?></option>
                                        <?php foreach ($mapping_criteria as $criteria_key => $criteria_label) : ?>
                                            <option value="<?php echo esc_attr($criteria_key); ?>"><?php echo esc_html($criteria_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                                <td>
                                    <select name="mapping_condition[]">
                                        <option value=""><?php esc_html_e('Select condition', 'squarekit'); ?></option>
                                        <option value="greater_than"><?php esc_html_e('Greater than', 'squarekit'); ?></option>
                                        <option value="greater_than_equal"><?php esc_html_e('Greater than or equal', 'squarekit'); ?></option>
                                        <option value="less_than"><?php esc_html_e('Less than', 'squarekit'); ?></option>
                                        <option value="less_than_equal"><?php esc_html_e('Less than or equal', 'squarekit'); ?></option>
                                        <option value="equals"><?php esc_html_e('Equals', 'squarekit'); ?></option>
                                        <option value="not_equals"><?php esc_html_e('Not equals', 'squarekit'); ?></option>
                                        <option value="contains"><?php esc_html_e('Contains', 'squarekit'); ?></option>
                                        <option value="not_contains"><?php esc_html_e('Not contains', 'squarekit'); ?></option>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" name="mapping_value[]" value="" class="regular-text" placeholder="Enter value" />
                                </td>
                                <td>
                                    <select name="mapping_role[]">
                                        <option value=""><?php esc_html_e('Select role', 'squarekit'); ?></option>
                                        <?php foreach ($available_roles as $role_key => $role_label) : ?>
                                            <option value="<?php echo esc_attr($role_key); ?>"><?php echo esc_html($role_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                                <td>
                                    <input type="number" name="mapping_priority[]" value="10" min="1" max="100" class="small-text" />
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <p><button type="submit" name="save_customer_role_mapping" class="button button-primary"><?php esc_html_e('Save Customer Role Mapping', 'squarekit'); ?></button></p>
                </form>
                
                <h4><?php esc_html_e('Bulk Role Mapping:', 'squarekit'); ?></h4>
                <p>
                    <button type="button" id="apply-role-mapping-all" class="button"><?php esc_html_e('Apply Role Mapping to All Customers', 'squarekit'); ?></button>
                </p>
                <div id="role-mapping-result" style="display:none; margin-top: 10px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
                
                <script>
                jQuery(function($){
                    // Remove mapping row
                    $(document).on('click', '.remove-mapping', function(){
                        $(this).closest('tr').remove();
                    });
                    // Add new row if last row is filled
                    $('#squarekit-customer-role-mapping-rows').on('change', 'input,select', function(){
                        var $last = $('#squarekit-customer-role-mapping-rows tr:last');
                        if ($last.find('select').first().val() && $last.find('select').eq(1).val() && $last.find('input').first().val() && $last.find('select').eq(2).val()) {
                            var $clone = $last.clone();
                            $clone.find('input').val('');
                            $clone.find('select').val('');
                            $clone.find('input[name="mapping_priority[]"]').val('10');
                            $last.after($clone);
                        }
                    });
                    
                    // Apply role mapping to all customers
                    $('#apply-role-mapping-all').on('click', function(){
                        var $btn = $(this);
                        var $result = $('#role-mapping-result');
                        
                        $btn.prop('disabled', true).text('<?php esc_html_e('Applying...', 'squarekit'); ?>');
                        $result.hide();
                        
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'squarekit_apply_role_mapping_all',
                                nonce: '<?php echo wp_create_nonce('squarekit_admin'); ?>'
                            },
                            success: function(response){
                                $result.html(response.message).show();
                                if (response.success) {
                                    $result.css('background', '#d4edda').css('border-color', '#c3e6cb');
                                } else {
                                    $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                                }
                            },
                            error: function(){
                                $result.html('<?php esc_html_e('Role mapping failed. Please try again.', 'squarekit'); ?>').show();
                                $result.css('background', '#f8d7da').css('border-color', '#f5c6cb');
                            },
                            complete: function(){
                                $btn.prop('disabled', false).text('<?php esc_html_e('Apply Role Mapping to All Customers', 'squarekit'); ?>');
                            }
                        });
                    });
                });
                </script>
                <p class="description"><?php esc_html_e('Map Square customer attributes to WooCommerce user roles. Rules are processed in priority order (lower numbers = higher priority).', 'squarekit'); ?></p>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Order Fulfillment & Pickup Mapping', 'squarekit'); ?></th>
            <td>
                <!--
                /**
                 * Order Fulfillment & Pickup Mapping Section
                 *
                 * Allows admin to map Square fulfillment types to WooCommerce shipping methods
                 * and manage pickup location mappings.
                 *
                 * @since 1.0.0
                 */
                -->
                <?php
                // Get fulfillment mapping settings
                $enable_fulfillment_mapping = $settings->get('enable_fulfillment_mapping', false);
                $enable_pickup_location_mapping = $settings->get('enable_pickup_location_mapping', false);
                $fulfillment_mapping = $settings->get_fulfillment_mapping();
                $pickup_location_mapping = $settings->get_pickup_location_mapping();
                $square_fulfillment_types = $settings->get_available_square_fulfillment_types();
                $wc_shipping_methods = $settings->get_available_wc_shipping_methods();
                $square_locations = $settings->get_available_square_locations();
                ?>
                
                <h4><?php esc_html_e('Fulfillment Mapping Settings:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_fulfillment_mapping" value="1" <?php checked($enable_fulfillment_mapping, true); ?> /> <?php esc_html_e('Enable fulfillment mapping', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Map Square fulfillment types to WooCommerce shipping methods for order sync.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('Pickup Location Mapping Settings:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_pickup_location_mapping" value="1" <?php checked($enable_pickup_location_mapping, true); ?> /> <?php esc_html_e('Enable pickup location mapping', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Map Square locations to WooCommerce pickup locations.', 'squarekit'); ?></p>
                
                <form method="post" action="">
                    <?php wp_nonce_field('squarekit_save_fulfillment_mapping', 'squarekit_fulfillment_mapping_nonce'); ?>
                    <h4><?php esc_html_e('Fulfillment Type Mapping:', 'squarekit'); ?></h4>
                    <table class="widefat" style="max-width:600px;">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('Square Fulfillment Type', 'squarekit'); ?></th>
                                <th><?php esc_html_e('WooCommerce Shipping Method', 'squarekit'); ?></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="squarekit-fulfillment-mapping-rows">
                            <?php if (!empty($fulfillment_mapping)) : ?>
                                <?php foreach ($fulfillment_mapping as $index => $mapping) : ?>
                                    <tr>
                                        <td>
                                            <select name="fulfillment_type[]">
                                                <?php foreach ($square_fulfillment_types as $type_key => $type_label) : ?>
                                                    <option value="<?php echo esc_attr($type_key); ?>" <?php selected($mapping['fulfillment_type'], $type_key); ?>><?php echo esc_html($type_label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <select name="shipping_method[]">
                                                <?php foreach ($wc_shipping_methods as $method_key => $method_label) : ?>
                                                    <option value="<?php echo esc_attr($method_key); ?>" <?php selected($mapping['shipping_method'], $method_key); ?>><?php echo esc_html($method_label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td><button type="button" class="button remove-mapping">&times;</button></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <!-- Empty row for adding new mapping -->
                            <tr>
                                <td>
                                    <select name="fulfillment_type[]">
                                        <option value=""><?php esc_html_e('Select fulfillment type', 'squarekit'); ?></option>
                                        <?php foreach ($square_fulfillment_types as $type_key => $type_label) : ?>
                                            <option value="<?php echo esc_attr($type_key); ?>"><?php echo esc_html($type_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                                <td>
                                    <select name="shipping_method[]">
                                        <option value=""><?php esc_html_e('Select shipping method', 'squarekit'); ?></option>
                                        <?php foreach ($wc_shipping_methods as $method_key => $method_label) : ?>
                                            <option value="<?php echo esc_attr($method_key); ?>"><?php echo esc_html($method_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <h4><?php esc_html_e('Pickup Location Mapping:', 'squarekit'); ?></h4>
                    <table class="widefat" style="max-width:600px;">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('Square Location', 'squarekit'); ?></th>
                                <th><?php esc_html_e('WooCommerce Pickup Location', 'squarekit'); ?></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="squarekit-pickup-location-mapping-rows">
                            <?php if (!empty($pickup_location_mapping)) : ?>
                                <?php foreach ($pickup_location_mapping as $index => $mapping) : ?>
                                    <tr>
                                        <td>
                                            <select name="square_location[]">
                                                <?php foreach ($square_locations as $location_key => $location_label) : ?>
                                                    <option value="<?php echo esc_attr($location_key); ?>" <?php selected($mapping['square_location'], $location_key); ?>><?php echo esc_html($location_label); ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" name="wc_pickup_location[]" value="<?php echo esc_attr($mapping['wc_pickup_location']); ?>" class="regular-text" placeholder="Enter pickup location name" />
                                        </td>
                                        <td><button type="button" class="button remove-mapping">&times;</button></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <!-- Empty row for adding new mapping -->
                            <tr>
                                <td>
                                    <select name="square_location[]">
                                        <option value=""><?php esc_html_e('Select Square location', 'squarekit'); ?></option>
                                        <?php foreach ($square_locations as $location_key => $location_label) : ?>
                                            <option value="<?php echo esc_attr($location_key); ?>"><?php echo esc_html($location_label); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" name="wc_pickup_location[]" value="" class="regular-text" placeholder="Enter pickup location name" />
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p><button type="submit" name="save_fulfillment_mapping" class="button button-primary"><?php esc_html_e('Save Fulfillment & Pickup Mapping', 'squarekit'); ?></button></p>
                </form>
                <script>
                jQuery(function($){
                    // Remove mapping row
                    $(document).on('click', '.remove-mapping', function(){
                        $(this).closest('tr').remove();
                    });
                    
                    // Add new fulfillment mapping row if last row is filled
                    $('#squarekit-fulfillment-mapping-rows').on('change', 'select', function(){
                        var $last = $('#squarekit-fulfillment-mapping-rows tr:last');
                        if ($last.find('select').first().val() && $last.find('select').eq(1).val()) {
                            var $clone = $last.clone();
                            $clone.find('select').val('');
                            $last.after($clone);
                        }
                    });
                    
                    // Add new pickup location mapping row if last row is filled
                    $('#squarekit-pickup-location-mapping-rows').on('change', 'input,select', function(){
                        var $last = $('#squarekit-pickup-location-mapping-rows tr:last');
                        if ($last.find('select').val() && $last.find('input').val()) {
                            var $clone = $last.clone();
                            $clone.find('input').val('');
                            $clone.find('select').val('');
                            $last.after($clone);
                        }
                    });
                });
                </script>
                <p class="description"><?php esc_html_e('Map Square fulfillment types to WooCommerce shipping methods and Square locations to WooCommerce pickup locations for order sync.', 'squarekit'); ?></p>
            </td>
        </tr>
        <tr>
            <th><?php esc_html_e('Advanced SKU Mapping', 'squarekit'); ?></th>
            <td>
                <!--
                /**
                 * Advanced SKU Mapping Section
                 *
                 * Allows admin to configure comprehensive SKU mapping for products,
                 * variations, modifiers, and addons with conflict resolution.
                 *
                 * @since 1.0.0
                 */
                -->
                <?php
                // Get SKU mapping settings
                $enable_sku_mapping = $settings->get('enable_sku_mapping', false);
                $sku_conflict_resolution = $settings->get_sku_conflict_resolution();
                $sku_mapping_rules = $settings->get_sku_mapping_rules();
                $sku_validation_settings = $settings->get_sku_validation_settings();
                $sku_conflict_resolution_options = $settings->get_sku_conflict_resolution_options();
                $sku_character_sets = $settings->get_sku_character_sets();
                ?>
                
                <h4><?php esc_html_e('SKU Mapping Settings:', 'squarekit'); ?></h4>
                <label><input type="checkbox" name="enable_sku_mapping" value="1" <?php checked($enable_sku_mapping, true); ?> /> <?php esc_html_e('Enable advanced SKU mapping', 'squarekit'); ?></label><br>
                <p class="description"><?php esc_html_e('Map SKUs for products, variations, modifiers, and addons during import/export.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('SKU Conflict Resolution:', 'squarekit'); ?></h4>
                <select name="sku_conflict_resolution">
                    <?php foreach ($sku_conflict_resolution_options as $key => $label) : ?>
                        <option value="<?php echo esc_attr($key); ?>" <?php selected($sku_conflict_resolution, $key); ?>><?php echo esc_html($label); ?></option>
                    <?php endforeach; ?>
                </select>
                <p class="description"><?php esc_html_e('How to handle SKU conflicts during import.', 'squarekit'); ?></p>
                
                <h4><?php esc_html_e('SKU Validation Settings:', 'squarekit'); ?></h4>
                <table class="widefat" style="max-width:600px;">
                    <tr>
                        <td><label><input type="checkbox" name="sku_enforce_unique" value="1" <?php checked($sku_validation_settings['enforce_unique'], true); ?> /> <?php esc_html_e('Enforce unique SKUs', 'squarekit'); ?></label></td>
                        <td><label><input type="checkbox" name="sku_allow_empty" value="1" <?php checked($sku_validation_settings['allow_empty'], true); ?> /> <?php esc_html_e('Allow empty SKUs', 'squarekit'); ?></label></td>
                    </tr>
                    <tr>
                        <td>
                            <label><?php esc_html_e('Max SKU length:', 'squarekit'); ?>
                                <input type="number" name="sku_max_length" value="<?php echo esc_attr($sku_validation_settings['max_length']); ?>" min="1" max="255" />
                            </label>
                        </td>
                        <td>
                            <label><?php esc_html_e('Allowed characters:', 'squarekit'); ?>
                                <select name="sku_allowed_characters">
                                    <?php foreach ($sku_character_sets as $key => $label) : ?>
                                        <option value="<?php echo esc_attr($key); ?>" <?php selected($sku_validation_settings['allowed_characters'], $key); ?>><?php echo esc_html($label); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <form method="post" action="">
                    <?php wp_nonce_field('squarekit_save_sku_mapping', 'squarekit_sku_mapping_nonce'); ?>
                    <h4><?php esc_html_e('SKU Mapping Rules:', 'squarekit'); ?></h4>
                    <table class="widefat" style="max-width:800px;">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('Square Item Type', 'squarekit'); ?></th>
                                <th><?php esc_html_e('SKU Pattern', 'squarekit'); ?></th>
                                <th><?php esc_html_e('Priority', 'squarekit'); ?></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="squarekit-sku-mapping-rows">
                            <?php if (!empty($sku_mapping_rules)) : ?>
                                <?php foreach ($sku_mapping_rules as $index => $rule) : ?>
                                    <tr>
                                        <td>
                                            <select name="sku_item_type[]">
                                                <option value="product" <?php selected($rule['item_type'], 'product'); ?>><?php esc_html_e('Product', 'squarekit'); ?></option>
                                                <option value="variation" <?php selected($rule['item_type'], 'variation'); ?>><?php esc_html_e('Variation', 'squarekit'); ?></option>
                                                <option value="modifier" <?php selected($rule['item_type'], 'modifier'); ?>><?php esc_html_e('Modifier', 'squarekit'); ?></option>
                                                <option value="addon" <?php selected($rule['item_type'], 'addon'); ?>><?php esc_html_e('Addon', 'squarekit'); ?></option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" name="sku_pattern[]" value="<?php echo esc_attr($rule['pattern']); ?>" class="regular-text" placeholder="e.g., {square_id}_{name}" />
                                            <p class="description"><?php esc_html_e('Use {square_id}, {name}, {category}, {price} as placeholders', 'squarekit'); ?></p>
                                        </td>
                                        <td>
                                            <input type="number" name="sku_priority[]" value="<?php echo esc_attr($rule['priority']); ?>" min="1" max="100" />
                                        </td>
                                        <td><button type="button" class="button remove-mapping">&times;</button></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            <!-- Empty row for adding new mapping -->
                            <tr>
                                <td>
                                    <select name="sku_item_type[]">
                                        <option value=""><?php esc_html_e('Select item type', 'squarekit'); ?></option>
                                        <option value="product"><?php esc_html_e('Product', 'squarekit'); ?></option>
                                        <option value="variation"><?php esc_html_e('Variation', 'squarekit'); ?></option>
                                        <option value="modifier"><?php esc_html_e('Modifier', 'squarekit'); ?></option>
                                        <option value="addon"><?php esc_html_e('Addon', 'squarekit'); ?></option>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" name="sku_pattern[]" value="" class="regular-text" placeholder="e.g., {square_id}_{name}" />
                                    <p class="description"><?php esc_html_e('Use {square_id}, {name}, {category}, {price} as placeholders', 'squarekit'); ?></p>
                                </td>
                                <td>
                                    <input type="number" name="sku_priority[]" value="50" min="1" max="100" />
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p><button type="submit" name="save_sku_mapping" class="button button-primary"><?php esc_html_e('Save SKU Mapping', 'squarekit'); ?></button></p>
                </form>
                <script>
                jQuery(function($){
                    // Remove mapping row
                    $(document).on('click', '.remove-mapping', function(){
                        $(this).closest('tr').remove();
                    });
                    
                    // Add new SKU mapping row if last row is filled
                    $('#squarekit-sku-mapping-rows').on('change', 'input,select', function(){
                        var $last = $('#squarekit-sku-mapping-rows tr:last');
                        if ($last.find('select').first().val() && $last.find('input').first().val() && $last.find('input').eq(1).val()) {
                            var $clone = $last.clone();
                            $clone.find('input').val('');
                            $clone.find('select').val('');
                            $last.after($clone);
                        }
                    });
                });
                </script>
                <p class="description"><?php esc_html_e('Configure SKU mapping rules for different item types with pattern-based generation.', 'squarekit'); ?></p>
            </td>
        </tr>
    </table>
    <p><button type="submit" class="button button-primary"><?php esc_html_e('Save Settings', 'squarekit'); ?></button></p>
</form> 