<?php
/**
 * Inventory Conflicts Management Page
 *
 * @package SquareKit
 * @subpackage SquareKit/admin/partials
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$settings = new SquareKit_Settings();
$conflicts = get_option( 'squarekit_inventory_conflicts', array() );

// Handle conflict resolution
if ( isset( $_POST['resolve_conflict'] ) && wp_verify_nonce( $_POST['squarekit_resolve_conflict_nonce'], 'squarekit_resolve_conflict' ) ) {
    $conflict_index = intval( $_POST['conflict_index'] );
    $resolution = sanitize_text_field( $_POST['resolution'] );
    
    if ( isset( $conflicts[$conflict_index] ) ) {
        $conflict = $conflicts[$conflict_index];
        $product_id = $conflict['product_id'];
        
        switch ( $resolution ) {
            case 'wc_wins':
                // Use WooCommerce quantity
                if ( ! class_exists('SquareKit_WooCommerce') ) {
                    require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
                }
                $wc = new SquareKit_WooCommerce();
                $wc->sync_inventory_to_square( $product_id );
                break;
                
            case 'square_wins':
                // Update WooCommerce with Square quantity
                $product = wc_get_product( $product_id );
                if ( $product ) {
                    $product->set_stock_quantity( $conflict['square_quantity'] );
                    $product->set_stock_status( $conflict['square_quantity'] > 0 ? 'instock' : 'outofstock' );
                    $product->save();
                }
                break;
                
            case 'manual':
                // Manual resolution - admin sets quantity
                $manual_quantity = intval( $_POST['manual_quantity'] );
                if ( $manual_quantity >= 0 ) {
                    $product = wc_get_product( $product_id );
                    if ( $product ) {
                        $product->set_stock_quantity( $manual_quantity );
                        $product->set_stock_status( $manual_quantity > 0 ? 'instock' : 'outofstock' );
                        $product->save();
                        
                        if ( ! class_exists('SquareKit_WooCommerce') ) {
                            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
                        }
                        $wc = new SquareKit_WooCommerce();
                        $wc->sync_inventory_to_square( $product_id );
                    }
                }
                break;
        }
        
        // Mark conflict as resolved
        $conflicts[$conflict_index]['resolved'] = true;
        $conflicts[$conflict_index]['resolved_at'] = current_time( 'mysql' );
        $conflicts[$conflict_index]['resolution'] = $resolution;
        update_option( 'squarekit_inventory_conflicts', $conflicts );
        
        echo '<div class="notice notice-success"><p>' . esc_html__( 'Inventory conflict resolved successfully.', 'squarekit' ) . '</p></div>';
    }
}

// Handle bulk actions
if ( isset( $_POST['bulk_action'] ) && wp_verify_nonce( $_POST['squarekit_bulk_conflicts_nonce'], 'squarekit_bulk_conflicts' ) ) {
    $selected_conflicts = isset( $_POST['selected_conflicts'] ) ? array_map( 'intval', $_POST['selected_conflicts'] ) : array();
    $bulk_action = sanitize_text_field( $_POST['bulk_action'] );
    
    if ( ! empty( $selected_conflicts ) ) {
        $resolved_count = 0;
        
        foreach ( $selected_conflicts as $conflict_index ) {
            if ( isset( $conflicts[$conflict_index] ) ) {
                $conflicts[$conflict_index]['resolved'] = true;
                $conflicts[$conflict_index]['resolved_at'] = current_time( 'mysql' );
                $conflicts[$conflict_index]['resolution'] = $bulk_action;
                $resolved_count++;
            }
        }
        
        update_option( 'squarekit_inventory_conflicts', $conflicts );
        
        echo '<div class="notice notice-success"><p>' . esc_html( sprintf( __( 'Resolved %d inventory conflicts.', 'squarekit' ), $resolved_count ) ) . '</p></div>';
    }
}

// Filter conflicts
$filter = isset( $_GET['filter'] ) ? sanitize_text_field( $_GET['filter'] ) : 'all';
$filtered_conflicts = array();

foreach ( $conflicts as $index => $conflict ) {
    if ( $filter === 'all' || 
         ( $filter === 'unresolved' && ! $conflict['resolved'] ) ||
         ( $filter === 'resolved' && $conflict['resolved'] ) ) {
        $filtered_conflicts[$index] = $conflict;
    }
}

// Pagination
$per_page = 20;
$current_page = isset( $_GET['paged'] ) ? max( 1, intval( $_GET['paged'] ) ) : 1;
$total_conflicts = count( $filtered_conflicts );
$total_pages = ceil( $total_conflicts / $per_page );
$offset = ( $current_page - 1 ) * $per_page;
$paged_conflicts = array_slice( $filtered_conflicts, $offset, $per_page, true );
?>

<div class="wrap">
    <h1><?php esc_html_e( 'Inventory Conflicts', 'squarekit' ); ?></h1>
    
    <div class="tablenav top">
        <div class="alignleft actions bulkactions">
            <form method="post" action="">
                <?php wp_nonce_field( 'squarekit_bulk_conflicts', 'squarekit_bulk_conflicts_nonce' ); ?>
                <select name="bulk_action">
                    <option value=""><?php esc_html_e( 'Bulk Actions', 'squarekit' ); ?></option>
                    <option value="wc_wins"><?php esc_html_e( 'Use WooCommerce Quantity', 'squarekit' ); ?></option>
                    <option value="square_wins"><?php esc_html_e( 'Use Square Quantity', 'squarekit' ); ?></option>
                    <option value="mark_resolved"><?php esc_html_e( 'Mark as Resolved', 'squarekit' ); ?></option>
                </select>
                <input type="submit" class="button action" value="<?php esc_attr_e( 'Apply', 'squarekit' ); ?>">
            </form>
        </div>
        
        <div class="alignright">
            <form method="get" action="">
                <input type="hidden" name="page" value="squarekit-inventory-conflicts">
                <select name="filter">
                    <option value="all" <?php selected( $filter, 'all' ); ?>><?php esc_html_e( 'All Conflicts', 'squarekit' ); ?></option>
                    <option value="unresolved" <?php selected( $filter, 'unresolved' ); ?>><?php esc_html_e( 'Unresolved', 'squarekit' ); ?></option>
                    <option value="resolved" <?php selected( $filter, 'resolved' ); ?>><?php esc_html_e( 'Resolved', 'squarekit' ); ?></option>
                </select>
                <input type="submit" class="button" value="<?php esc_attr_e( 'Filter', 'squarekit' ); ?>">
            </form>
        </div>
        
        <div class="tablenav-pages">
            <?php if ( $total_pages > 1 ) : ?>
                <span class="displaying-num"><?php printf( esc_html__( '%d items', 'squarekit' ), $total_conflicts ); ?></span>
                <span class="pagination-links">
                    <?php
                    $page_links = paginate_links( array(
                        'base' => add_query_arg( 'paged', '%#%' ),
                        'format' => '',
                        'prev_text' => __( '&laquo;' ),
                        'next_text' => __( '&raquo;' ),
                        'total' => $total_pages,
                        'current' => $current_page,
                        'type' => 'array'
                    ) );
                    
                    if ( $page_links ) {
                        echo join( "\n", $page_links );
                    }
                    ?>
                </span>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if ( empty( $paged_conflicts ) ) : ?>
        <div class="notice notice-info">
            <p><?php esc_html_e( 'No inventory conflicts found.', 'squarekit' ); ?></p>
        </div>
    <?php else : ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <td class="manage-column column-cb check-column">
                        <input type="checkbox" id="cb-select-all-1">
                    </td>
                    <th scope="col" class="manage-column column-product"><?php esc_html_e( 'Product', 'squarekit' ); ?></th>
                    <th scope="col" class="manage-column column-wc-quantity"><?php esc_html_e( 'WooCommerce Quantity', 'squarekit' ); ?></th>
                    <th scope="col" class="manage-column column-square-quantity"><?php esc_html_e( 'Square Quantity', 'squarekit' ); ?></th>
                    <th scope="col" class="manage-column column-difference"><?php esc_html_e( 'Difference', 'squarekit' ); ?></th>
                    <th scope="col" class="manage-column column-conflict-type"><?php esc_html_e( 'Conflict Type', 'squarekit' ); ?></th>
                    <th scope="col" class="manage-column column-created"><?php esc_html_e( 'Detected', 'squarekit' ); ?></th>
                    <th scope="col" class="manage-column column-status"><?php esc_html_e( 'Status', 'squarekit' ); ?></th>
                    <th scope="col" class="manage-column column-actions"><?php esc_html_e( 'Actions', 'squarekit' ); ?></th>
                </tr>
            </thead>
            
            <tbody>
                <?php foreach ( $paged_conflicts as $index => $conflict ) : ?>
                    <tr>
                        <th scope="row" class="check-column">
                            <input type="checkbox" name="selected_conflicts[]" value="<?php echo esc_attr( $index ); ?>">
                        </th>
                        <td class="column-product">
                            <?php
                            $product = wc_get_product( $conflict['product_id'] );
                            if ( $product ) {
                                echo '<strong>' . esc_html( $product->get_name() ) . '</strong><br>';
                                echo '<small>ID: ' . esc_html( $conflict['product_id'] ) . '</small>';
                            } else {
                                echo '<em>' . esc_html__( 'Product not found', 'squarekit' ) . '</em>';
                            }
                            ?>
                        </td>
                        <td class="column-wc-quantity">
                            <?php echo esc_html( $conflict['wc_quantity'] ); ?>
                        </td>
                        <td class="column-square-quantity">
                            <?php echo esc_html( $conflict['square_quantity'] ); ?>
                        </td>
                        <td class="column-difference">
                            <span class="<?php echo $conflict['difference'] > 0 ? 'conflict-high' : 'conflict-low'; ?>">
                                <?php echo esc_html( $conflict['difference'] ); ?>
                            </span>
                        </td>
                        <td class="column-conflict-type">
                            <?php
                            $type_labels = array(
                                'wc_higher' => __( 'WC Higher', 'squarekit' ),
                                'square_higher' => __( 'Square Higher', 'squarekit' )
                            );
                            echo esc_html( isset( $type_labels[$conflict['conflict_type']] ) ? $type_labels[$conflict['conflict_type']] : $conflict['conflict_type'] );
                            ?>
                        </td>
                        <td class="column-created">
                            <?php echo esc_html( date( 'Y-m-d H:i:s', strtotime( $conflict['created_at'] ) ) ); ?>
                        </td>
                        <td class="column-status">
                            <?php if ( $conflict['resolved'] ) : ?>
                                <span class="status-resolved"><?php esc_html_e( 'Resolved', 'squarekit' ); ?></span>
                                <?php if ( isset( $conflict['resolved_at'] ) ) : ?>
                                    <br><small><?php echo esc_html( date( 'Y-m-d H:i:s', strtotime( $conflict['resolved_at'] ) ) ); ?></small>
                                <?php endif; ?>
                            <?php else : ?>
                                <span class="status-unresolved"><?php esc_html_e( 'Unresolved', 'squarekit' ); ?></span>
                            <?php endif; ?>
                        </td>
                        <td class="column-actions">
                            <?php if ( ! $conflict['resolved'] ) : ?>
                                <button type="button" class="button resolve-conflict" data-conflict="<?php echo esc_attr( $index ); ?>">
                                    <?php esc_html_e( 'Resolve', 'squarekit' ); ?>
                                </button>
                            <?php else : ?>
                                <em><?php esc_html_e( 'Resolved', 'squarekit' ); ?></em>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</div>

<!-- Conflict Resolution Modal -->
<div id="conflict-resolution-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2><?php esc_html_e( 'Resolve Inventory Conflict', 'squarekit' ); ?></h2>
        
        <form method="post" action="" id="resolve-conflict-form">
            <?php wp_nonce_field( 'squarekit_resolve_conflict', 'squarekit_resolve_conflict_nonce' ); ?>
            <input type="hidden" name="conflict_index" id="conflict-index">
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e( 'Product:', 'squarekit' ); ?></th>
                    <td id="conflict-product-name"></td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e( 'WooCommerce Quantity:', 'squarekit' ); ?></th>
                    <td id="conflict-wc-quantity"></td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e( 'Square Quantity:', 'squarekit' ); ?></th>
                    <td id="conflict-square-quantity"></td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e( 'Difference:', 'squarekit' ); ?></th>
                    <td id="conflict-difference"></td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e( 'Resolution:', 'squarekit' ); ?></th>
                    <td>
                        <select name="resolution" id="conflict-resolution">
                            <option value="wc_wins"><?php esc_html_e( 'Use WooCommerce Quantity', 'squarekit' ); ?></option>
                            <option value="square_wins"><?php esc_html_e( 'Use Square Quantity', 'squarekit' ); ?></option>
                            <option value="manual"><?php esc_html_e( 'Set Manual Quantity', 'squarekit' ); ?></option>
                        </select>
                    </td>
                </tr>
                <tr id="manual-quantity-row" style="display: none;">
                    <th scope="row"><?php esc_html_e( 'Manual Quantity:', 'squarekit' ); ?></th>
                    <td>
                        <input type="number" name="manual_quantity" id="manual-quantity" min="0" value="0">
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="resolve_conflict" class="button button-primary" value="<?php esc_attr_e( 'Resolve Conflict', 'squarekit' ); ?>">
                <button type="button" class="button cancel-resolution"><?php esc_html_e( 'Cancel', 'squarekit' ); ?></button>
            </p>
        </form>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    border-radius: 5px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.conflict-high {
    color: #d63638;
    font-weight: bold;
}

.conflict-low {
    color: #00a32a;
    font-weight: bold;
}

.status-resolved {
    color: #00a32a;
    font-weight: bold;
}

.status-unresolved {
    color: #d63638;
    font-weight: bold;
}
</style>

<script>
jQuery(function($){
    // Conflict resolution modal
    $('.resolve-conflict').on('click', function(){
        var conflictIndex = $(this).data('conflict');
        var conflicts = <?php echo wp_json_encode( $paged_conflicts ); ?>;
        var conflict = conflicts[conflictIndex];
        
        $('#conflict-index').val(conflictIndex);
        $('#conflict-product-name').text(conflict.product_name);
        $('#conflict-wc-quantity').text(conflict.wc_quantity);
        $('#conflict-square-quantity').text(conflict.square_quantity);
        $('#conflict-difference').text(conflict.difference);
        $('#manual-quantity').val(Math.max(conflict.wc_quantity, conflict.square_quantity));
        
        $('#conflict-resolution-modal').show();
    });
    
    $('.close, .cancel-resolution').on('click', function(){
        $('#conflict-resolution-modal').hide();
    });
    
    $(window).on('click', function(event){
        if (event.target == document.getElementById('conflict-resolution-modal')) {
            $('#conflict-resolution-modal').hide();
        }
    });
    
    // Show/hide manual quantity field
    $('#conflict-resolution').on('change', function(){
        if ($(this).val() === 'manual') {
            $('#manual-quantity-row').show();
        } else {
            $('#manual-quantity-row').hide();
        }
    });
});
</script> 