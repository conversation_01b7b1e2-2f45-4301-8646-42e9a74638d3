<?php
/**
 * Onboarding & Welcome Wizard UI
 *
 * @package SquareKit
 * @subpackage SquareKit/admin/partials
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Get current step
$step = isset( $_GET['step'] ) ? sanitize_text_field( $_GET['step'] ) : 'welcome';
$steps = array(
    'welcome'      => __( 'Welcome', 'squarekit' ),
    'connect'      => __( 'Connect to Square', 'squarekit' ),
    'sync'         => __( 'Sync Preferences', 'squarekit' ),
    'location'     => __( 'Location & Payment', 'squarekit' ),
    'import'       => __( 'Initial Import', 'squarekit' ),
    'review'       => __( 'Review & Finish', 'squarekit' ),
);

function squarekit_wizard_step_url( $step ) {
    return admin_url( 'admin.php?page=squarekit-wizard&step=' . $step );
}
?>
<style>
body.squarekit-wizard-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
}
.squarekit-wizard-container {
    background: #fff;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15), 0 8px 32px rgba(0,0,0,0.1);
    padding: 3em 3em 2.5em 3em;
    max-width: 900px;
    margin: 2em auto;
    position: relative;
    animation: fadeInUp 0.8s cubic-bezier(.4,0,.2,1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
.squarekit-wizard-title {
    font-size: 2.4rem;
    font-weight: 900;
    margin-bottom: 2.5em;
    text-align: center;
    letter-spacing: -1px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    line-height: 1.2;
    padding: 0 1em;
}
.squarekit-wizard-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}
.squarekit-wizard-stepper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2em 0 3.5em 0;
    position: relative;
    gap: 1em;
    padding: 1.5em 20px 0 20px;
}
.squarekit-wizard-progress {
    position: absolute;
    left: 60px; right: 60px; top: 50%;
    height: 6px;
    background: linear-gradient(90deg, #f1f5f9 0%, #e2e8f0 100%);
    z-index: 0;
    border-radius: 10px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.squarekit-wizard-step {
    flex: 1 1 0;
    text-align: center;
    position: relative;
    z-index: 2;
    background: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 0;
    max-width: 140px;
    transition: transform 0.3s ease;
}
.squarekit-wizard-step:hover {
    transform: translateY(-2px);
}
.squarekit-wizard-step .icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px; height: 50px;
    border-radius: 50%;
    font-size: 1.4em;
    margin-bottom: 0.8em;
    border: 3px solid #e2e8f0;
    background: #fff !important;
    color: #94a3b8;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    position: relative;
}
.squarekit-wizard-step.active .icon {
    border-color: #667eea;
    color: #667eea;
    background: #fff !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transform: scale(1.1);
}
.squarekit-wizard-step.completed .icon {
    border-color: #10b981;
    color: #10b981;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: #fff;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}
.squarekit-wizard-step.completed .icon:before {
    content: '\f147'; /* dashicons-yes */
    font-family: dashicons;
    font-size: 1.3em;
    color: #fff;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.squarekit-wizard-step.completed .icon > * { display: none; }
.squarekit-wizard-step-label {
    font-size: 1em;
    color: #64748b;
    font-weight: 600;
    margin-top: 0.2em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
}
.squarekit-wizard-step.active .squarekit-wizard-step-label {
    color: #667eea;
    font-weight: 700;
    transform: scale(1.05);
}
.squarekit-wizard-step.completed .squarekit-wizard-step-label {
    color: #10b981;
    font-weight: 700;
}
.squarekit-wizard-status {
    margin: 2em 0 1.5em 0;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 1em;
    padding: 1.2em 1.5em;
    border-radius: 16px;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
    animation: slideIn 0.6s cubic-bezier(.4,0,.2,1);
    border: 1px solid rgba(220, 38, 38, 0.2);
}
.squarekit-wizard-status.connected {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    color: #16a34a;
    box-shadow: 0 4px 12px rgba(22, 163, 74, 0.1);
    border: 1px solid rgba(22, 163, 74, 0.2);
}
.squarekit-wizard-status .icon {
    font-size: 1.6em;
    margin-right: 0.2em;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}
@keyframes slideIn {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
}
.squarekit-wizard-content {
    margin-top: 2em;
    min-height: 200px;
    animation: fadeInContent 0.6s cubic-bezier(.4,0,.2,1);
    padding: 0;
}
.squarekit-wizard-content h2 {
    color: #1e293b;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1em;
    letter-spacing: -0.5px;
}
.squarekit-wizard-content p {
    color: #64748b;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5em;
}
@keyframes fadeInContent {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
.squarekit-wizard-buttons {
    display: flex;
    gap: 1.2em;
    margin-top: 3em;
    justify-content: flex-end;
}
.squarekit-wizard-buttons .button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: #fff;
    font-weight: 700;
    border-radius: 12px;
    padding: 1em 2.5em;
    font-size: 1.1em;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.squarekit-wizard-buttons .button-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}
.squarekit-wizard-buttons .button-primary:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    color: #fff;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}
.squarekit-wizard-buttons .button {
    border-radius: 12px;
    font-size: 1.1em;
    padding: 1em 2.5em;
    border: 2px solid #667eea;
    background: #fff;
    color: #667eea;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.squarekit-wizard-buttons .button:hover:not(:disabled) {
    background: #667eea;
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}
/* Form styling improvements */
.squarekit-wizard-content .form-table {
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
}
.squarekit-wizard-content .form-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    font-weight: 700;
    padding: 1.2em 1.5em;
    border-bottom: 1px solid #e2e8f0;
}
.squarekit-wizard-content .form-table td {
    padding: 1.2em 1.5em;
    border-bottom: 1px solid #f1f5f9;
}
/* Modern Toggle Switch Styles */
.squarekit-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    user-select: none;
}

.squarekit-toggle input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.squarekit-toggle-switch {
    position: relative;
    width: 48px;
    height: 24px;
    background: #e2e8f0;
    border-radius: 24px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
}

.squarekit-toggle-switch::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.squarekit-toggle input[type="checkbox"]:checked + .squarekit-toggle-switch {
    background: #3b82f6;
    border-color: #2563eb;
}

.squarekit-toggle input[type="checkbox"]:checked + .squarekit-toggle-switch::before {
    transform: translateX(24px);
}

.squarekit-toggle input[type="checkbox"]:focus + .squarekit-toggle-switch {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.squarekit-toggle input[type="checkbox"]:disabled + .squarekit-toggle-switch {
    opacity: 0.5;
    cursor: not-allowed;
}

.squarekit-toggle-label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.squarekit-toggle input[type="checkbox"]:checked ~ .squarekit-toggle-label {
    color: #1f2937;
}

/* Modern Radio Button Styles */
.squarekit-radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.squarekit-radio {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    user-select: none;
}

.squarekit-radio input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.squarekit-radio-button {
    position: relative;
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.squarekit-radio-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: #3b82f6;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.squarekit-radio input[type="radio"]:checked + .squarekit-radio-button {
    border-color: #3b82f6;
    background: white;
}

.squarekit-radio input[type="radio"]:checked + .squarekit-radio-button::before {
    transform: translate(-50%, -50%) scale(1);
}

.squarekit-radio input[type="radio"]:focus + .squarekit-radio-button {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.squarekit-radio input[type="radio"]:disabled + .squarekit-radio-button {
    opacity: 0.5;
    cursor: not-allowed;
}

.squarekit-radio-label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.squarekit-radio input[type="radio"]:checked ~ .squarekit-radio-label {
    color: #1f2937;
}
.squarekit-wizard-content select {
    padding: 0.8em 1em;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    font-size: 1em;
    width: 100%;
    transition: border-color 0.3s ease;
}
.squarekit-wizard-content select:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

@media (max-width: 900px) {
    .squarekit-wizard-container {
        max-width: 95vw;
        margin: 1em auto;
        padding: 2em 1.5em;
    }
    .squarekit-wizard-stepper {
        gap: 0.5em;
        padding: 0 10px;
    }
    .squarekit-wizard-step-label {
        font-size: 0.9em;
    }
    .squarekit-wizard-step .icon {
        width: 45px;
        height: 45px;
    }
}
@media (max-width: 600px) {
    .squarekit-wizard-container {
        padding: 1.5em 1em;
        border-radius: 16px;
        margin: 1em auto;
    }
    .squarekit-wizard-title {
        font-size: 1.8rem;
        margin-bottom: 2em;
        letter-spacing: -0.5px;
        padding: 0 0.5em;
        line-height: 1.3;
    }
    .squarekit-wizard-content {
        padding: 1.5em;
        margin-top: 1.5em;
    }
    .squarekit-wizard-step-label {
        font-size: 0.8em;
    }
    .squarekit-wizard-buttons {
        flex-direction: column-reverse;
        gap: 1em;
    }
    .squarekit-wizard-buttons .button,
    .squarekit-wizard-buttons .button-primary {
        font-size: 1em;
        padding: 0.9em 1.5em;
        width: 100%;
        text-align: center;
    }
    .squarekit-wizard-step {
        max-width: 90px;
    }
    .squarekit-wizard-step .icon {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
    }
    .squarekit-wizard-progress {
        left: 40px;
        right: 40px;
    }
}
</style>
<script>document.body.classList.add('squarekit-wizard-bg');</script>
<div class="squarekit-wizard-container">
    <div class="squarekit-wizard-title">Square Kit Setup Wizard</div>
    <?php
    $step_labels = [
        'welcome' => ['icon' => 'dashicons-sos', 'label' => 'Welcome'],
        'connect' => ['icon' => 'dashicons-admin-links', 'label' => 'Connect to Square'],
        'sync' => ['icon' => 'dashicons-update', 'label' => 'Sync Preferences'],
        'location' => ['icon' => 'dashicons-location-alt', 'label' => 'Location & Payment'],
        'import' => ['icon' => 'dashicons-download', 'label' => 'Initial Import'],
        'review' => ['icon' => 'dashicons-yes', 'label' => 'Review & Finish'],
    ];
    $step_keys = array_keys($step_labels);
    $current_index = array_search($step, $step_keys);
    $progress = ($current_index) / (count($step_keys) - 1) * 100;
    ?>
    <div class="squarekit-wizard-stepper">
        <div class="squarekit-wizard-progress"></div>
        <?php foreach ($step_labels as $key => $info):
            $index = array_search($key, $step_keys);
            $is_active = $key === $step;
            $is_completed = $index < $current_index;
        ?>
            <div class="squarekit-wizard-step<?php echo $is_active ? ' active' : ($is_completed ? ' completed' : ''); ?>">
                <span class="icon dashicons <?php echo esc_attr($info['icon']); ?>"><?php if (!$is_completed) { ?><span></span><?php } ?></span>
                <div class="squarekit-wizard-step-label"><?php echo esc_html($info['label']); ?></div>
            </div>
        <?php endforeach; ?>
    </div>
    <div class="squarekit-wizard-content">
        <?php
        switch ( $step ) {
            case 'welcome':
                ?>
                <h2><?php esc_html_e( 'Welcome to Square Kit!', 'squarekit' ); ?></h2>
                <p><?php esc_html_e( 'This wizard will help you connect your store to Square and configure sync settings.', 'squarekit' ); ?></p>
                <a class="button button-primary" href="<?php echo esc_url( squarekit_wizard_step_url( 'connect' ) ); ?>"><?php esc_html_e( 'Get Started', 'squarekit' ); ?></a>
                <?php
                break;
            case 'connect':
                $settings = new SquareKit_Settings();
                $environment = $settings->get_environment();

                // For wizard, check if we have OAuth tokens (location_id is set in later step)
                $has_oauth_tokens = false;
                if ( class_exists( 'SquareKit_Secure_Storage' ) ) {
                    $has_oauth_tokens = SquareKit_Secure_Storage::has_tokens( $environment );
                }

                // Use OAuth token check for wizard, full connection check for other contexts
                $is_connected = $has_oauth_tokens;

                // Get client credentials using the same logic as token exchange
                $secure_credentials = SquareKit_Secure_Storage::get_client_credentials( $environment );
                if ( $secure_credentials ) {
                    $client_id = $secure_credentials['client_id'];
                } else {
                    $client_id = $settings->get( $environment . '_application_id', '' );
                }
                // Store redirect URI to ensure exact match between authorization and token exchange
                $redirect_uri_raw = admin_url( 'admin.php?page=squarekit-wizard&step=connect' );
                $oauth_base_url = ( $environment === 'sandbox' ? 'https://connect.squareupsandbox.com' : 'https://connect.squareup.com' );
                $oauth_url = $oauth_base_url . '/oauth2/authorize?client_id=' . esc_attr( $client_id ) . '&scope=MERCHANT_PROFILE_READ+PAYMENTS_READ+PAYMENTS_WRITE+ORDERS_READ+ORDERS_WRITE+CUSTOMERS_READ+CUSTOMERS_WRITE+INVENTORY_READ+INVENTORY_WRITE+ITEMS_READ+ITEMS_WRITE&session=false&state=' . wp_create_nonce( 'squarekit_oauth' ) . '&redirect_uri=' . urlencode( $redirect_uri_raw );
                ?>
                <h2><?php esc_html_e( 'Connect to Square', 'squarekit' ); ?></h2>
                <p><?php esc_html_e( 'Connect your Square account to begin syncing.', 'squarekit' ); ?></p>
                <div id="squarekit-oauth-status">
                    <?php if ( $is_connected ) : ?>
                        <div class="squarekit-wizard-status connected"><span class="icon dashicons dashicons-yes"></span> <?php esc_html_e( 'Connected to Square!', 'squarekit' ); ?></div>
                    <?php else : ?>
                        <div class="squarekit-wizard-status"><span class="icon dashicons dashicons-no"></span> <?php esc_html_e( 'Not connected.', 'squarekit' ); ?></div>
                    <?php endif; ?>
                </div>
                <?php if ( ! $is_connected ) : ?>
                    <a class="squarekit-action-btn primary" href="<?php echo esc_url( $oauth_url ); ?>">
                        <span class="dashicons dashicons-admin-links"></span>
                        <?php esc_html_e( 'Connect to Square', 'squarekit' ); ?>
                    </a>
                    <?php
                    // Log that user is viewing the connect step
                    $logger = SquareKit_Logger::get_instance();
                    $logger->log_oauth( 'info', 'User viewing OAuth connect step', array(
                        'oauth_url' => $oauth_url,
                        'environment' => $settings->get_environment(),
                        'client_id_configured' => ! empty( $client_id ),
                        'client_id_preview' => ! empty( $client_id ) ? substr( $client_id, 0, 10 ) . '...' : 'empty',
                        'client_secret_configured' => ! empty( $settings->get( $settings->get_environment() . '_client_secret' ) ),
                        'using_secure_credentials_for_auth_url' => $secure_credentials !== false,
                        'redirect_uri_raw' => $redirect_uri_raw,
                        'redirect_uri_encoded' => urlencode( $redirect_uri_raw ),
                        'auth_url_credential_validation' => array(
                            'client_id_length' => strlen( $client_id ),
                            'client_id_format_valid' => strpos( $client_id, 'sandbox-sq0idb-' ) === 0 || strpos( $client_id, 'sq0idb-' ) === 0
                        )
                    ) );
                    ?>
                <?php endif; ?>
                <?php
                // Initialize logger
                $logger = SquareKit_Logger::get_instance();

                // Log wizard page load for testing
                $logger->log_oauth( 'debug', 'Wizard connect step loaded', array(
                    'user_id' => get_current_user_id(),
                    'timestamp' => current_time( 'mysql' ),
                    'has_oauth_tokens' => $has_oauth_tokens,
                    'is_connected' => $is_connected,
                    'environment' => $environment
                ) );

                // Handle OAuth callback
                if ( isset( $_GET['code'] ) && isset( $_GET['state'] ) && wp_verify_nonce( $_GET['state'], 'squarekit_oauth' ) ) {
                    $code = sanitize_text_field( $_GET['code'] );
                    $environment = $settings->get_environment();

                    // Get client credentials from secure storage (constants/env vars) or fallback to settings
                    $secure_credentials = SquareKit_Secure_Storage::get_client_credentials( $environment );
                    if ( $secure_credentials ) {
                        $client_id = $secure_credentials['client_id'];
                        $client_secret = $secure_credentials['client_secret'];
                        $logger->log_oauth( 'info', 'Using secure client credentials from constants/environment', array(
                            'environment' => $environment
                        ) );
                    } else {
                        $client_secret = $settings->get( $environment . '_client_secret', '' );
                        $client_id = $settings->get( $environment . '_application_id', '' );
                        $logger->log_oauth( 'warning', 'Using client credentials from database (not secure)', array(
                            'environment' => $environment
                        ) );
                    }
                    $token_url = ( $environment === 'sandbox' ? 'https://connect.squareupsandbox.com/oauth2/token' : 'https://connect.squareup.com/oauth2/token' );

                    // Debug logging with full details
                    $logger->log_oauth( 'debug', 'OAuth callback received', array(
                        'environment' => $environment,
                        'client_id_preview' => substr( $client_id, 0, 10 ) . '...',
                        'client_secret_exists' => ! empty( $client_secret ),
                        'token_url' => $token_url,
                        'auth_code_preview' => substr( $code, 0, 10 ) . '...',
                        'auth_code_length' => strlen( $code ),
                        'full_auth_code' => $code, // Log full code for debugging
                        'get_params' => $_GET
                    ) );

                    // Get the redirect URI (must match EXACTLY the one used in authorization request)
                    // Use the same raw URL as used in authorization (before URL encoding)
                    $redirect_uri_for_token = admin_url( 'admin.php?page=squarekit-wizard&step=connect' );

                    $request_body = array(
                        'client_id' => $client_id,
                        'client_secret' => $client_secret,
                        'code' => $code,
                        'grant_type' => 'authorization_code',
                        'redirect_uri' => $redirect_uri_for_token,
                    );

                    // Log the exact request being sent (including full credentials for debugging)
                    $logger->log_oauth( 'debug', 'Sending token exchange request', array(
                        'url' => $token_url,
                        'request_body' => array(
                            'client_id' => $client_id,
                            'client_secret' => substr( $client_secret, 0, 10 ) . '...',
                            'code' => $code,
                            'grant_type' => 'authorization_code',
                            'redirect_uri' => $redirect_uri_for_token
                        ),
                        'redirect_uri_comparison' => array(
                            'auth_url_used' => $redirect_uri_raw,
                            'token_exchange_used' => $redirect_uri_for_token,
                            'match' => $redirect_uri_raw === $redirect_uri_for_token
                        ),
                        'credential_validation' => array(
                            'client_id_length' => strlen( $client_id ),
                            'client_secret_length' => strlen( $client_secret ),
                            'client_id_format_valid' => strpos( $client_id, 'sandbox-sq0idb-' ) === 0,
                            'client_secret_format_valid' => strpos( $client_secret, 'sandbox-sq0csb-' ) === 0
                        )
                    ) );

                    $response = wp_remote_post( $token_url, array(
                        'headers' => array( 'Content-Type' => 'application/json' ),
                        'body' => wp_json_encode( $request_body ),
                        'timeout' => 30,
                    ) );

                    if ( is_wp_error( $response ) ) {
                        $logger->log_oauth( 'error', 'WordPress HTTP error during token exchange', array(
                            'error_message' => $response->get_error_message(),
                            'error_code' => $response->get_error_code()
                        ) );
                        echo '<div class="notice notice-error"><p>' . esc_html( $response->get_error_message() ) . '</p></div>';
                    } else {
                        $response_code = wp_remote_retrieve_response_code( $response );
                        $body = json_decode( wp_remote_retrieve_body( $response ), true );

                        $logger->log_oauth( 'debug', 'Token exchange response received', array(
                            'response_code' => $response_code,
                            'response_body' => wp_remote_retrieve_body( $response )
                        ) );

                        if ( ! empty( $body['access_token'] ) ) {
                            // Store tokens securely using encryption
                            $token_stored = SquareKit_Secure_Storage::store_oauth_tokens(
                                $body['access_token'],
                                $body['refresh_token'] ?? '',
                                $environment
                            );

                            if ( $token_stored ) {
                                // Also store merchant ID (not sensitive)
                                if ( ! empty( $body['merchant_id'] ) ) {
                                    $settings->set( 'merchant_id', $body['merchant_id'] );
                                }

                                // Clear any old unencrypted tokens from settings
                                $settings->delete( $environment . '_access_token' );
                                $settings->delete( $environment . '_refresh_token' );
                            }
                            $logger->log_oauth( 'info', 'OAuth connection successful', array(
                                'environment' => $environment,
                                'merchant_id' => $body['merchant_id'] ?? 'not_provided',
                                'has_refresh_token' => ! empty( $body['refresh_token'] )
                            ) );
                            echo '<div class="notice notice-success"><p>' . esc_html__( 'Successfully connected to Square!', 'squarekit' ) . '</p></div>';
                            // Redirect to remove code from URL
                            ?>
                            <script>
                            var redirectUrl = '<?php echo esc_js( squarekit_wizard_step_url( 'connect' ) ); ?>';
                            setTimeout(function(){
                                window.location.href = redirectUrl;
                            }, 1000);
                            </script>
                            <?php
                        } else {
                            $logger->log_oauth( 'error', 'No access token in response', array(
                                'response_body' => $body
                            ) );
                            if ( ! empty( $body['error'] ) ) {
                                $logger->log_oauth( 'error', 'Square API error', array(
                                    'error' => $body['error'],
                                    'error_description' => $body['error_description'] ?? 'no_description'
                                ) );
                                echo '<div class="notice notice-error"><p>' . esc_html__( 'Square Error: ', 'squarekit' ) . esc_html( $body['error'] ) . ( ! empty( $body['error_description'] ) ? ' - ' . esc_html( $body['error_description'] ) : '' ) . '</p></div>';
                            } else {
                                echo '<div class="notice notice-error"><p>' . esc_html__( 'Failed to connect to Square. Please try again.', 'squarekit' ) . '</p></div>';
                            }
                        }
                    }
                } elseif ( isset( $_GET['code'] ) || isset( $_GET['state'] ) ) {
                    // Debug OAuth callback issues
                    $logger->log_oauth( 'warning', 'OAuth callback validation failed', array(
                        'code_present' => isset( $_GET['code'] ),
                        'state_present' => isset( $_GET['state'] ),
                        'nonce_valid' => isset( $_GET['state'] ) ? wp_verify_nonce( $_GET['state'], 'squarekit_oauth' ) : false,
                        'get_params' => array_keys( $_GET )
                    ) );

                    if ( isset( $_GET['error'] ) ) {
                        $logger->log_oauth( 'error', 'OAuth authorization error', array(
                            'error' => sanitize_text_field( $_GET['error'] ),
                            'error_description' => isset( $_GET['error_description'] ) ? sanitize_text_field( $_GET['error_description'] ) : 'no_description'
                        ) );
                        echo '<div class="notice notice-error"><p>' . esc_html__( 'OAuth Error: ', 'squarekit' ) . esc_html( sanitize_text_field( $_GET['error'] ) ) . '</p></div>';
                    }
                }
                break;
            case 'sync':
                $settings = new SquareKit_Settings();
                $sync_products = $settings->get('sync_products', 'both');
                $sync_orders = $settings->get('sync_orders', true);
                $sync_customers = $settings->get('sync_customers', true);
                $enable_automatic_sync = $settings->get('enable_automatic_sync', true);


                if ( isset($_POST['squarekit_sync_nonce']) && wp_verify_nonce($_POST['squarekit_sync_nonce'], 'squarekit_sync_prefs') ) {
                    $sync_products = isset($_POST['sync_products']) ? sanitize_text_field($_POST['sync_products']) : 'both';
                    $sync_orders = isset($_POST['sync_orders']) ? (bool) $_POST['sync_orders'] : false;
                    $sync_customers = isset($_POST['sync_customers']) ? (bool) $_POST['sync_customers'] : false;
                    $enable_automatic_sync = isset($_POST['enable_automatic_sync']) ? (bool) $_POST['enable_automatic_sync'] : false;

                    // Debug: Check current settings before save
                    $current_sync_products = $settings->get('sync_products', 'not_set');
                    echo '<div class="notice notice-info"><p>DEBUG: Current sync_products before save: ' . esc_html($current_sync_products) . '</p></div>';

                    // Convert wizard selections to sync direction settings
                    $sync_direction_settings = array(
                        'woo_to_square' => false,
                        'square_to_woo' => false,
                    );

                    // Map sync_products setting to direction controls
                    switch ($sync_products) {
                        case 'both':
                            $sync_direction_settings['woo_to_square'] = true;
                            $sync_direction_settings['square_to_woo'] = true;
                            break;
                        case 'square_to_woo':
                            $sync_direction_settings['square_to_woo'] = true;
                            break;
                        case 'woo_to_square':
                            $sync_direction_settings['woo_to_square'] = true;
                            break;
                    }

                    // Save the settings (both old format for compatibility and new format)
                    $settings->update([
                        'sync_products' => $sync_products,
                        'sync_orders' => $sync_orders,
                        'sync_customers' => $sync_customers,
                        'enable_automatic_sync' => $enable_automatic_sync,
                        // New sync direction controls
                        'sync_woo_to_square' => $sync_direction_settings['woo_to_square'],
                        'sync_square_to_woo' => $sync_direction_settings['square_to_woo'],
                    ]);

                    // Verify the settings were saved by checking the values
                    $saved_sync_products = $settings->get('sync_products');
                    $saved_sync_orders = $settings->get('sync_orders');
                    $saved_sync_customers = $settings->get('sync_customers');
                    $saved_enable_automatic_sync = $settings->get('enable_automatic_sync');

                    // Check if the values match what we tried to save
                    $save_successful = (
                        $saved_sync_products === $sync_products &&
                        $saved_sync_orders === $sync_orders &&
                        $saved_sync_customers === $sync_customers &&
                        $saved_enable_automatic_sync === $enable_automatic_sync
                    );

                    echo '<div class="notice notice-success"><p>' . esc_html__('Preferences saved.', 'squarekit') . '</p></div>';
                    echo '<div class="notice notice-info"><p>DEBUG: Save verification: ' . ($save_successful ? 'SUCCESS' : 'FAILED') . '</p></div>';
                    echo '<div class="notice notice-info"><p>DEBUG: Saved values - Products: ' . esc_html($sync_products) . ', Orders: ' . ($sync_orders ? 'Yes' : 'No') . ', Customers: ' . ($sync_customers ? 'Yes' : 'No') . ', Auto: ' . ($enable_automatic_sync ? 'Yes' : 'No') . '</p></div>';

                    // Use PHP redirect instead of JavaScript
                    wp_redirect( squarekit_wizard_step_url( 'location' ) );
                    exit;
                }
                ?>
                <h2><?php esc_html_e( 'Sync Preferences', 'squarekit' ); ?></h2>
                <p><?php esc_html_e( 'Choose what you want to sync between WooCommerce and Square.', 'squarekit' ); ?></p>
                <form id="squarekit-sync-form" method="post" action="">
                    <?php wp_nonce_field('squarekit_sync_prefs', 'squarekit_sync_nonce'); ?>
                    <table class="form-table">
                        <tr>
                            <th><?php esc_html_e('Products', 'squarekit'); ?></th>
                            <td>
                                <div class="squarekit-radio-group">
                                    <label class="squarekit-radio">
                                        <input type="radio" name="sync_products" value="both" <?php checked($sync_products, 'both'); ?> />
                                        <span class="squarekit-radio-button"></span>
                                        <span class="squarekit-radio-label"><?php esc_html_e('Both directions', 'squarekit'); ?></span>
                                    </label>
                                    <label class="squarekit-radio">
                                        <input type="radio" name="sync_products" value="wc_to_square" <?php checked($sync_products, 'wc_to_square'); ?> />
                                        <span class="squarekit-radio-button"></span>
                                        <span class="squarekit-radio-label"><?php esc_html_e('WooCommerce → Square', 'squarekit'); ?></span>
                                    </label>
                                    <label class="squarekit-radio">
                                        <input type="radio" name="sync_products" value="square_to_wc" <?php checked($sync_products, 'square_to_wc'); ?> />
                                        <span class="squarekit-radio-button"></span>
                                        <span class="squarekit-radio-label"><?php esc_html_e('Square → WooCommerce', 'squarekit'); ?></span>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th><?php esc_html_e('Orders', 'squarekit'); ?></th>
                            <td>
                                <label class="squarekit-toggle">
                                    <input type="checkbox" name="sync_orders" value="1" <?php checked($sync_orders, true); ?> />
                                    <span class="squarekit-toggle-switch"></span>
                                    <span class="squarekit-toggle-label"><?php esc_html_e('Enable order sync', 'squarekit'); ?></span>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th><?php esc_html_e('Customers', 'squarekit'); ?></th>
                            <td>
                                <label class="squarekit-toggle">
                                    <input type="checkbox" name="sync_customers" value="1" <?php checked($sync_customers, true); ?> />
                                    <span class="squarekit-toggle-switch"></span>
                                    <span class="squarekit-toggle-label"><?php esc_html_e('Enable customer sync', 'squarekit'); ?></span>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th><?php esc_html_e('Automatic Sync', 'squarekit'); ?></th>
                            <td>
                                <label class="squarekit-toggle">
                                    <input type="checkbox" name="enable_automatic_sync" value="1" <?php checked($enable_automatic_sync, true); ?> />
                                    <span class="squarekit-toggle-switch"></span>
                                    <span class="squarekit-toggle-label"><?php esc_html_e('Enable automatic sync (recommended)', 'squarekit'); ?></span>
                                </label>
                            </td>
                        </tr>
                    </table>
                </form>

                <?php
                break;
            case 'location':
                $settings = new SquareKit_Settings();
                $square_api = new SquareKit_Square_API();
                $selected_location = $settings->get('location_id', '');
                $payment_methods = $settings->get('payment_methods', array());
                $default_methods = array('google_pay' => false, 'apple_pay' => false, 'afterpay' => false);
                $payment_methods = wp_parse_args($payment_methods, $default_methods);
                $locations = array();
                $locations_error = '';
                // Fetch locations from Square API
                $locations_response = $square_api->get_locations();
                if (is_wp_error($locations_response)) {
                    $locations_error = $locations_response->get_error_message();
                } elseif (is_array($locations_response)) {
                    $locations = $locations_response;
                }
                if (isset($_POST['squarekit_location_nonce']) && wp_verify_nonce($_POST['squarekit_location_nonce'], 'squarekit_location_payment')) {
                    $selected_location = isset($_POST['location_id']) ? sanitize_text_field($_POST['location_id']) : '';
                    $payment_methods = array(
                        'google_pay' => isset($_POST['google_pay']),
                        'apple_pay' => isset($_POST['apple_pay']),
                        'afterpay' => isset($_POST['afterpay']),
                    );

                    $settings->update([
                        'location_id' => $selected_location,
                        'payment_methods' => $payment_methods,
                    ]);

                    echo '<div class="notice notice-success"><p>' . esc_html__('Location and payment methods saved.', 'squarekit') . '</p></div>';
                }
                ?>
                <h2><?php esc_html_e('Select Location & Payment Methods', 'squarekit'); ?></h2>
                <p><?php esc_html_e('Choose your Square location and enable payment methods.', 'squarekit'); ?></p>
                <form id="squarekit-location-form" method="post" action="">
                    <?php wp_nonce_field('squarekit_location_payment', 'squarekit_location_nonce'); ?>
                    <table class="form-table">
                        <tr>
                            <th><?php esc_html_e('Square Location', 'squarekit'); ?></th>
                            <td>
                                <?php if ($locations_error): ?>
                                    <span style="color:red;"><?php echo esc_html($locations_error); ?></span>
                                <?php elseif (empty($locations)): ?>
                                    <span><?php esc_html_e('No locations found. Please check your Square connection.', 'squarekit'); ?></span>
                                <?php else: ?>
                                    <select name="location_id" required>
                                        <option value=""><?php esc_html_e('-- Select Location --', 'squarekit'); ?></option>
                                        <?php foreach ($locations as $location): ?>
                                            <option value="<?php echo esc_attr($location['id']); ?>" <?php selected($selected_location, $location['id']); ?>>
                                                <?php echo esc_html($location['name'] . (!empty($location['address']) ? ' (' . $location['address']['address_line_1'] . ')' : '')); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th><?php esc_html_e('Payment Methods', 'squarekit'); ?></th>
                            <td>
                                <div style="display: flex; flex-direction: column; gap: 12px;">
                                    <label class="squarekit-toggle">
                                        <input type="checkbox" name="google_pay" <?php checked($payment_methods['google_pay']); ?> />
                                        <span class="squarekit-toggle-switch"></span>
                                        <span class="squarekit-toggle-label"><?php esc_html_e('Enable Google Pay', 'squarekit'); ?></span>
                                    </label>
                                    <label class="squarekit-toggle">
                                        <input type="checkbox" name="apple_pay" <?php checked($payment_methods['apple_pay']); ?> />
                                        <span class="squarekit-toggle-switch"></span>
                                        <span class="squarekit-toggle-label"><?php esc_html_e('Enable Apple Pay', 'squarekit'); ?></span>
                                    </label>
                                    <label class="squarekit-toggle">
                                        <input type="checkbox" name="afterpay" <?php checked($payment_methods['afterpay']); ?> />
                                        <span class="squarekit-toggle-switch"></span>
                                        <span class="squarekit-toggle-label"><?php esc_html_e('Enable Afterpay', 'squarekit'); ?></span>
                                    </label>
                                </div>
                            </td>
                        </tr>
                    </table>
                </form>
                <?php if (isset($_POST['squarekit_location_nonce']) && wp_verify_nonce($_POST['squarekit_location_nonce'], 'squarekit_location_payment')): ?>
                <script>
                var redirectUrl = <?php echo json_encode(admin_url('admin.php') . '?page=squarekit-wizard&step=import'); ?>;
                setTimeout(function() {
                    window.location.href = redirectUrl;
                }, 1000);
                </script>
                <?php endif; ?>
                <?php
                break;
            case 'import':
                $settings = new SquareKit_Settings();
                $has_imported_products = $settings->get('has_imported_products', false);
                $has_imported_customers = $settings->get('has_imported_customers', false);

                // Handle skip and finish
                if (isset($_POST['squarekit_skip_finish_nonce']) && wp_verify_nonce($_POST['squarekit_skip_finish_nonce'], 'squarekit_skip_finish')) {
                    $settings->mark_setup_complete();
                    ?>
                    <div class="squarekit-success-container" style="text-align: center; padding: 2em; margin: 2em 0; background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border-radius: 16px; box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2); border: 2px solid #10b981;">
                        <img src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'assets/images/success.gif'); ?>" alt="Success Animation" style="width: 80px; height: 80px; margin-bottom: 1em;" />
                        <h3 style="margin: 0 0 0.5em 0; color: #065f46; font-size: 1.4em; font-weight: 700;">
                            🎉 Square Kit Setup Completed Successfully!
                        </h3>
                        <p style="margin: 0; color: #047857; font-size: 1.1em; font-weight: 500;">
                            <?php esc_html_e('Redirecting you to the dashboard...', 'squarekit'); ?>
                        </p>
                    </div>
                    <?php
                }
                ?>
                <h2><?php esc_html_e('Initial Data Import (Optional)', 'squarekit'); ?></h2>
                <p><?php esc_html_e('Import products and customers from Square now, or skip and do it later.', 'squarekit'); ?></p>
                
                <div id="squarekit-import-container">
                    <div class="squarekit-import-option">
                        <h3><span class="dashicons dashicons-products" style="color: #667eea;"></span><?php esc_html_e('Products', 'squarekit'); ?></h3>
                        <div class="squarekit-import-status">
                            <?php if ($has_imported_products): ?>
                                <span class="squarekit-status-complete"><?php esc_html_e('Products imported', 'squarekit'); ?></span>
                            <?php else: ?>
                                <span class="squarekit-status-pending"><?php esc_html_e('Not imported yet', 'squarekit'); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="squarekit-import-progress" style="display:none;">
                            <div class="squarekit-progress-bar">
                                <div class="squarekit-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="squarekit-progress-status"></div>
                        </div>
                        <div class="squarekit-import-actions">
                            <?php if (!$has_imported_products): ?>
                                <button type="button" class="button" id="squarekit-import-products">
                                    <?php esc_html_e('Import Products from Square', 'squarekit'); ?>
                                </button>
                            <?php else: ?>
                                <button type="button" class="button" id="squarekit-reimport-products">
                                    <?php esc_html_e('Reimport Products', 'squarekit'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="squarekit-import-option">
                        <h3><span class="dashicons dashicons-category" style="color: #667eea;"></span><?php esc_html_e('Categories', 'squarekit'); ?></h3>
                        <div class="squarekit-import-status">
                            <span class="squarekit-status-pending"><?php esc_html_e('Not imported yet', 'squarekit'); ?></span>
                        </div>
                        <div class="squarekit-import-progress" style="display:none;">
                            <div class="squarekit-progress-bar">
                                <div class="squarekit-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="squarekit-progress-status"></div>
                        </div>
                        <div class="squarekit-import-actions">
                            <button type="button" class="button" id="squarekit-import-categories">
                                <?php esc_html_e('Import Categories from Square', 'squarekit'); ?>
                            </button>
                        </div>
                    </div>
                    
                    <div class="squarekit-import-option">
                        <h3><span class="dashicons dashicons-chart-bar" style="color: #667eea;"></span><?php esc_html_e('Inventory', 'squarekit'); ?></h3>
                        <div class="squarekit-import-status">
                            <span class="squarekit-status-pending"><?php esc_html_e('Not synced yet', 'squarekit'); ?></span>
                        </div>
                        <div class="squarekit-import-progress" style="display:none;">
                            <div class="squarekit-progress-bar">
                                <div class="squarekit-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="squarekit-progress-status"></div>
                        </div>
                        <div class="squarekit-import-actions">
                            <button type="button" class="button" id="squarekit-sync-inventory">
                                <?php esc_html_e('Sync Inventory from Square', 'squarekit'); ?>
                            </button>
                        </div>
                    </div>
                    
                    <div class="squarekit-import-option">
                        <h3><span class="dashicons dashicons-groups" style="color: #667eea;"></span><?php esc_html_e('Customers', 'squarekit'); ?></h3>
                        <div class="squarekit-import-status">
                            <?php if ($has_imported_customers): ?>
                                <span class="squarekit-status-complete"><?php esc_html_e('Customers imported', 'squarekit'); ?></span>
                            <?php else: ?>
                                <span class="squarekit-status-pending"><?php esc_html_e('Not imported yet', 'squarekit'); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="squarekit-import-progress" style="display:none;">
                            <div class="squarekit-progress-bar">
                                <div class="squarekit-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="squarekit-progress-status"></div>
                        </div>
                        <div class="squarekit-import-actions">
                            <?php if (!$has_imported_customers): ?>
                                <button type="button" class="button" id="squarekit-import-customers">
                                    <?php esc_html_e('Import Customers from Square', 'squarekit'); ?>
                                </button>
                            <?php else: ?>
                                <button type="button" class="button" id="squarekit-reimport-customers">
                                    <?php esc_html_e('Reimport Customers', 'squarekit'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Skip & Finish Form -->
                <form id="squarekit-skip-form" method="post" action="" style="display: none;">
                    <?php wp_nonce_field('squarekit_skip_finish', 'squarekit_skip_finish_nonce'); ?>
                </form>

                <?php if (isset($_POST['squarekit_skip_finish_nonce']) && wp_verify_nonce($_POST['squarekit_skip_finish_nonce'], 'squarekit_skip_finish')): ?>
                <script>
                var redirectUrl = '<?php echo esc_js(admin_url('admin.php?page=squarekit')); ?>';
                console.log('Setup completed via skip! Redirecting to: ' + redirectUrl);
                setTimeout(function(){
                    window.location.href = redirectUrl;
                },1200);
                </script>
                <?php endif; ?>

                <style>
                .squarekit-import-option {
                    margin-bottom: 1.5em;
                    padding: 2em;
                    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
                    border: 2px solid #e2e8f0;
                    border-radius: 20px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
                    transition: all 0.3s cubic-bezier(.4,0,.2,1);
                    position: relative;
                    overflow: hidden;
                }
                .squarekit-import-option:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                    border-color: #667eea;
                }
                .squarekit-import-option::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }
                .squarekit-import-option h3 {
                    color: #1e293b;
                    font-size: 1.3rem;
                    font-weight: 700;
                    margin-bottom: 1em;
                    display: flex;
                    align-items: center;
                    gap: 0.5em;
                }
                .squarekit-import-status {
                    margin: 1em 0 1.5em 0;
                }
                .squarekit-status-complete {
                    color: #16a34a;
                    font-weight: 700;
                    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                    padding: 0.5em 1em;
                    border-radius: 8px;
                    border: 1px solid #bbf7d0;
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5em;
                }
                .squarekit-status-complete::before {
                    content: '✓';
                    font-weight: bold;
                }
                .squarekit-status-pending {
                    color: #64748b;
                    font-weight: 600;
                    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                    padding: 0.5em 1em;
                    border-radius: 8px;
                    border: 1px solid #e2e8f0;
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5em;
                }
                .squarekit-status-pending::before {
                    content: '⏳';
                }
                .squarekit-progress-bar {
                    height: 12px;
                    background: linear-gradient(90deg, #f1f5f9 0%, #e2e8f0 100%);
                    border-radius: 20px;
                    margin: 1em 0;
                    overflow: hidden;
                    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
                }
                .squarekit-progress-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                    transition: width 0.6s cubic-bezier(.4,0,.2,1);
                    border-radius: 20px;
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                }
                .squarekit-progress-status {
                    font-style: italic;
                    font-size: 0.95em;
                    margin-bottom: 1em;
                    color: #64748b;
                    font-weight: 500;
                }
                .squarekit-import-actions button {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: #fff;
                    border: none;
                    padding: 0.8em 1.8em;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 0.95em;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(.4,0,.2,1);
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                .squarekit-import-actions button:hover:not(:disabled) {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
                }
                .squarekit-import-actions button:disabled {
                    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
                    cursor: not-allowed;
                    transform: none;
                    box-shadow: none;
                }
                .squarekit-wizard-nav {
                    margin-top: 2em;
                    display: flex;
                    justify-content: space-between;
                    gap: 1em;
                }

                /* Enhanced Connect Button */
                .squarekit-action-btn {
                    display: inline-flex !important;
                    align-items: center;
                    gap: 0.8em;
                    padding: 1.2em 2.5em !important;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    color: #fff !important;
                    text-decoration: none !important;
                    border-radius: 16px !important;
                    border: none !important;
                    cursor: pointer;
                    font-size: 1.1rem !important;
                    font-weight: 700 !important;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    transition: all 0.3s cubic-bezier(.4,0,.2,1) !important;
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
                    margin: 1.5em 0;
                }

                .squarekit-action-btn:hover {
                    transform: translateY(-3px) !important;
                    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5) !important;
                    color: #fff !important;
                }

                .squarekit-action-btn .dashicons {
                    font-size: 1.2em;
                }
                </style>
                
                <script>
                jQuery(document).ready(function($) {
                    var restUrl = '<?php echo esc_url(get_rest_url(null, 'squarekit/v1')); ?>';
                    var restNonce = '<?php echo esc_js(wp_create_nonce('wp_rest')); ?>';
                    
                    // Import products
                    $('#squarekit-import-products, #squarekit-reimport-products').on('click', function() {
                        var $btn = $(this);
                        var $option = $btn.closest('.squarekit-import-option');
                        var $progress = $option.find('.squarekit-import-progress');
                        var $progressFill = $progress.find('.squarekit-progress-fill');
                        var $progressStatus = $progress.find('.squarekit-progress-status');
                        
                        $btn.prop('disabled', true);
                        $progress.show();
                        $progressStatus.text('<?php echo esc_js(__('Starting import...', 'squarekit')); ?>');
                        
                        // Start the import process
                        $.ajax({
                            url: restUrl + '/inventory',
                            method: 'POST',
                            beforeSend: function(xhr) {
                                xhr.setRequestHeader('X-WP-Nonce', restNonce);
                            },
                            data: {
                                action: 'start_import'
                            },
                            success: function(response) {
                                if (response.success) {
                                    checkImportProgress('products', $progressFill, $progressStatus, $btn, $option);
                                } else {
                                    $progressStatus.text('<?php echo esc_js(__('Error starting import.', 'squarekit')); ?>');
                                    $btn.prop('disabled', false);
                                }
                            },
                            error: function() {
                                $progressStatus.text('<?php echo esc_js(__('Error connecting to server.', 'squarekit')); ?>');
                                $btn.prop('disabled', false);
                            }
                        });
                    });
                    
                    // Import customers
                    $('#squarekit-import-customers, #squarekit-reimport-customers').on('click', function() {
                        var $btn = $(this);
                        var $option = $btn.closest('.squarekit-import-option');
                        var $progress = $option.find('.squarekit-import-progress');
                        var $progressFill = $progress.find('.squarekit-progress-fill');
                        var $progressStatus = $progress.find('.squarekit-progress-status');
                        
                        $btn.prop('disabled', true);
                        $progress.show();
                        $progressStatus.text('<?php echo esc_js(__('Starting import...', 'squarekit')); ?>');
                        
                        // Start the import process
                        $.ajax({
                            url: restUrl + '/customers',
                            method: 'POST',
                            beforeSend: function(xhr) {
                                xhr.setRequestHeader('X-WP-Nonce', restNonce);
                            },
                            data: {
                                action: 'start_import'
                            },
                            success: function(response) {
                                if (response.success) {
                                    checkImportProgress('customers', $progressFill, $progressStatus, $btn, $option);
                                } else {
                                    $progressStatus.text('<?php echo esc_js(__('Error starting import.', 'squarekit')); ?>');
                                    $btn.prop('disabled', false);
                                }
                            },
                            error: function() {
                                $progressStatus.text('<?php echo esc_js(__('Error connecting to server.', 'squarekit')); ?>');
                                $btn.prop('disabled', false);
                            }
                        });
                    });
                    
                    // Import categories
                    $('#squarekit-import-categories').on('click', function() {
                        var $btn = $(this);
                        var $option = $btn.closest('.squarekit-import-option');
                        var $progress = $option.find('.squarekit-import-progress');
                        var $progressFill = $progress.find('.squarekit-progress-fill');
                        var $progressStatus = $progress.find('.squarekit-progress-status');
                        
                        $btn.prop('disabled', true);
                        $progress.show();
                        $progressStatus.text('<?php echo esc_js(__('Starting import...', 'squarekit')); ?>');
                        
                        // Start the import process
                        $.ajax({
                            url: restUrl + '/inventory',
                            method: 'POST',
                            beforeSend: function(xhr) {
                                xhr.setRequestHeader('X-WP-Nonce', restNonce);
                            },
                            data: {
                                action: 'import_categories'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $progressFill.css('width', '100%');
                                    $progressStatus.text('<?php echo esc_js(__('Import complete!', 'squarekit')); ?>');
                                    $option.find('.squarekit-import-status').html('<span class="squarekit-status-complete"><?php echo esc_js(__('Categories imported', 'squarekit')); ?></span>');
                                    $btn.prop('disabled', false);
                                } else {
                                    $progressStatus.text('<?php echo esc_js(__('Error starting import.', 'squarekit')); ?>');
                                    $btn.prop('disabled', false);
                                }
                            },
                            error: function() {
                                $progressStatus.text('<?php echo esc_js(__('Error connecting to server.', 'squarekit')); ?>');
                                $btn.prop('disabled', false);
                            }
                        });
                    });
                    
                    // Sync inventory
                    $('#squarekit-sync-inventory').on('click', function() {
                        var $btn = $(this);
                        var $option = $btn.closest('.squarekit-import-option');
                        var $progress = $option.find('.squarekit-import-progress');
                        var $progressFill = $progress.find('.squarekit-progress-fill');
                        var $progressStatus = $progress.find('.squarekit-progress-status');
                        
                        $btn.prop('disabled', true);
                        $progress.show();
                        $progressStatus.text('<?php echo esc_js(__('Starting sync...', 'squarekit')); ?>');
                        
                        // Start the sync process
                        $.ajax({
                            url: restUrl + '/inventory',
                            method: 'POST',
                            beforeSend: function(xhr) {
                                xhr.setRequestHeader('X-WP-Nonce', restNonce);
                            },
                            data: {
                                action: 'import_inventory'
                            },
                            success: function(response) {
                                if (response.success) {
                                    $progressFill.css('width', '100%');
                                    $progressStatus.text('<?php echo esc_js(__('Sync complete!', 'squarekit')); ?>');
                                    $option.find('.squarekit-import-status').html('<span class="squarekit-status-complete"><?php echo esc_js(__('Inventory synced', 'squarekit')); ?></span>');
                                    $btn.prop('disabled', false);
                                } else {
                                    $progressStatus.text('<?php echo esc_js(__('Error starting sync.', 'squarekit')); ?>');
                                    $btn.prop('disabled', false);
                                }
                            },
                            error: function() {
                                $progressStatus.text('<?php echo esc_js(__('Error connecting to server.', 'squarekit')); ?>');
                                $btn.prop('disabled', false);
                            }
                        });
                    });
                    
                    // Check import progress
                    function checkImportProgress(type, $progressFill, $progressStatus, $btn, $option) {
                        var endpoint = type === 'products' ? '/inventory' : '/customers';

                        $.ajax({
                            url: restUrl + endpoint,
                            method: 'GET',
                            beforeSend: function(xhr) {
                                xhr.setRequestHeader('X-WP-Nonce', restNonce);
                            },
                            data: {
                                action: 'import_status'
                            },
                            success: function(response) {
                                if (response.status === 'in_progress') {
                                    var percent = response.percent_complete || 0;
                                    $progressFill.css('width', percent + '%');
                                    $progressStatus.text(response.message || '<?php echo esc_js(__('Importing...', 'squarekit')); ?>');
                                    
                                    // Check again in a moment
                                    setTimeout(function() {
                                        checkImportProgress(type, $progressFill, $progressStatus, $btn, $option);
                                    }, 2000);
                                }
                                else if (response.status === 'complete') {
                                    $progressFill.css('width', '100%');
                                    $progressStatus.text(response.message || '<?php echo esc_js(__('Import complete!', 'squarekit')); ?>');
                                    $option.find('.squarekit-import-status').html('<span class="squarekit-status-complete"><?php echo esc_js(__('Import complete', 'squarekit')); ?></span>');
                                    
                                    // Update the setting
                                    var settingData = {};
                                    settingData['has_imported_' + type] = true;
                                    $.ajax({
                                        url: restUrl + '/settings',
                                        method: 'POST',
                                        beforeSend: function(xhr) {
                                            xhr.setRequestHeader('X-WP-Nonce', restNonce);
                                        },
                                        data: settingData
                                    });
                                    
                                    // Change button text
                                    $btn.text('<?php echo esc_js(__('Reimport', 'squarekit')); ?>');
                                    $btn.prop('disabled', false);
                                }
                                else {
                                    $progressStatus.text(response.message || '<?php echo esc_js(__('Error during import.', 'squarekit')); ?>');
                                    $btn.prop('disabled', false);
                                }
                            },
                            error: function() {
                                $progressStatus.text('<?php echo esc_js(__('Error checking import status.', 'squarekit')); ?>');
                                $btn.prop('disabled', false);
                            }
                        });
                    }
                });
                </script>
                <?php
                break;
            case 'review':
                $settings = new SquareKit_Settings();
                $env = $settings->get_environment();
                $is_connected = $settings->is_connected();
                $sync_products = $settings->get('sync_products', 'both');
                $sync_orders = $settings->get('sync_orders', true);
                $sync_customers = $settings->get('sync_customers', true);
                $enable_automatic_sync = $settings->get('enable_automatic_sync', true);
                $location_id = $settings->get('location_id', '');
                $payment_methods = $settings->get('payment_methods', array());
                $default_methods = array('google_pay' => false, 'apple_pay' => false, 'afterpay' => false);
                $payment_methods = wp_parse_args($payment_methods, $default_methods);
                $locations = array();
                $location_name = '';
                if ($is_connected) {
                    $square_api = new SquareKit_Square_API();
                    $locations_response = $square_api->get_locations();
                    if (is_array($locations_response)) {
                        foreach ($locations_response as $loc) {
                            if ($loc['id'] === $location_id) {
                                $location_name = $loc['name'];
                                break;
                            }
                        }
                    }
                }
                if (isset($_POST['squarekit_review_nonce']) && wp_verify_nonce($_POST['squarekit_review_nonce'], 'squarekit_review_finish')) {
                    $settings->mark_setup_complete();
                    ?>
                    <div class="squarekit-success-container" style="text-align: center; padding: 2em; margin: 2em 0; background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border-radius: 16px; box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2); border: 2px solid #10b981;">
                        <img src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'assets/images/success.gif'); ?>" alt="Success Animation" style="width: 80px; height: 80px; margin-bottom: 1em;" />
                        <h3 style="margin: 0 0 0.5em 0; color: #065f46; font-size: 1.4em; font-weight: 700;">
                            🎉 Square Kit Setup Completed Successfully!
                        </h3>
                        <p style="margin: 0; color: #047857; font-size: 1.1em; font-weight: 500;">
                            <?php esc_html_e('Redirecting you to the dashboard...', 'squarekit'); ?>
                        </p>
                    </div>
                    <?php
                }

                // Handle skip and finish from import step (this shouldn't happen here, but keeping for safety)
                if (isset($_POST['squarekit_skip_finish_nonce']) && wp_verify_nonce($_POST['squarekit_skip_finish_nonce'], 'squarekit_skip_finish')) {
                    $settings->mark_setup_complete();
                    ?>
                    <div class="squarekit-success-container" style="text-align: center; padding: 2em; margin: 2em 0; background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); border-radius: 16px; box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2); border: 2px solid #10b981;">
                        <img src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'assets/images/success.gif'); ?>" alt="Success Animation" style="width: 80px; height: 80px; margin-bottom: 1em;" />
                        <h3 style="margin: 0 0 0.5em 0; color: #065f46; font-size: 1.4em; font-weight: 700;">
                            🎉 Square Kit Setup Completed Successfully!
                        </h3>
                        <p style="margin: 0; color: #047857; font-size: 1.1em; font-weight: 500;">
                            <?php esc_html_e('Redirecting you to the dashboard...', 'squarekit'); ?>
                        </p>
                    </div>
                    <?php
                }
                ?>
                <h2><?php esc_html_e('Review & Finish', 'squarekit'); ?></h2>
                <p><?php esc_html_e('Review your selections and finish setup.', 'squarekit'); ?></p>
                <form id="squarekit-review-form" method="post" action="">
                    <?php wp_nonce_field('squarekit_review_finish', 'squarekit_review_nonce'); ?>
                    <table class="form-table">
                        <tr><th><?php esc_html_e('Square Connection', 'squarekit'); ?></th><td><?php echo $is_connected ? '<span style="color:green;">' . esc_html__('Connected', 'squarekit') . '</span>' : '<span style="color:red;">' . esc_html__('Not Connected', 'squarekit') . '</span>'; ?></td></tr>
                        <tr><th><?php esc_html_e('Sync Products', 'squarekit'); ?></th><td><?php echo esc_html(ucwords(str_replace('_', ' ', $sync_products))); ?></td></tr>
                        <tr><th><?php esc_html_e('Sync Orders', 'squarekit'); ?></th><td><?php echo $sync_orders ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?></td></tr>
                        <tr><th><?php esc_html_e('Sync Customers', 'squarekit'); ?></th><td><?php echo $sync_customers ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?></td></tr>
                        <tr><th><?php esc_html_e('Automatic Sync', 'squarekit'); ?></th><td><?php echo $enable_automatic_sync ? esc_html__('Enabled', 'squarekit') : esc_html__('Disabled', 'squarekit'); ?></td></tr>
                        <tr><th><?php esc_html_e('Location', 'squarekit'); ?></th><td><?php echo $location_name ? esc_html($location_name) : esc_html__('Not selected', 'squarekit'); ?></td></tr>
                        <tr><th><?php esc_html_e('Payment Methods', 'squarekit'); ?></th><td>
                            <?php
                            $enabled_methods = array();
                            foreach ($payment_methods as $method => $enabled) {
                                if ($enabled) {
                                    $enabled_methods[] = ucwords(str_replace('_', ' ', $method));
                                }
                            }
                            echo $enabled_methods ? esc_html(implode(', ', $enabled_methods)) : esc_html__('None', 'squarekit');
                            ?>
                        </td></tr>
                    </table>
                </form>
                <?php if (isset($_POST['squarekit_review_nonce']) && wp_verify_nonce($_POST['squarekit_review_nonce'], 'squarekit_review_finish')): ?>
                <script>
                var redirectUrl = '<?php echo esc_js(admin_url('admin.php?page=squarekit')); ?>';
                console.log('Setup complete! Redirecting to: ' + redirectUrl);
                setTimeout(function(){
                    window.location.href = redirectUrl;
                },1200);
                </script>
                <?php endif; ?>

                <?php if (isset($_POST['squarekit_skip_finish_nonce']) && wp_verify_nonce($_POST['squarekit_skip_finish_nonce'], 'squarekit_skip_finish')): ?>
                <script>
                var redirectUrl = '<?php echo esc_js(admin_url('admin.php?page=squarekit')); ?>';
                console.log('Setup complete via skip! Redirecting to: ' + redirectUrl);
                setTimeout(function(){
                    window.location.href = redirectUrl;
                },1200);
                </script>
                <?php endif; ?>
                <?php
                break;
        }
        ?>
    </div>
    <div class="squarekit-wizard-buttons">
        <?php
        switch ( $step ) {
            case 'welcome':
                ?>
                <a class="button button-primary" href="<?php echo esc_url( squarekit_wizard_step_url( 'connect' ) ); ?>"><?php esc_html_e( 'Get Started', 'squarekit' ); ?></a>
                <?php
                break;
            case 'connect':
                ?>
                <a class="button" href="<?php echo esc_url( squarekit_wizard_step_url( 'welcome' ) ); ?>"><?php esc_html_e( 'Back', 'squarekit' ); ?></a>
                <a class="button button-primary<?php echo $is_connected ? '' : ' disabled'; ?>" href="<?php echo esc_url( squarekit_wizard_step_url( 'sync' ) ); ?>">
                    <?php esc_html_e( 'Continue', 'squarekit' ); ?>
                </a>
                <?php
                break;
            case 'sync':
                ?>
                <a class="button" href="<?php echo esc_url( squarekit_wizard_step_url( 'connect' ) ); ?>"><?php esc_html_e( 'Back', 'squarekit' ); ?></a>
                <button type="button" class="button button-primary" onclick="document.getElementById('squarekit-sync-form').submit();"><?php esc_html_e( 'Save & Continue', 'squarekit' ); ?></button>
                <?php
                break;
            case 'location':
                ?>
                <a class="button" href="<?php echo esc_url( squarekit_wizard_step_url( 'sync' ) ); ?>"><?php esc_html_e( 'Back', 'squarekit' ); ?></a>
                <button type="button" class="button button-primary" onclick="document.getElementById('squarekit-location-form').submit();"><?php esc_html_e( 'Save & Continue', 'squarekit' ); ?></button>
                <?php
                break;
            case 'import':
                ?>
                <a class="button" href="<?php echo esc_url( squarekit_wizard_step_url( 'location' ) ); ?>"><?php esc_html_e( 'Back', 'squarekit' ); ?></a>
                <a class="button button-primary" href="<?php echo esc_url( squarekit_wizard_step_url( 'review' ) ); ?>"><?php esc_html_e( 'Continue to Review', 'squarekit' ); ?></a>
                <button type="button" class="button button-secondary" onclick="document.getElementById('squarekit-skip-form').submit();" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: #fff; border: none; font-weight: 600;"><?php esc_html_e( 'Skip & Finish Setup', 'squarekit' ); ?></button>
                <?php
                break;
            case 'review':
                ?>
                <a class="button" href="<?php echo esc_url( squarekit_wizard_step_url( 'import' ) ); ?>"><?php esc_html_e( 'Back', 'squarekit' ); ?></a>
                <button type="button" class="button button-primary" onclick="document.getElementById('squarekit-review-form').submit();"><?php esc_html_e( 'Finish & Go to Dashboard', 'squarekit' ); ?></button>
                <?php
                break;
        }
        ?>
    </div>
</div> 