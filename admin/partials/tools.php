<?php
// Robust Tools Admin UI for Square Kit
if ( ! defined( 'ABSPATH' ) ) exit;
$logs = (new SquareKit_DB())->get_logs(['number' => 20]);
?>
<div class="wrap squarekit-tools">
    <h1><?php esc_html_e('Square Kit Tools', 'squarekit'); ?></h1>
    <div id="squarekit-tools-feedback" role="status" aria-live="polite" style="margin-bottom:1em;"></div>
    <h2><?php esc_html_e('Manual Sync', 'squarekit'); ?></h2>
    <p>
        <a href="#" class="button sync-all" data-type="products" aria-busy="false"><?php esc_html_e('Sync All Products', 'squarekit'); ?></a>
        <a href="#" class="button sync-all" data-type="customers" aria-busy="false"><?php esc_html_e('Sync All Customers', 'squarekit'); ?></a>
        <a href="#" class="button sync-all" data-type="orders" aria-busy="false"><?php esc_html_e('Sync All Orders', 'squarekit'); ?></a>
    </p>
    <h2><?php esc_html_e('Data Repair & Cleanup', 'squarekit'); ?></h2>
    <p>
        <a href="#" class="button cleanup" data-type="orphaned_meta" aria-busy="false"><?php esc_html_e('Remove Orphaned Metadata', 'squarekit'); ?></a>
        <a href="#" class="button cleanup" data-type="remap_ids" aria-busy="false"><?php esc_html_e('Re-map Square IDs', 'squarekit'); ?></a>
    </p>
    <h2><?php esc_html_e('Logs', 'squarekit'); ?></h2>
    <div class="squarekit-tools-logs" style="background:#fff;padding:1em;border-radius:8px;box-shadow:0 1px 4px #eee;max-width:600px;">
        <?php if ($logs): ?>
            <ul>
                <?php foreach ($logs as $log): ?>
                    <li><strong><?php echo esc_html($log->created_at); ?></strong> [<?php echo esc_html($log->log_type); ?>] <?php echo esc_html($log->log_message); ?></li>
                <?php endforeach; ?>
            </ul>
        <?php else: ?>
            <p><?php esc_html_e('No recent logs.', 'squarekit'); ?></p>
        <?php endif; ?>
    </div>
    <h2><?php esc_html_e('Export/Import Settings', 'squarekit'); ?></h2>
    <form id="squarekit-tools-export-import" method="post" enctype="multipart/form-data" autocomplete="off">
        <?php wp_nonce_field('squarekit_tools_export_import', 'squarekit_tools_nonce'); ?>
        <button type="submit" name="squarekit_export_settings" class="button export-settings" aria-busy="false"><?php esc_html_e('Export Settings', 'squarekit'); ?></button>
        <input type="file" name="squarekit_import_file" />
        <button type="submit" name="squarekit_import_settings" class="button import-settings" aria-busy="false"><?php esc_html_e('Import Settings', 'squarekit'); ?></button>
    </form>
</div>
<script>
jQuery(function($){
    function showFeedback(msg, type) {
        $('#squarekit-tools-feedback').html('<div class="notice notice-'+type+'">'+msg+'</div>');
    }
    $('.sync-all').on('click', function(e){
        e.preventDefault();
        var btn = $(this), type = btn.data('type');
        btn.prop('disabled', true).attr('aria-busy','true');
        showFeedback('<span class="spinner is-active"></span> <?php echo esc_js(__('Syncing...', 'squarekit')); ?>', 'info');
        $.post(squarekit_admin.ajax_url, {
            action: 'squarekit_tools_sync_all',
            nonce: squarekit_admin.nonce,
            sync_type: type
        }, function(resp){
            if(resp.success) {
                showFeedback(resp.data.message, 'success');
            } else {
                showFeedback((resp.data && resp.data.message ? resp.data.message : 'Sync failed'), 'error');
            }
            btn.prop('disabled', false).attr('aria-busy','false');
        }).fail(function(){
            showFeedback('AJAX error.', 'error');
            btn.prop('disabled', false).attr('aria-busy','false');
        });
    });
    $('.cleanup').on('click', function(e){
        e.preventDefault();
        var btn = $(this), type = btn.data('type');
        btn.prop('disabled', true).attr('aria-busy','true');
        showFeedback('<span class="spinner is-active"></span> <?php echo esc_js(__('Cleaning up...', 'squarekit')); ?>', 'info');
        $.post(squarekit_admin.ajax_url, {
            action: 'squarekit_tools_cleanup',
            nonce: squarekit_admin.nonce,
            cleanup_type: type
        }, function(resp){
            if(resp.success) {
                showFeedback(resp.data.message, 'success');
            } else {
                showFeedback((resp.data && resp.data.message ? resp.data.message : 'Cleanup failed'), 'error');
            }
            btn.prop('disabled', false).attr('aria-busy','false');
        }).fail(function(){
            showFeedback('AJAX error.', 'error');
            btn.prop('disabled', false).attr('aria-busy','false');
        });
    });
    $('#squarekit-tools-export-import').on('submit', function(e){
        var isExport = $(document.activeElement).hasClass('export-settings');
        var isImport = $(document.activeElement).hasClass('import-settings');
        if(isExport || isImport) {
            e.preventDefault();
            var btn = isExport ? $('.export-settings') : $('.import-settings');
            btn.prop('disabled', true).attr('aria-busy','true');
            showFeedback('<span class="spinner is-active"></span> <?php echo esc_js(__('Processing...', 'squarekit')); ?>', 'info');
            var formData = new FormData(this);
            formData.append('action', isExport ? 'squarekit_tools_export_settings' : 'squarekit_tools_import_settings');
            formData.append('nonce', squarekit_admin.nonce);
            $.ajax({
                url: squarekit_admin.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(resp) {
                    if(resp.success) {
                        showFeedback(resp.data.message, 'success');
                        if(isExport && resp.data.download_url) {
                            window.location = resp.data.download_url;
                        }
                    } else {
                        showFeedback((resp.data && resp.data.message ? resp.data.message : 'Operation failed'), 'error');
                    }
                    btn.prop('disabled', false).attr('aria-busy','false');
                },
                error: function() {
                    showFeedback('AJAX error.', 'error');
                    btn.prop('disabled', false).attr('aria-busy','false');
                }
            });
        }
    });
});
</script>
<style>
.spinner.is-active { display:inline-block; width:24px; height:24px; border:4px solid #ccc; border-top:4px solid #0073aa; border-radius:50%; animation:spin 1s linear infinite; vertical-align:middle; }
@keyframes spin { 100% { transform:rotate(360deg); } }
</style> 