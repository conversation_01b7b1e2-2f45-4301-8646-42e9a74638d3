<?php
// Beautiful and Interactive Products Admin UI for Square Kit
if ( ! defined( 'ABSPATH' ) ) exit;

$settings = new SquareKit_Settings();
$environment = $settings->get_environment();
$is_connected = $settings->is_connected();
?>
<div class="wrap squarekit-products">
    <div class="squarekit-header">
        <h1><?php esc_html_e('Square Inventory', 'squarekit'); ?></h1>
        <div class="squarekit-header-actions">
            <span class="squarekit-environment-badge <?php echo $environment; ?>">
                <?php echo esc_html(ucfirst($environment)); ?>
            </span>
            <?php if ($is_connected): ?>
                <span class="squarekit-status-badge connected">
                    <span class="dashicons dashicons-yes"></span>
                    <?php esc_html_e('Connected', 'squarekit'); ?>
                </span>
            <?php else: ?>
                <span class="squarekit-status-badge disconnected">
                    <span class="dashicons dashicons-no"></span>
                    <?php esc_html_e('Not Connected', 'squarekit'); ?>
                </span>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!$is_connected): ?>
        <div class="squarekit-notice notice-warning">
            <p>
                <strong><?php esc_html_e('Not Connected to Square', 'squarekit'); ?></strong><br>
                <?php esc_html_e('Please connect to Square in the settings to view and import products.', 'squarekit'); ?>
                <a href="<?php echo admin_url('admin.php?page=squarekit-settings'); ?>" class="button button-primary"><?php esc_html_e('Go to Settings', 'squarekit'); ?></a>
            </p>
        </div>
    <?php else: ?>
        <!-- Filters and Search -->
        <div class="squarekit-filters">
            <div class="squarekit-search-box">
                <input type="search" id="squarekit-products-search" placeholder="<?php esc_attr_e('Search products...', 'squarekit'); ?>" />
                <span class="dashicons dashicons-search"></span>
            </div>
            
            <div class="squarekit-filter-controls">
                <select id="squarekit-sync-status-filter">
                    <option value=""><?php esc_html_e('All Statuses', 'squarekit'); ?></option>
                    <option value="imported"><?php esc_html_e('Imported', 'squarekit'); ?></option>
                    <option value="not_imported"><?php esc_html_e('Not Imported', 'squarekit'); ?></option>
                    <option value="synced"><?php esc_html_e('Synced', 'squarekit'); ?></option>
                    <option value="out_of_sync"><?php esc_html_e('Out of Sync', 'squarekit'); ?></option>
                </select>
                
                <select id="squarekit-type-filter">
                    <option value="ITEM" selected><?php esc_html_e('Products Only', 'squarekit'); ?></option>
                </select>
                
                <select id="squarekit-sort-filter">
                    <option value="name_asc"><?php esc_html_e('Name A-Z', 'squarekit'); ?></option>
                    <option value="name_desc"><?php esc_html_e('Name Z-A', 'squarekit'); ?></option>
                    <option value="price_asc"><?php esc_html_e('Price Low-High', 'squarekit'); ?></option>
                    <option value="price_desc"><?php esc_html_e('Price High-Low', 'squarekit'); ?></option>
                    <option value="updated_desc"><?php esc_html_e('Recently Updated', 'squarekit'); ?></option>
                </select>
            </div>
        </div>

        <!-- Main Actions -->
        <div class="squarekit-main-actions">
            <div class="main-actions-left">
                <button type="button" id="squarekit-fetch-from-square" class="squarekit-action-btn primary">
                    <div class="action-icon">
                        <span class="dashicons dashicons-cloud"></span>
                    </div>
                    <div class="action-content">
                        <span class="action-title"><?php esc_html_e('Fetch From Square', 'squarekit'); ?></span>
                        <span class="action-description"><?php esc_html_e('Load Square catalog for review', 'squarekit'); ?></span>
                    </div>
                </button>
                <button type="button" id="squarekit-import-all" class="squarekit-action-btn success" disabled>
                    <div class="action-icon">
                        <span class="dashicons dashicons-download"></span>
                    </div>
                    <div class="action-content">
                        <span class="action-title"><?php esc_html_e('Import All', 'squarekit'); ?></span>
                        <span class="action-description"><?php esc_html_e('Import fetched products to WooCommerce', 'squarekit'); ?></span>
                    </div>
                </button>
            </div>

            <div class="main-actions-right">
                <div class="products-summary">
                    <span id="squarekit-fetched-count" class="count-badge fetched">0 <?php esc_html_e('fetched', 'squarekit'); ?></span>
                    <span id="squarekit-imported-count" class="count-badge imported">0 <?php esc_html_e('imported', 'squarekit'); ?></span>
                    <span id="squarekit-selected-count" class="count-badge selected">0 <?php esc_html_e('selected', 'squarekit'); ?></span>
                </div>
                <div class="cache-status" id="squarekit-cache-status" style="display: none;">
                    <span class="cache-indicator">
                        <span class="dashicons dashicons-database"></span>
                        <span id="squarekit-cache-time"><?php esc_html_e('Cached', 'squarekit'); ?></span>
                    </span>
                    <button type="button" id="squarekit-refresh-cache" class="button button-small">
                        <span class="dashicons dashicons-update"></span>
                        <?php esc_html_e('Refresh', 'squarekit'); ?>
                    </button>
                </div>
                <button type="button" id="squarekit-select-all" class="button"><?php esc_html_e('Select All', 'squarekit'); ?></button>
                <button type="button" id="squarekit-import-selected" class="button button-primary" disabled>
                    <span class="dashicons dashicons-download"></span>
                    <?php esc_html_e('Import Selected', 'squarekit'); ?>
                </button>
            </div>
        </div>

        <!-- Products Grid -->
        <div id="squarekit-products-grid" class="squarekit-products-grid">
            <div class="squarekit-loading">
                <div class="squarekit-spinner"></div>
                <p><?php esc_html_e('Loading Square inventory...', 'squarekit'); ?></p>
            </div>
        </div>

        <!-- Pagination -->
        <div id="squarekit-pagination" class="squarekit-pagination"></div>
    <?php endif; ?>
</div>

<!-- Import Progress Modal -->
<div id="squarekit-import-modal" class="squarekit-modal" style="display: none;">
    <div class="squarekit-modal-content">
        <div class="squarekit-modal-header">
            <h2 id="squarekit-import-title"><?php esc_html_e('Importing Products', 'squarekit'); ?></h2>
            <button type="button" class="squarekit-modal-close">&times;</button>
        </div>
        
        <div class="squarekit-modal-body">
            <div id="squarekit-import-progress" class="squarekit-import-progress">
                <!-- Progress content will be dynamically generated by JavaScript -->
                <div class="squarekit-progress-steps">
                    <div class="progress-step active">
                        <span class="step-icon">...</span>
                        <span class="step-text"><?php esc_html_e('Preparing...', 'squarekit'); ?></span>
                    </div>
                </div>
                <div class="squarekit-progress-bar">
                    <div class="squarekit-progress-fill" style="width: 0%"></div>
                </div>
                <div class="squarekit-progress-percentage">0%</div>
            </div>

            <div class="squarekit-import-details" style="display: none;">
                <div class="squarekit-import-summary">
                    <div class="squarekit-summary-item">
                        <span class="squarekit-summary-label"><?php esc_html_e('Total:', 'squarekit'); ?></span>
                        <span class="squarekit-summary-value" id="squarekit-total-count">0</span>
                    </div>
                    <div class="squarekit-summary-item">
                        <span class="squarekit-summary-label"><?php esc_html_e('Processed:', 'squarekit'); ?></span>
                        <span class="squarekit-summary-value" id="squarekit-processed-count">0</span>
                    </div>
                    <div class="squarekit-summary-item">
                        <span class="squarekit-summary-label"><?php esc_html_e('Success:', 'squarekit'); ?></span>
                        <span class="squarekit-summary-value success" id="squarekit-success-count">0</span>
                    </div>
                    <div class="squarekit-summary-item">
                        <span class="squarekit-summary-label"><?php esc_html_e('Failed:', 'squarekit'); ?></span>
                        <span class="squarekit-summary-value error" id="squarekit-failed-count">0</span>
                    </div>
                </div>

                <div class="squarekit-import-log">
                    <div class="log-header">
                        <h4><?php esc_html_e('Import Log', 'squarekit'); ?></h4>
                        <button type="button" id="squarekit-toggle-log" class="toggle-log-btn">
                            <span class="toggle-text"><?php esc_html_e('Show More', 'squarekit'); ?></span>
                            <span class="toggle-icon">▼</span>
                        </button>
                    </div>
                    <div id="squarekit-import-log-content" class="squarekit-log-content collapsed"></div>
                    <div id="squarekit-log-summary" class="log-summary"></div>
                </div>
            </div>
        </div>
        
        <div class="squarekit-modal-footer">
            <button type="button" id="squarekit-cancel-import" class="button"><?php esc_html_e('Cancel', 'squarekit'); ?></button>
            <button type="button" id="squarekit-close-import" class="button button-primary" style="display: none;"><?php esc_html_e('Close', 'squarekit'); ?></button>
        </div>
    </div>
</div>

<!-- Product Details Modal -->
<div id="squarekit-product-modal" class="squarekit-modal" style="display: none;">
    <div class="squarekit-modal-content">
        <div class="squarekit-modal-header">
            <h2 id="squarekit-product-title"><?php esc_html_e('Product Details', 'squarekit'); ?></h2>
            <button type="button" class="squarekit-modal-close">&times;</button>
        </div>
        
        <div class="squarekit-modal-body" id="squarekit-product-details">
            <!-- Product details will be loaded here -->
        </div>
        
        <div class="squarekit-modal-footer">
            <button type="button" class="squarekit-modal-close button"><?php esc_html_e('Close', 'squarekit'); ?></button>
        </div>
    </div>
</div>

<style>
/* Square Kit Products Page Styles */
.squarekit-products {
    margin: 20px 0;
}

.squarekit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f1;
}

.squarekit-header h1 {
    margin: 0;
    color: #1d2327;
}

.squarekit-header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.squarekit-environment-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.squarekit-environment-badge.sandbox {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.squarekit-environment-badge.production {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.squarekit-status-badge {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.squarekit-status-badge.connected {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.squarekit-status-badge.disconnected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.squarekit-notice {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.squarekit-notice.notice-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.squarekit-filters {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.squarekit-search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.squarekit-search-box input {
    width: 100%;
    padding: 8px 35px 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.squarekit-search-box .dashicons {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.squarekit-filter-controls {
    display: flex;
    gap: 10px;
}

.squarekit-filter-controls select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

.squarekit-bulk-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.squarekit-bulk-actions button {
    display: flex;
    align-items: center;
    gap: 5px;
}

.squarekit-bulk-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.squarekit-selected-count {
    margin-left: auto;
    font-size: 14px;
    color: #666;
}

.cache-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    padding: 8px 12px;
    background: #f0f6fc;
    border: 1px solid #c8e1ff;
    border-radius: 6px;
    font-size: 13px;
}

.cache-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #0969da;
}

.cache-indicator .dashicons {
    font-size: 16px;
}

#squarekit-refresh-cache {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    line-height: 1.2;
}

.count-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-right: 8px;
}

.count-badge.fetched {
    background: #e3f2fd;
    color: #1565c0;
}

.count-badge.imported {
    background: #e8f5e8;
    color: #2e7d32;
}

.count-badge.selected {
    background: #fff3e0;
    color: #ef6c00;
}

.squarekit-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.squarekit-product-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
    border: 2px solid transparent;
}

.squarekit-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.squarekit-product-card.selected {
    border-color: #0073aa;
    box-shadow: 0 4px 16px rgba(0,115,170,0.2);
}

.squarekit-product-image {
    position: relative;
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.squarekit-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.squarekit-product-image .dashicons {
    font-size: 48px;
    color: #ddd;
}

.squarekit-product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.squarekit-product-badge.imported {
    background: #d4edda;
    color: #155724;
}

.squarekit-product-badge.not_imported {
    background: #f8d7da;
    color: #721c24;
}

.squarekit-product-badge.synced {
    background: #d1ecf1;
    color: #0c5460;
}

.squarekit-product-badge.out_of_sync {
    background: #fff3cd;
    color: #856404;
}

.squarekit-product-content {
    padding: 20px;
}

.squarekit-product-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: #1d2327;
    line-height: 1.4;
}

.squarekit-product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
    color: #666;
}

.squarekit-product-price {
    font-weight: 600;
    color: #1d2327;
}

.squarekit-product-type {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    background: #e9ecef;
    color: #495057;
}

.squarekit-product-actions {
    display: flex;
    gap: 8px;
}

.squarekit-product-actions button {
    flex: 1;
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.squarekit-product-actions .import-btn {
    background: #0073aa;
    color: white;
}

.squarekit-product-actions .import-btn:hover {
    background: #005a87;
}

.squarekit-product-actions .sync-btn {
    background: #28a745;
    color: white;
}

.squarekit-product-actions .sync-btn:hover {
    background: #218838;
}

.squarekit-product-actions .view-btn {
    background: #6c757d;
    color: white;
}

.squarekit-product-actions .view-btn:hover {
    background: #545b62;
}

.squarekit-loading {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
}

.squarekit-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.squarekit-pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.squarekit-pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.squarekit-pagination button:hover {
    background: #f8f9fa;
}

.squarekit-pagination button.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.squarekit-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal Styles */
.squarekit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.squarekit-modal-content {
    background: #fff;
    border-radius: 12px;
    max-width: 800px;
    width: 90%;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.squarekit-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
}

.squarekit-modal-header h2 {
    margin: 0;
    color: #1d2327;
}

.squarekit-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.squarekit-modal-close:hover {
    background: #f8f9fa;
    color: #333;
}

.squarekit-modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.squarekit-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

/* Import Progress Styles */
.squarekit-import-progress {
    margin-bottom: 30px;
}

.squarekit-progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.squarekit-progress-fill {
    height: 100%;
    background: #0073aa;
    width: 0%;
    transition: width 0.3s ease;
}

.squarekit-progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.squarekit-progress-percentage {
    font-weight: 600;
    color: #0073aa;
}

.squarekit-import-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.squarekit-import-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.squarekit-summary-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.squarekit-summary-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.squarekit-summary-value {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #1d2327;
}

.squarekit-summary-value.success {
    color: #28a745;
}

.squarekit-summary-value.error {
    color: #dc3545;
}

.squarekit-import-log {
    grid-column: 1 / -1;
}

.squarekit-import-log h4 {
    margin: 0 0 15px 0;
    color: #1d2327;
}

.squarekit-log-content {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.4;
}

.squarekit-log-item {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
}

.squarekit-log-item:last-child {
    border-bottom: none;
}

.squarekit-log-item.success {
    color: #28a745;
}

.squarekit-log-item.error {
    color: #dc3545;
}

.squarekit-log-item.info {
    color: #0073aa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .squarekit-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .squarekit-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .squarekit-filter-controls {
        flex-wrap: wrap;
    }
    
    .squarekit-products-grid {
        grid-template-columns: 1fr;
    }
    
    .squarekit-import-details {
        grid-template-columns: 1fr;
    }
    
    .squarekit-import-summary {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(function($){
    let currentCursor = '';
    let cursorHistory = []; // Stack to track previous cursors for back navigation
    let selectedProducts = new Set();
    let importInProgress = false;
    
    // Initialize
    if ($('#squarekit-products-grid').length) {
        checkCachedDataAndLoadProducts();
    }

    // Check for cached data and enable Import All button if available
    function checkCachedDataAndLoadProducts() {
        // First check if we have cached data
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_check_cached_products',
                nonce: squarekit_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data.has_cached_data) {
                    // Enable Import All button since we have cached data
                    $('#squarekit-import-all').prop('disabled', false);

                    // Update button text to show cached count
                    const cachedCount = response.data.cached_count;
                    $('#squarekit-import-all .action-title').text('Import All (' + cachedCount + ')');
                    $('#squarekit-import-all .action-description').text('Import ' + cachedCount + ' cached products to WooCommerce');
                } else {
                    // No cached data, keep Import All button disabled
                    $('#squarekit-import-all').prop('disabled', true);
                    $('#squarekit-import-all .action-title').text('Import All');
                    $('#squarekit-import-all .action-description').text('Fetch products first, then import to WooCommerce');
                }

                // Now load the products grid
                loadProducts();
            },
            error: function() {
                // On error, just load products normally
                loadProducts();
            }
        });
    }
    
    // Search functionality
    let searchTimeout;
    $('#squarekit-products-search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            resetPagination();
            loadProducts();
        }, 500);
    });

    // Filter functionality
    $('.squarekit-filter-controls select').on('change', function() {
        resetPagination();
        loadProducts();
    });

    // Reset pagination to start
    function resetPagination() {
        currentCursor = '';
        cursorHistory = [];
    }
    
    // Load products from Square
    function loadProducts() {
        const search = $('#squarekit-products-search').val();
        const syncStatus = $('#squarekit-sync-status-filter').val();
        const type = $('#squarekit-type-filter').val();
        const sort = $('#squarekit-sort-filter').val();

        $('#squarekit-products-grid').html('<div class="squarekit-loading"><div class="squarekit-spinner"></div><p>Loading Square inventory...</p></div>');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_get_square_inventory',
                nonce: squarekit_admin.nonce,
                search: search,
                sync_status: syncStatus,
                type: type,
                sort: sort,
                cursor: currentCursor,
                per_page: 20
            },
            success: function(response) {
                if (response.success) {
                    renderProducts(response.data.products);
                    renderPagination(response.data.pagination);
                } else {
                    $('#squarekit-products-grid').html('<div class="notice notice-error"><p>' + (response.data.message || 'Error loading products') + '</p></div>');
                }
            },
            error: function() {
                $('#squarekit-products-grid').html('<div class="notice notice-error"><p>Failed to load products. Please try again.</p></div>');
            }
        });
    }
    
    // Render products grid
    function renderProducts(products) {
        if (!products || products.length === 0) {
            $('#squarekit-products-grid').html('<div class="squarekit-loading"><p>No products found.</p></div>');
            return;
        }
        
        let html = '';
        products.forEach(function(product) {
            const imageUrl = product.image_url || '';
            const price = product.price_money ? formatPrice(product.price_money) : 'N/A';
            const status = getProductStatus(product);
            const statusClass = getStatusClass(status);
            
            html += `
                <div class="squarekit-product-card" data-product-id="${product.id}">
                    <div class="squarekit-product-image">
                        ${imageUrl ? `<img src="${imageUrl}" alt="${product.name}" />` : '<span class="dashicons dashicons-format-image"></span>'}
                        <div class="squarekit-product-badge ${statusClass}">${status}</div>
                    </div>
                    <div class="squarekit-product-content">
                        <h3 class="squarekit-product-title">${product.name}</h3>
                        <div class="squarekit-product-meta">
                            <span class="squarekit-product-price">${price}</span>
                            <span class="squarekit-product-type">${product.type || 'ITEM'}</span>
                        </div>
                        <div class="squarekit-product-actions">
                            <button type="button" class="import-btn" data-product-id="${product.id}" data-product-name="${product.name}">
                                <span class="dashicons dashicons-download"></span>
                                Import
                            </button>
                            <button type="button" class="sync-btn" data-product-id="${product.id}" data-product-name="${product.name}">
                                <span class="dashicons dashicons-update"></span>
                                Sync
                            </button>
                            <button type="button" class="view-btn" data-product-id="${product.id}" data-product-name="${product.name}">
                                <span class="dashicons dashicons-visibility"></span>
                                View
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        $('#squarekit-products-grid').html(html);
    }
    
    // Render pagination
    function renderPagination(pagination) {
        if (!pagination) {
            $('#squarekit-pagination').empty();
            return;
        }

        let html = '';

        // Previous button - enabled if we have cursor history
        const hasPrevious = cursorHistory.length > 0;
        html += `<button type="button" class="prev-page" ${!hasPrevious ? 'disabled' : ''}>&laquo; Previous</button>`;

        // Show current page info
        const currentPageNum = cursorHistory.length + 1;
        html += `<span class="pagination-info">Page ${currentPageNum}</span>`;

        // Next button - enabled if there's more data
        const hasNext = pagination.has_more;
        html += `<button type="button" class="next-page" ${!hasNext ? 'disabled' : ''}" data-next-cursor="${pagination.cursor || ''}">Next &raquo;</button>`;

        $('#squarekit-pagination').html(html);
    }
    
    // Pagination events
    $(document).on('click', '.prev-page', function() {
        if (cursorHistory.length > 0) {
            // Go back to previous page
            currentCursor = cursorHistory.pop();
            loadProducts();
        }
    });

    $(document).on('click', '.next-page', function() {
        const nextCursor = $(this).data('next-cursor');
        if (nextCursor) {
            // Save current cursor to history for back navigation
            cursorHistory.push(currentCursor);
            currentCursor = nextCursor;
            loadProducts();
        }
    });
    
    // Product card selection
    $(document).on('click', '.squarekit-product-card', function(e) {
        if ($(e.target).closest('.squarekit-product-actions').length) {
            return; // Don't select when clicking action buttons
        }
        
        const productId = $(this).data('product-id');
        if (selectedProducts.has(productId)) {
            selectedProducts.delete(productId);
            $(this).removeClass('selected');
        } else {
            selectedProducts.add(productId);
            $(this).addClass('selected');
        }
        
        updateBulkActions();
    });
    
    // Select all functionality
    $('#squarekit-select-all').on('click', function() {
        if (selectedProducts.size === $('.squarekit-product-card').length) {
            // Deselect all
            selectedProducts.clear();
            $('.squarekit-product-card').removeClass('selected');
        } else {
            // Select all
            $('.squarekit-product-card').each(function() {
                selectedProducts.add($(this).data('product-id'));
                $(this).addClass('selected');
            });
        }
        updateBulkActions();
    });
    
    // Update bulk actions state
    function updateBulkActions() {
        const count = selectedProducts.size;
        $('#squarekit-selected-count').text(count + ' selected');
        $('#squarekit-import-selected').prop('disabled', count === 0);
        $('#squarekit-sync-selected').prop('disabled', count === 0);
    }
    
    // Import selected products
    $('#squarekit-import-selected').on('click', function() {
        if (selectedProducts.size === 0) return;

        const productIds = Array.from(selectedProducts);
        startImport(productIds, 'selected');
    });

    // Fetch from Square (new functionality)
    $('#squarekit-fetch-from-square').on('click', function() {
        if (importInProgress) {
            return;
        }

        importInProgress = true;
        const $btn = $(this);
        const originalHtml = $btn.html();

        // Update button state
        $btn.prop('disabled', true).find('.action-title').text('Fetching...');
        $btn.find('.action-description').text('Loading Square catalog...');
        $btn.find('.dashicons').removeClass('dashicons-cloud').addClass('dashicons-update spin');

        // Show progress modal
        showFetchProgressModal();

        // Start fetching via AJAX (explicit fetch)
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_fetch_from_square',
                nonce: squarekit_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Successfully fetched ' + response.data.products_count + ' products from Square!');

                    // Enable Import All button
                    $('#squarekit-import-all').prop('disabled', false);

                    // Refresh the products grid to show cached products
                    loadProducts();

                    // Close progress modal
                    closeFetchProgressModal(true);
                } else {
                    alert('Fetch failed: ' + (response.data.message || 'Unknown error'));
                    closeFetchProgressModal(false);
                }
            },
            error: function() {
                alert('Network error. Please try again.');
                closeFetchProgressModal(false);
            },
            complete: function() {
                // Restore button state
                $btn.prop('disabled', false).html(originalHtml);
                importInProgress = false;
            }
        });
    });

    // Import all fetched products
    $('#squarekit-import-all').on('click', function() {
        if ($(this).prop('disabled')) return;

        // Confirm action
        if (!confirm('This will import all fetched products to WooCommerce. Continue?')) {
            return;
        }

        const $btn = $(this);
        const originalHtml = $btn.html();

        // Update button state
        $btn.prop('disabled', true).find('.action-title').text('Importing...');
        $btn.find('.action-description').text('Creating WooCommerce products...');
        $btn.find('.dashicons').removeClass('dashicons-download').addClass('dashicons-update spin');

        // Show progress modal
        showImportProgressModal();

        // Start bulk import via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_import_all_products',
                nonce: squarekit_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Start polling for progress
                    startBulkImportProgressPolling(response.data.operation_id, $btn, originalHtml);
                } else {
                    alert('Import failed: ' + (response.data.message || 'Unknown error'));
                    closeImportProgressModal(false);
                    // Restore button state
                    $btn.prop('disabled', false).html(originalHtml);
                }
            },
            error: function() {
                alert('Network error. Please try again.');
                closeImportProgressModal(false);
                // Restore button state
                $btn.prop('disabled', false).html(originalHtml);
            }
        });
    });


    
    // Individual product actions
    $(document).on('click', '.import-btn', function() {
        const productId = $(this).data('product-id');
        startImport([productId], 'single');
    });
    
    $(document).on('click', '.sync-btn', function() {
        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        syncProduct(productId, productName);
    });
    
    $(document).on('click', '.view-btn', function() {
        const productId = $(this).data('product-id');
        viewProduct(productId);
    });
    
    // Start import process
    function startImport(productIds, type) {
        if (importInProgress) return;

        importInProgress = true;

        // Use the enhanced progress modal system
        showImportProgressModal();

        const title = type === 'selected' ?
            `Importing ${productIds.length} Products` :
            `Importing Product`;

        $('#squarekit-import-title').text(title);

        // Initialize progress with enhanced modal
        updateProgressModal(['Preparing import...'], 0);

        // Show summary section for detailed tracking
        $('.squarekit-import-details').show();
        $('#squarekit-total-count').text(productIds.length);
        $('#squarekit-processed-count').text('0');
        $('#squarekit-success-count').text('0');
        $('#squarekit-failed-count').text('0');
        $('#squarekit-import-log-content').empty();

        // Start import process
        importProducts(productIds, 0);
    }
    
    // Import products recursively
    function importProducts(productIds, index) {
        if (index >= productIds.length) {
            // Import complete
            importInProgress = false;
            updateProgressModal(['🎉 Import completed successfully!'], 100);

            // Show close button, hide cancel button
            $('#squarekit-cancel-import').hide();
            $('#squarekit-close-import').show();

            // Auto-close after 3 seconds
            setTimeout(function() {
                $('#squarekit-import-modal').hide();
                loadProducts(); // Refresh the grid
            }, 3000);
            return;
        }

        const productId = productIds[index];
        const progress = (index / productIds.length) * 100;

        // Update progress with enhanced modal system
        const statusMessage = `Importing product ${index + 1} of ${productIds.length}...`;
        updateProgressModal([statusMessage], progress);

        $('#squarekit-processed-count').text(index);
        
        // Import single product
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_import_product',
                nonce: squarekit_admin.nonce,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    const successCount = parseInt($('#squarekit-success-count').text()) + 1;
                    $('#squarekit-success-count').text(successCount);

                    // Add enhanced log entry with timestamp
                    const timestamp = new Date().toLocaleTimeString();
                    const logEntry = `<div class="log-entry success"><span class="log-time">${timestamp}</span> ✅ ${response.data.product_name} - Imported successfully</div>`;
                    $('#squarekit-import-log-content').prepend(logEntry);
                } else {
                    const failedCount = parseInt($('#squarekit-failed-count').text()) + 1;
                    $('#squarekit-failed-count').text(failedCount);

                    // Add enhanced log entry with timestamp
                    const timestamp = new Date().toLocaleTimeString();
                    const logEntry = `<div class="log-entry error"><span class="log-time">${timestamp}</span> ❌ ${response.data.product_name || 'Unknown Product'} - ${response.data.message}</div>`;
                    $('#squarekit-import-log-content').prepend(logEntry);
                }

                // Update processed count
                const processedCount = parseInt($('#squarekit-processed-count').text()) + 1;
                $('#squarekit-processed-count').text(processedCount);

                // Continue with next product
                setTimeout(function() {
                    importProducts(productIds, index + 1);
                }, 500);
            },
            error: function() {
                const failedCount = parseInt($('#squarekit-failed-count').text()) + 1;
                $('#squarekit-failed-count').text(failedCount);

                // Add enhanced log entry with timestamp
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `<div class="log-entry error"><span class="log-time">${timestamp}</span> ❌ Network error during import</div>`;
                $('#squarekit-import-log-content').prepend(logEntry);

                // Update processed count
                const processedCount = parseInt($('#squarekit-processed-count').text()) + 1;
                $('#squarekit-processed-count').text(processedCount);

                // Continue with next product
                setTimeout(function() {
                    importProducts(productIds, index + 1);
                }, 500);
            }
        });
    }
    
    // Sync product
    function syncProduct(productId, productName) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_sync_product',
                nonce: squarekit_admin.nonce,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    alert('Product synced successfully: ' + productName);
                    loadProducts(); // Refresh the grid
                } else {
                    alert('Sync failed: ' + response.data.message);
                }
            },
            error: function() {
                alert('Network error. Please try again.');
            }
        });
    }
    
    // View product details
    function viewProduct(productId) {
        $('#squarekit-product-modal').show();
        $('#squarekit-product-details').html('<div class="squarekit-loading"><div class="squarekit-spinner"></div><p>Loading product details...</p></div>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'squarekit_get_product_details',
                nonce: squarekit_admin.nonce,
                product_id: productId
            },
            success: function(response) {
                if (response.success) {
                    $('#squarekit-product-details').html(response.data.html);
                } else {
                    $('#squarekit-product-details').html('<div class="notice notice-error"><p>' + (response.data.message || 'Error loading product details') + '</p></div>');
                }
            },
            error: function() {
                $('#squarekit-product-details').html('<div class="notice notice-error"><p>Network error. Please try again.</p></div>');
            }
        });
    }
    
    // Modal close functionality
    $('.squarekit-modal-close').on('click', function() {
        $(this).closest('.squarekit-modal').hide();
    });
    
    // Cancel import
    $('#squarekit-cancel-import').on('click', function() {
        if (confirm('Are you sure you want to cancel the import?')) {
            importInProgress = false;
            $('#squarekit-import-modal').hide();
        }
    });
    
    // Close import
    $('#squarekit-close-import').on('click', function() {
        $('#squarekit-import-modal').hide();
        loadProducts(); // Refresh the grid
    });
    
    // Close modal on ESC
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('.squarekit-modal').hide();
        }
    });
    
    // Utility functions
    function formatPrice(priceMoney) {
        if (!priceMoney || !priceMoney.amount) return 'N/A';
        
        const amount = priceMoney.amount / 100; // Convert cents to dollars
        const currency = priceMoney.currency || 'USD';
        
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
    
    function getProductStatus(product) {
        if (product.imported) return 'Imported';
        if (product.synced) return 'Synced';
        if (product.out_of_sync) return 'Out of Sync';
        return 'Not Imported';
    }
    
    function getStatusClass(status) {
        switch (status) {
            case 'Imported':
                return 'imported';
            case 'Synced':
                return 'synced';
            case 'Out of Sync':
                return 'out_of_sync';
            default:
                return 'not_imported';
        }
    }

    // Update product counts in the UI
    function updateProductCounts(data) {
        const fetched = data.pagination ? data.pagination.total_items : 0;
        const imported = data.imported_count || 0;

        $('#squarekit-fetched-count').text(fetched + ' fetched');
        $('#squarekit-imported-count').text(imported + ' imported');

        // Update cache status if available
        if (data.cache_info) {
            updateCacheStatus(data.cache_info);
        }
    }

    // Update cache status indicator
    function updateCacheStatus(cacheInfo) {
        if (cacheInfo.last_fetch_time) {
            const fetchTime = new Date(cacheInfo.last_fetch_time);
            const now = new Date();
            const diffMinutes = Math.floor((now - fetchTime) / (1000 * 60));

            let timeText = '';
            if (diffMinutes < 1) {
                timeText = 'Just now';
            } else if (diffMinutes < 60) {
                timeText = diffMinutes + ' min ago';
            } else {
                const diffHours = Math.floor(diffMinutes / 60);
                timeText = diffHours + ' hour' + (diffHours > 1 ? 's' : '') + ' ago';
            }

            $('#squarekit-cache-time').text('Cached ' + timeText);
            $('#squarekit-cache-status').show();
        } else {
            $('#squarekit-cache-status').hide();
        }
    }

    // Refresh cache button
    $('#squarekit-refresh-cache').on('click', function() {
        $('#squarekit-fetch-from-square').click();
    });

    // Show fetch progress modal
    function showFetchProgressModal() {
        $('#squarekit-import-modal').show();
        $('#squarekit-import-title').text('Fetching From Square');
        updateProgressModal([
            'Connecting to Square API...',
            'Reading Square catalog...',
            'Processing products...'
        ], 0);
    }

    // Close fetch progress modal
    function closeFetchProgressModal(success) {
        if (success) {
            updateProgressModal(['Fetch completed successfully!'], 100);
            setTimeout(function() {
                $('#squarekit-import-modal').hide();
            }, 1500);
        } else {
            $('#squarekit-import-modal').hide();
        }
    }

    // Show import progress modal
    function showImportProgressModal() {
        $('#squarekit-import-modal').show();
        $('#squarekit-import-title').text('Importing to WooCommerce');

        // Initialize progress display
        updateProgressModal(['Preparing import...'], 0);

        // Reset and hide summary section initially
        $('.squarekit-import-details').hide();
        $('#squarekit-total-count').text('0');
        $('#squarekit-processed-count').text('0');
        $('#squarekit-success-count').text('0');
        $('#squarekit-failed-count').text('0');
        $('#squarekit-import-log-content').empty();

        // Show cancel button, hide close button initially
        $('#squarekit-cancel-import').show();
        $('#squarekit-close-import').hide();
    }

    // Close import progress modal
    function closeImportProgressModal(success) {
        if (success) {
            updateProgressModal(['Import completed successfully!'], 100);
            setTimeout(function() {
                $('#squarekit-import-modal').hide();
            }, 1500);
        } else {
            $('#squarekit-import-modal').hide();
        }
    }

    // Start bulk import progress polling
    function startBulkImportProgressPolling(operationId, $btn, originalHtml) {
        let pollInterval;
        let pollCount = 0;
        const maxPolls = 600; // 10 minutes max (600 * 1 second)

        function pollProgress() {
            pollCount++;

            // Stop polling after max attempts
            if (pollCount > maxPolls) {
                clearInterval(pollInterval);
                updateProgressModal(['Import timeout - check logs for details'], 100);
                setTimeout(function() {
                    $('#squarekit-import-modal').hide();
                    $btn.prop('disabled', false).html(originalHtml);
                }, 2000);
                return;
            }

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'squarekit_get_bulk_operation_progress',
                    operation_id: operationId,
                    nonce: squarekit_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        const progress = response.data;

                        // Update progress display
                        updateBulkImportProgress(progress);

                        // Check if completed
                        if (progress.is_completed) {
                            clearInterval(pollInterval);

                            if (progress.status === 'completed') {
                                // Show final success message
                                updateProgressModal(['🎉 Import completed successfully!'], 100);
                                updateProgressSummary(progress);

                                // Add final log entry
                                const timestamp = new Date().toLocaleTimeString();
                                const finalLogEntry = `<div class="log-entry success"><span class="log-time">${timestamp}</span> ✅ Import completed! ${progress.successful_items} products imported successfully.</div>`;
                                $('#squarekit-import-log-content').prepend(finalLogEntry);

                                // Update counts and refresh grid
                                loadProducts();
                            } else if (progress.status === 'failed') {
                                updateProgressModal(['❌ Import failed - check logs for details'], progress.progress_percentage);
                            } else if (progress.status === 'cancelled') {
                                updateProgressModal(['⚠️ Import cancelled'], progress.progress_percentage);
                            }

                            // Show close button, hide cancel button
                            $('#squarekit-cancel-import').hide();
                            $('#squarekit-close-import').show();

                            // Auto-close after 5 seconds for successful imports
                            if (progress.status === 'completed') {
                                setTimeout(function() {
                                    $('#squarekit-import-modal').hide();
                                    $btn.prop('disabled', false).html(originalHtml);
                                }, 5000);
                            } else {
                                // For failed/cancelled, restore button immediately
                                $btn.prop('disabled', false).html(originalHtml);
                            }
                        }
                    } else {
                        // Error getting progress, continue polling
                        console.log('Error getting progress:', response.data.message);
                    }
                },
                error: function() {
                    // Network error, continue polling
                    console.log('Network error getting progress');
                }
            });
        }

        // Start polling immediately, then every second
        pollProgress();
        pollInterval = setInterval(pollProgress, 1000);
    }

    // Update bulk import progress display
    function updateBulkImportProgress(progress) {
        // Build detailed progress steps based on actual progress
        const steps = [];

        // Main progress step
        let mainStep = 'Importing Products from Square';
        if (progress.current_item_name && progress.current_item_name !== 'Import completed') {
            mainStep = `Importing: ${progress.current_item_name}`;
        } else if (progress.current_item_name === 'Import completed') {
            mainStep = 'Import Completed Successfully!';
        }

        // Add detailed progress information
        let progressDetails = '';
        if (progress.processed_items > 0) {
            progressDetails += `Progress: ${progress.processed_items}/${progress.total_items} products`;
            if (progress.successful_items > 0 || progress.failed_items > 0) {
                progressDetails += ` (${progress.successful_items} successful`;
                if (progress.failed_items > 0) {
                    progressDetails += `, ${progress.failed_items} failed`;
                }
                progressDetails += ')';
            }
        }

        // Build the complete status message
        let statusMessage = mainStep;
        if (progressDetails) {
            statusMessage += '\n' + progressDetails;
        }

        // Add step-by-step details for current product
        if (progress.current_item_name && progress.current_item_name !== 'Import completed') {
            statusMessage += '\nSteps: Fetching data → Creating product → Importing images → Setting variations → Validating';
        }

        steps.push(statusMessage);

        // Update the modal with enhanced progress display
        updateProgressModal(steps, progress.progress_percentage);

        // Also update the summary section if it exists
        updateProgressSummary(progress);
    }

    // Enhanced progress tracking with detailed stages
    let currentProductStage = 0;
    let allLogEntries = [];
    let isLogExpanded = false;

    const importStages = [
        'Fetching Square data',
        'Validating product data',
        'Creating WooCommerce product',
        'Importing images',
        'Setting up variations',
        'Importing modifiers',
        'Final validation'
    ];

    // Update progress summary section
    function updateProgressSummary(progress) {
        // Show the import details section
        $('.squarekit-import-details').show();

        // Update summary counts
        $('#squarekit-total-count').text(progress.total_items || 0);
        $('#squarekit-processed-count').text(progress.processed_items || 0);
        $('#squarekit-success-count').text(progress.successful_items || 0);
        $('#squarekit-failed-count').text(progress.failed_items || 0);

        // Add detailed log entry with import stages
        if (progress.current_item_name && progress.current_item_name !== 'Import completed') {
            const timestamp = new Date().toLocaleTimeString();

            // Cycle through import stages for visual effect
            const stageIndex = (progress.processed_items - 1) % importStages.length;
            const currentStage = importStages[stageIndex];

            const logEntry = {
                timestamp: timestamp,
                message: `${currentStage}: ${progress.current_item_name}`,
                type: 'info'
            };

            allLogEntries.unshift(logEntry);
            updateLogDisplay();
        }
    }

    // Update log display based on expanded/collapsed state
    function updateLogDisplay() {
        const logContainer = $('#squarekit-import-log-content');
        const logSummary = $('#squarekit-log-summary');

        if (isLogExpanded) {
            // Show all entries
            logContainer.empty();
            allLogEntries.forEach(entry => {
                const logEntryHtml = `<div class="log-entry ${entry.type}"><span class="log-time">${entry.timestamp}</span> ${entry.message}</div>`;
                logContainer.append(logEntryHtml);
            });
            logSummary.hide();
        } else {
            // Show only last 5 entries
            const recentEntries = allLogEntries.slice(0, 5);
            logContainer.empty();
            recentEntries.forEach(entry => {
                const logEntryHtml = `<div class="log-entry ${entry.type}"><span class="log-time">${entry.timestamp}</span> ${entry.message}</div>`;
                logContainer.append(logEntryHtml);
            });

            // Show summary of hidden entries
            const hiddenCount = allLogEntries.length - 5;
            if (hiddenCount > 0) {
                logSummary.html(`<small class="hidden-entries-count">${hiddenCount} more entries...</small>`).show();
            } else {
                logSummary.hide();
            }
        }
    }

    // Update progress modal with steps and percentage
    function updateProgressModal(steps, percentage) {
        let html = '<div class="squarekit-progress-steps">';

        steps.forEach(function(step, index) {
            // Handle multi-line status messages
            const stepLines = step.split('\n');
            const mainStep = stepLines[0];
            const details = stepLines.slice(1);

            const isActive = index === 0; // For bulk imports, show first step as active
            const isComplete = percentage >= 100;

            html += '<div class="progress-step ' + (isActive ? 'active' : '') + (isComplete ? ' complete' : '') + '">';
            html += '<span class="step-icon">' + (isComplete ? '✓' : (isActive ? '⟳' : '○')) + '</span>';
            html += '<span class="step-text">' + mainStep;

            // Add details on separate lines with better styling
            if (details.length > 0) {
                details.forEach(function(detail) {
                    if (detail.trim()) {
                        html += '<br><small style="color: #666; font-size: 12px; margin-left: 20px;">' + detail + '</small>';
                    }
                });
            }

            html += '</span>';
            html += '</div>';
        });

        html += '</div>';

        // Enhanced progress bar with animation
        html += '<div class="squarekit-progress-bar">';
        html += '<div class="squarekit-progress-fill" style="width: ' + percentage + '%; transition: width 0.3s ease;"></div>';
        html += '</div>';
        html += '<div class="squarekit-progress-percentage">' + Math.round(percentage) + '%</div>';

        $('#squarekit-import-progress').html(html);
    }

    // Toggle log expansion
    $(document).on('click', '#squarekit-toggle-log', function() {
        isLogExpanded = !isLogExpanded;
        const toggleBtn = $(this);
        const toggleText = toggleBtn.find('.toggle-text');
        const toggleIcon = toggleBtn.find('.toggle-icon');
        const logContent = $('#squarekit-import-log-content');

        if (isLogExpanded) {
            toggleText.text('Show Less');
            toggleIcon.text('▲');
            logContent.removeClass('collapsed').addClass('expanded');
        } else {
            toggleText.text('Show More');
            toggleIcon.text('▼');
            logContent.removeClass('expanded').addClass('collapsed');
        }

        updateLogDisplay();
    });
});
</script>