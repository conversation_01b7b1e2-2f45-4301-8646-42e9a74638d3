<?php
/**
 * Account Management Page
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$settings = new SquareKit_Settings();
$environment = $settings->get_environment();
$is_connected = $settings->is_connected();

// Get merchant information if connected
$merchant_name = '';
$merchant_id = '';
$location_name = '';
$location_id = '';

if ( $is_connected ) {
    // Get merchant info from Square API
    if ( ! class_exists( 'SquareKit_Square_API' ) ) {
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
    }
    
    $square_api = new SquareKit_Square_API();
    $merchant_info = $square_api->get_merchant_info();
    
    if ( ! is_wp_error( $merchant_info ) && is_array( $merchant_info ) ) {
        $merchant_name = $merchant_info['business_name'] ?? '';
        $merchant_id = $merchant_info['id'] ?? '';
    }
    
    // Get location info
    $location_id = $settings->get_location_id();
    if ( $location_id ) {
        $locations = $square_api->get_locations();
        if ( ! is_wp_error( $locations ) && is_array( $locations ) ) {
            foreach ( $locations as $location ) {
                if ( $location['id'] === $location_id ) {
                    $location_name = $location['name'];
                    break;
                }
            }
        }
    }
}

// Handle disconnect action
if ( isset( $_POST['squarekit_disconnect_nonce'] ) && wp_verify_nonce( $_POST['squarekit_disconnect_nonce'], 'squarekit_disconnect' ) ) {
    if ( isset( $_POST['disconnect_square'] ) ) {
        // Clear OAuth tokens
        if ( class_exists( 'SquareKit_Secure_Storage' ) ) {
            SquareKit_Secure_Storage::clear_tokens( $environment );
        }
        
        // Clear settings
        $settings->delete( 'access_token' );
        $settings->delete( 'refresh_token' );
        $settings->delete( 'location_id' );
        $settings->delete( 'merchant_id' );
        $settings->delete( 'webhook_signature_key' );
        $settings->set( 'setup_complete', false );
        
        // Clear any cached data
        delete_transient( 'squarekit_locations' );
        delete_transient( 'squarekit_merchant_info' );
        
        echo '<div class="notice notice-success"><p>' . esc_html__( 'Square account disconnected successfully.', 'squarekit' ) . '</p></div>';
        
        // Refresh connection status
        $is_connected = false;
        $merchant_name = '';
        $merchant_id = '';
        $location_name = '';
    }
}
?>

<div class="wrap squarekit-account">
    <div class="squarekit-header">
        <h1><?php esc_html_e( 'Account Management', 'squarekit' ); ?></h1>
        <div class="squarekit-header-actions">
            <span class="squarekit-environment-badge <?php echo esc_attr( $environment ); ?>">
                <?php echo esc_html( ucfirst( $environment ) ); ?>
            </span>
            <?php if ( $is_connected ): ?>
                <span class="squarekit-status-badge connected">
                    <span class="dashicons dashicons-yes"></span>
                    <?php esc_html_e( 'Connected', 'squarekit' ); ?>
                </span>
            <?php else: ?>
                <span class="squarekit-status-badge disconnected">
                    <span class="dashicons dashicons-no"></span>
                    <?php esc_html_e( 'Not Connected', 'squarekit' ); ?>
                </span>
            <?php endif; ?>
        </div>
    </div>

    <?php if ( $is_connected ): ?>
        <div class="squarekit-account-info">
            <div class="squarekit-card">
                <h2><?php esc_html_e( 'Connected Square Account', 'squarekit' ); ?></h2>
                
                <table class="squarekit-info-table">
                    <tr>
                        <td class="label"><?php esc_html_e( 'Business Name:', 'squarekit' ); ?></td>
                        <td class="value"><?php echo esc_html( $merchant_name ?: __( 'Not available', 'squarekit' ) ); ?></td>
                    </tr>
                    <tr>
                        <td class="label"><?php esc_html_e( 'Merchant ID:', 'squarekit' ); ?></td>
                        <td class="value">
                            <code><?php echo esc_html( $merchant_id ?: __( 'Not available', 'squarekit' ) ); ?></code>
                        </td>
                    </tr>
                    <tr>
                        <td class="label"><?php esc_html_e( 'Environment:', 'squarekit' ); ?></td>
                        <td class="value">
                            <span class="squarekit-environment-badge <?php echo esc_attr( $environment ); ?>">
                                <?php echo esc_html( ucfirst( $environment ) ); ?>
                            </span>
                        </td>
                    </tr>
                    <?php if ( $location_name ): ?>
                    <tr>
                        <td class="label"><?php esc_html_e( 'Active Location:', 'squarekit' ); ?></td>
                        <td class="value"><?php echo esc_html( $location_name ); ?></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>

            <div class="squarekit-card squarekit-danger-zone">
                <h2><?php esc_html_e( 'Disconnect Square Account', 'squarekit' ); ?></h2>
                <p class="description">
                    <?php esc_html_e( 'Disconnecting your Square account will remove all stored credentials and stop synchronization. You will need to reconnect and reconfigure your settings.', 'squarekit' ); ?>
                </p>
                
                <form method="post" action="" onsubmit="return confirm('<?php esc_attr_e( 'Are you sure you want to disconnect your Square account? This action cannot be undone and will require you to reconnect and reconfigure your settings.', 'squarekit' ); ?>');">
                    <?php wp_nonce_field( 'squarekit_disconnect', 'squarekit_disconnect_nonce' ); ?>
                    <button type="submit" name="disconnect_square" class="button button-danger">
                        <span class="dashicons dashicons-no"></span>
                        <?php esc_html_e( 'Disconnect Square Account', 'squarekit' ); ?>
                    </button>
                </form>
            </div>
        </div>
    <?php else: ?>
        <div class="squarekit-not-connected">
            <div class="squarekit-card">
                <h2><?php esc_html_e( 'No Square Account Connected', 'squarekit' ); ?></h2>
                <p><?php esc_html_e( 'You need to connect your Square account to manage it here.', 'squarekit' ); ?></p>
                
                <div class="squarekit-actions">
                    <a href="<?php echo esc_url( admin_url( 'admin.php?page=squarekit-wizard' ) ); ?>" class="button button-primary">
                        <span class="dashicons dashicons-admin-links"></span>
                        <?php esc_html_e( 'Connect Square Account', 'squarekit' ); ?>
                    </a>
                    <a href="<?php echo esc_url( admin_url( 'admin.php?page=squarekit-oauth-settings' ) ); ?>" class="button">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php esc_html_e( 'Configure OAuth Settings', 'squarekit' ); ?>
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.squarekit-account .squarekit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2em;
    padding-bottom: 1em;
    border-bottom: 1px solid #e2e8f0;
}

.squarekit-account .squarekit-header h1 {
    margin: 0;
    color: #1e293b;
}

.squarekit-header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.squarekit-environment-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.squarekit-environment-badge.sandbox {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fbbf24;
}

.squarekit-environment-badge.production {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

.squarekit-status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.squarekit-status-badge.connected {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #10b981;
}

.squarekit-status-badge.disconnected {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #f87171;
}

.squarekit-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2em;
    margin-bottom: 2em;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.squarekit-card h2 {
    margin-top: 0;
    margin-bottom: 1.5em;
    color: #1e293b;
    font-size: 1.25em;
}

.squarekit-info-table {
    width: 100%;
    border-collapse: collapse;
}

.squarekit-info-table td {
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.squarekit-info-table td.label {
    font-weight: 600;
    color: #475569;
    width: 200px;
}

.squarekit-info-table td.value {
    color: #1e293b;
}

.squarekit-info-table code {
    background: #f8fafc;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
}

.squarekit-danger-zone {
    border-color: #fecaca;
    background: #fef2f2;
}

.squarekit-danger-zone h2 {
    color: #dc2626;
}

.button-danger {
    background: #dc2626 !important;
    border-color: #dc2626 !important;
    color: white !important;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.button-danger:hover {
    background: #b91c1c !important;
    border-color: #b91c1c !important;
}

.squarekit-actions {
    display: flex;
    gap: 12px;
    margin-top: 1.5em;
}

.squarekit-actions .button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}
</style>
