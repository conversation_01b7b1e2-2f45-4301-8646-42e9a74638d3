<?php
/**
 * Bulk Operations Admin Page
 *
 * @package SquareKit
 * @subpackage SquareKit/admin/partials
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>

<div class="wrap squarekit-bulk-operations">
    <h1><?php esc_html_e( 'Square Kit Bulk Operations', 'squarekit' ); ?></h1>
    
    <div id="squarekit-bulk-operations-feedback" role="status" aria-live="polite"></div>
    
    <!-- Active Operations -->
    <?php if ( ! empty( $active_operations ) ): ?>
        <div class="squarekit-active-operations">
            <h2><?php esc_html_e( 'Active Operations', 'squarekit' ); ?></h2>
            <div class="squarekit-operations-grid">
                <?php foreach ( $active_operations as $operation ): ?>
                    <div class="squarekit-operation-card" data-operation-id="<?php echo esc_attr( $operation->id ); ?>">
                        <div class="squarekit-operation-header">
                            <h3><?php echo esc_html( ucfirst( str_replace( '_', ' ', $operation->operation_type ) ) ); ?></h3>
                            <span class="squarekit-operation-status <?php echo esc_attr( $operation->operation_status ); ?>">
                                <?php echo esc_html( ucfirst( $operation->operation_status ) ); ?>
                            </span>
                        </div>
                        
                        <div class="squarekit-operation-progress">
                            <div class="squarekit-progress-bar">
                                <div class="squarekit-progress-fill" style="width: <?php echo esc_attr( $operation->progress_percentage ); ?>%"></div>
                            </div>
                            <div class="squarekit-progress-stats">
                                <span><?php echo esc_html( sprintf( __( '%d of %d items processed', 'squarekit' ), $operation->processed_items, $operation->total_items ) ); ?></span>
                                <span><?php echo esc_html( sprintf( __( '%d successful, %d failed', 'squarekit' ), $operation->successful_items, $operation->failed_items ) ); ?></span>
                            </div>
                        </div>
                        
                        <?php if ( $operation->current_item_name ): ?>
                            <div class="squarekit-current-item">
                                <strong><?php esc_html_e( 'Currently processing:', 'squarekit' ); ?></strong>
                                <?php echo esc_html( $operation->current_item_name ); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="squarekit-operation-actions">
                            <button type="button" class="button cancel-operation" data-operation-id="<?php echo esc_attr( $operation->id ); ?>">
                                <?php esc_html_e( 'Cancel', 'squarekit' ); ?>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- New Operation -->
    <div class="squarekit-new-operation">
        <h2><?php esc_html_e( 'Start New Operation', 'squarekit' ); ?></h2>
        
        <form id="squarekit-bulk-operation-form" method="post">
            <?php wp_nonce_field( 'squarekit_bulk_operation', 'squarekit_bulk_nonce' ); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="operation_type"><?php esc_html_e( 'Operation Type', 'squarekit' ); ?></label>
                    </th>
                    <td>
                        <select name="operation_type" id="operation_type" required>
                            <option value=""><?php esc_html_e( 'Select operation type...', 'squarekit' ); ?></option>
                            <option value="import_products"><?php esc_html_e( 'Import Products from Square', 'squarekit' ); ?></option>
                            <option value="export_products"><?php esc_html_e( 'Export Products to Square', 'squarekit' ); ?></option>
                            <option value="sync_inventory"><?php esc_html_e( 'Sync Inventory from Square', 'squarekit' ); ?></option>
                            <option value="import_customers"><?php esc_html_e( 'Import Customers from Square', 'squarekit' ); ?></option>
                            <option value="export_customers"><?php esc_html_e( 'Export Customers to Square', 'squarekit' ); ?></option>
                            <option value="bulk_status_update"><?php esc_html_e( 'Bulk Status Update', 'squarekit' ); ?></option>
                            <option value="bulk_category_update"><?php esc_html_e( 'Bulk Category Update', 'squarekit' ); ?></option>
                            <option value="bulk_attribute_update"><?php esc_html_e( 'Bulk Attribute Update', 'squarekit' ); ?></option>
                        </select>
                    </td>
                </tr>
                
                <!-- Operation-specific options -->
                <tr class="operation-options" style="display: none;">
                    <th scope="row"><?php esc_html_e( 'Options', 'squarekit' ); ?></th>
                    <td>
                        <div id="operation-options-container">
                            <!-- Dynamic options will be loaded here -->
                        </div>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <button type="submit" class="button button-primary" id="start-operation">
                    <?php esc_html_e( 'Start Operation', 'squarekit' ); ?>
                </button>
            </p>
        </form>
    </div>
    
    <!-- Recent Operations -->
    <?php if ( ! empty( $recent_operations ) ): ?>
        <div class="squarekit-recent-operations">
            <h2><?php esc_html_e( 'Recent Operations', 'squarekit' ); ?></h2>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php esc_html_e( 'Type', 'squarekit' ); ?></th>
                        <th><?php esc_html_e( 'Status', 'squarekit' ); ?></th>
                        <th><?php esc_html_e( 'Progress', 'squarekit' ); ?></th>
                        <th><?php esc_html_e( 'Started', 'squarekit' ); ?></th>
                        <th><?php esc_html_e( 'Completed', 'squarekit' ); ?></th>
                        <th><?php esc_html_e( 'Actions', 'squarekit' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ( $recent_operations as $operation ): ?>
                        <tr>
                            <td><?php echo esc_html( ucfirst( str_replace( '_', ' ', $operation->operation_type ) ) ); ?></td>
                            <td>
                                <span class="squarekit-status <?php echo esc_attr( $operation->operation_status ); ?>">
                                    <?php echo esc_html( ucfirst( $operation->operation_status ) ); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ( $operation->total_items > 0 ): ?>
                                    <?php echo esc_html( sprintf( '%d/%d (%s%%)', $operation->processed_items, $operation->total_items, number_format( $operation->progress_percentage, 1 ) ) ); ?>
                                <?php else: ?>
                                    <?php esc_html_e( 'N/A', 'squarekit' ); ?>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $operation->started_at ) ) ); ?></td>
                            <td>
                                <?php if ( $operation->completed_at ): ?>
                                    <?php echo esc_html( date_i18n( get_option( 'date_format' ) . ' ' . get_option( 'time_format' ), strtotime( $operation->completed_at ) ) ); ?>
                                <?php else: ?>
                                    <?php esc_html_e( 'In Progress', 'squarekit' ); ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button type="button" class="button view-details" data-operation-id="<?php echo esc_attr( $operation->id ); ?>">
                                    <?php esc_html_e( 'View Details', 'squarekit' ); ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Operation Details Modal -->
<div id="squarekit-operation-modal" class="squarekit-modal" style="display: none;">
    <div class="squarekit-modal-content">
        <div class="squarekit-modal-header">
            <h3 id="modal-title"><?php esc_html_e( 'Operation Details', 'squarekit' ); ?></h3>
            <button type="button" class="squarekit-modal-close">&times;</button>
        </div>
        <div class="squarekit-modal-body" id="modal-body">
            <!-- Operation details will be loaded here -->
        </div>
    </div>
</div>

<script>
jQuery(function($) {
    var restUrl = '<?php echo esc_url( get_rest_url( null, 'squarekit/v1' ) ); ?>';
    var restNonce = '<?php echo esc_js( wp_create_nonce( 'wp_rest' ) ) ?>';
    
    // Operation type change handler
    $('#operation_type').on('change', function() {
        var operationType = $(this).val();
        var $optionsRow = $('.operation-options');
        var $optionsContainer = $('#operation-options-container');
        
        if (operationType) {
            $optionsRow.show();
            loadOperationOptions(operationType, $optionsContainer);
        } else {
            $optionsRow.hide();
        }
    });
    
    // Load operation-specific options
    function loadOperationOptions(operationType, container) {
        var options = '';
        
        switch (operationType) {
            case 'bulk_status_update':
                options = '<select name="status" required>' +
                    '<option value=""><?php esc_html_e( 'Select status...', 'squarekit' ); ?></option>' +
                    '<option value="publish"><?php esc_html_e( 'Published', 'squarekit' ); ?></option>' +
                    '<option value="draft"><?php esc_html_e( 'Draft', 'squarekit' ); ?></option>' +
                    '<option value="private"><?php esc_html_e( 'Private', 'squarekit' ); ?></option>' +
                    '</select>';
                break;
                
            case 'bulk_category_update':
                options = '<select name="category_ids[]" multiple required>' +
                    '<?php 
                    $categories = get_terms( array( 'taxonomy' => 'product_cat', 'hide_empty' => false ) );
                    foreach ( $categories as $category ): ?>
                    <option value="<?php echo esc_attr( $category->term_id ); ?>"><?php echo esc_html( $category->name ); ?></option>' +
                    '<?php endforeach; ?>
                    '</select>' +
                    '<label><input type="checkbox" name="append" value="1"> <?php esc_html_e( 'Append to existing categories', 'squarekit' ); ?></label>';
                break;
                
            case 'bulk_attribute_update':
                options = '<div class="attribute-fields">' +
                    '<div class="attribute-field">' +
                    '<input type="text" name="attributes[0][name]" placeholder="<?php esc_attr_e( 'Attribute name', 'squarekit' ); ?>" required>' +
                    '<input type="text" name="attributes[0][value]" placeholder="<?php esc_attr_e( 'Attribute value', 'squarekit' ); ?>" required>' +
                    '</div>' +
                    '</div>' +
                    '<button type="button" class="button add-attribute-field"><?php esc_html_e( 'Add Another Attribute', 'squarekit' ); ?></button>';
                break;
                
            default:
                options = '<p><?php esc_html_e( 'No additional options required for this operation.', 'squarekit' ); ?></p>';
        }
        
        container.html(options);
    }
    
    // Add attribute field
    $(document).on('click', '.add-attribute-field', function() {
        var fieldCount = $('.attribute-field').length;
        var newField = '<div class="attribute-field">' +
            '<input type="text" name="attributes[' + fieldCount + '][name]" placeholder="<?php esc_attr_e( 'Attribute name', 'squarekit' ); ?>" required>' +
            '<input type="text" name="attributes[' + fieldCount + '][value]" placeholder="<?php esc_attr_e( 'Attribute value', 'squarekit' ); ?>" required>' +
            '<button type="button" class="button remove-attribute-field"><?php esc_html_e( 'Remove', 'squarekit' ); ?></button>' +
            '</div>';
        $('.attribute-fields').append(newField);
    });
    
    // Remove attribute field
    $(document).on('click', '.remove-attribute-field', function() {
        $(this).closest('.attribute-field').remove();
    });
    
    // Start operation
    $('#squarekit-bulk-operation-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $('#start-operation');
        var operationType = $('#operation_type').val();
        
        if (!operationType) {
            showFeedback('<?php esc_html_e( 'Please select an operation type.', 'squarekit' ); ?>', 'error');
            return;
        }
        
        // Collect form data
        var formData = new FormData(this);
        var operationData = {};
        
        // Parse form data based on operation type
        switch (operationType) {
            case 'bulk_status_update':
                operationData.status = $('select[name="status"]').val();
                break;
                
            case 'bulk_category_update':
                operationData.category_ids = $('select[name="category_ids[]"]').val();
                operationData.append = $('input[name="append"]').is(':checked');
                break;
                
            case 'bulk_attribute_update':
                operationData.attributes = [];
                $('.attribute-field').each(function() {
                    var name = $(this).find('input[name$="[name]"]').val();
                    var value = $(this).find('input[name$="[value]"]').val();
                    if (name && value) {
                        operationData.attributes.push({ name: name, value: value });
                    }
                });
                break;
        }
        
        $submitBtn.prop('disabled', true).text('<?php esc_html_e( 'Starting...', 'squarekit' ); ?>');
        
        $.ajax({
            url: restUrl + '/bulk-operations',
            method: 'POST',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', restNonce);
            },
            data: {
                operation_type: operationType,
                operation_data: operationData
            },
            success: function(response) {
                if (response.success) {
                    showFeedback('<?php esc_html_e( 'Operation started successfully!', 'squarekit' ); ?>', 'success');
                    $form[0].reset();
                    $('.operation-options').hide();
                    
                    // Refresh page after a moment to show new operation
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showFeedback(response.message || '<?php esc_html_e( 'Failed to start operation.', 'squarekit' ); ?>', 'error');
                }
            },
            error: function() {
                showFeedback('<?php esc_html_e( 'AJAX error occurred.', 'squarekit' ); ?>', 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text('<?php esc_html_e( 'Start Operation', 'squarekit' ); ?>');
            }
        });
    });
    
    // Cancel operation
    $(document).on('click', '.cancel-operation', function() {
        var operationId = $(this).data('operation-id');
        var $btn = $(this);
        
        if (!confirm('<?php esc_html_e( 'Are you sure you want to cancel this operation?', 'squarekit' ); ?>')) {
            return;
        }
        
        $btn.prop('disabled', true).text('<?php esc_html_e( 'Cancelling...', 'squarekit' ); ?>');
        
        $.ajax({
            url: restUrl + '/bulk-operations/' + operationId,
            method: 'DELETE',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', restNonce);
            },
            success: function(response) {
                if (response.success) {
                    showFeedback('<?php esc_html_e( 'Operation cancelled successfully!', 'squarekit' ); ?>', 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showFeedback(response.message || '<?php esc_html_e( 'Failed to cancel operation.', 'squarekit' ); ?>', 'error');
                }
            },
            error: function() {
                showFeedback('<?php esc_html_e( 'AJAX error occurred.', 'squarekit' ); ?>', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text('<?php esc_html_e( 'Cancel', 'squarekit' ); ?>');
            }
        });
    });
    
    // View operation details
    $(document).on('click', '.view-details', function() {
        var operationId = $(this).data('operation-id');
        
        $.ajax({
            url: restUrl + '/bulk-operations/' + operationId,
            method: 'GET',
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-WP-Nonce', restNonce);
            },
            success: function(response) {
                if (response.success) {
                    showOperationModal(response.operation);
                } else {
                    showFeedback(response.message || '<?php esc_html_e( 'Failed to load operation details.', 'squarekit' ); ?>', 'error');
                }
            },
            error: function() {
                showFeedback('<?php esc_html_e( 'AJAX error occurred.', 'squarekit' ); ?>', 'error');
            }
        });
    });
    
    // Show operation modal
    function showOperationModal(operation) {
        var modalContent = '<div class="operation-details">' +
            '<p><strong><?php esc_html_e( 'Type:', 'squarekit' ); ?></strong> ' + operation.operation_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + '</p>' +
            '<p><strong><?php esc_html_e( 'Status:', 'squarekit' ); ?></strong> ' + operation.operation_status + '</p>' +
            '<p><strong><?php esc_html_e( 'Progress:', 'squarekit' ); ?></strong> ' + operation.processed_items + ' / ' + operation.total_items + ' (' + operation.progress_percentage + '%)</p>' +
            '<p><strong><?php esc_html_e( 'Results:', 'squarekit' ); ?></strong> ' + operation.successful_items + ' successful, ' + operation.failed_items + ' failed</p>' +
            '<p><strong><?php esc_html_e( 'Started:', 'squarekit' ); ?></strong> ' + operation.started_at + '</p>';
            
        if (operation.completed_at) {
            modalContent += '<p><strong><?php esc_html_e( 'Completed:', 'squarekit' ); ?></strong> ' + operation.completed_at + '</p>';
        }
        
        if (operation.error_log) {
            modalContent += '<p><strong><?php esc_html_e( 'Error Log:', 'squarekit' ); ?></strong></p>' +
                '<pre>' + operation.error_log + '</pre>';
        }
        
        modalContent += '</div>';
        
        $('#modal-body').html(modalContent);
        $('#squarekit-operation-modal').show();
    }
    
    // Close modal
    $(document).on('click', '.squarekit-modal-close', function() {
        $('#squarekit-operation-modal').hide();
    });
    
    // Close modal when clicking outside
    $(document).on('click', '#squarekit-operation-modal', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
    
    // Update active operations progress
    function updateActiveOperations() {
        $('.squarekit-operation-card').each(function() {
            var $card = $(this);
            var operationId = $card.data('operation-id');
            
            $.ajax({
                url: restUrl + '/bulk-operations/' + operationId + '/progress',
                method: 'GET',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-WP-Nonce', restNonce);
                },
                success: function(response) {
                    if (response.success) {
                        var progress = response.progress;
                        
                        // Update progress bar
                        $card.find('.squarekit-progress-fill').css('width', progress.progress_percentage + '%');
                        
                        // Update stats
                        $card.find('.squarekit-progress-stats').html(
                            progress.processed_items + ' of ' + progress.total_items + ' items processed<br>' +
                            progress.successful_items + ' successful, ' + progress.failed_items + ' failed'
                        );
                        
                        // Update current item
                        if (progress.current_item_name) {
                            $card.find('.squarekit-current-item').html(
                                '<strong><?php esc_html_e( 'Currently processing:', 'squarekit' ); ?></strong> ' + progress.current_item_name
                            );
                        }
                        
                        // If completed, reload page
                        if (progress.status === 'completed' || progress.status === 'failed' || progress.status === 'cancelled') {
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        }
                    }
                }
            });
        });
    }
    
    // Update progress every 5 seconds for active operations
    if ($('.squarekit-operation-card').length > 0) {
        setInterval(updateActiveOperations, 5000);
    }
    
    // Show feedback message
    function showFeedback(message, type) {
        var $feedback = $('#squarekit-bulk-operations-feedback');
        $feedback.html('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $feedback.empty();
        }, 5000);
    }
});
</script>

<style>
.squarekit-bulk-operations {
    max-width: 1200px;
}

.squarekit-operations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.squarekit-operation-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.squarekit-operation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.squarekit-operation-header h3 {
    margin: 0;
    color: #333;
}

.squarekit-operation-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.squarekit-operation-status.pending {
    background: #fff3cd;
    color: #856404;
}

.squarekit-operation-status.in_progress {
    background: #d1ecf1;
    color: #0c5460;
}

.squarekit-operation-status.completed {
    background: #d4edda;
    color: #155724;
}

.squarekit-operation-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.squarekit-operation-status.cancelled {
    background: #e2e3e5;
    color: #383d41;
}

.squarekit-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.squarekit-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    transition: width 0.3s ease;
}

.squarekit-progress-stats {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.squarekit-current-item {
    margin: 10px 0;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
    font-size: 12px;
}

.squarekit-operation-actions {
    margin-top: 15px;
}

.squarekit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
}

.squarekit-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.squarekit-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.squarekit-modal-header h3 {
    margin: 0;
}

.squarekit-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.squarekit-modal-body {
    padding: 20px;
}

.operation-details p {
    margin: 10px 0;
}

.operation-details pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    max-height: 200px;
}

.attribute-fields {
    margin-bottom: 10px;
}

.attribute-field {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.attribute-field input {
    flex: 1;
}

.squarekit-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.squarekit-status.completed {
    background: #d4edda;
    color: #155724;
}

.squarekit-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.squarekit-status.cancelled {
    background: #e2e3e5;
    color: #383d41;
}
</style> 