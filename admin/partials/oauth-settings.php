<?php
/**
 * Square OAuth Settings Page
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Check if secure credentials are configured
$settings = new SquareKit_Settings();
$has_secure_sandbox = SquareKit_Secure_Storage::has_secure_credentials( 'sandbox' );
$has_secure_production = SquareKit_Secure_Storage::has_secure_credentials( 'production' );

// Handle form submission
if ( isset( $_POST['squarekit_oauth_nonce'] ) && wp_verify_nonce( $_POST['squarekit_oauth_nonce'], 'squarekit_oauth_settings' ) ) {
    $settings = new SquareKit_Settings();

    // Sanitize OAuth credentials
    $oauth_settings = array(
        'sandbox_application_id' => sanitize_text_field( $_POST['sandbox_application_id'] ?? '' ),
        'sandbox_client_secret' => sanitize_text_field( $_POST['sandbox_client_secret'] ?? '' ),
        'production_application_id' => sanitize_text_field( $_POST['production_application_id'] ?? '' ),
        'production_client_secret' => sanitize_text_field( $_POST['production_client_secret'] ?? '' ),
        'environment' => sanitize_text_field( $_POST['environment'] ?? 'sandbox' ),
    );

    // Validate Application ID formats
    $validation_errors = array();

    // Validate sandbox Application ID if provided
    if ( ! empty( $oauth_settings['sandbox_application_id'] ) ) {
        if ( strpos( $oauth_settings['sandbox_application_id'], 'sandbox-' ) !== 0 ) {
            if ( strpos( $oauth_settings['sandbox_application_id'], 'sq0idp-' ) === 0 ) {
                $validation_errors[] = __( 'Sandbox Application ID: This appears to be a Production Application ID. Sandbox IDs start with "sandbox-"', 'squarekit' );
            } else {
                $validation_errors[] = __( 'Sandbox Application ID: Invalid format. Sandbox IDs should start with "sandbox-"', 'squarekit' );
            }
        }
    }

    // Validate production Application ID if provided
    if ( ! empty( $oauth_settings['production_application_id'] ) ) {
        if ( strpos( $oauth_settings['production_application_id'], 'sq0idp-' ) !== 0 ) {
            if ( strpos( $oauth_settings['production_application_id'], 'sandbox-' ) === 0 ) {
                $validation_errors[] = __( 'Production Application ID: This appears to be a Sandbox Application ID. Production IDs start with "sq0idp-"', 'squarekit' );
            } else {
                $validation_errors[] = __( 'Production Application ID: Invalid format. Production IDs should start with "sq0idp-"', 'squarekit' );
            }
        }
    }

    // Save settings if validation passes
    if ( empty( $validation_errors ) ) {
        $settings->update( $oauth_settings );
        echo '<div class="notice notice-success"><p>' . esc_html__( 'OAuth settings saved successfully!', 'squarekit' ) . '</p></div>';
    } else {
        echo '<div class="notice notice-error"><p><strong>' . esc_html__( 'Validation Error:', 'squarekit' ) . '</strong></p><ul>';
        foreach ( $validation_errors as $error ) {
            echo '<li>' . esc_html( $error ) . '</li>';
        }
        echo '</ul></div>';
    }
}

$settings = new SquareKit_Settings();
$current_env = $settings->get_environment();
?>

<div class="squarekit-admin-wrap">
    <div class="squarekit-admin-header">
        <h1><?php esc_html_e( 'Square OAuth Configuration', 'squarekit' ); ?></h1>
        <p><?php esc_html_e( 'Configure your Square application credentials for both sandbox and production environments.', 'squarekit' ); ?></p>
    </div>

    <!-- Security Warning -->
    <?php if ( ! $has_secure_sandbox || ! $has_secure_production ) : ?>
    <div class="notice notice-warning">
        <h3><?php esc_html_e( '⚠️ Security Warning', 'squarekit' ); ?></h3>
        <p><strong><?php esc_html_e( 'Storing credentials in the database is NOT secure for production use.', 'squarekit' ); ?></strong></p>
        <p><?php esc_html_e( 'For production sites, store credentials in wp-config.php using constants:', 'squarekit' ); ?></p>
        <pre style="background: #f0f0f0; padding: 10px; margin: 10px 0;">
// Add to wp-config.php
define( 'SQUAREKIT_SANDBOX_APPLICATION_ID', 'sandbox-sq0idb-YOUR_APP_ID' );
define( 'SQUAREKIT_SANDBOX_CLIENT_SECRET', 'sandbox-sq0csp-YOUR_SECRET' );
define( 'SQUAREKIT_PRODUCTION_APPLICATION_ID', 'sq0idb-YOUR_APP_ID' );
define( 'SQUAREKIT_PRODUCTION_CLIENT_SECRET', 'sq0csp-YOUR_SECRET' );
        </pre>
        <p><?php esc_html_e( 'See wp-config-example.php in the plugin directory for a complete example.', 'squarekit' ); ?></p>
    </div>
    <?php endif; ?>

    <!-- Secure Credentials Status -->
    <?php if ( $has_secure_sandbox || $has_secure_production ) : ?>
    <div class="notice notice-success">
        <h3><?php esc_html_e( '✅ Secure Credentials Detected', 'squarekit' ); ?></h3>
        <ul>
            <?php if ( $has_secure_sandbox ) : ?>
                <li><?php esc_html_e( 'Sandbox credentials: Using secure constants/environment variables', 'squarekit' ); ?></li>
            <?php endif; ?>
            <?php if ( $has_secure_production ) : ?>
                <li><?php esc_html_e( 'Production credentials: Using secure constants/environment variables', 'squarekit' ); ?></li>
            <?php endif; ?>
        </ul>
        <p><em><?php esc_html_e( 'These secure credentials will override any values entered below.', 'squarekit' ); ?></em></p>
    </div>
    <?php endif; ?>

    <div class="squarekit-card">
        <h2><?php esc_html_e( 'OAuth Application Settings', 'squarekit' ); ?></h2>
        
        <div class="squarekit-oauth-instructions" id="oauth-instructions">
            <h3><?php esc_html_e( 'How to Get Your OAuth Credentials', 'squarekit' ); ?></h3>
            <ol>
                <li><?php esc_html_e( 'Go to the Square Developer Dashboard:', 'squarekit' ); ?> <a href="https://developer.squareup.com/apps" target="_blank">https://developer.squareup.com/apps</a></li>
                <li><?php esc_html_e( 'Create a new application or select an existing one', 'squarekit' ); ?></li>
                <li id="environment-instruction"><?php
                    if ( $current_env === 'sandbox' ) {
                        esc_html_e( 'Navigate to the "Sandbox" tab, then the "OAuth" section in your application settings', 'squarekit' );
                    } else {
                        esc_html_e( 'Navigate to the "Production" tab, then the "OAuth" section in your application settings', 'squarekit' );
                    }
                ?></li>
                <li><?php esc_html_e( 'Copy the Application ID and Application Secret', 'squarekit' ); ?></li>
                <li id="redirect-uri-instruction"><?php esc_html_e( 'Add this redirect URI to your Square app:', 'squarekit' ); ?> <code id="redirect-uri-display"><?php echo esc_url( admin_url( 'admin.php?page=squarekit-wizard&step=connect' ) ); ?></code></li>
            </ol>
        </div>

        <form method="post" action="" id="oauth-settings-form">
            <?php wp_nonce_field( 'squarekit_oauth_settings', 'squarekit_oauth_nonce' ); ?>

            <h3><?php esc_html_e( 'Active Environment', 'squarekit' ); ?></h3>
            <div class="squarekit-environment-selector">
                <label class="squarekit-radio-option <?php echo $current_env === 'sandbox' ? 'selected' : ''; ?>">
                    <input type="radio" name="environment" value="sandbox" <?php checked( $current_env, 'sandbox' ); ?> />
                    <div class="radio-content">
                        <strong><?php esc_html_e( 'Sandbox (Testing)', 'squarekit' ); ?></strong>
                        <small><?php esc_html_e( 'For development and testing', 'squarekit' ); ?></small>
                    </div>
                </label>

                <label class="squarekit-radio-option <?php echo $current_env === 'production' ? 'selected' : ''; ?>">
                    <input type="radio" name="environment" value="production" <?php checked( $current_env, 'production' ); ?> />
                    <div class="radio-content">
                        <strong><?php esc_html_e( 'Production (Live)', 'squarekit' ); ?></strong>
                        <small><?php esc_html_e( 'For live transactions', 'squarekit' ); ?></small>
                    </div>
                </label>
            </div>

            <div class="squarekit-credentials-section">
                <h3 id="credentials-title"><?php echo $current_env === 'sandbox' ? esc_html__( 'Sandbox Credentials', 'squarekit' ) : esc_html__( 'Production Credentials', 'squarekit' ); ?></h3>

                <div class="squarekit-credentials-grid">
                    <div class="credential-field">
                        <label for="application_id"><?php esc_html_e( 'Application ID', 'squarekit' ); ?></label>
                        <input type="text"
                               id="application_id"
                               name="<?php echo esc_attr( $current_env ); ?>_application_id"
                               value="<?php echo esc_attr( $settings->get( $current_env . '_application_id', '' ) ); ?>"
                               placeholder="<?php esc_attr_e( 'Enter Application ID', 'squarekit' ); ?>"
                               class="regular-text" />
                    </div>

                    <div class="credential-field">
                        <label for="client_secret"><?php esc_html_e( 'Application Secret', 'squarekit' ); ?></label>
                        <input type="password"
                               id="client_secret"
                               name="<?php echo esc_attr( $current_env ); ?>_client_secret"
                               value="<?php echo esc_attr( $settings->get( $current_env . '_client_secret', '' ) ); ?>"
                               placeholder="<?php esc_attr_e( 'Enter Application Secret', 'squarekit' ); ?>"
                               class="regular-text" />
                    </div>
                </div>

                <!-- Hidden fields for the non-active environment to preserve values -->
                <?php $other_env = $current_env === 'sandbox' ? 'production' : 'sandbox'; ?>
                <input type="hidden" name="<?php echo esc_attr( $other_env ); ?>_application_id" value="<?php echo esc_attr( $settings->get( $other_env . '_application_id', '' ) ); ?>" />
                <input type="hidden" name="<?php echo esc_attr( $other_env ); ?>_client_secret" value="<?php echo esc_attr( $settings->get( $other_env . '_client_secret', '' ) ); ?>" />
            </div>

            <p class="submit">
                <button type="submit" class="squarekit-action-btn">
                    <span class="dashicons dashicons-admin-settings"></span>
                    <?php esc_html_e( 'Save OAuth Settings', 'squarekit' ); ?>
                </button>
            </p>
        </form>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('oauth-settings-form');
        const radioButtons = form.querySelectorAll('input[name="environment"]');
        const credentialsTitle = document.getElementById('credentials-title');
        const applicationIdField = document.getElementById('application_id');
        const clientSecretField = document.getElementById('client_secret');

        // Store current values
        const sandboxAppId = '<?php echo esc_js( $settings->get( 'sandbox_application_id', '' ) ); ?>';
        const sandboxSecret = '<?php echo esc_js( $settings->get( 'sandbox_client_secret', '' ) ); ?>';
        const productionAppId = '<?php echo esc_js( $settings->get( 'production_application_id', '' ) ); ?>';
        const productionSecret = '<?php echo esc_js( $settings->get( 'production_client_secret', '' ) ); ?>';

        function updateCredentialsSection(environment) {
            // Update title
            credentialsTitle.textContent = environment === 'sandbox' ?
                '<?php echo esc_js( __( 'Sandbox Credentials', 'squarekit' ) ); ?>' :
                '<?php echo esc_js( __( 'Production Credentials', 'squarekit' ) ); ?>';

            // Update field names and values
            applicationIdField.name = environment + '_application_id';
            clientSecretField.name = environment + '_client_secret';

            if (environment === 'sandbox') {
                applicationIdField.value = sandboxAppId;
                clientSecretField.value = sandboxSecret;
            } else {
                applicationIdField.value = productionAppId;
                clientSecretField.value = productionSecret;
            }

            // Update radio button styling
            document.querySelectorAll('.squarekit-radio-option').forEach(option => {
                option.classList.remove('selected');
            });

            const selectedRadio = form.querySelector(`input[value="${environment}"]`);
            if (selectedRadio) {
                selectedRadio.closest('.squarekit-radio-option').classList.add('selected');
            }

            // Update hidden fields for the other environment
            const otherEnv = environment === 'sandbox' ? 'production' : 'sandbox';
            let hiddenAppId = form.querySelector(`input[name="${otherEnv}_application_id"][type="hidden"]`);
            let hiddenSecret = form.querySelector(`input[name="${otherEnv}_client_secret"][type="hidden"]`);

            if (hiddenAppId) {
                hiddenAppId.value = otherEnv === 'sandbox' ? sandboxAppId : productionAppId;
            }
            if (hiddenSecret) {
                hiddenSecret.value = otherEnv === 'sandbox' ? sandboxSecret : productionSecret;
            }
        }

        // Handle radio button changes
        radioButtons.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    updateCredentialsSection(this.value);
                }
            });
        });

        // Initialize with current environment
        const currentEnv = '<?php echo esc_js( $current_env ); ?>';
        updateCredentialsSection(currentEnv);
    });
    </script>

    <div class="squarekit-card">
        <h3><?php esc_html_e( 'Current Status', 'squarekit' ); ?></h3>
        
        <?php
        $sandbox_configured = !empty( $settings->get( 'sandbox_application_id' ) ) && !empty( $settings->get( 'sandbox_client_secret' ) );
        $production_configured = !empty( $settings->get( 'production_application_id' ) ) && !empty( $settings->get( 'production_client_secret' ) );
        $current_env_configured = $current_env === 'sandbox' ? $sandbox_configured : $production_configured;
        $is_connected = $settings->is_connected();
        // Only show connected if the current environment is both configured AND connected
        $current_env_connected = $current_env_configured && $is_connected;
        ?>

        <div class="squarekit-status-grid">
            <div class="squarekit-status-item">
                <span class="squarekit-status <?php echo $sandbox_configured ? 'connected' : 'disconnected'; ?>">
                    <?php esc_html_e( 'Sandbox Configured', 'squarekit' ); ?>
                </span>
            </div>

            <div class="squarekit-status-item">
                <span class="squarekit-status <?php echo $production_configured ? 'connected' : 'disconnected'; ?>">
                    <?php esc_html_e( 'Production Configured', 'squarekit' ); ?>
                </span>
            </div>

            <div class="squarekit-status-item">
                <span class="squarekit-status <?php echo $current_env_connected ? 'connected' : 'disconnected'; ?>">
                    <?php printf( esc_html__( '%s Connected', 'squarekit' ), ucfirst( $current_env ) ); ?>
                </span>
            </div>
        </div>

        <?php if ( !$sandbox_configured && !$production_configured ): ?>
            <div class="squarekit-notice">
                <p><strong><?php esc_html_e( 'Action Required:', 'squarekit' ); ?></strong> <?php esc_html_e( 'Please configure your OAuth credentials above to enable Square integration.', 'squarekit' ); ?></p>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.squarekit-oauth-instructions {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 1.5em;
    border-radius: 12px;
    margin-bottom: 2em;
    border: 1px solid #e2e8f0;
}

.squarekit-oauth-instructions h3 {
    margin-top: 0;
    color: #1e293b;
}

.squarekit-oauth-instructions ol {
    margin: 1em 0;
    padding-left: 1.5em;
}

.squarekit-oauth-instructions li {
    margin-bottom: 0.5em;
    line-height: 1.6;
}

.squarekit-oauth-instructions code {
    background: #e2e8f0;
    padding: 0.2em 0.4em;
    border-radius: 4px;
    font-size: 0.9em;
}

.squarekit-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
    margin: 1em 0;
}

.squarekit-status-item {
    text-align: center;
}

.squarekit-notice {
    background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
    border: 1px solid #f59e0b;
    border-radius: 12px;
    padding: 1em;
    margin-top: 1em;
}

.squarekit-notice p {
    margin: 0;
    color: #92400e;
}

.regular-text {
    width: 100%;
    max-width: 300px;
    padding: 0.5em;
    border: 1px solid #ddd;
    border-radius: 6px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const environmentRadios = document.querySelectorAll('input[name="environment"]');
    const environmentInstruction = document.getElementById('environment-instruction');
    const applicationIdInput = document.getElementById('application_id');
    const form = document.getElementById('oauth-settings-form');

    function updateInstructions() {
        const selectedEnvironment = document.querySelector('input[name="environment"]:checked').value;

        if (selectedEnvironment === 'sandbox') {
            environmentInstruction.textContent = <?php echo wp_json_encode( __( 'Navigate to the "Sandbox" tab, then the "OAuth" section in your application settings', 'squarekit' ) ); ?>;
        } else {
            environmentInstruction.textContent = <?php echo wp_json_encode( __( 'Navigate to the "Production" tab, then the "OAuth" section in your application settings', 'squarekit' ) ); ?>;
        }

        // Validate Application IDs when environment changes
        validateApplicationIds();
    }

    function validateApplicationId(input, expectedEnvironment) {
        if (!input || !input.value.trim()) {
            clearValidation(input);
            return true; // Empty is valid (not required)
        }

        const value = input.value.trim();
        const isSandboxFormat = value.startsWith('sandbox-');
        const isProductionFormat = value.startsWith('sq0idp-');

        let isValid = false;
        let message = '';

        if (expectedEnvironment === 'sandbox') {
            isValid = isSandboxFormat;
            if (!isValid && isProductionFormat) {
                message = <?php echo wp_json_encode( __( 'This appears to be a Production Application ID. Sandbox IDs start with "sandbox-"', 'squarekit' ) ); ?>;
            } else if (!isValid) {
                message = <?php echo wp_json_encode( __( 'Sandbox Application IDs should start with "sandbox-"', 'squarekit' ) ); ?>;
            }
        } else {
            isValid = isProductionFormat;
            if (!isValid && isSandboxFormat) {
                message = <?php echo wp_json_encode( __( 'This appears to be a Sandbox Application ID. Production IDs start with "sq0idp-"', 'squarekit' ) ); ?>;
            } else if (!isValid) {
                message = <?php echo wp_json_encode( __( 'Production Application IDs should start with "sq0idp-"', 'squarekit' ) ); ?>;
            }
        }

        showValidation(input, isValid, message);
        return isValid;
    }

    function validateApplicationIds() {
        const selectedEnvironment = document.querySelector('input[name="environment"]:checked').value;

        // Validate the currently visible Application ID input
        validateApplicationId(applicationIdInput, selectedEnvironment);
    }

    function showValidation(input, isValid, message) {
        if (!input) return;

        // Remove existing validation
        clearValidation(input);

        if (!isValid && message) {
            input.style.borderColor = '#dc3545';
            input.style.backgroundColor = '#fff5f5';

            const errorDiv = document.createElement('div');
            errorDiv.className = 'squarekit-validation-error';
            errorDiv.style.color = '#dc3545';
            errorDiv.style.fontSize = '12px';
            errorDiv.style.marginTop = '4px';
            errorDiv.textContent = message;

            input.parentNode.appendChild(errorDiv);
        } else if (isValid && input.value.trim()) {
            input.style.borderColor = '#28a745';
            input.style.backgroundColor = '#f8fff8';
        }
    }

    function clearValidation(input) {
        if (!input) return;

        input.style.borderColor = '';
        input.style.backgroundColor = '';

        const existingError = input.parentNode.querySelector('.squarekit-validation-error');
        if (existingError) {
            existingError.remove();
        }
    }

    // Update instructions when environment changes
    environmentRadios.forEach(function(radio) {
        radio.addEventListener('change', updateInstructions);
    });

    // Validate Application ID on input
    if (applicationIdInput) {
        applicationIdInput.addEventListener('input', function() {
            const selectedEnvironment = document.querySelector('input[name="environment"]:checked').value;
            validateApplicationId(this, selectedEnvironment);
        });
    }

    // Validate on form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            const selectedEnvironment = document.querySelector('input[name="environment"]:checked').value;
            const isValid = validateApplicationId(applicationIdInput, selectedEnvironment);

            if (!isValid) {
                e.preventDefault();
                alert(<?php echo wp_json_encode( __( 'Please fix the Application ID format before saving.', 'squarekit' ) ); ?>);
            }
        });
    }

    // Set initial state
    updateInstructions();
});
</script>
