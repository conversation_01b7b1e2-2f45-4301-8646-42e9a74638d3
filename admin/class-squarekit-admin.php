<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @package SquareKit
 * @subpackage SquareKit/admin
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two hooks for
 * enqueuing the admin-specific stylesheet and JavaScript.
 *
 * @since 1.0.0
 */
class SquareKit_Admin {

    /**
     * Settings instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_Settings
     */
    protected $settings;

    /**
     * DB instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_DB
     */
    protected $db;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->settings = new SquareKit_Settings();
        $this->db = new SquareKit_DB();
        // Register bulk actions
        add_filter( 'bulk_actions-edit-product', array( $this, 'register_product_bulk_actions' ) );
        add_filter( 'handle_bulk_actions-edit-product', array( $this, 'handle_product_bulk_actions' ), 10, 3 );
        add_action( 'admin_notices', array( $this, 'bulk_action_notices' ) );
        // AJAX for re-importing modifiers
        add_action( 'wp_ajax_squarekit_reimport_modifiers', array( $this, 'ajax_reimport_modifiers' ) );
        
        // AJAX for webhook management
        add_action( 'wp_ajax_squarekit_test_webhook', array( $this, 'ajax_test_webhook' ) );
        add_action( 'wp_ajax_squarekit_refresh_webhook', array( $this, 'ajax_refresh_webhook' ) );
        
        // AJAX for cron management
        add_action( 'wp_ajax_squarekit_test_cron', array( $this, 'ajax_test_cron' ) );
        
        // AJAX for customer role mapping
        add_action( 'wp_ajax_squarekit_apply_role_mapping_all', array( $this, 'ajax_apply_role_mapping_all' ) );
        
        // AJAX for Square inventory management
        add_action( 'wp_ajax_squarekit_get_square_inventory', array( $this, 'ajax_get_square_inventory' ) );
        add_action( 'wp_ajax_squarekit_fetch_from_square', array( $this, 'ajax_fetch_from_square' ) );
        add_action( 'wp_ajax_squarekit_import_product', array( $this, 'ajax_import_product' ) );
        add_action( 'wp_ajax_squarekit_sync_product', array( $this, 'ajax_sync_product' ) );
        add_action( 'wp_ajax_squarekit_get_product_details', array( $this, 'ajax_get_product_details' ) );
        add_action( 'wp_ajax_squarekit_check_cached_products', array( $this, 'ajax_check_cached_products' ) );
        
        // AJAX for connection testing
        add_action( 'wp_ajax_squarekit_test_connection', array( $this, 'ajax_test_connection' ) );
        add_action( 'wp_ajax_squarekit_refresh_connection', array( $this, 'ajax_refresh_connection' ) );

        // AJAX for manual sync operations
        add_action( 'wp_ajax_squarekit_manual_sync', array( $this, 'ajax_manual_sync' ) );

        // AJAX for bulk import operations
        add_action( 'wp_ajax_squarekit_import_all_products', array( $this, 'ajax_import_all_products' ) );
        add_action( 'wp_ajax_squarekit_get_bulk_operation_progress', array( $this, 'ajax_get_bulk_operation_progress' ) );

        // AJAX for orders management
        add_action( 'wp_ajax_squarekit_orders_table_ajax', array( $this, 'ajax_orders_table' ) );
        add_action( 'wp_ajax_squarekit_order_action', array( $this, 'ajax_order_action' ) );
        add_action( 'wp_ajax_squarekit_order_details', array( $this, 'ajax_order_details' ) );
    }

    /**
     * AJAX handler: Re-import modifiers from Square
     */
    public function ajax_reimport_modifiers() {
        check_ajax_referer( 'squarekit-admin', 'nonce' );
        
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Insufficient permissions.', 'squarekit' ) ), 403 );
        }
        
        $product_id = intval( $_POST['product_id'] );
        if ( ! $product_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid product ID.', 'squarekit' ) ), 400 );
        }
        
        $modifiers = $this->get_modifiers_from_square( $product_id );
        if ( is_wp_error( $modifiers ) ) {
            wp_send_json_error( array( 'message' => $modifiers->get_error_message() ), 500 );
        }
        wp_send_json_success( array( 'modifiers' => $modifiers ) );
    }

    /**
     * Fetch modifiers from Square for a product (stub)
     */
    public function get_modifiers_from_square( $product_id ) {
        // Get the Square ID for this product
        $square_id = get_post_meta( $product_id, '_square_id', true );
        if ( ! $square_id ) {
            return array(); // No Square link
        }
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        $square_api = new SquareKit_Square_API();
        $item = $square_api->get_catalog_item( $square_id );
        if ( is_wp_error( $item ) || empty( $item ) ) {
            return array();
        }
        // Get modifier_list_info from the item
        $modifier_lists = isset( $item['item_data']['modifier_list_info'] ) ? $item['item_data']['modifier_list_info'] : array();
        $all_modifiers = array();
        foreach ( $modifier_lists as $mod_list ) {
            $list_id = $mod_list['modifier_list_id'];
            $list_obj = $square_api->get_catalog_item( $list_id );
            if ( is_wp_error( $list_obj ) || empty( $list_obj ) ) continue;
            $set = array(
                'name' => isset($list_obj['modifier_list_data']['name']) ? $list_obj['modifier_list_data']['name'] : '',
                'square_list_id' => $list_id,
                'single' => isset($list_obj['modifier_list_data']['selection_type']) && $list_obj['modifier_list_data']['selection_type'] === 'SINGLE',
                'source' => 'square',
                'options' => array()
            );
            $modifiers = isset($list_obj['modifier_list_data']['modifiers']) ? $list_obj['modifier_list_data']['modifiers'] : array();
            foreach ($modifiers as $mod) {
                $set['options'][] = array(
                    'name' => isset($mod['modifier_data']['name']) ? $mod['modifier_data']['name'] : '',
                    'price' => isset($mod['modifier_data']['price_money']['amount']) ? ($mod['modifier_data']['price_money']['amount'] / 100) : '',
                    'stock' => '', // Square does not track stock per modifier
                    'square_id' => $mod['id'],
                );
            }
            $all_modifiers[] = $set;
        }
        return $all_modifiers;
    }

    /**
     * Process bulk operation in background
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     */
    public function process_bulk_operation_background( $operation_id ) {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $bulk_ops = new SquareKit_Bulk_Operations();
        $bulk_ops->process_operation( $operation_id );
    }

    /**
     * Cleanup old operations
     *
     * @since 1.0.0
     */
    public function cleanup_old_operations() {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $bulk_ops = new SquareKit_Bulk_Operations();
        $cleaned = $bulk_ops->cleanup_old_operations( 30 ); // Clean up operations older than 30 days
        
        if ( $cleaned > 0 ) {
            $this->db->log( 'bulk_operation', sprintf(
                __( 'Cleaned up %d old bulk operations.', 'squarekit' ),
                $cleaned
            ) );
        }
    }

    /**
     * Add bulk operations admin page
     *
     * @since 1.0.0
     */
    public function add_bulk_operations_page() {
        add_submenu_page(
            'squarekit',
            __( 'Bulk Operations', 'squarekit' ),
            __( 'Bulk Operations', 'squarekit' ),
            'manage_options',
            'squarekit-bulk-operations',
            array( $this, 'render_bulk_operations_page' )
        );
    }

    /**
     * Render bulk operations page
     *
     * @since 1.0.0
     */
    public function render_bulk_operations_page() {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $bulk_ops = new SquareKit_Bulk_Operations();
        $recent_operations = $bulk_ops->get_recent_operations( 10 );
        $active_operations = $bulk_ops->get_active_operations();
        
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/bulk-operations.php';
    }

    /**
     * Register custom bulk actions for products
     */
    public function register_product_bulk_actions( $actions ) {
        $actions['squarekit_sync_to_square'] = __( 'Sync to Square', 'squarekit' );
        $actions['squarekit_export_to_square'] = __( 'Export to Square', 'squarekit' );
        $actions['squarekit_remove_square_meta'] = __( 'Remove Square Metadata', 'squarekit' );
        $actions['squarekit_import_categories'] = __( 'Import Categories from Square', 'squarekit' );
        $actions['squarekit_sync_inventory'] = __( 'Sync Inventory from Square', 'squarekit' );
        return $actions;
    }

    /**
     * Handle custom bulk actions for products
     */
    public function handle_product_bulk_actions( $redirect_to, $action, $post_ids ) {
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        $wc = new SquareKit_WooCommerce();
        $count = 0;
        switch ( $action ) {
            case 'squarekit_sync_to_square':
            case 'squarekit_export_to_square':
                foreach ( $post_ids as $post_id ) {
                    $product = get_post( $post_id );
                    if ( $product && $product->post_type === 'product' ) {
                        $wc->sync_product_to_square( $post_id, $product, true );
                        $count++;
                    }
                }
                $redirect_to = add_query_arg( 'squarekit_bulk_synced', $count, $redirect_to );
                break;
            case 'squarekit_remove_square_meta':
                foreach ( $post_ids as $post_id ) {
                    delete_post_meta( $post_id, '_square_id' );
                    $count++;
                }
                $redirect_to = add_query_arg( 'squarekit_bulk_meta_removed', $count, $redirect_to );
                break;
            case 'squarekit_import_categories':
                // Import categories from Square
                $result = $wc->import_categories_from_square();
                if ( $result['success'] ) {
                    $redirect_to = add_query_arg( 'squarekit_categories_imported', $result['imported'], $redirect_to );
                } else {
                    $redirect_to = add_query_arg( 'squarekit_import_error', urlencode($result['message']), $redirect_to );
                }
                break;
            case 'squarekit_sync_inventory':
                // Sync inventory from Square
                $result = $wc->import_inventory_from_square();
                if ( $result['success'] ) {
                    $redirect_to = add_query_arg( 'squarekit_inventory_synced', $result['updated'], $redirect_to );
                } else {
                    $redirect_to = add_query_arg( 'squarekit_import_error', urlencode($result['message']), $redirect_to );
                }
                break;
        }
        return $redirect_to;
    }

    /**
     * Show admin notices for bulk actions
     */
    public function bulk_action_notices() {
        if ( ! empty( $_REQUEST['squarekit_bulk_synced'] ) ) {
            printf( '<div class="notice notice-success is-dismissible"><p>%s</p></div>',
                esc_html( sprintf( __( 'Synced %d products to Square.', 'squarekit' ), intval( $_REQUEST['squarekit_bulk_synced'] ) ) )
            );
        }
        if ( ! empty( $_REQUEST['squarekit_bulk_meta_removed'] ) ) {
            printf( '<div class="notice notice-success is-dismissible"><p>%s</p></div>',
                esc_html( sprintf( __( 'Removed Square metadata from %d products.', 'squarekit' ), intval( $_REQUEST['squarekit_bulk_meta_removed'] ) ) )
            );
        }
        if ( ! empty( $_REQUEST['squarekit_categories_imported'] ) ) {
            printf( '<div class="notice notice-success is-dismissible"><p>%s</p></div>',
                esc_html( sprintf( __( 'Imported %d categories from Square.', 'squarekit' ), intval( $_REQUEST['squarekit_categories_imported'] ) ) )
            );
        }
        if ( ! empty( $_REQUEST['squarekit_inventory_synced'] ) ) {
            printf( '<div class="notice notice-success is-dismissible"><p>%s</p></div>',
                esc_html( sprintf( __( 'Synced inventory for %d products from Square.', 'squarekit' ), intval( $_REQUEST['squarekit_inventory_synced'] ) ) )
            );
        }
        if ( ! empty( $_REQUEST['squarekit_import_error'] ) ) {
            printf( '<div class="notice notice-error is-dismissible"><p>%s</p></div>',
                esc_html( sprintf( __( 'Import error: %s', 'squarekit' ), urldecode( $_REQUEST['squarekit_import_error'] ) ) )
            );
        }
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since 1.0.0
     */
    public function enqueue_styles() {
        $screen = get_current_screen();

        // Enqueue on our plugin pages OR product edit pages (for modifiers)
        $should_enqueue = false;

        if ( $screen ) {
            // Our plugin pages
            if ( strpos( $screen->id, 'squarekit' ) !== false ) {
                $should_enqueue = true;
            }
            // Product edit pages (for modifier admin interface)
            elseif ( $screen->post_type === 'product' && in_array( $screen->base, array( 'post', 'post-new' ) ) ) {
                $should_enqueue = true;
            }
        }

        if ( ! $should_enqueue ) {
            return;
        }

        wp_enqueue_style(
            'squarekit-admin',
            SQUAREKIT_PLUGIN_URL . 'assets/css/squarekit-admin.css',
            array(),
            SQUAREKIT_VERSION . '-' . time(),
            'all'
        );

        // Enqueue settings-specific styles on settings page
        if ( isset( $_GET['page'] ) && $_GET['page'] === 'squarekit-settings' ) {
            wp_enqueue_style(
                'squarekit-settings',
                SQUAREKIT_PLUGIN_URL . 'assets/css/squarekit-settings.css',
                array( 'squarekit-admin' ),
                SQUAREKIT_VERSION,
                'all'
            );
        }
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since 1.0.0
     */
    public function enqueue_scripts() {
        $screen = get_current_screen();
        
        // Only enqueue on our plugin pages
        if ( ! $screen || strpos( $screen->id, 'squarekit' ) === false ) {
            return;
        }
        
        wp_enqueue_script(
            'squarekit-admin',
            SQUAREKIT_PLUGIN_URL . 'assets/js/squarekit-admin.js',
            array( 'jquery' ),
            SQUAREKIT_VERSION,
            false
        );
        
        // Localize script
        wp_localize_script(
            'squarekit-admin',
            'squarekit_admin',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'squarekit-admin' ),
                'rest_url' => get_rest_url( null, 'sws/v1' ),
                'rest_nonce' => wp_create_nonce( 'wp_rest' ),
                'is_connected' => $this->settings->is_connected(),
                'is_setup_complete' => $this->settings->is_setup_complete(),
            )
        );
    }

    /**
     * Register admin menu
     *
     * @since 1.0.0
     */
    public function register_admin_menu() {
        // Main menu
        add_menu_page(
            __( 'Square Kit', 'squarekit' ),
            __( 'Square Kit', 'squarekit' ),
            'manage_options',
            'squarekit',
            array( $this, 'render_dashboard_page' ),
            'dashicons-cart',
            58
        );
        
        // Dashboard submenu
        add_submenu_page(
            'squarekit',
            __( 'Dashboard', 'squarekit' ),
            __( 'Dashboard', 'squarekit' ),
            'manage_options',
            'squarekit',
            array( $this, 'render_dashboard_page' )
        );
        
        // Products submenu
        add_submenu_page(
            'squarekit',
            __( 'Products', 'squarekit' ),
            __( 'Products', 'squarekit' ),
            'manage_options',
            'squarekit-products',
            array( $this, 'render_products_page' )
        );
        
        // Customers submenu
        add_submenu_page(
            'squarekit',
            __( 'Customers', 'squarekit' ),
            __( 'Customers', 'squarekit' ),
            'manage_options',
            'squarekit-customers',
            array( $this, 'render_customers_page' )
        );
        
        // Orders submenu
        add_submenu_page(
            'squarekit',
            __( 'Orders', 'squarekit' ),
            __( 'Orders', 'squarekit' ),
            'manage_options',
            'squarekit-orders',
            array( $this, 'render_orders_page' )
        );
        
        // Settings submenu
        add_submenu_page(
            'squarekit',
            __( 'Settings', 'squarekit' ),
            __( 'Settings', 'squarekit' ),
            'manage_options',
            'squarekit-settings',
            array( $this, 'render_settings_page' )
        );

        // OAuth Configuration submenu
        add_submenu_page(
            'squarekit',
            __( 'OAuth Configuration', 'squarekit' ),
            __( 'OAuth Config', 'squarekit' ),
            'manage_options',
            'squarekit-oauth-settings',
            array( $this, 'render_oauth_settings_page' )
        );

        // Account Management submenu
        add_submenu_page(
            'squarekit',
            __( 'Account Management', 'squarekit' ),
            __( 'Account', 'squarekit' ),
            'manage_options',
            'squarekit-account',
            array( $this, 'render_account_page' )
        );
        
        // Tools submenu
        add_submenu_page(
            'squarekit',
            __( 'Tools', 'squarekit' ),
            __( 'Tools', 'squarekit' ),
            'manage_options',
            'squarekit-tools',
            array( $this, 'render_tools_page' )
        );

        // Image Import Tests submenu (under Tools)
        add_submenu_page(
            'squarekit',
            __( 'Image Import Tests', 'squarekit' ),
            __( 'Image Tests', 'squarekit' ),
            'manage_options',
            'squarekit-image-tests',
            array( $this, 'render_image_tests_page' )
        );

        // Debug Logs submenu
        add_submenu_page(
            'squarekit',
            __( 'Debug Logs', 'squarekit' ),
            __( 'Debug Logs', 'squarekit' ),
            'manage_options',
            'squarekit-debug-logs',
            array( $this, 'render_debug_logs_page' )
        );

        // Inventory Conflicts submenu
        add_submenu_page(
            'squarekit',
            __( 'Inventory Conflicts', 'squarekit' ),
            __( 'Inventory Conflicts', 'squarekit' ),
            'manage_options',
            'squarekit-inventory-conflicts',
            array( $this, 'render_inventory_conflicts_page' )
        );
        
        // Bulk Operations submenu
        add_submenu_page(
            'squarekit',
            __( 'Bulk Operations', 'squarekit' ),
            __( 'Bulk Operations', 'squarekit' ),
            'manage_options',
            'squarekit-bulk-operations',
            array( $this, 'render_bulk_operations_page' )
        );
        
        // Onboarding wizard (hidden from menu)
        add_submenu_page(
            null,
            __( 'Setup Wizard', 'squarekit' ),
            __( 'Setup Wizard', 'squarekit' ),
            'manage_options',
            'squarekit-wizard',
            array( $this, 'render_wizard_page' )
        );
    }

    /**
     * Render dashboard page
     *
     * @since 1.0.0
     */
    public function render_dashboard_page() {
        // Check if setup is complete
        if ( ! $this->settings->is_setup_complete() ) {
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }
        
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/dashboard.php';
    }

    /**
     * Render products page
     *
     * @since 1.0.0
     */
    public function render_products_page() {
        // Check if setup is complete
        if ( ! $this->settings->is_setup_complete() ) {
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }
        
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/products.php';
    }

    /**
     * Render customers page
     *
     * @since 1.0.0
     */
    public function render_customers_page() {
        // Check if setup is complete
        if ( ! $this->settings->is_setup_complete() ) {
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }
        
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/customers.php';
    }

    /**
     * Render orders page
     *
     * @since 1.0.0
     */
    public function render_orders_page() {
        // Check if setup is complete
        if ( ! $this->settings->is_setup_complete() ) {
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }
        
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/orders.php';
    }

    /**
     * Render settings page
     *
     * @since 1.0.0
     */
    public function render_settings_page() {
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/settings.php';
    }

    /**
     * Render OAuth settings page
     *
     * @since 1.0.0
     */
    public function render_oauth_settings_page() {
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/oauth-settings.php';
    }

    /**
     * Render account management page
     *
     * @since 1.0.0
     */
    public function render_account_page() {
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/account.php';
    }

    /**
     * Render tools page
     *
     * @since 1.0.0
     */
    public function render_tools_page() {
        // Check if setup is complete
        if ( ! $this->settings->is_setup_complete() ) {
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }

        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/tools.php';
    }

    /**
     * Render image import tests page
     *
     * @since 1.0.0
     */
    public function render_image_tests_page() {
        // Check if setup is complete
        if ( ! $this->settings->is_setup_complete() ) {
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }

        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/test-image-imports.php';
    }

    /**
     * Render debug logs page
     *
     * @since 1.0.0
     */
    public function render_debug_logs_page() {
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/debug-logs.php';
    }

    /**
     * Render wizard page
     *
     * @since 1.0.0
     */
    public function render_wizard_page() {
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/wizard.php';
    }

    /**
     * Render inventory conflicts page
     *
     * @since 1.0.0
     */
    public function render_inventory_conflicts_page() {
        // Check if setup is complete
        if ( ! $this->settings->is_setup_complete() ) {
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }
        
        include SQUAREKIT_PLUGIN_DIR . 'admin/partials/inventory-conflicts.php';
    }

    /**
     * Maybe redirect to wizard
     *
     * @since 1.0.0
     */
    public function maybe_redirect_to_wizard() {
        // Check if we need to redirect to the wizard
        if ( get_transient( 'squarekit_activation_redirect' ) ) {
            // Delete the transient
            delete_transient( 'squarekit_activation_redirect' );
            
            // Bail if activating from network, or bulk
            if ( is_network_admin() || isset( $_GET['activate-multi'] ) ) {
                return;
            }
            
            // Redirect to wizard
            wp_redirect( admin_url( 'admin.php?page=squarekit-wizard' ) );
            exit;
        }
    }

    /**
     * Admin notices
     *
     * @since 1.0.0
     */
    public function admin_notices() {
        // Check if WooCommerce is active
        if ( ! class_exists( 'WooCommerce' ) ) {
            ?>
            <div class="notice notice-error">
                <p><?php esc_html_e( 'Square Kit requires WooCommerce to be installed and active.', 'squarekit' ); ?></p>
            </div>
            <?php
        }
        
        // Check if Square is connected
        if ( class_exists( 'WooCommerce' ) && ! $this->settings->is_connected() && ! isset( $_GET['page'] ) ) {
            ?>
            <div class="notice notice-warning is-dismissible">
                <p>
                    <?php
                    printf(
                        /* translators: %s: Settings page URL */
                        esc_html__( 'Square Kit is not connected to Square. Please %s to connect.', 'squarekit' ),
                        '<a href="' . esc_url( admin_url( 'admin.php?page=squarekit-wizard' ) ) . '">' . esc_html__( 'complete the setup wizard', 'squarekit' ) . '</a>'
                    );
                    ?>
                </p>
            </div>
            <?php
        }
    }

    /**
     * Add plugin action links
     *
     * @since 1.0.0
     * @param array $links Plugin action links
     * @return array Plugin action links
     */
    public function plugin_action_links( $links ) {
        $plugin_links = array(
            '<a href="' . admin_url( 'admin.php?page=squarekit-settings' ) . '">' . __( 'Settings', 'squarekit' ) . '</a>',
        );
        
        return array_merge( $plugin_links, $links );
    }

    /**
     * AJAX handler: Test webhook connection
     *
     * @since 1.0.0
     */
    public function ajax_test_webhook() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }
        
        check_ajax_referer( 'squarekit_test_webhook', 'nonce' );
        
        $settings = new SquareKit_Settings();
        
        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Square is not connected. Please connect to Square first.', 'squarekit' ) ) );
        }
        
        // Check if webhook is enabled
        if ( ! $settings->get_webhook_status() ) {
            wp_send_json_error( array( 'message' => __( 'Webhooks are not enabled. Please enable webhooks in settings.', 'squarekit' ) ) );
        }
        
        // Test webhook URL accessibility
        $webhook_url = get_rest_url( null, 'sws/v1/webhook' );
        $response = wp_remote_post( $webhook_url, array(
            'timeout' => 10,
            'body' => json_encode( array(
                'type' => 'test',
                'id' => 'test_' . time(),
                'data' => array(
                    'object' => array(
                        'test' => true
                    )
                )
            ) ),
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'SquareKit-Webhook-Test/1.0'
            )
        ) );
        
        if ( is_wp_error( $response ) ) {
            wp_send_json_error( array( 'message' => __( 'Webhook URL test failed: ', 'squarekit' ) . $response->get_error_message() ) );
        }
        
        $status_code = wp_remote_retrieve_response_code( $response );
        
        if ( $status_code === 200 ) {
            wp_send_json_success( array( 'message' => __( 'Webhook connection test successful! Your webhook endpoint is accessible.', 'squarekit' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Webhook connection test failed. Status code: ', 'squarekit' ) . $status_code ) );
        }
    }

    /**
     * AJAX handler: Refresh webhook registration
     *
     * @since 1.0.0
     */
    public function ajax_refresh_webhook() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }
        
        check_ajax_referer( 'squarekit_refresh_webhook', 'nonce' );
        
        $settings = new SquareKit_Settings();
        
        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Square is not connected. Please connect to Square first.', 'squarekit' ) ) );
        }
        
        // Unregister existing webhooks
        if ( ! class_exists('SquareKit_Loader') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
        }
        $loader = new SquareKit_Loader();
        $unregister_result = $loader->unregister_square_webhooks();
        
        if ( ! $unregister_result ) {
            wp_send_json_error( array( 'message' => __( 'Failed to unregister existing webhooks.', 'squarekit' ) ) );
        }
        
        // Register new webhooks
        $register_result = $loader->register_square_webhooks();
        
        if ( $register_result ) {
            wp_send_json_success( array( 'message' => __( 'Webhook registration refreshed successfully!', 'squarekit' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to register new webhooks. Please check your Square connection.', 'squarekit' ) ) );
        }
    }

    /**
     * AJAX handler: Test cron functionality
     *
     * @since 1.0.0
     */
    public function ajax_test_cron() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }
        
        check_ajax_referer( 'squarekit_test_cron', 'nonce' );
        
        $settings = new SquareKit_Settings();
        
        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Square is not connected. Please connect to Square first.', 'squarekit' ) ) );
        }
        
        // Check if automatic sync is enabled
        if ( ! $settings->get( 'enable_automatic_sync', true ) ) {
            wp_send_json_error( array( 'message' => __( 'Automatic sync is disabled. Please enable it in settings.', 'squarekit' ) ) );
        }
        
        // Test cron functionality by running a small sync operation
        if ( ! class_exists('SquareKit_Loader') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-loader.php';
        }
        
        $loader = new SquareKit_Loader();
        $cron_status = $loader->get_cron_status();
        
        // Check if cron is properly scheduled
        if ( ! $cron_status['next_run'] ) {
            wp_send_json_error( array( 'message' => __( 'Cron is not properly scheduled. Please check your WordPress cron configuration.', 'squarekit' ) ) );
        }
        
        // Test a simple sync operation
        try {
            if ( ! class_exists('SquareKit_WooCommerce') ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
            }
            
            $wc = new SquareKit_WooCommerce();
            
            // Test a small import operation
            $test_result = $wc->import_categories_from_square();
            
            if ( is_wp_error( $test_result ) ) {
                wp_send_json_error( array( 'message' => __( 'Cron test failed: ', 'squarekit' ) . $test_result->get_error_message() ) );
            }
            
            wp_send_json_success( array( 'message' => __( 'Cron test successful! Your scheduled sync is working properly.', 'squarekit' ) ) );
            
        } catch ( Exception $e ) {
            wp_send_json_error( array( 'message' => __( 'Cron test failed: ', 'squarekit' ) . $e->getMessage() ) );
        }
    }

    /**
     * AJAX handler: Apply role mapping to all customers
     *
     * @since 1.0.0
     */
    public function ajax_apply_role_mapping_all() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }

        $wc = new SquareKit_WooCommerce();
        $result = $wc->apply_role_mapping_to_all_customers();

        if ( $result['success'] ) {
            wp_send_json_success( $result );
        } else {
            wp_send_json_error( $result );
        }
    }

    /**
     * AJAX handler: Get Square inventory
     *
     * @since 1.0.0
     */
    public function ajax_get_square_inventory() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );
        
        $settings = new SquareKit_Settings();
        
        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Square is not connected. Please connect to Square first.', 'squarekit' ) ) );
        }
        
        $search = isset( $_POST['search'] ) ? sanitize_text_field( $_POST['search'] ) : '';
        $sync_status = isset( $_POST['sync_status'] ) ? sanitize_text_field( $_POST['sync_status'] ) : '';
        $type = isset( $_POST['type'] ) ? sanitize_text_field( $_POST['type'] ) : 'ITEM'; // Default to ITEM for products
        $sort = isset( $_POST['sort'] ) ? sanitize_text_field( $_POST['sort'] ) : 'name_asc';
        $cursor = isset( $_POST['cursor'] ) ? sanitize_text_field( $_POST['cursor'] ) : '';
        $per_page = isset( $_POST['per_page'] ) ? intval( $_POST['per_page'] ) : 20;
        $force_fetch = isset( $_POST['force_fetch'] ) ? (bool) $_POST['force_fetch'] : false;

        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }

        $db = new SquareKit_DB();

        // Check if we should fetch fresh data from Square
        $should_fetch_fresh = false;

        if ( $force_fetch ) {
            $should_fetch_fresh = true;
        } else {
            // Check if we have recent cached data
            $cached_count = $db->get_fetched_products_count();
            if ( $cached_count === 0 ) {
                // No cached data, need to fetch
                $should_fetch_fresh = true;
            }
        }

        if ( $should_fetch_fresh ) {
            // Fetch fresh data from Square and cache it
            if ( ! class_exists( 'SquareKit_Square_API' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
            }

            $square_api = new SquareKit_Square_API();

            // For the regular inventory view, we still use pagination for UI performance
            // But increase the per_page limit to show more items at once
            $result = $square_api->get_catalog_items( array(
                'search' => '',
                'sync_status' => '',
                'type' => $type,
                'sort' => $sort,
                'cursor' => '',
                'per_page' => 200 // Increased from 100 for better caching
            ) );

            if ( is_wp_error( $result ) ) {
                wp_send_json_error( array( 'message' => $result->get_error_message() ) );
            }

            // Save to cache
            if ( isset( $result['products'] ) && ! empty( $result['products'] ) ) {
                $session_id = uniqid( 'fetch_' . time() . '_' );
                $cache_success = $db->save_fetched_products( $result['products'], $session_id );

                if ( ! $cache_success ) {
                    error_log( 'SquareKit: Failed to cache fetched products' );
                }
            }
        }

        // Get products from cache with filtering
        $cache_args = array(
            'search' => $search,
            'import_status' => $this->map_sync_status_to_import_status( $sync_status ),
            'limit' => $per_page,
            'offset' => 0, // We'll handle pagination differently for cache
            'order_by' => $this->map_sort_to_order_by( $sort ),
            'order' => $this->map_sort_to_order( $sort )
        );

        $cached_products = $db->get_fetched_products( null, $cache_args );

        // Get cache statistics
        $cache_stats = $db->get_cache_stats();

        // Format result
        $result = array(
            'products' => $cached_products,
            'pagination' => array(
                'per_page' => $per_page,
                'total_items' => $cache_stats['total_products'],
                'has_more' => count( $cached_products ) >= $per_page,
                'cursor' => '' // Cache doesn't use cursor pagination
            ),
            'imported_count' => $cache_stats['imported_count'],
            'cache_info' => array(
                'last_fetch_time' => $cache_stats['last_fetch_time'],
                'total_cached' => $cache_stats['total_products'],
                'from_cache' => true
            )
        );

        wp_send_json_success( $result );
    }

    /**
     * Map sync status to import status for cache filtering
     *
     * @since 1.0.0
     * @param string $sync_status
     * @return string
     */
    private function map_sync_status_to_import_status( $sync_status ) {
        switch ( $sync_status ) {
            case 'imported':
                return 'imported';
            case 'not_imported':
                return 'not_imported';
            case 'synced':
                return 'synced';
            case 'out_of_sync':
                return 'out_of_sync';
            default:
                return '';
        }
    }

    /**
     * Map sort parameter to database order by field
     *
     * @since 1.0.0
     * @param string $sort
     * @return string
     */
    private function map_sort_to_order_by( $sort ) {
        switch ( $sort ) {
            case 'name_asc':
            case 'name_desc':
                return 'product_data'; // We'll need to extract name from JSON
            case 'price_asc':
            case 'price_desc':
                return 'product_data'; // We'll need to extract price from JSON
            case 'updated_desc':
                return 'fetched_at';
            default:
                return 'fetched_at';
        }
    }

    /**
     * Map sort parameter to database order direction
     *
     * @since 1.0.0
     * @param string $sort
     * @return string
     */
    private function map_sort_to_order( $sort ) {
        switch ( $sort ) {
            case 'name_asc':
            case 'price_asc':
                return 'ASC';
            case 'name_desc':
            case 'price_desc':
            case 'updated_desc':
                return 'DESC';
            default:
                return 'DESC';
        }
    }

    /**
     * AJAX handler: Fetch products from Square (explicit fetch)
     *
     * @since 1.0.0
     */
    public function ajax_fetch_from_square() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        $settings = new SquareKit_Settings();

        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Square is not connected. Please connect to Square first.', 'squarekit' ) ) );
        }

        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }

        $square_api = new SquareKit_Square_API();
        $db = new SquareKit_DB();

        // Fetch ALL products from Square using unlimited pagination
        $all_products = array();
        $cursor = null;
        $batch_count = 0;
        $batch_size = defined( 'SQUAREKIT_SYNC_BATCH_SIZE' ) ? SQUAREKIT_SYNC_BATCH_SIZE : 100;

        do {
            $batch_count++;

            // Build query parameters for this batch
            $query_params = array(
                'types' => 'ITEM',
                'limit' => $batch_size
            );

            if ( $cursor ) {
                $query_params['cursor'] = $cursor;
            }

            // Use the new Square API method that returns cursor
            $catalog_response = $square_api->get_catalog_with_cursor( $query_params );

            if ( is_wp_error( $catalog_response ) ) {
                wp_send_json_error( array( 'message' => $catalog_response->get_error_message() ) );
            }

            // Extract items and cursor from response
            $batch_items = isset( $catalog_response['objects'] ) ? $catalog_response['objects'] : array();
            $cursor = isset( $catalog_response['cursor'] ) ? $catalog_response['cursor'] : null;

            // Filter only ITEM objects and add to collection
            $item_objects = array_filter( $batch_items, function( $object ) {
                return isset( $object['type'] ) && $object['type'] === 'ITEM';
            } );

            $all_products = array_merge( $all_products, $item_objects );

        } while ( $cursor );

        // Process the raw Square objects to format them for the frontend
        $processed_products = array();
        foreach ( $all_products as $product ) {
            if ( isset( $product['type'] ) && $product['type'] === 'ITEM' ) {
                $processed_product = $this->process_square_product_for_display( $product );
                if ( $processed_product ) {
                    $processed_products[] = $processed_product;
                }
            }
        }

        // Convert to the format expected by the rest of the code
        $result = array(
            'products' => $processed_products,
            'pagination' => array(
                'total' => count( $processed_products ),
                'current_page' => 1,
                'total_pages' => 1,
                'per_page' => count( $processed_products )
            )
        );

        if ( empty( $processed_products ) ) {
            wp_send_json_error( array( 'message' => 'No products found in Square catalog.' ) );
        }

        // Save to cache
        if ( isset( $result['products'] ) && ! empty( $result['products'] ) ) {
            $session_id = uniqid( 'fetch_' . time() . '_' );
            $cache_success = $db->save_fetched_products( $result['products'], $session_id );

            if ( ! $cache_success ) {
                wp_send_json_error( array( 'message' => __( 'Failed to cache fetched products.', 'squarekit' ) ) );
            }

            // Get cache statistics
            $cache_stats = $db->get_cache_stats();

            wp_send_json_success( array(
                'message' => sprintf( __( 'Successfully fetched %d products from Square.', 'squarekit' ), count( $result['products'] ) ),
                'products_count' => count( $result['products'] ),
                'cache_stats' => $cache_stats,
                'session_id' => $session_id
            ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'No products found in Square catalog.', 'squarekit' ) ) );
        }
    }

    /**
     * AJAX handler: Import product from Square
     *
     * @since 1.0.0
     */
    public function ajax_import_product() {
        // Initialize logger for debugging
        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }
        $logger = SquareKit_Logger::get_instance();

        $logger->log( 'ajax', 'info', 'Starting ajax_import_product request' );

        if ( ! current_user_can( 'manage_options' ) ) {
            $logger->log( 'ajax', 'error', 'Permission denied for single product import' );
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        $product_id = isset( $_POST['product_id'] ) ? sanitize_text_field( $_POST['product_id'] ) : '';

        if ( empty( $product_id ) ) {
            $logger->log( 'ajax', 'error', 'Product ID is required for import' );
            wp_send_json_error( array( 'message' => __( 'Product ID is required.', 'squarekit' ) ) );
        }

        $logger->log( 'ajax', 'info', "Importing product: {$product_id}" );

        try {
            // Use the SWEVER-style Product Importer (same as working test)
            if ( ! class_exists( 'SquareKit_Product_Importer' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
            }

            $importer = new SquareKit_Product_Importer();

            // Configure import settings (same as working test)
            $import_config = array(
                'name' => true,
                'description' => true,
                'images' => true,
                'categories' => true,
                'variations' => true,
                'modifiers' => true,
                'attributesDisabled' => false
            );

            // Import the product using SWEVER-style architecture
            $result = $importer->import_product_swever_style(
                $product_id,
                $import_config,
                false // Not update-only
            );

            if ( is_wp_error( $result ) ) {
                $logger->log( 'ajax', 'error', "Import failed for {$product_id}: " . $result->get_error_message() );

                // Update cache status to failed
                if ( ! class_exists( 'SquareKit_DB' ) ) {
                    require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
                }
                $db = new SquareKit_DB();
                $db->update_product_import_status( $product_id, 'failed' );

                wp_send_json_error( array(
                    'message' => $result->get_error_message(),
                    'product_name' => $this->get_product_name_from_square( $product_id )
                ) );
            }

            $logger->log( 'ajax', 'info', "Import successful for {$product_id}" );

            // Update cache status to imported
            if ( ! class_exists( 'SquareKit_DB' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
            }
            $db = new SquareKit_DB();
            $wc_product_id = isset( $result['product_id'] ) ? $result['product_id'] : null;
            $db->update_product_import_status( $product_id, 'imported', $wc_product_id );

            wp_send_json_success( array(
                'message' => __( 'Product imported successfully.', 'squarekit' ),
                'product_name' => $result['product_name'] ?? $this->get_product_name_from_square( $product_id ),
                'wc_product_id' => $wc_product_id
            ) );

        } catch ( Exception $e ) {
            $logger->log( 'ajax', 'error', "Exception in ajax_import_product: " . $e->getMessage() );
            wp_send_json_error( array( 'message' => sprintf( __( 'Import failed: %s', 'squarekit' ), $e->getMessage() ) ) );
        }
    }

    /**
     * AJAX handler: Sync product with Square
     *
     * @since 1.0.0
     */
    public function ajax_sync_product() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        $square_product_id = isset( $_POST['product_id'] ) ? sanitize_text_field( $_POST['product_id'] ) : '';

        if ( empty( $square_product_id ) ) {
            wp_send_json_error( array( 'message' => __( 'Product ID is required.', 'squarekit' ) ) );
        }

        // Initialize logger and settings
        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }
        $logger = new SquareKit_Logger();

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }
        $settings = new SquareKit_Settings();

        try {
            // Check sync direction settings to determine what action to take
            $sync_direction = $settings->get_sync_direction_settings();
            $square_to_woo_enabled = $sync_direction['square_to_woo'];
            $woo_to_square_enabled = $sync_direction['woo_to_square'];

            $logger->log( 'ajax', 'info', "Sync button clicked for Square product {$square_product_id}", array(
                'square_to_woo_enabled' => $square_to_woo_enabled,
                'woo_to_square_enabled' => $woo_to_square_enabled
            ) );

            // Check if any sync direction is enabled
            if ( ! $square_to_woo_enabled && ! $woo_to_square_enabled ) {
                $logger->log( 'ajax', 'error', "No sync direction enabled" );
                wp_send_json_error( array( 'message' => __( 'No sync direction is enabled. Please configure sync settings first.', 'squarekit' ) ) );
            }

            // Initialize product sync module
            if ( ! class_exists( 'SquareKit_Product_Sync' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-product-sync.php';
            }
            $product_sync = new SquareKit_Product_Sync();

            // Check if this Square product already exists in WooCommerce
            $wc_product_id = $product_sync->find_wc_product_by_square_id( $square_product_id );

            if ( $wc_product_id && $woo_to_square_enabled ) {
                // Product exists in WooCommerce and WooCommerce to Square sync is enabled
                $logger->log( 'ajax', 'info', "Syncing existing WC product {$wc_product_id} (Square ID: {$square_product_id}) to Square" );
                $result = $product_sync->sync_product_to_square( $wc_product_id );

                if ( is_wp_error( $result ) ) {
                    $logger->log( 'ajax', 'error', "Sync to Square failed: " . $result->get_error_message() );
                    wp_send_json_error( array( 'message' => $result->get_error_message() ) );
                }

                $logger->log( 'ajax', 'info', "Successfully synced WC product {$wc_product_id} to Square" );
                wp_send_json_success( array( 'message' => __( 'Product synced to Square successfully.', 'squarekit' ) ) );

            } else if ( $square_to_woo_enabled ) {
                // Square to WooCommerce sync is enabled - import/update from Square
                $logger->log( 'ajax', 'info', "Importing/updating Square product {$square_product_id} to WooCommerce" );

                // Get the product data from Square first
                if ( ! class_exists( 'SquareKit_Square_API' ) ) {
                    require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
                }

                $square_api = new SquareKit_Square_API();
                $square_product = $square_api->get_catalog_item( $square_product_id );

                if ( is_wp_error( $square_product ) ) {
                    $logger->log( 'ajax', 'error', "Failed to fetch Square product: " . $square_product->get_error_message() );
                    wp_send_json_error( array( 'message' => $square_product->get_error_message() ) );
                }

                if ( empty( $square_product ) ) {
                    $logger->log( 'ajax', 'error', "Square product {$square_product_id} not found" );
                    wp_send_json_error( array( 'message' => __( 'Product not found in Square.', 'squarekit' ) ) );
                }

                // Import/update the product
                $result = $product_sync->import_single_product( $square_product, array(
                    'include_images' => true,
                    'include_variations' => true,
                    'include_modifiers' => true,
                    'update_existing' => true
                ) );

                if ( is_wp_error( $result ) ) {
                    $logger->log( 'ajax', 'error', "Import from Square failed: " . $result->get_error_message() );
                    wp_send_json_error( array( 'message' => $result->get_error_message() ) );
                }

                $action = $wc_product_id ? 'updated' : 'imported';
                $logger->log( 'ajax', 'info', "Successfully {$action} Square product {$square_product_id} as WC product {$result['product_id']}" );
                wp_send_json_success( array( 'message' => sprintf( __( 'Product %s from Square successfully.', 'squarekit' ), $action ) ) );

            } else {
                // No appropriate sync direction enabled for this scenario
                if ( $wc_product_id ) {
                    $message = __( 'Product exists in WooCommerce but WooCommerce to Square sync is disabled.', 'squarekit' );
                } else {
                    $message = __( 'Product not found in WooCommerce and Square to WooCommerce sync is disabled.', 'squarekit' );
                }

                $logger->log( 'ajax', 'error', "Sync not possible: " . $message );
                wp_send_json_error( array( 'message' => $message ) );
            }

        } catch ( Exception $e ) {
            $logger->log( 'ajax', 'error', "Sync operation failed with exception: " . $e->getMessage() );
            wp_send_json_error( array( 'message' => sprintf( __( 'Sync failed: %s', 'squarekit' ), $e->getMessage() ) ) );
        }
    }

    /**
     * AJAX handler: Get product details
     *
     * @since 1.0.0
     */
    public function ajax_get_product_details() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }
        
        check_ajax_referer( 'squarekit-admin', 'nonce' );
        
        $product_id = isset( $_POST['product_id'] ) ? sanitize_text_field( $_POST['product_id'] ) : '';
        
        if ( empty( $product_id ) ) {
            wp_send_json_error( array( 'message' => __( 'Product ID is required.', 'squarekit' ) ) );
        }
        
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $square_api = new SquareKit_Square_API();
        $product = $square_api->get_catalog_item( $product_id );
        
        if ( is_wp_error( $product ) ) {
            wp_send_json_error( array( 'message' => $product->get_error_message() ) );
        }
        
        if ( empty( $product ) ) {
            wp_send_json_error( array( 'message' => __( 'Product not found.', 'squarekit' ) ) );
        }
        
        $html = $this->render_product_details_html( $product );
        
        wp_send_json_success( array( 'html' => $html ) );
    }

    /**
     * AJAX handler: Test Square connection
     *
     * @since 1.0.0
     */
    public function ajax_test_connection() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }
        
        check_ajax_referer( 'squarekit_test_connection', 'nonce' );
        
        $settings = new SquareKit_Settings();
        
        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Square is not connected. Please connect to Square first.', 'squarekit' ) ) );
        }
        
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $square_api = new SquareKit_Square_API();
        $result = $square_api->test_connection();
        
        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array( 'message' => __( 'Connection test failed: ', 'squarekit' ) . $result->get_error_message() ) );
        }
        
        wp_send_json_success( array( 'message' => __( 'Connection test successful! Square API is working properly.', 'squarekit' ) ) );
    }

    /**
     * AJAX handler: Refresh Square connection
     *
     * @since 1.0.0
     */
    public function ajax_refresh_connection() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }
        
        check_ajax_referer( 'squarekit_refresh_connection', 'nonce' );
        
        $settings = new SquareKit_Settings();
        
        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Square is not connected. Please connect to Square first.', 'squarekit' ) ) );
        }
        
        // Refresh access token if needed
        $environment = $settings->get_environment();
        $refresh_token = $settings->get( $environment . '_refresh_token', '' );
        
        if ( ! empty( $refresh_token ) ) {
            if ( ! class_exists( 'SquareKit_Square_API' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
            }
            
            $square_api = new SquareKit_Square_API();
            $result = $square_api->refresh_access_token( $refresh_token );
            
            if ( is_wp_error( $result ) ) {
                wp_send_json_error( array( 'message' => __( 'Failed to refresh connection: ', 'squarekit' ) . $result->get_error_message() ) );
            }
        }
        
        wp_send_json_success( array( 'message' => __( 'Connection refreshed successfully!', 'squarekit' ) ) );
    }

    /**
     * AJAX handler: Manual sync operation
     *
     * @since 1.0.0
     */
    public function ajax_manual_sync() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        $sync_type = isset( $_POST['sync_type'] ) ? sanitize_text_field( $_POST['sync_type'] ) : 'all';

        if ( ! $this->settings->is_connected() ) {
            wp_send_json_error( array( 'message' => __( 'Not connected to Square.', 'squarekit' ) ) );
        }

        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }

        $wc = new SquareKit_WooCommerce();
        $result = array( 'success' => false, 'message' => '' );

        try {
            switch ( $sync_type ) {
                case 'products':
                    $import_result = $wc->import_products_from_square();
                    if ( $import_result['success'] ) {
                        $result['success'] = true;
                        $result['message'] = sprintf(
                            __( 'Successfully imported %d products from Square.', 'squarekit' ),
                            $import_result['imported_count'] ?? 0
                        );
                    } else {
                        $result['message'] = $import_result['message'] ?? __( 'Failed to import products.', 'squarekit' );
                    }
                    break;

                case 'customers':
                    $import_result = $wc->import_customers_from_square();
                    if ( $import_result['success'] ) {
                        $result['success'] = true;
                        $result['message'] = sprintf(
                            __( 'Successfully imported %d customers from Square.', 'squarekit' ),
                            $import_result['imported_count'] ?? 0
                        );
                    } else {
                        $result['message'] = $import_result['message'] ?? __( 'Failed to import customers.', 'squarekit' );
                    }
                    break;

                case 'inventory':
                    $sync_result = $wc->import_inventory_from_square();
                    if ( $sync_result['success'] ) {
                        $result['success'] = true;
                        $result['message'] = sprintf(
                            __( 'Successfully synced inventory for %d products.', 'squarekit' ),
                            $sync_result['synced_count'] ?? 0
                        );
                    } else {
                        $result['message'] = $sync_result['message'] ?? __( 'Failed to sync inventory.', 'squarekit' );
                    }
                    break;

                default:
                    $result['message'] = __( 'Invalid sync type.', 'squarekit' );
            }

            // Update last sync time if successful
            if ( $result['success'] ) {
                update_option( 'squarekit_last_sync', current_time( 'mysql' ) );
            }

        } catch ( Exception $e ) {
            $result['message'] = sprintf( __( 'Sync failed: %s', 'squarekit' ), $e->getMessage() );
        }

        if ( $result['success'] ) {
            wp_send_json_success( array( 'message' => $result['message'] ) );
        } else {
            wp_send_json_error( array( 'message' => $result['message'] ) );
        }
    }

    /**
     * AJAX handler: Import all products from Square
     *
     * @since 1.0.0
     */
    public function ajax_import_all_products() {
        // Initialize logger for debugging
        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }
        $logger = SquareKit_Logger::get_instance();

        $logger->log( 'ajax', 'info', 'Starting ajax_import_all_products request' );

        try {
            // Check user permissions
            if ( ! current_user_can( 'manage_options' ) ) {
                $logger->log( 'ajax', 'error', 'Permission denied for user: ' . get_current_user_id() );
                wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
            }

            $logger->log( 'ajax', 'info', 'User permission check passed' );

            // Check nonce
            if ( ! check_ajax_referer( 'squarekit-admin', 'nonce', false ) ) {
                $logger->log( 'ajax', 'error', 'Nonce verification failed' );
                wp_send_json_error( array( 'message' => __( 'Security check failed.', 'squarekit' ) ), 403 );
            }

            $logger->log( 'ajax', 'info', 'Nonce verification passed' );

            // Check Square connection
            if ( ! $this->settings->is_connected() ) {
                $logger->log( 'ajax', 'error', 'Not connected to Square' );
                wp_send_json_error( array( 'message' => __( 'Not connected to Square.', 'squarekit' ) ) );
            }

            $logger->log( 'ajax', 'info', 'Square connection verified' );

            // Check if bulk operations class exists
            if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
                $logger->log( 'ajax', 'info', 'Loading SquareKit_Bulk_Operations class' );
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
            }

            // Check if class loaded successfully
            if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
                $logger->log( 'ajax', 'error', 'Failed to load SquareKit_Bulk_Operations class' );
                wp_send_json_error( array( 'message' => __( 'Failed to load bulk operations class.', 'squarekit' ) ) );
            }

            $logger->log( 'ajax', 'info', 'Creating SquareKit_Bulk_Operations instance' );
            $bulk_ops = new SquareKit_Bulk_Operations();

            $logger->log( 'ajax', 'info', 'SquareKit_Bulk_Operations instance created successfully' );

            // Start bulk import operation
            $operation_data = array(
                'import_all' => true,
                'include_images' => true,
                'include_categories' => true,
                'include_variations' => true
            );

            $logger->log( 'ajax', 'info', 'Starting bulk operation with data', $operation_data );

            $operation_id = $bulk_ops->start_operation( 'import_products', $operation_data );

            $logger->log( 'ajax', 'info', 'Bulk operation start_operation returned', array( 'result' => $operation_id ) );

            if ( is_wp_error( $operation_id ) ) {
                $error_message = $operation_id->get_error_message();
                $logger->log( 'ajax', 'error', 'Bulk operation failed: ' . $error_message );
                wp_send_json_error( array( 'message' => $error_message ) );
            }

            $logger->log( 'ajax', 'info', 'Bulk operation started successfully with ID: ' . $operation_id );

            // Schedule the background processing
            $scheduled = wp_schedule_single_event( time(), 'squarekit_process_bulk_operation', array( $operation_id ) );

            $logger->log( 'ajax', 'info', 'Background processing scheduled', array(
                'operation_id' => $operation_id,
                'scheduled' => $scheduled
            ) );

            // Get current product counts for the response
            $imported_count = $this->get_imported_products_count();

            wp_send_json_success( array(
                'message' => __( 'Bulk import started successfully. Check the logs for progress.', 'squarekit' ),
                'operation_id' => $operation_id,
                'imported_count' => $imported_count
            ) );

        } catch ( Exception $e ) {
            $error_message = $e->getMessage();
            $error_trace = $e->getTraceAsString();

            $logger->log( 'ajax', 'error', 'Exception in ajax_import_all_products: ' . $error_message, array(
                'trace' => $error_trace,
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ) );

            wp_send_json_error( array( 'message' => sprintf( __( 'Import failed: %s', 'squarekit' ), $error_message ) ) );
        } catch ( Error $e ) {
            $error_message = $e->getMessage();
            $error_trace = $e->getTraceAsString();

            $logger->log( 'ajax', 'error', 'Fatal error in ajax_import_all_products: ' . $error_message, array(
                'trace' => $error_trace,
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ) );

            wp_send_json_error( array( 'message' => 'A fatal error occurred: ' . $error_message ) );
        }
    }

    /**
     * AJAX handler: Check for cached products
     *
     * @since 1.0.0
     */
    public function ajax_check_cached_products() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }

        $db = new SquareKit_DB();

        // Get cached products count
        $cached_products = $db->get_fetched_products( null, array( 'limit' => 1 ) );
        $has_cached_data = ! empty( $cached_products );

        if ( $has_cached_data ) {
            // Get total count
            $total_cached = $db->get_fetched_products_count();

            wp_send_json_success( array(
                'has_cached_data' => true,
                'cached_count' => $total_cached,
                'message' => sprintf( __( '%d products available for import', 'squarekit' ), $total_cached )
            ) );
        } else {
            wp_send_json_success( array(
                'has_cached_data' => false,
                'cached_count' => 0,
                'message' => __( 'No cached products found. Please fetch from Square first.', 'squarekit' )
            ) );
        }
    }

    /**
     * AJAX handler: Get bulk operation progress
     *
     * @since 1.0.0
     */
    public function ajax_get_bulk_operation_progress() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        $operation_id = isset( $_POST['operation_id'] ) ? intval( $_POST['operation_id'] ) : 0;

        if ( empty( $operation_id ) ) {
            wp_send_json_error( array( 'message' => __( 'Operation ID is required.', 'squarekit' ) ) );
        }

        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }

        $bulk_ops = new SquareKit_Bulk_Operations();
        $operation = $bulk_ops->get_operation( $operation_id );

        if ( ! $operation ) {
            wp_send_json_error( array( 'message' => __( 'Operation not found.', 'squarekit' ) ) );
        }

        // Format progress data for frontend
        $progress_data = array(
            'operation_id' => $operation->id,
            'status' => $operation->operation_status,
            'progress_percentage' => floatval( $operation->progress_percentage ),
            'total_items' => intval( $operation->total_items ),
            'processed_items' => intval( $operation->processed_items ),
            'successful_items' => intval( $operation->successful_items ),
            'failed_items' => intval( $operation->failed_items ),
            'current_item_name' => $operation->current_item_name,
            'created_at' => $operation->created_at,
            'completed_at' => $operation->completed_at,
            'is_completed' => in_array( $operation->operation_status, array( 'completed', 'failed', 'cancelled' ) )
        );

        wp_send_json_success( $progress_data );
    }

    /**
     * Get product name from Square
     *
     * @since 1.0.0
     * @param string $product_id Square product ID
     * @return string Product name
     */
    private function get_product_name_from_square( $product_id ) {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $square_api = new SquareKit_Square_API();
        $product = $square_api->get_catalog_item( $product_id );
        
        if ( is_wp_error( $product ) || empty( $product ) ) {
            return __( 'Unknown Product', 'squarekit' );
        }
        
        return isset( $product['item_data']['name'] ) ? $product['item_data']['name'] : __( 'Unknown Product', 'squarekit' );
    }

    /**
     * Render product details HTML
     *
     * @since 1.0.0
     * @param array $product Square product data
     * @return string HTML content
     */
    private function render_product_details_html( $product ) {
        $html = '<div class="squarekit-product-details">';
        
        // Product image
        if ( ! empty( $product['item_data']['image_ids'] ) ) {
            $image_id = $product['item_data']['image_ids'][0];
            $image_url = $this->get_square_image_url( $image_id );
            if ( $image_url ) {
                $html .= '<div class="squarekit-product-image-large">';
                $html .= '<img src="' . esc_url( $image_url ) . '" alt="' . esc_attr( $product['item_data']['name'] ) . '" />';
                $html .= '</div>';
            }
        }
        
        // Product info
        $html .= '<div class="squarekit-product-info">';
        $html .= '<h3>' . esc_html( $product['item_data']['name'] ) . '</h3>';
        
        // Description
        if ( ! empty( $product['item_data']['description'] ) ) {
            $html .= '<p><strong>' . __( 'Description:', 'squarekit' ) . '</strong> ' . esc_html( $product['item_data']['description'] ) . '</p>';
        }
        
        // Category
        if ( ! empty( $product['item_data']['category_id'] ) ) {
            $category_name = $this->get_category_name( $product['item_data']['category_id'] );
            $html .= '<p><strong>' . __( 'Category:', 'squarekit' ) . '</strong> ' . esc_html( $category_name ) . '</p>';
        }
        
        // Pricing
        if ( ! empty( $product['item_data']['variations'] ) ) {
            $html .= '<p><strong>' . __( 'Pricing:', 'squarekit' ) . '</strong></p>';
            $html .= '<ul>';
            foreach ( $product['item_data']['variations'] as $variation ) {
                $price = isset( $variation['item_variation_data']['price_money']['amount'] ) ? 
                    $this->format_price( $variation['item_variation_data']['price_money'] ) : __( 'N/A', 'squarekit' );
                $html .= '<li>' . esc_html( $variation['item_variation_data']['name'] ) . ': ' . $price . '</li>';
            }
            $html .= '</ul>';
        }
        
        // Modifiers
        if ( ! empty( $product['item_data']['modifier_list_info'] ) ) {
            $html .= '<p><strong>' . __( 'Modifiers:', 'squarekit' ) . '</strong></p>';
            $html .= '<ul>';
            foreach ( $product['item_data']['modifier_list_info'] as $modifier ) {
                $html .= '<li>' . esc_html( $modifier['modifier_list_id'] ) . '</li>';
            }
            $html .= '</ul>';
        }
        
        // Tax info
        if ( ! empty( $product['item_data']['tax_ids'] ) ) {
            $html .= '<p><strong>' . __( 'Taxes:', 'squarekit' ) . '</strong> ' . __( 'Taxable', 'squarekit' ) . '</p>';
        }
        
        // Availability
        $availability = isset( $product['item_data']['present_at_all_locations'] ) && $product['item_data']['present_at_all_locations'] ? 
            __( 'Available at all locations', 'squarekit' ) : __( 'Limited availability', 'squarekit' );
        $html .= '<p><strong>' . __( 'Availability:', 'squarekit' ) . '</strong> ' . $availability . '</p>';
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Get Square image URL
     *
     * @since 1.0.0
     * @param string $image_id Square image ID
     * @return string|false Image URL or false
     */
    private function get_square_image_url( $image_id ) {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $square_api = new SquareKit_Square_API();
        $image = $square_api->get_catalog_item( $image_id );
        
        if ( is_wp_error( $image ) || empty( $image ) ) {
            return false;
        }
        
        return isset( $image['image_data']['url'] ) ? $image['image_data']['url'] : false;
    }

    /**
     * Get category name
     *
     * @since 1.0.0
     * @param string $category_id Square category ID
     * @return string Category name
     */
    private function get_category_name( $category_id ) {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $square_api = new SquareKit_Square_API();
        $category = $square_api->get_catalog_item( $category_id );
        
        if ( is_wp_error( $category ) || empty( $category ) ) {
            return __( 'Unknown Category', 'squarekit' );
        }
        
        return isset( $category['category_data']['name'] ) ? $category['category_data']['name'] : __( 'Unknown Category', 'squarekit' );
    }

    /**
     * Process Square product for display in admin interface
     *
     * @since 1.0.0
     * @param array $product Raw Square product object
     * @return array|false Processed product or false
     */
    private function process_square_product_for_display( $product ) {
        if ( ! isset( $product['type'] ) || $product['type'] !== 'ITEM' ) {
            return false;
        }

        $item_data = $product['item_data'] ?? array();

        // Get primary image URL
        $image_url = '';
        if ( ! empty( $item_data['image_ids'] ) ) {
            $image_url = $this->get_square_image_url( $item_data['image_ids'][0] );
        }

        // Get price from first variation
        $price_money = null;
        if ( ! empty( $item_data['variations'] ) ) {
            $first_variation = $item_data['variations'][0];
            $price_money = $first_variation['item_variation_data']['price_money'] ?? null;
        }

        // Check if product is already imported to WooCommerce
        $sync_status = $this->get_product_sync_status( $product['id'] );

        return array(
            'id' => $product['id'],
            'name' => $item_data['name'] ?? 'Unnamed Product',
            'description' => $item_data['description'] ?? '',
            'type' => $product['type'],
            'image_url' => $image_url,
            'price_money' => $price_money,
            'category_id' => $item_data['category_id'] ?? '',
            'variations' => $item_data['variations'] ?? array(),
            'modifier_list_info' => $item_data['modifier_list_info'] ?? array(),
            'tax_ids' => $item_data['tax_ids'] ?? array(),
            'present_at_all_locations' => $item_data['present_at_all_locations'] ?? false,
            'imported' => $sync_status['imported'],
            'synced' => $sync_status['synced'],
            'out_of_sync' => $sync_status['out_of_sync']
        );
    }

    /**
     * Get product sync status
     *
     * @since 1.0.0
     * @param string $square_id Square product ID
     * @return array Sync status
     */
    private function get_product_sync_status( $square_id ) {
        global $wpdb;

        // Check if product exists in WooCommerce
        $wc_product_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = '_square_id' AND meta_value = %s",
            $square_id
        ) );

        $imported = ! empty( $wc_product_id );
        $synced = $imported; // For now, assume imported products are synced
        $out_of_sync = false; // This would need more complex logic to determine

        return array(
            'imported' => $imported,
            'synced' => $synced,
            'out_of_sync' => $out_of_sync
        );
    }

    /**
     * Format price
     *
     * @since 1.0.0
     * @param array $price_money Square price money object
     * @return string Formatted price
     */
    private function format_price( $price_money ) {
        if ( ! isset( $price_money['amount'] ) ) {
            return __( 'N/A', 'squarekit' );
        }

        $amount = $price_money['amount'] / 100; // Convert cents to dollars
        $currency = $price_money['currency'] ?? 'USD';

        return number_format( $amount, 2 ) . ' ' . strtoupper( $currency );
    }

    /**
     * Get count of imported products (products with Square ID)
     *
     * @since 1.0.0
     * @return int Number of imported products
     */
    private function get_imported_products_count() {
        global $wpdb;

        $count = $wpdb->get_var(
            "SELECT COUNT(DISTINCT post_id)
             FROM {$wpdb->postmeta}
             WHERE meta_key = '_square_id'
             AND meta_value != ''"
        );

        return (int) $count;
    }

    /**
     * AJAX handler: Get orders table
     *
     * @since 1.0.0
     */
    public function ajax_orders_table() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        // Get filter parameters
        $search = isset( $_POST['s'] ) ? sanitize_text_field( $_POST['s'] ) : '';
        $sync_status = isset( $_POST['sync_status'] ) ? sanitize_text_field( $_POST['sync_status'] ) : '';
        $order_status = isset( $_POST['order_status'] ) ? sanitize_text_field( $_POST['order_status'] ) : '';
        $per_page = isset( $_POST['per_page'] ) ? intval( $_POST['per_page'] ) : 50;
        $paged = isset( $_POST['paged'] ) ? intval( $_POST['paged'] ) : 1;

        // Build WooCommerce order query
        $args = array(
            'limit' => $per_page,
            'page' => $paged,
            'orderby' => 'date',
            'order' => 'DESC',
            'return' => 'objects'
        );

        // Add search filter
        if ( ! empty( $search ) ) {
            $args['search'] = $search;
        }

        // Add status filter
        if ( ! empty( $order_status ) ) {
            $args['status'] = $order_status;
        }

        // Get orders
        $orders = wc_get_orders( $args );
        $total_orders = wc_get_orders( array_merge( $args, array( 'limit' => -1, 'return' => 'ids' ) ) );
        $total_count = count( $total_orders );

        // Filter by sync status if specified
        if ( ! empty( $sync_status ) ) {
            $filtered_orders = array();
            foreach ( $orders as $order ) {
                $square_id = $order->get_meta( '_square_order_id' );
                $is_synced = ! empty( $square_id );

                if ( ( $sync_status === 'synced' && $is_synced ) ||
                     ( $sync_status === 'not_synced' && ! $is_synced ) ) {
                    $filtered_orders[] = $order;
                }
            }
            $orders = $filtered_orders;
        }

        // Generate table HTML
        $table_html = $this->render_orders_table( $orders, $paged, $per_page, $total_count );

        wp_send_json_success( array( 'table' => $table_html ) );
    }

    /**
     * AJAX handler: Handle order actions (sync, export, remove meta)
     *
     * @since 1.0.0
     */
    public function ajax_order_action() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        $order_id = isset( $_POST['order_id'] ) ? intval( $_POST['order_id'] ) : 0;
        $action = isset( $_POST['order_action'] ) ? sanitize_text_field( $_POST['order_action'] ) : '';

        if ( empty( $order_id ) || empty( $action ) ) {
            wp_send_json_error( array( 'message' => __( 'Order ID and action are required.', 'squarekit' ) ) );
        }

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wp_send_json_error( array( 'message' => __( 'Order not found.', 'squarekit' ) ) );
        }

        // Load order sync class
        if ( ! class_exists( 'SquareKit_Order_Sync' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-order-sync.php';
        }

        $order_sync = new SquareKit_Order_Sync();

        switch ( $action ) {
            case 'sync':
                $result = $order_sync->sync_order_to_square( $order_id, '', $order->get_status(), $order );
                if ( $result ) {
                    wp_send_json_success( array( 'message' => __( 'Order synced to Square successfully.', 'squarekit' ) ) );
                } else {
                    wp_send_json_error( array( 'message' => __( 'Failed to sync order to Square.', 'squarekit' ) ) );
                }
                break;

            case 'export':
                $result = $order_sync->export_order_to_square( $order_id );
                if ( $result ) {
                    wp_send_json_success( array( 'message' => __( 'Order exported to Square successfully.', 'squarekit' ) ) );
                } else {
                    wp_send_json_error( array( 'message' => __( 'Failed to export order to Square.', 'squarekit' ) ) );
                }
                break;

            case 'remove_meta':
                $order->delete_meta_data( '_square_order_id' );
                $order->delete_meta_data( '_square_payment_id' );
                $order->delete_meta_data( '_square_sync_status' );
                $order->save();
                wp_send_json_success( array( 'message' => __( 'Square metadata removed from order.', 'squarekit' ) ) );
                break;

            default:
                wp_send_json_error( array( 'message' => __( 'Invalid action.', 'squarekit' ) ) );
        }
    }

    /**
     * AJAX handler: Get order details
     *
     * @since 1.0.0
     */
    public function ajax_order_details() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit-admin', 'nonce' );

        $order_id = isset( $_POST['order_id'] ) ? intval( $_POST['order_id'] ) : 0;

        if ( empty( $order_id ) ) {
            wp_send_json_error( array( 'message' => __( 'Order ID is required.', 'squarekit' ) ) );
        }

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wp_send_json_error( array( 'message' => __( 'Order not found.', 'squarekit' ) ) );
        }

        $html = $this->render_order_details_html( $order );

        wp_send_json_success( array( 'html' => $html ) );
    }

    /**
     * Render orders table HTML
     *
     * @since 1.0.0
     * @param array $orders Array of WC_Order objects
     * @param int $paged Current page
     * @param int $per_page Items per page
     * @param int $total_count Total orders count
     * @return string HTML content
     */
    private function render_orders_table( $orders, $paged, $per_page, $total_count ) {
        $html = '<div class="squarekit-orders-table-container">';

        if ( empty( $orders ) ) {
            $html .= '<div class="notice notice-info"><p>' . __( 'No orders found.', 'squarekit' ) . '</p></div>';
            $html .= '</div>';
            return $html;
        }

        // Table
        $html .= '<table class="wp-list-table widefat fixed striped">';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>' . __( 'Order', 'squarekit' ) . '</th>';
        $html .= '<th>' . __( 'Date', 'squarekit' ) . '</th>';
        $html .= '<th>' . __( 'Status', 'squarekit' ) . '</th>';
        $html .= '<th>' . __( 'Customer', 'squarekit' ) . '</th>';
        $html .= '<th>' . __( 'Total', 'squarekit' ) . '</th>';
        $html .= '<th>' . __( 'Square Sync', 'squarekit' ) . '</th>';
        $html .= '<th>' . __( 'Actions', 'squarekit' ) . '</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';

        foreach ( $orders as $order ) {
            $square_id = $order->get_meta( '_square_order_id' );
            $is_synced = ! empty( $square_id );

            $html .= '<tr>';

            // Order number
            $html .= '<td><strong>#' . $order->get_order_number() . '</strong></td>';

            // Date
            $html .= '<td>' . $order->get_date_created()->date_i18n( 'Y-m-d H:i' ) . '</td>';

            // Status
            $status_name = wc_get_order_status_name( $order->get_status() );
            $html .= '<td><span class="order-status status-' . esc_attr( $order->get_status() ) . '">' . esc_html( $status_name ) . '</span></td>';

            // Customer
            $customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
            if ( empty( trim( $customer_name ) ) ) {
                $customer_name = $order->get_billing_email();
            }
            $html .= '<td>' . esc_html( $customer_name ) . '</td>';

            // Total
            $html .= '<td>' . $order->get_formatted_order_total() . '</td>';

            // Square sync status
            if ( $is_synced ) {
                $html .= '<td><span class="squarekit-sync-status synced">' . __( 'Synced', 'squarekit' ) . '</span><br>';
                $html .= '<small>ID: ' . esc_html( $square_id ) . '</small></td>';
            } else {
                $html .= '<td><span class="squarekit-sync-status not-synced">' . __( 'Not Synced', 'squarekit' ) . '</span></td>';
            }

            // Actions
            $html .= '<td>';
            $html .= '<button class="button button-small view-order-details" data-id="' . $order->get_id() . '">' . __( 'View', 'squarekit' ) . '</button> ';

            if ( ! $is_synced ) {
                $html .= '<button class="button button-small sync-order" data-id="' . $order->get_id() . '">' . __( 'Sync', 'squarekit' ) . '</button> ';
                $html .= '<button class="button button-small export-order" data-id="' . $order->get_id() . '">' . __( 'Export', 'squarekit' ) . '</button>';
            } else {
                $html .= '<button class="button button-small remove-meta-order" data-id="' . $order->get_id() . '">' . __( 'Remove Meta', 'squarekit' ) . '</button>';
            }

            $html .= '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';

        // Pagination
        $total_pages = ceil( $total_count / $per_page );
        if ( $total_pages > 1 ) {
            $html .= '<div class="squarekit-orders-pagination">';
            $html .= '<span class="pagination-info">' . sprintf(
                __( 'Page %d of %d (%d total orders)', 'squarekit' ),
                $paged,
                $total_pages,
                $total_count
            ) . '</span>';

            $html .= '<div class="pagination-links">';

            // Previous page
            if ( $paged > 1 ) {
                $html .= '<a href="#" class="button" data-page="' . ( $paged - 1 ) . '">' . __( 'Previous', 'squarekit' ) . '</a> ';
            }

            // Page numbers
            for ( $i = max( 1, $paged - 2 ); $i <= min( $total_pages, $paged + 2 ); $i++ ) {
                if ( $i == $paged ) {
                    $html .= '<span class="button button-primary">' . $i . '</span> ';
                } else {
                    $html .= '<a href="#" class="button" data-page="' . $i . '">' . $i . '</a> ';
                }
            }

            // Next page
            if ( $paged < $total_pages ) {
                $html .= '<a href="#" class="button" data-page="' . ( $paged + 1 ) . '">' . __( 'Next', 'squarekit' ) . '</a>';
            }

            $html .= '</div>';
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Render order details HTML
     *
     * @since 1.0.0
     * @param WC_Order $order Order object
     * @return string HTML content
     */
    private function render_order_details_html( $order ) {
        $html = '<div class="squarekit-order-details">';

        // Order header
        $html .= '<div class="order-header">';
        $html .= '<h3>' . sprintf( __( 'Order #%s', 'squarekit' ), $order->get_order_number() ) . '</h3>';
        $html .= '<p><strong>' . __( 'Date:', 'squarekit' ) . '</strong> ' . $order->get_date_created()->date_i18n( 'F j, Y g:i A' ) . '</p>';
        $html .= '<p><strong>' . __( 'Status:', 'squarekit' ) . '</strong> ' . wc_get_order_status_name( $order->get_status() ) . '</p>';
        $html .= '</div>';

        // Customer information
        $html .= '<div class="order-customer">';
        $html .= '<h4>' . __( 'Customer Information', 'squarekit' ) . '</h4>';
        $html .= '<p><strong>' . __( 'Name:', 'squarekit' ) . '</strong> ' . $order->get_billing_first_name() . ' ' . $order->get_billing_last_name() . '</p>';
        $html .= '<p><strong>' . __( 'Email:', 'squarekit' ) . '</strong> ' . $order->get_billing_email() . '</p>';
        if ( $order->get_billing_phone() ) {
            $html .= '<p><strong>' . __( 'Phone:', 'squarekit' ) . '</strong> ' . $order->get_billing_phone() . '</p>';
        }
        $html .= '</div>';

        // Billing address
        $html .= '<div class="order-billing">';
        $html .= '<h4>' . __( 'Billing Address', 'squarekit' ) . '</h4>';
        $html .= '<p>' . $order->get_formatted_billing_address() . '</p>';
        $html .= '</div>';

        // Shipping address
        if ( $order->has_shipping_address() ) {
            $html .= '<div class="order-shipping">';
            $html .= '<h4>' . __( 'Shipping Address', 'squarekit' ) . '</h4>';
            $html .= '<p>' . $order->get_formatted_shipping_address() . '</p>';
            $html .= '</div>';
        }

        // Order items
        $html .= '<div class="order-items">';
        $html .= '<h4>' . __( 'Order Items', 'squarekit' ) . '</h4>';
        $html .= '<table class="wp-list-table widefat">';
        $html .= '<thead><tr><th>' . __( 'Product', 'squarekit' ) . '</th><th>' . __( 'Quantity', 'squarekit' ) . '</th><th>' . __( 'Price', 'squarekit' ) . '</th><th>' . __( 'Total', 'squarekit' ) . '</th></tr></thead>';
        $html .= '<tbody>';

        foreach ( $order->get_items() as $item ) {
            $product = $item->get_product();
            $html .= '<tr>';
            $html .= '<td>' . $item->get_name();
            if ( $product ) {
                $html .= '<br><small>SKU: ' . $product->get_sku() . '</small>';
            }
            $html .= '</td>';
            $html .= '<td>' . $item->get_quantity() . '</td>';
            $html .= '<td>' . wc_price( $item->get_subtotal() / $item->get_quantity() ) . '</td>';
            $html .= '<td>' . wc_price( $item->get_total() ) . '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        // Order totals
        $html .= '<div class="order-totals">';
        $html .= '<h4>' . __( 'Order Totals', 'squarekit' ) . '</h4>';
        $html .= '<table class="wp-list-table widefat">';
        $html .= '<tr><td>' . __( 'Subtotal:', 'squarekit' ) . '</td><td>' . wc_price( $order->get_subtotal() ) . '</td></tr>';

        if ( $order->get_total_tax() > 0 ) {
            $html .= '<tr><td>' . __( 'Tax:', 'squarekit' ) . '</td><td>' . wc_price( $order->get_total_tax() ) . '</td></tr>';
        }

        if ( $order->get_shipping_total() > 0 ) {
            $html .= '<tr><td>' . __( 'Shipping:', 'squarekit' ) . '</td><td>' . wc_price( $order->get_shipping_total() ) . '</td></tr>';
        }

        $html .= '<tr><td><strong>' . __( 'Total:', 'squarekit' ) . '</strong></td><td><strong>' . $order->get_formatted_order_total() . '</strong></td></tr>';
        $html .= '</table>';
        $html .= '</div>';

        // Square sync information
        $square_id = $order->get_meta( '_square_order_id' );
        $square_payment_id = $order->get_meta( '_square_payment_id' );

        if ( ! empty( $square_id ) || ! empty( $square_payment_id ) ) {
            $html .= '<div class="order-square-info">';
            $html .= '<h4>' . __( 'Square Information', 'squarekit' ) . '</h4>';

            if ( ! empty( $square_id ) ) {
                $html .= '<p><strong>' . __( 'Square Order ID:', 'squarekit' ) . '</strong> ' . esc_html( $square_id ) . '</p>';
            }

            if ( ! empty( $square_payment_id ) ) {
                $html .= '<p><strong>' . __( 'Square Payment ID:', 'squarekit' ) . '</strong> ' . esc_html( $square_payment_id ) . '</p>';
            }

            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}