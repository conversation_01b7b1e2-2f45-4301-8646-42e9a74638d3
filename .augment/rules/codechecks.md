---
type: "always_apply"
---

# Augment AI — Code Verification & Recheck Loop Rules

> **Purpose**: This ruleset enforces a strict 3-pass verification process before any code is considered complete. You must recheck your output line-by-line multiple times to catch missing characters, broken syntax, unsafe types, and inconsistencies — especially those that could easily leak into production unnoticed.

---

## Required Passes

### **Pass 1: Immediate Syntax & Typing Check**
- Confirm that **every bracket, quote, backtick, template literal, and block** is properly closed.
- Ensure **no Markdown syntax errors** (e.g., unclosed backticks or malformed output).
- Validate all TypeScript syntax and ensure output passes `tsc --strict` without errors.

### **Pass 2: Structural & Logical Integrity**
- Ensure:
  - All **return types are defined**
  - All **interfaces and types are correctly imported and referenced**
  - **Async/await logic** is properly handled with try/catch blocks
  - No variable or function is **declared but unused**
- Double-check:
  - Each comment describes the logic it is above
  - No duplicated, stubbed, or unreachable logic

###  **Pass 3: Line-by-Line Human-Level Review**
- Imagine you’re doing a manual pull request review:
  - Do **any lines look visually off** (misaligned, typo, missing semi)?
  - Are all **imports actually used**?
  - Do all **named exports** exist?
  - Are **folder paths** consistent and correct?

---

## Mental Model

> “Assume you're reviewing another engineer’s pull request in a regulated prod environment. Be skeptical. Be methodical. You don’t get to explain mistakes later — only prevent them now.”

---

## Final Output Checklist (Before You Say “Done”)

- ✅ Code has been verified through **3 separate full reviews**
- ✅ No backticks, brackets, or blocks are left open
- ✅ All types, logic, and imports are complete and consistent
- ✅ All business logic is **safe, complete, and explainable**
- ✅ No line has been missed or output hastily

> ❌ Do not end your response until every one of these steps is completed internally.

---

## ❗ DO NOT:

- Skip the verification step due to "confidence"
- Output unchecked or partially verified code
- Apologize for errors — prevent them instead

---

## ✅ How to Use

This ruleset must be applied **in addition to** the main production/development rules. It governs the AI's **internal validation loop**, not just what is shown to the user.

