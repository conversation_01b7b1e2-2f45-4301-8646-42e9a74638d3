<?php
class Test_SquareKit_DB extends WP_UnitTestCase {
    public function setUp(): void {
        parent::setUp();
        $this->db = new SquareKit_DB();
    }

    public function test_tables_exist() {
        global $wpdb;
        $tables = [
            $wpdb->prefix . 'squarekit_logs',
            $wpdb->prefix . 'squarekit_inventory',
            $wpdb->prefix . 'squarekit_customers',
            $wpdb->prefix . 'squarekit_locations',
        ];
        foreach ($tables as $table) {
            $this->assertEquals($table, $wpdb->get_var($wpdb->prepare('SHOW TABLES LIKE %s', $table)));
        }
    }

    public function test_insert_and_get_log() {
        $log_id = $this->db->add_log('test', 'Unit test log message');
        $this->assertNotEmpty($log_id);
        $logs = $this->db->get_logs(['number' => 1]);
        $this->assertNotEmpty($logs);
        $this->assertEquals('Unit test log message', $logs[0]->log_message);
    }
} 