# SquareKit Test Files

This directory contains test files and debugging tools for the SquareKit plugin.

## Available Test Files

### 1. `test-price-calculation-fix.php`
**Purpose**: Tests the fixed price calculation logic for modifiers

**Usage**: 
```
http://yoursite.local/wp-content/plugins/squarekit/tests/test-price-calculation-fix.php?product_id=123
```

**What it tests**:
- Base price calculation for variable and simple products
- JavaScript localization data
- Modifier pricing calculations
- Price formatting logic
- Frontend JavaScript integration

**Features**:
- Automatic product detection if no ID provided
- Comprehensive price calculation testing
- JavaScript debugging tools
- Console logging verification

---

### 2. `debug-modifier-pricing.php`
**Purpose**: Investigates modifier pricing data structure and identifies issues

**Usage**:
```
http://yoursite.local/wp-content/plugins/squarekit/tests/debug-modifier-pricing.php?product_id=123
```

**What it analyzes**:
- Stored modifier data structure
- Price data integrity
- Frontend HTML output simulation
- Potential data issues identification

**Features**:
- Raw modifier data inspection
- Price analysis tables
- Issue detection and reporting
- HTML output preview

---

### 3. `test-modifier-display.php`
**Purpose**: Tests the fixed modifier display to ensure prices show correctly formatted

**Usage**:
```
http://yoursite.local/wp-content/plugins/squarekit/tests/test-modifier-display.php
```

**What it tests**:
- WooCommerce price function behavior
- HTML escaping issues
- Before/after comparison of fixes
- CSS styling verification

**Features**:
- Visual comparison of old vs new display
- Price formatting examples
- CSS styling tests
- HTML markup validation

---

## Debugging Tools

### Browser Console Commands

When testing on the frontend, use these console commands:

```javascript
// Debug current pricing state
squareKitDebug()

// Test price formatting
testPriceFormatting(25.00)

// Check localization data
console.log(squarekit_frontend)
```

### Common Issues & Solutions

#### Issue: "Failed opening required wp-config.php"
**Solution**: The test files automatically detect the WordPress root directory. If this fails, ensure you're running the tests from a properly configured WordPress installation.

#### Issue: "No products with modifiers found"
**Solution**: 
1. Import products with modifiers from Square
2. Manually specify a product ID in the URL: `?product_id=123`

#### Issue: JavaScript errors in console
**Solution**: 
1. Check that the product has modifiers
2. Verify the squarekit-frontend.js is loading
3. Use `squareKitDebug()` to inspect the data

---

## Test Scenarios

### Basic Price Calculation Test
1. Load a product with modifiers
2. Check base price calculation
3. Select modifiers and verify price updates
4. Test multiple modifier combinations

### Variable Product Test
1. Use a variable product with modifiers
2. Verify minimum variation price is used as base
3. Test modifier calculations with different variations

### Currency Formatting Test
1. Test with different currency settings
2. Verify thousand separators
3. Test decimal places
4. Check currency symbol positioning

---

## File Structure

```
tests/
├── README.md                     # This documentation
├── test-price-calculation-fix.php # Main price calculation testing
├── debug-modifier-pricing.php    # Data structure debugging
└── test-modifier-display.php     # Display formatting testing
```

---

## Contributing

When adding new test files:

1. Place them in the `/tests/` directory
2. Use proper WordPress loading: `require_once( $wp_root . '/wp-config.php' );`
3. Include comprehensive documentation
4. Add error handling and user-friendly output
5. Update this README with the new test information

---

## Notes

- All test files are designed to work without affecting the live site
- Tests are read-only and don't modify any data
- Use these tools for debugging modifier pricing issues
- Check browser console for additional debugging information
