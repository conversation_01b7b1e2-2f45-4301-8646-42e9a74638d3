<?php
/**
 * Test Import Progress Live
 * 
 * This creates a test bulk operation and demonstrates the real-time progress tracking.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

// Check if user is admin
if ( ! current_user_can( 'manage_options' ) ) {
    die( 'Access denied. Admin privileges required.' );
}

echo "<h1>Live Import Progress Test</h1>\n";
echo "<div id='test-output'><pre>\n";

// Check if WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    echo "❌ WooCommerce is not active. Please activate WooCommerce first.\n";
    exit;
}

echo "✅ WooCommerce is active\n";

// Check if required classes exist
if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
    echo "❌ SquareKit_Bulk_Operations class not found\n";
    exit;
}

if ( ! class_exists( 'SquareKit_Square_API' ) ) {
    echo "❌ SquareKit_Square_API class not found\n";
    exit;
}

echo "✅ Required classes are available\n";

// Check Square API connection
try {
    if ( ! class_exists( 'SquareKit_Settings' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
    }
    
    $settings = new SquareKit_Settings();
    $is_connected = $settings->is_connected();
    
    if ( ! $is_connected ) {
        echo "⚠️  Square API is not connected. Creating demo operation instead.\n";
        $demo_mode = true;
    } else {
        echo "✅ Square API is connected\n";
        $demo_mode = false;
    }
    
} catch ( Exception $e ) {
    echo "⚠️  Square API check failed: " . $e->getMessage() . ". Using demo mode.\n";
    $demo_mode = true;
}

// Create a test bulk operation
echo "\n=== Creating Test Bulk Operation ===\n";

$bulk_ops = new SquareKit_Bulk_Operations();

if ( $demo_mode ) {
    // Create a demo operation manually
    global $wpdb;
    $table_name = $wpdb->prefix . 'squarekit_bulk_operations';
    
    $operation_data = array(
        'operation_type' => 'import_products',
        'operation_status' => 'in_progress',
        'total_items' => 5,
        'processed_items' => 2,
        'successful_items' => 2,
        'failed_items' => 0,
        'progress_percentage' => 40.0,
        'current_item_name' => 'Test Product #3',
        'operation_data' => json_encode( array( 'demo' => true ) ),
        'created_by' => get_current_user_id(),
        'created_at' => current_time( 'mysql' )
    );
    
    $result = $wpdb->insert( $table_name, $operation_data );
    
    if ( $result ) {
        $operation_id = $wpdb->insert_id;
        echo "✅ Demo operation created with ID: {$operation_id}\n";
    } else {
        echo "❌ Failed to create demo operation\n";
        exit;
    }
} else {
    // Create a real operation (but don't process it)
    try {
        $operation_id = $bulk_ops->start_operation( 'import_products', array(
            'import_all' => true,
            'include_images' => true,
            'include_categories' => true,
            'include_variations' => true
        ) );
        
        if ( $operation_id ) {
            echo "✅ Real operation created with ID: {$operation_id}\n";
        } else {
            echo "❌ Failed to create real operation\n";
            exit;
        }
    } catch ( Exception $e ) {
        echo "❌ Error creating operation: " . $e->getMessage() . "\n";
        exit;
    }
}

// Test progress retrieval
echo "\n=== Testing Progress Retrieval ===\n";

$operation = $bulk_ops->get_operation( $operation_id );
if ( $operation ) {
    echo "✅ Operation retrieved successfully\n";
    echo "  Status: {$operation->operation_status}\n";
    echo "  Progress: {$operation->progress_percentage}%\n";
    echo "  Items: {$operation->processed_items}/{$operation->total_items}\n";
    if ( $operation->current_item_name ) {
        echo "  Current Item: {$operation->current_item_name}\n";
    }
} else {
    echo "❌ Failed to retrieve operation\n";
    exit;
}

// Test AJAX endpoint
echo "\n=== Testing AJAX Endpoint ===\n";

// Simulate AJAX request
$_POST['operation_id'] = $operation_id;
$_POST['nonce'] = wp_create_nonce( 'squarekit-admin' );

if ( ! class_exists( 'SquareKit_Admin' ) ) {
    require_once( dirname( __FILE__ ) . '/admin/class-squarekit-admin.php' );
}

$admin = new SquareKit_Admin();

// Capture the JSON response
ob_start();
try {
    $admin->ajax_get_bulk_operation_progress();
} catch ( Exception $e ) {
    echo "❌ AJAX endpoint error: " . $e->getMessage() . "\n";
}
$ajax_output = ob_get_clean();

if ( ! empty( $ajax_output ) ) {
    echo "✅ AJAX endpoint response received\n";
    echo "Response: " . $ajax_output . "\n";
} else {
    echo "✅ AJAX endpoint executed (no output is normal for wp_send_json_* functions)\n";
}

echo "\n=== Live Progress Tracking Demo ===\n";
echo "Operation ID: {$operation_id}\n";
echo "You can now test the frontend progress tracking!\n";

echo "</pre></div>\n";

// Add JavaScript for live testing
?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
jQuery(document).ready(function($) {
    var operationId = <?php echo $operation_id; ?>;
    var ajaxUrl = '<?php echo admin_url( 'admin-ajax.php' ); ?>';
    var nonce = '<?php echo wp_create_nonce( 'squarekit-admin' ); ?>';
    
    $('#test-output').append('\n=== Live AJAX Test ===\n');
    $('#test-output').append('Testing AJAX progress endpoint...\n');
    
    // Test the AJAX endpoint
    $.ajax({
        url: ajaxUrl,
        type: 'POST',
        data: {
            action: 'squarekit_get_bulk_operation_progress',
            operation_id: operationId,
            nonce: nonce
        },
        success: function(response) {
            if (response.success) {
                $('#test-output').append('✅ AJAX request successful!\n');
                $('#test-output').append('Progress Data:\n');
                $('#test-output').append('  Status: ' + response.data.status + '\n');
                $('#test-output').append('  Progress: ' + response.data.progress_percentage + '%\n');
                $('#test-output').append('  Items: ' + response.data.processed_items + '/' + response.data.total_items + '\n');
                if (response.data.current_item_name) {
                    $('#test-output').append('  Current Item: ' + response.data.current_item_name + '\n');
                }
                $('#test-output').append('  Is Completed: ' + response.data.is_completed + '\n');
                
                $('#test-output').append('\n✅ Frontend progress tracking is working!\n');
                $('#test-output').append('✅ Ready to test bulk import with real-time progress\n');
            } else {
                $('#test-output').append('❌ AJAX request failed: ' + response.data.message + '\n');
            }
        },
        error: function(xhr, status, error) {
            $('#test-output').append('❌ AJAX network error: ' + error + '\n');
        }
    });
    
    // Add test buttons
    $('body').append('<div style="margin: 20px; padding: 20px; border: 1px solid #ccc; background: #f9f9f9;">' +
        '<h3>Manual Test Controls</h3>' +
        '<button id="test-progress" style="margin: 5px; padding: 10px;">Test Progress Retrieval</button>' +
        '<button id="simulate-progress" style="margin: 5px; padding: 10px;">Simulate Progress Update</button>' +
        '<button id="complete-operation" style="margin: 5px; padding: 10px;">Mark Operation Complete</button>' +
        '</div>');
    
    // Test progress button
    $('#test-progress').click(function() {
        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'squarekit_get_bulk_operation_progress',
                operation_id: operationId,
                nonce: nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('Progress: ' + response.data.progress_percentage + '% - Status: ' + response.data.status);
                } else {
                    alert('Error: ' + response.data.message);
                }
            }
        });
    });
    
    // Simulate progress button
    $('#simulate-progress').click(function() {
        // This would normally be done by the background process
        var newProgress = Math.min(100, Math.random() * 100);
        alert('In a real scenario, the background process would update progress to ' + newProgress.toFixed(1) + '%');
    });
    
    // Complete operation button
    $('#complete-operation').click(function() {
        if (confirm('Mark this test operation as complete?')) {
            // Update the operation status (this is just for testing)
            alert('In a real scenario, this would mark the operation as completed');
        }
    });
});
</script>

<style>
#test-output {
    background: #f0f0f0;
    padding: 15px;
    border: 1px solid #ccc;
    font-family: monospace;
    white-space: pre-wrap;
    max-height: 500px;
    overflow-y: auto;
}
</style>

<?php
echo "<h3>Next Steps:</h3>\n";
echo "<ol>\n";
echo "<li>The test operation has been created with ID: <strong>{$operation_id}</strong></li>\n";
echo "<li>Use the test buttons above to verify AJAX functionality</li>\n";
echo "<li>Go to SquareKit Products page and test real bulk import</li>\n";
echo "<li>The progress modal should now show real-time updates</li>\n";
echo "</ol>\n";

echo "<p><strong>Expected Behavior:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ Modal stays open during import</li>\n";
echo "<li>✅ Progress updates every second</li>\n";
echo "<li>✅ Shows current item being processed</li>\n";
echo "<li>✅ Displays success/failure counts</li>\n";
echo "<li>✅ Closes automatically when complete</li>\n";
echo "</ul>\n";
?>
