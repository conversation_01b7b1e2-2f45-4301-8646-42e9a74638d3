OAuth Permissions Reference
Applies to: OAuth API

View all OAuth permissions (scopes) needed to call Square API endpoints with an OAuth access token.

Overview
To get a scoped OAuth access token used in calling a Square API endpoint, you need to specify the permissions that your application needs to access Square account resources. The permissions you specify are shown to the user in an authorization dialog that lets the user grant access to your application.

The following sections list and describe all the Square API services, their endpoints, and the OAuth permissions needed to access them. As a best practice, only request the permissions that your application requires.

Bank Accounts
The Bank Accounts API lets developers retrieve information about the bank accounts linked to a Square account.

API	Permission
GetBankAccount	BANK_ACCOUNTS_READ
ListBankAccounts	BANK_ACCOUNTS_READ
GetBankAccountByV1Id	BANK_ACCOUNTS_READ
Bookings
The Bookings API creates and maintains service appointments.

API	Permission
CreateBooking	APPOINTMENTS_WRITE (buyer-level). APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE (seller-level).
SearchAvailability	APPOINTMENTS_READ (buyer-level). APPOINTMENTS_READ and APPOINTMENTS_ALL_READ (seller-level).
RetrieveBusinessBookingProfile	APPOINTMENTS_BUSINESS_SETTINGS_READ
ListTeamMemberBookingProfiles	APPOINTMENTS_BUSINESS_SETTINGS_READ
RetrieveTeamMemberBookingProfile	APPOINTMENTS_BUSINESS_SETTINGS_READ
ListBookings	APPOINTMENTS_READ (buyer-level). APPOINTMENTS_READ and APPOINTMENTS_ALL_READ (seller-level).
RetrieveBooking	APPOINTMENTS_READ (buyer-level). APPOINTMENTS_READ and APPOINTMENTS_ALL_READ (seller-level).
UpdateBooking	APPOINTMENTS_WRITE(buyer-level). APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE (seller-level).
CancelBooking	APPOINTMENTS_WRITE (buyer-level). APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE (seller-level).
Booking Custom Attributes
The Booking Custom Attributes API lets you create and manage custom attributes for bookings.

API	Permission
CreateBookingCustomAttributeDefinition	Buyer-level: APPOINTMENTS_WRITE
Seller-level: APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE
UpdateBookingCustomAttributeDefinition	Buyer-level: APPOINTMENTS_WRITE
Seller-level: APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE
ListBookingCustomAttributeDefinitions	Buyer-level: APPOINTMENTS_READ
Seller-level: APPOINTMENTS_READ and APPOINTMENTS_ALL_READ
RetrieveBookingCustomAttributeDefinition	Buyer-level: APPOINTMENTS_READ
Seller-level: APPOINTMENTS_READ and APPOINTMENTS_ALL_READ
DeleteBookingCustomAttributeDefinition	Buyer-level: APPOINTMENTS_WRITE
Seller-level: APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE
UpsertBookingCustomAttribute	Buyer-level: APPOINTMENTS_WRITE
Seller-level: APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE
BulkUpsertBookingCustomAttributes	Buyer-level: APPOINTMENTS_WRITE
Seller-level: APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE
ListBookingCustomAttributes	Buyer-level: APPOINTMENTS_READ
Seller-level: APPOINTMENTS_READ and APPOINTMENTS_ALL_READ
RetrieveBookingCustomAttribute	Buyer-level: APPOINTMENTS_READ
Seller-level: APPOINTMENTS_READ and APPOINTMENTS_ALL_READ
DeleteBookingCustomAttribute	Buyer-level: APPOINTMENTS_WRITE
Seller-level: APPOINTMENTS_WRITE and APPOINTMENTS_ALL_WRITE
Cards
The Cards API provides endpoints to access a payment card stored on file.

API	Permissions
ListCards	PAYMENTS_READ
CreateCard	PAYMENTS_WRITE
RetrieveCard	PAYMENTS_READ
DisableCard	PAYMENTS_WRITE
Cash Drawer Shifts
The Cash Drawer Shifts API provides details about cash drawer shifts.

API	Permission
ListCashDrawerShifts	CASH_DRAWER_READ
ListCashDrawerShiftEvents	CASH_DRAWER_READ
RetrieveCashDrawerShift	CASH_DRAWER_READ
Catalog
The Catalog API syncs items to Square Point of Sale to itemize payments consistently across all channels.

API	Permission
BatchDeleteCatalogObjects	ITEMS_WRITE
BatchUpsertCatalogObjects	ITEMS_WRITE
BatchRetrieveCatalogObjects	ITEMS_READ
CatalogInfo	ITEMS_READ
CreateCatalogImage	ITEMS_WRITE
DeleteCatalogObject	ITEMS_WRITE
ListCatalog	ITEMS_READ
RetrieveCatalogObject	ITEMS_READ
SearchCatalogItems	ITEMS_READ
SearchCatalogObjects	ITEMS_READ
UpdateItemTaxes	ITEMS_WRITE
UpdateItemModifierLists	ITEMS_WRITE
UpsertCatalogObject	ITEMS_WRITE
Checkout
The Checkout API accepts itemized payments on a Square-hosted web page. No frontend experience is required.

API	Permission
CreatePaymentLink	ORDERS_WRITE
ORDERS_READ
PAYMENTS_WRITE
Customers
The Customers API creates and manages customer profiles and syncs customer relationship management (CRM) systems with Square.

API	Permission
AddGroupToCustomer	CUSTOMERS_WRITE
BulkCreateCustomers	CUSTOMERS_WRITE
BulkDeleteCustomers	CUSTOMERS_WRITE
BulkRetrieveCustomers	CUSTOMERS_READ
BulkUpdateCustomers	CUSTOMERS_WRITE
CreateCustomer	CUSTOMERS_WRITE
CreateCustomerCard (deprecated)	CUSTOMERS_WRITE
DeleteCustomer	CUSTOMERS_WRITE
DeleteCustomerCard (deprecated)	CUSTOMERS_WRITE
ListCustomers	CUSTOMERS_READ
RemoveGroupFromCustomer	CUSTOMERS_WRITE
RetrieveCustomer	CUSTOMERS_READ
SearchCustomers	CUSTOMERS_READ
UpdateCustomer	CUSTOMERS_WRITE
Customer Custom Attributes
The Customer Custom Attributes API lets you create and manage custom attributes for customer profiles.

API	Permission
CreateCustomerCustomAttributeDefinition	CUSTOMERS_WRITE
UpdateCustomerCustomAttributeDefinition	CUSTOMERS_WRITE
ListCustomerCustomAttributeDefinitions	CUSTOMERS_READ
RetrieveCustomerCustomAttributeDefinition	CUSTOMERS_READ
DeleteCustomerCustomAttributeDefinition	CUSTOMERS_WRITE
UpsertCustomerCustomAttribute	CUSTOMERS_WRITE
BulkUpsertCustomerCustomAttributes	CUSTOMERS_WRITE
ListCustomerCustomAttributes	CUSTOMERS_READ
RetrieveCustomerCustomAttribute	CUSTOMERS_READ
DeleteCustomerCustomAttribute	CUSTOMERS_WRITE
Customer Groups
The Customer Groups API manages customers by groups.

API	Permission
CreateCustomerGroup	CUSTOMERS_WRITE
DeleteCustomerGroup	CUSTOMERS_WRITE
ListCustomerGroups	CUSTOMERS_READ
RetrieveCustomerGroup	CUSTOMERS_READ
UpdateCustomerGroup	CUSTOMERS_WRITE
Customer Segments
The Customer Segments API manages customers by segments.

API	Permission
ListCustomerSegments	CUSTOMERS_READ
RetrieveCustomerSegment	CUSTOMERS_READ
Devices
Use the Devices API to configure a Square Terminal.

API	Permission
CreateDeviceCode	DEVICE_CREDENTIAL_MANAGEMENT
GetDeviceCode	DEVICE_CREDENTIAL_MANAGEMENT
ListDeviceCodes	DEVICE_CREDENTIAL_MANAGEMENT
ListDevices	DEVICES_READ
GetDevice	DEVICES_READ
Disputes
Use the Disputes API to manage disputes (chargebacks).

API	Permission
AcceptDispute	DISPUTES_WRITE
CreateDisputeEvidenceFile	DISPUTES_WRITE
CreateDisputeEvidenceText	DISPUTES_WRITE
ListDisputeEvidence	DISPUTES_READ
ListDisputes	DISPUTES_READ
DeleteDisputeEvidence	DISPUTES_WRITE
RetrieveDispute	DISPUTES_READ
RetrieveDisputeEvidence	DISPUTES_READ
SubmitEvidence	DISPUTES_WRITE
Employees
Retrieves employees for a seller. The Employees API is deprecated and replaced by the Team API.

API	Permission
ListEmployees (deprecated)	EMPLOYEES_READ
RetrieveEmployee (deprecated)	EMPLOYEES_READ
Events
The Events API returns information about Square events. This is the same information included in webhook event notifications.

To call Events API endpoints, provide the application's personal access token instead of an OAuth access token. Square uses OAuth on the backend to determine whether the application is authorized to access specific events.

Gift Cards
The Gift Cards API provides endpoints to create and manage gift cards.

API	Permission
ListGiftCards	GIFTCARDS_READ
CreateGiftCard	GIFTCARDS_WRITE
RetrieveGiftCard	GIFTCARDS_READ
RetrieveGiftCardFromGAN	GIFTCARDS_READ
RetrieveGiftCardFromNonce	GIFTCARDS_READ
LinkCustomerToGiftCard	GIFTCARDS_WRITE
UnlinkCustomerFromGiftCard	GIFTCARDS_WRITE
Gift Card Activities
The Gift Card Activities API provides endpoints to create gift card activities, such as activating a gift card, adding funds to a gift card, and redeeming a gift card.

API	Permission
ListGiftCardActivities	GIFTCARDS_READ
CreateGiftCardActivity	GIFTCARDS_WRITE
Inventory
The Inventory API keeps an inventory of catalog items in sync across all commerce channels.

API	Permission
BatchChangeInventory	INVENTORY_WRITE
BatchRetrieveInventoryCounts	INVENTORY_READ
BatchRetrieveInventoryChanges	INVENTORY_READ
RetrieveInventoryAdjustment	INVENTORY_READ
RetrieveInventoryChanges	INVENTORY_READ
RetrieveInventoryCount	INVENTORY_READ
RetrieveInventoryPhysicalCount	INVENTORY_READ
Invoices
Use the Invoices API to manage invoices.

API	Permissions
CreateInvoice	ORDERS_WRITE
INVOICES_WRITE
PublishInvoice	ORDERS_WRITE
INVOICES_WRITE
CUSTOMERS_READ and PAYMENTS_WRITE (for Square to charge cards on file)
GetInvoice	INVOICES_READ
ListInvoices	INVOICES_READ
SearchInvoices	INVOICES_READ
CreateInvoiceAttachment	ORDERS_WRITE
INVOICES_WRITE
DeleteInvoiceAttachment	ORDERS_WRITE
INVOICES_WRITE
UpdateInvoice	ORDERS_WRITE
INVOICES_WRITE
DeleteInvoice	ORDERS_WRITE
INVOICES_WRITE
CancelInvoice	ORDERS_WRITE
INVOICES_WRITE
Labor
The Labor API manages scheduled shifts, timecards (shifts), breaks, and wages for team members.

API	Permission
Break Types	
CreateBreakType	TIMECARDS_SETTINGS_WRITE
DeleteBreakType	TIMECARDS_SETTINGS_WRITE
GetBreakType	TIMECARDS_SETTINGS_READ
ListBreakTypes	TIMECARDS_SETTINGS_READ
UpdateBreakType	TIMECARDS_SETTINGS_READ
TIMECARDS_SETTINGS_WRITE
Scheduled Shifts	
BulkPublishScheduledShifts	TIMECARDS_WRITE
CreateScheduledShift	TIMECARDS_WRITE
PublishScheduledShift	TIMECARDS_WRITE
RetrieveScheduledShift	TIMECARDS_READ
SearchScheduledShifts	TIMECARDS_READ
UpdateScheduledShift	TIMECARDS_WRITE
Shifts	
CreateShift (deprecated)	TIMECARDS_WRITE
DeleteShift (deprecated)	TIMECARDS_WRITE
GetShift (deprecated)	TIMECARDS_READ
SearchShifts (deprecated)	TIMECARDS_READ
UpdateShift (deprecated)	TIMECARDS_WRITE
TIMECARDS_READ
Timecards	
CreateTimecard	TIMECARDS_WRITE
DeleteTimecard	TIMECARDS_WRITE
RetrieveTimecard	TIMECARDS_READ
SearchTimecards	TIMECARDS_READ
UpdateTimecard	TIMECARDS_SETTINGS_READ
TIMECARDS_SETTINGS_WRITE
Team Member Wages	
GetTeamMemberWage	EMPLOYEES_READ
ListTeamMemberWages	EMPLOYEES_READ
Workweek Configs	
ListWorkweekConfigs	TIMECARDS_SETTINGS_READ
UpdateWorkweekConfig	TIMECARDS_SETTINGS_READ
TIMECARDS_SETTINGS_WRITE
Locations
The Locations API gets a list of a seller's locations.

API	Permission
CreateLocation	MERCHANT_PROFILE_WRITE
ListLocations	MERCHANT_PROFILE_READ
RetrieveLocation	MERCHANT_PROFILE_READ
UpdateLocation	MERCHANT_PROFILE_WRITE
Location Custom Attributes
The Location Custom Attributes API lets you create and manage custom attributes for locations.

API	Permission
CreateLocationCustomAttributeDefinition	MERCHANT_PROFILE_WRITE
UpdateLocationCustomAttributeDefinition	MERCHANT_PROFILE_WRITE
ListLocationCustomAttributeDefinitions	MERCHANT_PROFILE_READ
RetrieveLocationCustomAttributeDefinition	MERCHANT_PROFILE_READ
DeleteLocationCustomAttributeDefinition	MERCHANT_PROFILE_WRITE
UpsertLocationCustomAttribute	MERCHANT_PROFILE_WRITE
BulkUpsertLocationCustomAttributes	MERCHANT_PROFILE_WRITE
ListLocationCustomAttributes	MERCHANT_PROFILE_READ
RetrieveLocationCustomAttribute	MERCHANT_PROFILE_READ
DeleteLocationCustomAttribute	MERCHANT_PROFILE_WRITE
BulkDeleteLocationCustomAttributes	MERCHANT_PROFILE_WRITE
Loyalty
The Loyalty API provides endpoints to work with loyalty programs, loyalty promotions, loyalty accounts, loyalty points, loyalty rewards, and loyalty events.

API	Permission
Loyalty programs	
ListLoyaltyPrograms (deprecated)	LOYALTY_READ
RetrieveLoyaltyProgram	LOYALTY_READ
Loyalty promotions	
CancelLoyaltyPromotion	LOYALTY_WRITE
CreateLoyaltyPromotion	LOYALTY_WRITE
ListLoyaltyPromotions	LOYALTY_READ
RetrieveLoyaltyPromotion	LOYALTY_READ
Loyalty accounts	
CreateLoyaltyAccount	LOYALTY_WRITE
RetrieveLoyaltyAccount	LOYALTY_READ
SearchLoyaltyAccounts	LOYALTY_READ
Loyalty points	
AccumulateLoyaltyPoints	LOYALTY_WRITE
AdjustLoyaltyPoints	LOYALTY_WRITE
CalculateLoyaltyPoints	LOYALTY_READ
Loyalty rewards	
CreateLoyaltyReward	LOYALTY_WRITE
DeleteLoyaltyReward	LOYALTY_WRITE
RedeemLoyaltyReward	LOYALTY_WRITE
RetrieveLoyaltyReward	LOYALTY_READ
SearchLoyaltyRewards	LOYALTY_READ
Loyalty events	
SearchLoyaltyEvents	LOYALTY_READ
Merchants
Use the Merchants API to retrieve information about a Square merchant account.

API	Permission
ListMerchants	MERCHANT_PROFILE_READ
RetrieveMerchant	MERCHANT_PROFILE_READ
Merchant Custom Attributes
The Merchant Custom Attributes API lets you create and manage custom attributes for merchants.

API	Permission
CreateMerchantCustomAttributeDefinition	MERCHANT_PROFILE_WRITE
UpdateMerchantCustomAttributeDefinition	MERCHANT_PROFILE_WRITE
ListMerchantCustomAttributeDefinitions	MERCHANT_PROFILE_READ
RetrieveMerchantCustomAttributeDefinition	MERCHANT_PROFILE_READ
DeleteMerchantCustomAttributeDefinition	MERCHANT_PROFILE_WRITE
UpsertMerchantCustomAttribute	MERCHANT_PROFILE_WRITE
BulkUpsertMerchantCustomAttributes	MERCHANT_PROFILE_WRITE
ListMerchantCustomAttributes	MERCHANT_PROFILE_READ
RetrieveMerchantCustomAttribute	MERCHANT_PROFILE_READ
DeleteMerchantCustomAttribute	MERCHANT_PROFILE_WRITE
BulkDeleteMerchantCustomAttributes	MERCHANT_PROFILE_WRITE
Mobile Authorization
The Mobile Authorization API provides an endpoint for getting a mobile authorization code for use in Reader SDK applications.

API	Permission
CreateMobileAuthorizationCode	PAYMENTS_WRITE_IN_PERSON
Orders
The Orders API gets sales data for a Square seller, itemize payments, push orders to Point of Sale, and more.

API	Permission
CalculateOrder	N/A
CloneOrder	ORDERS_WRITE
CreateOrder	ORDERS_WRITE
BatchRetrieveOrders	ORDERS_READ
PayOrder	ORDERS_WRITE
PAYMENTS_WRITE
RetrieveOrder	ORDERS_WRITE
ORDERS_READ
SearchOrders	ORDERS_READ
UpdateOrder	ORDERS_WRITE
Order Custom Attributes
The Order Custom Attributes API lets you create and manage custom attributes for orders.

API	Permission
CreateOrderCustomAttributeDefinition	ORDERS_WRITE
UpdateOrderCustomAttributeDefinition	ORDERS_WRITE
ListOrderCustomAttributeDefinitions	ORDERS_READ
RetrieveOrderCustomAttributeDefinition	ORDERS_READ
DeleteOrderCustomAttributeDefinition	ORDERS_WRITE
UpsertOrderCustomAttribute	ORDERS_WRITE
BulkUpsertOrderCustomAttributes	ORDERS_WRITE
ListOrderCustomAttributes	ORDERS_READ
RetrieveOrderCustomAttribute	ORDERS_READ
DeleteOrderCustomAttribute	ORDERS_WRITE
BulkDeleteOrderCustomAttributes	ORDERS_WRITE
Payments and Refunds
The Payments API lets developers take and manage payments. The Refunds API lets developers refund payments.

API	Permission
CancelPayment	PAYMENTS_WRITE
CancelPaymentByIdempotencyKey	PAYMENTS_WRITE
CompletePayment	PAYMENTS_WRITE
CreatePayment	PAYMENTS_WRITE
PAYMENTS_WRITE_SHARED_ONFILE
PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS
GetPayment	PAYMENTS_READ
GetPaymentRefund	PAYMENTS_READ
ListPayments	PAYMENTS_READ
ListPaymentRefunds	PAYMENTS_READ
RefundPayment	PAYMENTS_WRITE
PAYMENTS_WRITE_ADDITIONAL_RECIPIENTS
Payouts
Use the Payouts API to see a list of deposits and withdrawals from a seller’s external bank account, a Square checking or savings account (available only in the United States), or a debit card (for instant transfers).

API	Permission
ListPayouts	PAYOUTS_READ
GetPayout	PAYOUTS_READ
ListPayoutEntries	PAYOUTS_READ
Sites
Use the Sites API to list the Square Online sites for a seller account.

API	Permissions
ListSites	ONLINE_STORE_SITE_READ
Snippets
Use the Snippets API to manage snippets on Square Online sites.

API	Permissions
UpsertSnippet	ONLINE_STORE_SNIPPETS_WRITE
RetrieveSnippet	ONLINE_STORE_SNIPPETS_READ
DeleteSnippet	ONLINE_STORE_SNIPPETS_WRITE
Subscriptions
Use the Subscriptions API to manage subscriptions. For more information about required permissions to create and update subscriptions, see Requirements and limitations.

API	Permissions
CreateSubscription	CUSTOMERS_READ
PAYMENTS_WRITE
SUBSCRIPTIONS_WRITE
ITEMS_READ
ORDERS_WRITE
INVOICES_WRITE
SearchSubscriptions	SUBSCRIPTIONS_READ
RetrieveSubscription	SUBSCRIPTIONS_READ
UpdateSubscription	CUSTOMERS_READ
PAYMENTS_WRITE
SUBSCRIPTIONS_WRITE
ITEMS_READ
ORDERS_WRITE
INVOICES_WRITE
CancelSubscription	SUBSCRIPTIONS_WRITE
ListSubscriptionEvents	SUBSCRIPTIONS_READ
ResumeSubscription	CUSTOMERS_READ
PAYMENTS_WRITE
SUBSCRIPTIONS_WRITE
ITEMS_READ
ORDERS_WRITE
INVOICES_WRITE
PauseSubscription	CUSTOMERS_READ
PAYMENTS_WRITE
SUBSCRIPTIONS_WRITE
ITEMS_READ
ORDERS_WRITE
INVOICES_WRITE
SwapPlan	CUSTOMERS_READ
PAYMENTS_WRITE
SUBSCRIPTIONS_WRITE
ITEMS_READ
ORDERS_WRITE
INVOICES_WRITE
DeleteSubscriptionAction	SUBSCRIPTIONS_WRITE
Team
The Team API lets developers manage team members and job definitions.

API	Permission
BulkCreateTeamMembers	EMPLOYEES_WRITE
BulkUpdateTeamMembers	EMPLOYEES_WRITE
CreateTeamMember	EMPLOYEES_WRITE
UpdateTeamMember	EMPLOYEES_WRITE
RetrieveTeamMember	EMPLOYEES_READ
SearchTeamMembers	EMPLOYEES_READ
UpdateWageSetting	EMPLOYEES_WRITE
RetrieveWageSetting	EMPLOYEES_READ
CreateJob	EMPLOYEES_WRITE
UpdateJob	EMPLOYEES_WRITE
ListJobs	EMPLOYEES_READ
RetrieveJob	EMPLOYEES_READ
Terminal
The Terminal API lets developers request Square Terminal checkouts and process Interac refunds.

API	Permission
CreateTerminalCheckout	PAYMENTS_WRITE
CancelTerminalCheckout	PAYMENTS_WRITE
GetTerminalCheckout	PAYMENTS_READ
SearchTerminalCheckouts	PAYMENTS_READ
CreateTerminalRefund	PAYMENTS_WRITE
CancelTerminalRefund	PAYMENTS_WRITE
GetTerminalRefund	PAYMENTS_READ
SearchTerminalRefunds	PAYMENTS_READ
CreateTerminalAction	PAYMENTS_WRITE
CancelTerminalAction	PAYMENTS_WRITE
GetTerminalAction	PAYMENTS_READ
CUSTOMERS_READ
SearchTerminalAction	PAYMENTS_READ
Vendors
The Vendors API supports managing a seller's suppliers in an application.

API	Permission
BulkCreateVendors	VENDOR_WRITE
BulkRetrieveVendors	VENDOR_READ
BulkUpdateVendors	VENDOR_WRITE
CreateVendor	VENDOR_WRITE
SearchVendors	VENDOR_READ
RetrieveVendor	VENDOR_READ
UpdateVendors	VENDOR_WRITE
Webhook Subscriptions
The Webhook Subscriptions API lets you programmatically manage webhook subscriptions.

To call Webhook Subscriptions endpoints, provide the application's personal access token instead of an OAuth access token. Square uses OAuth on the backend to determine whether the application is authorized to access specific events.