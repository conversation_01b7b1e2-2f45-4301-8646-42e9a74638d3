<?php
/**
 * Test Option Resolution System
 * Tests the SWEVER-style option resolution that transforms Square API responses
 */

// Include required classes
require_once __DIR__ . '/includes/api/class-squarekit-square-api.php';
require_once __DIR__ . '/includes/importers/class-squarekit-option-resolver.php';
require_once __DIR__ . '/includes/core/class-squarekit-settings.php';
require_once __DIR__ . '/includes/core/class-squarekit-logger.php';

echo "=== SquareKit Option Resolution Test ===\n";

// Test the specific product that has variations
$square_item_id = '26TOQGLJ7VBL6H5MNCVJNSH5'; // Jamaica Blue Mountain

try {
    echo "1. Creating Option Resolver...\n";
    
    // Create instances (this might fail without WordPress environment)
    $settings = new SquareKit_Settings();
    $logger = new SquareKit_Logger();
    $square_api = new SquareKit_Square_API( $settings, $logger );
    $option_resolver = new SquareKit_Option_Resolver( $square_api );
    
    echo "✅ Option Resolver created successfully\n";
    
    echo "\n2. Testing lookup table initialization...\n";
    $init_result = $option_resolver->initialize_lookup_tables();
    
    if ( $init_result ) {
        echo "✅ Lookup tables initialized successfully\n";
        
        // Get debug info
        $debug_info = $option_resolver->get_debug_info();
        echo "   - Options loaded: " . $debug_info['option_count'] . "\n";
        echo "   - Option values loaded: " . $debug_info['option_value_count'] . "\n";
        
        if ( $debug_info['option_count'] > 0 ) {
            echo "   - Sample options:\n";
            $sample_options = array_slice( $debug_info['options'], 0, 3, true );
            foreach ( $sample_options as $id => $name ) {
                echo "     * {$id} => {$name}\n";
            }
        }
        
    } else {
        echo "❌ Failed to initialize lookup tables\n";
    }
    
    echo "\n3. Testing specific option resolution...\n";
    
    // Test the specific option ID from the debug output
    $test_option_id = 'FS76HZLFWYVWREUZHXFQC436';
    $option_name = $option_resolver->get_option_name( $test_option_id );
    
    if ( $option_name ) {
        echo "✅ Option ID {$test_option_id} resolved to: {$option_name}\n";
    } else {
        echo "❌ Could not resolve option ID {$test_option_id}\n";
    }
    
    // Test the specific option value IDs from the debug output
    $test_value_ids = array(
        'ADH7IXGU4OHVENAQNVLJZH7D', // Should be "Small"
        '5BPZOBN6DUFMNMXIKYSPIDD7', // Should be "Medium"
        'EKU24L53RJNTY5EC5XWNYTQE'  // Should be "Large"
    );
    
    foreach ( $test_value_ids as $value_id ) {
        $value_name = $option_resolver->get_option_value_name( $value_id );
        if ( $value_name ) {
            echo "✅ Option value ID {$value_id} resolved to: {$value_name}\n";
        } else {
            echo "❌ Could not resolve option value ID {$value_id}\n";
        }
    }
    
    echo "\n4. Testing data transformation...\n";
    
    // Create a mock Square product data structure like the one from the debug output
    $mock_square_product = array(
        'id' => $square_item_id,
        'type' => 'ITEM',
        'item_data' => array(
            'name' => 'Jamaica Blue Mountain Gr 1',
            'variations' => array(
                array(
                    'id' => 'GSJEAAHBG2YLMTAAIDRFCVGH',
                    'item_variation_data' => array(
                        'name' => 'Small',
                        'item_option_values' => array(
                            array(
                                'item_option_id' => 'FS76HZLFWYVWREUZHXFQC436',
                                'item_option_value_id' => 'ADH7IXGU4OHVENAQNVLJZH7D'
                            )
                        )
                    )
                )
            )
        )
    );
    
    echo "Before transformation:\n";
    echo "   - Option ID: " . $mock_square_product['item_data']['variations'][0]['item_variation_data']['item_option_values'][0]['item_option_id'] . "\n";
    echo "   - Option Value ID: " . $mock_square_product['item_data']['variations'][0]['item_variation_data']['item_option_values'][0]['item_option_value_id'] . "\n";
    
    // Transform the data
    $transformed_product = $option_resolver->transform_product_data( $mock_square_product );
    
    echo "\nAfter transformation:\n";
    $option_value = $transformed_product['item_data']['variations'][0]['item_variation_data']['item_option_values'][0];
    
    if ( isset( $option_value['option_name'] ) ) {
        echo "✅ Option name added: " . $option_value['option_name'] . "\n";
    } else {
        echo "❌ Option name not added\n";
    }
    
    if ( isset( $option_value['option_value'] ) ) {
        echo "✅ Option value added: " . $option_value['option_value'] . "\n";
    } else {
        echo "❌ Option value not added\n";
    }
    
} catch ( Exception $e ) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
    echo "   This is expected if running outside WordPress environment\n";
}

echo "\n=== Test Complete ===\n";
echo "Note: This test may fail outside WordPress environment due to missing database connection.\n";
echo "The important thing is that the class structure and logic are correct.\n";
