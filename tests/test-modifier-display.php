<?php
/**
 * Test Modifier Display Changes
 * 
 * Quick test to verify the modifier display changes are working
 */

// WordPress environment
require_once('../../../wp-config.php');

// Check if we can access the WooCommerce integration
if (!class_exists('SquareKit_WooCommerce')) {
    require_once('includes/integrations/class-squarekit-woocommerce.php');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>SquareKit Modifier Display Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>SquareKit Modifier Display Test</h1>
    
    <div class="test-section">
        <h2>1. Check WooCommerce Integration Class</h2>
        <?php
        if (class_exists('SquareKit_WooCommerce')) {
            echo '<p class="success">✓ SquareKit_WooCommerce class exists</p>';
            
            $wc = new SquareKit_WooCommerce();
            
            // Check if the display method exists
            if (method_exists($wc, 'display_product_modifiers')) {
                echo '<p class="success">✓ display_product_modifiers method exists</p>';
            } else {
                echo '<p class="error">✗ display_product_modifiers method missing</p>';
            }
            
        } else {
            echo '<p class="error">✗ SquareKit_WooCommerce class not found</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>2. Check Hook Registration</h2>
        <?php
        // Check if the hook is registered
        global $wp_filter;
        
        if (isset($wp_filter['woocommerce_single_product_summary'])) {
            echo '<p class="info">Hooks registered for woocommerce_single_product_summary:</p>';
            foreach ($wp_filter['woocommerce_single_product_summary']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'SquareKit_WooCommerce' &&
                        $callback['function'][1] === 'display_product_modifiers') {
                        echo '<p class="success">✓ Found SquareKit modifier display hook at priority ' . $priority . '</p>';
                    }
                }
            }
        }
        
        if (isset($wp_filter['woocommerce_before_add_to_cart_form'])) {
            echo '<p class="info">Checking old hook location (should be empty):</p>';
            $found_old = false;
            foreach ($wp_filter['woocommerce_before_add_to_cart_form']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && 
                        is_object($callback['function'][0]) && 
                        get_class($callback['function'][0]) === 'SquareKit_WooCommerce' &&
                        $callback['function'][1] === 'display_product_modifiers') {
                        echo '<p class="error">✗ Found old hook still registered at priority ' . $priority . '</p>';
                        $found_old = true;
                    }
                }
            }
            if (!$found_old) {
                echo '<p class="success">✓ Old hook location is clean</p>';
            }
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>3. Check CSS File</h2>
        <?php
        $css_file = 'assets/css/squarekit-frontend.css';
        if (file_exists($css_file)) {
            echo '<p class="success">✓ CSS file exists</p>';
            
            $css_content = file_get_contents($css_file);
            if (strpos($css_content, '#083624') !== false) {
                echo '<p class="success">✓ Found #083624 color in CSS</p>';
            } else {
                echo '<p class="error">✗ #083624 color not found in CSS</p>';
            }
            
            if (strpos($css_content, 'linear-gradient(135deg, #083624') !== false) {
                echo '<p class="success">✓ Found gradient with #083624 in CSS</p>';
            } else {
                echo '<p class="error">✗ Gradient with #083624 not found in CSS</p>';
            }
        } else {
            echo '<p class="error">✗ CSS file not found</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>4. Check JavaScript File</h2>
        <?php
        $js_file = 'assets/js/squarekit-frontend.js';
        if (file_exists($js_file)) {
            echo '<p class="success">✓ JavaScript file exists</p>';
            
            $js_content = file_get_contents($js_file);
            if (strpos($js_content, 'getCurrentVariationPrice') !== false) {
                echo '<p class="success">✓ Found getCurrentVariationPrice function</p>';
            } else {
                echo '<p class="error">✗ getCurrentVariationPrice function not found</p>';
            }
        } else {
            echo '<p class="error">✗ JavaScript file not found</p>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>5. Recommendations</h2>
        <p class="info">To see the changes:</p>
        <ul>
            <li>Clear any caching plugins</li>
            <li>Hard refresh the browser (Ctrl+F5)</li>
            <li>Check that the product has modifiers assigned</li>
            <li>Verify WooCommerce is active and working</li>
        </ul>
    </div>
    
</body>
</html>
