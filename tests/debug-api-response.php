<?php
/**
 * Debug API response structure
 */

// Include the Square API class
require_once __DIR__ . '/includes/api/class-squarekit-square-api.php';
require_once __DIR__ . '/includes/core/class-squarekit-settings.php';
require_once __DIR__ . '/includes/core/class-squarekit-logger.php';

echo "=== API Response Structure Debug ===\n";

// Test the specific product
$square_item_id = '26TOQGLJ7VBL6H5MNCVJNSH5';

try {
    // Create API instance (this might fail without WordPress, but let's see the structure)
    $settings = new SquareKit_Settings();
    $logger = new SquareKit_Logger();
    $api = new SquareKit_Square_API( $settings, $logger );
    
    echo "Testing get_catalog_item_with_relations for ID: {$square_item_id}\n\n";
    
    // This will likely fail due to missing token, but we can see the method structure
    $result = $api->get_catalog_item_with_relations( $square_item_id );
    
    if ( is_wp_error( $result ) ) {
        echo "Expected error (no token): " . $result->get_error_message() . "\n";
    } else {
        echo "Unexpected success: " . print_r( $result, true ) . "\n";
    }
    
} catch ( Exception $e ) {
    echo "Exception: " . $e->getMessage() . "\n";
}

echo "\n=== Based on debug output analysis ===\n";
echo "From the test file, we know:\n";
echo "1. get_catalog_object() returns item data directly\n";
echo "2. get_catalog_item_with_relations() should return:\n";
echo "   - item: the catalog object\n";
echo "   - related_objects: organized related data\n";
echo "\nThe issue is likely in the API response parsing.\n";
echo "The Square API returns the object in response['object'], not directly.\n";

echo "\n=== Debug Complete ===\n";
