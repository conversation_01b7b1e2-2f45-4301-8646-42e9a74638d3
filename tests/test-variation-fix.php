<?php
/**
 * Test Variation Import Fix
 * 
 * This file tests the fixed variation import logic for Square products
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;
    
    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }
    
    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Test Variation Import Fix</h2>\n";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.debug-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.debug-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.issue { background: #ffe6e6; border-left: 3px solid #d63638; }
.success { background: #e6ffe6; border-left: 3px solid #00a32a; }
.warning { background: #fff3cd; border-left: 3px solid #ffc107; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
</style>\n";

$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 175;
$force_reimport = isset( $_GET['reimport'] ) && $_GET['reimport'] === '1';

echo "<div class='debug-section'>";
echo "<div class='debug-title'>Testing Variation Import Fix for Product ID: $product_id</div>";
echo "<div class='debug-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span style='color: red;'>Product not found with ID: $product_id</span>\n";
    exit;
}

echo "Product: " . $product->get_name() . "<br>\n";
echo "Current Type: " . $product->get_type() . "<br>\n";

$square_item_id = get_post_meta( $product_id, '_square_id', true );
if ( ! $square_item_id ) {
    echo "<span style='color: red;'>No Square ID found for this product</span>\n";
    exit;
}

echo "Square Item ID: " . $square_item_id . "<br>\n";

echo "</div></div>\n";

if ( $force_reimport ) {
    echo "<div class='debug-section'>";
    echo "<div class='debug-title'>🔄 Re-importing Product from Square</div>";
    echo "<div class='debug-data'>";
    
    try {
        // Load required classes
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $square_api = new SquareKit_Square_API();
        $wc_integration = new SquareKit_WooCommerce();
        
        // Get the Square item data
        echo "Fetching Square item data...<br>\n";
        $square_item_response = $square_api->get_catalog_object( $square_item_id );
        
        if ( is_wp_error( $square_item_response ) ) {
            echo "<div class='issue'>❌ Failed to fetch Square item: " . $square_item_response->get_error_message() . "</div>\n";
        } else {
            $square_item = $square_item_response['object'] ?? null;
            
            if ( ! $square_item ) {
                echo "<div class='issue'>❌ No item data in Square response</div>\n";
            } else {
                echo "<div class='success'>✅ Successfully fetched Square item data</div>\n";
                
                // Check variations
                $variations = $square_item['item_data']['variations'] ?? array();
                echo "Variations found: " . count( $variations ) . "<br>\n";
                
                // Re-import the product
                echo "Re-importing product...<br>\n";
                $import_result = $wc_integration->import_product_from_square( $square_item, array(), $product_id );
                
                if ( is_wp_error( $import_result ) ) {
                    echo "<div class='issue'>❌ Import failed: " . $import_result->get_error_message() . "</div>\n";
                } else {
                    echo "<div class='success'>✅ Import completed successfully!</div>\n";
                    echo "Result: <pre>" . print_r( $import_result, true ) . "</pre>\n";
                }
            }
        }
        
    } catch ( Exception $e ) {
        echo "<div class='issue'>❌ Exception during import: " . $e->getMessage() . "</div>\n";
    }
    
    echo "</div></div>\n";
    
    // Refresh product data
    wp_cache_delete( $product_id, 'posts' );
    $product = wc_get_product( $product_id );
}

// Check current product state
echo "<div class='debug-section'>";
echo "<div class='debug-title'>📊 Current Product State Analysis</div>";
echo "<div class='debug-data'>";

echo "<h4>Product Information:</h4>\n";
echo "Name: " . $product->get_name() . "<br>\n";
echo "Type: " . $product->get_type() . "<br>\n";
echo "Status: " . $product->get_status() . "<br>\n";

// Check attributes
$attributes = $product->get_attributes();
echo "<h4>Attributes:</h4>\n";
if ( ! empty( $attributes ) ) {
    echo "<div class='success'>✅ Found " . count( $attributes ) . " attributes:</div>\n";
    echo "<ul>\n";
    foreach ( $attributes as $attribute ) {
        echo "<li>";
        echo "<strong>" . $attribute->get_name() . "</strong>: ";
        echo implode( ', ', $attribute->get_options() );
        echo " (Variation: " . ( $attribute->get_variation() ? 'Yes' : 'No' ) . ")";
        echo "</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<div class='issue'>❌ No attributes found</div>\n";
}

// Check variations
if ( $product->is_type( 'variable' ) ) {
    echo "<h4>Variations:</h4>\n";
    $variations = $product->get_available_variations();
    
    if ( ! empty( $variations ) ) {
        echo "<div class='success'>✅ Found " . count( $variations ) . " variations:</div>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Attributes</th><th>Price</th><th>SKU</th></tr>\n";
        
        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            
            echo "<tr>";
            echo "<td>" . $variation->get_id() . "</td>";
            echo "<td>";
            
            $variation_attributes = $variation->get_variation_attributes();
            if ( ! empty( $variation_attributes ) ) {
                foreach ( $variation_attributes as $attr_name => $attr_value ) {
                    echo str_replace( 'attribute_', '', $attr_name ) . ": " . $attr_value . "<br>";
                }
            } else {
                echo "No attributes";
            }
            
            echo "</td>";
            echo "<td>$" . number_format( $variation->get_price(), 2 ) . "</td>";
            echo "<td>" . ( $variation->get_sku() ?: 'No SKU' ) . "</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    } else {
        echo "<div class='issue'>❌ Variable product but no variations found</div>\n";
    }
} else {
    echo "<h4>Simple Product:</h4>\n";
    echo "Price: $" . number_format( $product->get_price(), 2 ) . "<br>\n";
    echo "SKU: " . ( $product->get_sku() ?: 'No SKU' ) . "<br>\n";
}

echo "</div></div>\n";

// Check modifiers
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🔧 SquareKit Modifiers</div>";
echo "<div class='debug-data'>";

$modifiers = get_post_meta( $product_id, '_squarekit_modifiers', true );
$modifiers_frontend = get_post_meta( $product_id, '_squarekit_modifiers_frontend', true );

if ( ! empty( $modifiers ) ) {
    echo "<div class='success'>✅ Found SquareKit modifiers:</div>\n";
    echo "<pre>" . print_r( $modifiers, true ) . "</pre>\n";
} else {
    echo "<div class='warning'>⚠️ No SquareKit modifiers found</div>\n";
}

if ( ! empty( $modifiers_frontend ) ) {
    echo "<div class='success'>✅ Found frontend modifier data:</div>\n";
    echo "<pre>" . print_r( $modifiers_frontend, true ) . "</pre>\n";
}

echo "</div></div>\n";

// Action buttons
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🎯 Actions</div>";
echo "<div class='debug-data'>";

if ( ! $force_reimport ) {
    echo "<p><a href='?product_id={$product_id}&reimport=1' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔄 Re-import Product from Square</a></p>\n";
}

echo "<p><a href='?product_id={$product_id}' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔍 Refresh Analysis</a></p>\n";

echo "<p><a href='/wp-admin/post.php?post={$product_id}&action=edit' style='background: #00a32a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>✏️ Edit Product in WooCommerce</a></p>\n";

echo "</div></div>\n";

?>
