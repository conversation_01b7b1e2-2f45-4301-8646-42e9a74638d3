# Plugin Reload Instructions

## Issue
The new importer classes are not being loaded because they were added after the plugin was already active. WordPress only loads plugin files when the plugin is activated.

## Solution
You need to reload the plugin to load the new classes. Here are the steps:

### Method 1: Deactivate and Reactivate Plugin (Recommended)
1. Go to WordPress Admin → Plugins
2. Find "SquareKit" plugin
3. Click "Deactivate"
4. Wait for the page to reload
5. Click "Activate"

### Method 2: Alternative - Clear Object Cache (if using caching)
If you're using object caching or opcache:
1. Clear any object cache (Redis, Memcached, etc.)
2. Clear PHP opcache if enabled
3. Refresh the page

## Verification Steps

### Step 1: Run Class Loading Test
Visit: `http://teapot.local/wp-content/plugins/squarekit/verify-class-loading.php`

You should see:
```
✅ All 4 new classes are properly loaded!
✅ Ready to run the full import system test
```

### Step 2: Run Full Import System Test
Visit: `http://teapot.local/wp-content/plugins/squarekit/test-fixed-import-system.php`

You should see:
```
=== Class Availability Check ===
✅ SquareKit_Square_API is available
✅ SquareKit_WooCommerce is available
✅ SquareKit_Attribute_Importer is available
✅ SquareKit_Variation_Importer is available
✅ SquareKit_Modifier_Importer is available
✅ SquareKit_Price_Calculator is available
```

## What Was Changed

### Files Modified:
1. **squarekit.php** - Added new class loading with proper dependency order
2. **includes/class-squarekit-loader.php** - Updated dependency loading order

### New Classes Added:
1. **SquareKit_Attribute_Importer** - Handles Square item options → WooCommerce attributes
2. **SquareKit_Variation_Importer** - Handles Square variations → WooCommerce variations  
3. **SquareKit_Modifier_Importer** - Handles Square modifiers → WooCommerce modifiers
4. **SquareKit_Price_Calculator** - Fixes variable product price ranges

### Loading Order:
1. API classes (dependencies)
2. Attribute mapper and utilities
3. Import handler classes
4. Integration classes

## Troubleshooting

### If Classes Still Not Loading:
1. **Check file permissions** - Ensure new files are readable
2. **Check for PHP errors** - Look in WordPress error logs
3. **Verify file paths** - Ensure all new files exist in correct locations
4. **Clear all caches** - Browser, WordPress, server-level caches

### Common Issues:
- **Plugin conflicts** - Temporarily deactivate other plugins
- **PHP memory limits** - Increase if needed
- **File permissions** - Ensure 644 for files, 755 for directories
- **Syntax errors** - Check PHP error logs

## Expected Result
After reloading the plugin, all new classes should be available and the Square import system should work with:
- ✅ Proper attribute creation
- ✅ Correct variation import
- ✅ Fixed modifier display
- ✅ Accurate price ranges
- ✅ Real-time progress tracking

## Next Steps After Successful Loading:
1. Test Square product import
2. Verify attributes and variations are created
3. Check modifier display on frontend
4. Test bulk import progress tracking
5. Monitor logs for any issues
