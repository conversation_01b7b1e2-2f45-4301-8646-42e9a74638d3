# Enhanced Image Handling with Galleries

## Overview

The Square Kit plugin now includes enhanced image handling capabilities that support product galleries, image optimization, and improved import/export functionality between WooCommerce and Square.

## Features

### 1. Gallery Support
- **Multiple Images**: Import and export multiple images per product
- **Gallery Management**: Automatically handle product galleries in WooCommerce
- **Square Integration**: Support for Square's image catalog objects

### 2. Image Optimization
- **Automatic Resizing**: Resize large images to configurable dimensions
- **Quality Control**: Adjustable JPEG quality settings
- **Format Support**: JPEG, PNG, GIF, and WebP support
- **Transparency Preservation**: Maintains PNG transparency

### 3. Enhanced Import/Export
- **URL-based Import**: Download images from Square URLs
- **Duplicate Prevention**: Prevents importing the same image multiple times
- **Error Handling**: Comprehensive error logging and handling
- **Batch Processing**: Efficient handling of multiple images

## Settings

### Image Optimization Settings
- **Enable Optimization**: Toggle image optimization on/off
- **Maximum Width**: Set maximum image width (default: 1200px)
- **Maximum Height**: Set maximum image height (default: 1200px)
- **JPEG Quality**: Set JPEG compression quality (default: 85%)

### Gallery Settings
- **Automatic Gallery Import**: Import all product images from Square
- **Gallery Export**: Export WooCommerce galleries to Square
- **Main Image Selection**: First image becomes the main product image

## Implementation Details

### Image Import Process
1. **URL Validation**: Check if image URL is valid
2. **Duplicate Check**: Check if image already exists by source URL
3. **Download**: Download image data from URL
4. **Optimization**: Resize and compress if enabled
5. **Upload**: Add to WordPress media library
6. **Metadata**: Store source URL for future reference

### Gallery Import Process
1. **Image Collection**: Gather all image URLs from Square
2. **Main Image**: Set first image as main product image
3. **Gallery Images**: Add remaining images to product gallery
4. **Metadata Update**: Update product gallery metadata

### Export Process
1. **Image Collection**: Gather all product images from WooCommerce
2. **Main Image**: Export main product image to Square
3. **Gallery Images**: Create Square image objects for gallery images
4. **Catalog Update**: Update Square catalog with new images

## Error Handling

### Common Issues
- **Network Errors**: Failed downloads are logged and skipped
- **Invalid Images**: Non-image files are rejected with error logging
- **Upload Failures**: WordPress upload errors are logged
- **Memory Issues**: Large images are optimized to prevent memory problems

### Error Logging
- **Error Types**: Network, validation, upload, and optimization errors
- **Error Details**: URL, error message, and timestamp
- **Database Logging**: Errors stored in plugin logs table

## Performance Considerations

### Optimization Benefits
- **File Size Reduction**: Typically 20-50% smaller files
- **Loading Speed**: Faster page loads with optimized images
- **Storage Efficiency**: Reduced server storage requirements
- **Bandwidth Savings**: Lower bandwidth usage for image delivery

### Memory Management
- **Image Processing**: Efficient memory usage during optimization
- **Batch Processing**: Process images in manageable batches
- **Cleanup**: Proper cleanup of image resources

## Usage Examples

### Import Images from Square
```php
$wc = new SquareKit_WooCommerce();
$image_urls = array(
    'https://example.com/main-image.jpg',
    'https://example.com/gallery-1.jpg',
    'https://example.com/gallery-2.jpg'
);
$attachment_ids = $wc->import_product_gallery($image_urls, $product_id);
```

### Export Images to Square
```php
$wc = new SquareKit_WooCommerce();
$image_urls = $wc->export_product_images($product_id);
// $image_urls contains all product image URLs
```

### Optimize Single Image
```php
$wc = new SquareKit_WooCommerce();
$optimized_data = $wc->optimize_image($image_data, 'image/jpeg');
```

## Configuration

### Settings Page
Navigate to **Square Kit > Settings** to configure:
- Image optimization settings
- Maximum dimensions
- Quality settings
- Gallery import/export preferences

### Default Settings
- **Optimization**: Enabled
- **Max Width**: 1200px
- **Max Height**: 1200px
- **Quality**: 85%

## Testing

### Test Suite
Run the included test suite to verify functionality:
```bash
wp eval-file tests/test-image-handling.php
```

### Manual Testing
1. Import products from Square with images
2. Verify gallery images are imported correctly
3. Export products to Square
4. Check that gallery images are exported

## Troubleshooting

### Common Issues
1. **Images Not Importing**: Check network connectivity and URL validity
2. **Optimization Not Working**: Verify GD library is installed
3. **Memory Errors**: Reduce maximum image dimensions
4. **Upload Failures**: Check WordPress upload directory permissions

### Debug Information
- Check error logs for detailed error messages
- Verify image URLs are accessible
- Test with smaller images first
- Check server memory limits

## Future Enhancements

### Planned Features
- **WebP Support**: Native WebP format support
- **Lazy Loading**: Implement lazy loading for galleries
- **CDN Integration**: Support for CDN image delivery
- **Advanced Filters**: Image filters and effects
- **Bulk Operations**: Bulk image optimization tools

### API Improvements
- **Async Processing**: Background image processing
- **Progress Tracking**: Real-time import progress
- **Resume Support**: Resume interrupted imports
- **Batch Limits**: Configurable batch processing limits

## Support

For issues or questions regarding enhanced image handling:
1. Check the error logs for specific error messages
2. Verify all settings are configured correctly
3. Test with a small set of images first
4. Contact support with specific error details

---

*This enhanced image handling system provides robust, efficient, and user-friendly image management for the Square Kit plugin.* 