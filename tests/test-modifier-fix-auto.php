<?php
/**
 * Auto Modifier Import Fix Test
 * 
 * One-click test that automatically finds a product with modifiers and tests the import fix.
 * No configuration needed - just run this file!
 */

// WordPress environment - adjust path for Local by Flywheel
$wp_load_paths = [
    dirname(__FILE__) . '/../../../wp-load.php',  // Standard WordPress
    dirname(__FILE__) . '/../../../../wp-load.php', // Local by Flywheel
    dirname(__FILE__) . '/../../../../../wp-load.php' // Alternative structure
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die("❌ Could not find wp-load.php. Please run this file from a web browser.");
}

// Check if SquareKit plugin is loaded
if (!defined('SQUAREKIT_PLUGIN_DIR')) {
    die("❌ SquareKit plugin is not loaded. Please ensure the plugin is active.");
}

// Include required classes
if (!class_exists('SquareKit_Product_Importer')) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
}
if (!class_exists('SquareKit_Square_API')) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
}

echo "<h1>🚀 Auto Modifier Import Fix Test</h1>\n";
echo "<p><strong>One-click test</strong> - automatically finds and tests a product with modifiers!</p>\n";

try {
    // Step 1: Auto-find product with modifiers
    echo "<h2>🔍 Step 1: Finding Product with Modifiers</h2>\n";
    
    $square_api = new SquareKit_Square_API();
    $catalog_response = $square_api->get_catalog(array('types' => 'ITEM'));
    
    if (is_wp_error($catalog_response)) {
        echo "❌ Failed to fetch catalog: " . $catalog_response->get_error_message() . "<br>\n";
        exit;
    }
    
    $items = $catalog_response['objects'] ?? array();
    echo "📦 Scanning " . count($items) . " products...<br>\n";
    
    $test_product = null;
    $checked = 0;
    
    foreach ($items as $item) {
        $checked++;
        if ($checked > 30) break; // Limit scan to avoid timeout
        
        $modifier_list_info = $item['item_data']['modifier_list_info'] ?? array();
        if (!empty($modifier_list_info)) {
            $test_product = $item;
            break;
        }
    }
    
    if (!$test_product) {
        echo "❌ No products with modifiers found in first {$checked} products.<br>\n";
        echo "Please ensure you have products with modifiers in your Square catalog.<br>\n";
        exit;
    }
    
    $product_id = $test_product['id'];
    $product_name = $test_product['item_data']['name'] ?? 'Unknown';
    $modifier_count = count($test_product['item_data']['modifier_list_info']);
    
    echo "✅ Found: <strong>{$product_name}</strong><br>\n";
    echo "📋 Square ID: <code>{$product_id}</code><br>\n";
    echo "🔧 Modifier Lists: {$modifier_count}<br>\n";
    
    // Step 2: Test import
    echo "<h2>🚀 Step 2: Testing Import</h2>\n";
    
    $importer = new SquareKit_Product_Importer();
    
    $import_config = array(
        'name' => true,
        'description' => true,
        'images' => true,
        'categories' => true,
        'variations' => true,
        'modifiers' => true,  // CRITICAL: Test the fix
        'attributesDisabled' => false
    );
    
    echo "🔄 Importing product with modifiers enabled...<br>\n";
    
    $result = $importer->import_product_swever_style(
        $product_id,
        $import_config,
        false
    );
    
    if (is_wp_error($result)) {
        echo "❌ Import failed: " . $result->get_error_message() . "<br>\n";
        exit;
    }
    
    $wc_product_id = $result['product_id'];
    echo "✅ Import successful!<br>\n";
    echo "📦 WooCommerce Product ID: {$wc_product_id}<br>\n";
    
    // Step 3: Verify modifier persistence
    echo "<h2>🔍 Step 3: Verifying Modifier Fix</h2>\n";
    
    // Check SWEVER format
    $swever_modifiers = get_post_meta($wc_product_id, '_squarekit_modifier_sets', true);
    $swever_count = is_array($swever_modifiers) ? count($swever_modifiers) : 0;
    
    // Check legacy format
    $legacy_modifiers = get_post_meta($wc_product_id, '_squarekit_modifiers', true);
    $legacy_count = is_array($legacy_modifiers) ? count($legacy_modifiers) : 0;
    
    // Check modifier count
    $modifier_count_meta = get_post_meta($wc_product_id, '_squarekit_modifier_count', true);
    
    echo "📊 Database Check Results:<br>\n";
    echo "  • SWEVER format: " . ($swever_count > 0 ? "✅ {$swever_count} sets" : "❌ Empty") . "<br>\n";
    echo "  • Legacy format: " . ($legacy_count > 0 ? "✅ {$legacy_count} sets" : "❌ Empty") . "<br>\n";
    echo "  • Count meta: " . ($modifier_count_meta ? "✅ {$modifier_count_meta}" : "❌ Missing") . "<br>\n";
    
    // Step 4: Test results
    echo "<h2>🎯 Test Results</h2>\n";
    
    $success = ($swever_count > 0 && $legacy_count > 0 && $modifier_count_meta > 0);
    
    if ($success) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724;'>\n";
        echo "<h3>🎉 SUCCESS! Modifier Import Fix is Working!</h3>\n";
        echo "<ul>\n";
        echo "<li>✅ Modifiers are properly imported from Square</li>\n";
        echo "<li>✅ Data is saved in both SWEVER and legacy formats</li>\n";
        echo "<li>✅ Modifier count is tracked correctly</li>\n";
        echo "<li>✅ Modifiers should persist after page refresh</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        echo "<h3>🔗 Next Steps</h3>\n";
        $admin_url = admin_url("post.php?post={$wc_product_id}&action=edit");
        $product_url = get_permalink($wc_product_id);
        
        echo "<ol>\n";
        echo "<li><a href='{$admin_url}' target='_blank'>🔧 Check Admin Interface</a> - Verify modifier sets display correctly</li>\n";
        echo "<li><a href='{$product_url}' target='_blank'>🌐 Check Frontend</a> - Verify 'Customization Options' appear</li>\n";
        echo "<li>Test persistence by adding a manual modifier set and refreshing</li>\n";
        echo "</ol>\n";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; color: #721c24;'>\n";
        echo "<h3>❌ FAILURE! Modifier Import is Still Broken!</h3>\n";
        echo "<ul>\n";
        if ($swever_count === 0) echo "<li>❌ SWEVER modifier sets not saved</li>\n";
        if ($legacy_count === 0) echo "<li>❌ Legacy modifier sets not saved</li>\n";
        if (!$modifier_count_meta) echo "<li>❌ Modifier count not tracked</li>\n";
        echo "</ul>\n";
        echo "<p><strong>The fix needs more work!</strong></p>\n";
        echo "</div>\n";
    }
    
    // Show modifier details for debugging
    if ($swever_count > 0) {
        echo "<h3>🔍 Imported Modifier Details</h3>\n";
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Set Name</th><th>Square ID</th><th>Options</th><th>Type</th></tr>\n";
        
        foreach ($swever_modifiers as $set) {
            $set_name = $set['set_name'] ?? 'Unknown';
            $square_id = $set['square_mod_list_id'] ?? 'Missing';
            $option_count = count($set['options'] ?? array());
            $choice_type = ($set['single_choice'] ?? false) ? 'Single' : 'Multiple';
            
            echo "<tr>\n";
            echo "<td>{$set_name}</td>\n";
            echo "<td><code>{$square_id}</code></td>\n";
            echo "<td>{$option_count} options</td>\n";
            echo "<td>{$choice_type}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<br>\n<hr>\n";
echo "<em>Auto-test completed at " . date('Y-m-d H:i:s') . "</em>\n";
