<?php
/**
 * Debug Square Data for Variation Issues
 * 
 * This file examines the actual Square data to understand why variations aren't importing
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;
    
    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }
    
    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Debug Square Data for Variation Issues</h2>\n";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.debug-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.debug-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.issue { background: #ffe6e6; border-left: 3px solid #d63638; }
.success { background: #e6ffe6; border-left: 3px solid #00a32a; }
.warning { background: #fff3cd; border-left: 3px solid #ffc107; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
</style>\n";

$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 175;

echo "<div class='debug-section'>";
echo "<div class='debug-title'>Square Data Analysis for Product ID: $product_id</div>";
echo "<div class='debug-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span style='color: red;'>Product not found with ID: $product_id</span>\n";
    exit;
}

$square_item_id = get_post_meta( $product_id, '_square_id', true );
if ( ! $square_item_id ) {
    echo "<span style='color: red;'>No Square ID found for this product</span>\n";
    exit;
}

echo "Product: " . $product->get_name() . "<br>\n";
echo "Square Item ID: " . $square_item_id . "<br>\n";

echo "</div></div>\n";

// Get Square API data
echo "<div class='debug-section'>";
echo "<div class='debug-title'>Fetching Live Square Data</div>";
echo "<div class='debug-data'>";

// Load Square API
if ( ! class_exists( 'SquareKit_Square_API' ) ) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
}

$square_api = new SquareKit_Square_API();

try {
    // Get the catalog object
    $response = $square_api->get_catalog_object( $square_item_id );
    
    if ( is_wp_error( $response ) ) {
        echo "<div class='issue'>Error fetching Square data: " . $response->get_error_message() . "</div>\n";
    } else {
        $catalog_object = $response['object'] ?? null;
        
        if ( ! $catalog_object ) {
            echo "<div class='issue'>No catalog object found in Square response</div>\n";
        } else {
            echo "<div class='success'>✅ Successfully fetched Square data</div>\n";
            
            // Analyze the structure
            echo "<h4>Square Item Structure:</h4>\n";
            echo "<pre>" . print_r( $catalog_object, true ) . "</pre>\n";
            
            // Check variations
            if ( isset( $catalog_object['item_data']['variations'] ) ) {
                $variations = $catalog_object['item_data']['variations'];
                echo "<h4>Variations Analysis:</h4>\n";
                echo "Number of variations: " . count( $variations ) . "<br>\n";
                echo "Is variable logic (count > 1): " . ( count( $variations ) > 1 ? 'TRUE' : 'FALSE' ) . "<br>\n";
                echo "Should be variable (count >= 1): " . ( count( $variations ) >= 1 ? 'TRUE' : 'FALSE' ) . "<br>\n";
                
                echo "<h4>Individual Variations:</h4>\n";
                foreach ( $variations as $i => $variation ) {
                    echo "<h5>Variation " . ($i + 1) . ":</h5>\n";
                    echo "<pre>" . print_r( $variation, true ) . "</pre>\n";
                    
                    // Check for item option values
                    if ( isset( $variation['item_variation_data']['item_option_values'] ) ) {
                        echo "<div class='success'>✅ Has item_option_values (this creates attributes)</div>\n";
                        $option_values = $variation['item_variation_data']['item_option_values'];
                        echo "Option values: <pre>" . print_r( $option_values, true ) . "</pre>\n";
                    } else {
                        echo "<div class='warning'>⚠️ No item_option_values found</div>\n";
                    }
                }
            } else {
                echo "<div class='issue'>❌ No variations found in Square data</div>\n";
            }
            
            // Check for option sets
            if ( isset( $catalog_object['item_data']['item_option_data'] ) ) {
                echo "<h4>Item Option Data:</h4>\n";
                echo "<pre>" . print_r( $catalog_object['item_data']['item_option_data'], true ) . "</pre>\n";
            }
        }
    }
    
} catch ( Exception $e ) {
    echo "<div class='issue'>Exception: " . $e->getMessage() . "</div>\n";
}

echo "</div></div>\n";

// Check the import logic issue
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🚨 Import Logic Issue Analysis</div>";
echo "<div class='debug-data issue'>";

echo "<h4>CRITICAL BUG FOUND:</h4>\n";
echo "<p><strong>File:</strong> <code>includes/integrations/class-squarekit-woocommerce.php</code></p>\n";
echo "<p><strong>Line 3672:</strong> <code>\$is_variable = count(\$variations) > 1;</code></p>\n";

echo "<h4>The Problem:</h4>\n";
echo "<ul>\n";
echo "<li>❌ Current logic: Product is variable only if it has <strong>MORE than 1</strong> variation</li>\n";
echo "<li>✅ Correct logic: Product should be variable if it has <strong>1 or more</strong> variations with item options</li>\n";
echo "<li>🔍 Your product likely has exactly 3 variations (Small, Medium, Large), so count > 1 = TRUE</li>\n";
echo "<li>🤔 But there might be another issue in the variation import process</li>\n";
echo "</ul>\n";

echo "<h4>Recommended Fix:</h4>\n";
echo "<p>Change line 3672 to:</p>\n";
echo "<pre>\$is_variable = count(\$variations) > 1 && \$this->has_item_options(\$variations);</pre>\n";
echo "<p>Or simply:</p>\n";
echo "<pre>\$is_variable = \$this->should_be_variable_product(\$item);</pre>\n";

echo "</div></div>\n";

// Test the current logic
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🧪 Test Current Import Logic</div>";
echo "<div class='debug-data'>";

echo "<h4>Simulating Import Logic:</h4>\n";

// Simulate what happens during import
if ( isset( $catalog_object['item_data']['variations'] ) ) {
    $variations = $catalog_object['item_data']['variations'];
    $variation_count = count( $variations );
    $is_variable_current = $variation_count > 1;
    $is_variable_correct = $variation_count >= 1;
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Logic</th><th>Result</th><th>Product Type</th></tr>\n";
    echo "<tr><td>Current: count > 1</td><td>" . ($is_variable_current ? 'TRUE' : 'FALSE') . "</td><td>" . ($is_variable_current ? 'Variable' : 'Simple') . "</td></tr>\n";
    echo "<tr><td>Correct: count >= 1</td><td>" . ($is_variable_correct ? 'TRUE' : 'FALSE') . "</td><td>" . ($is_variable_correct ? 'Variable' : 'Simple') . "</td></tr>\n";
    echo "</table>\n";
    
    if ( $is_variable_current ) {
        echo "<div class='success'>✅ Current logic would make this a variable product</div>\n";
        echo "<p>Since the logic is working for this case, the issue must be in the variation import process itself.</p>\n";
    } else {
        echo "<div class='issue'>❌ Current logic would make this a simple product (WRONG!)</div>\n";
    }
}

echo "</div></div>\n";

?>
