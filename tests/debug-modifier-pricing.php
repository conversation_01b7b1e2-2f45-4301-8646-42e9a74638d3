<?php
/**
 * Debug Modifier Pricing Issues
 * 
 * This file investigates the modifier pricing data structure and calculations
 * to identify why prices are displaying incorrectly.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;

    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }

    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Debugging Modifier Pricing Issues</h2>\n";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.debug-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.debug-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.error { color: #d63638; }
.success { color: #00a32a; }
.warning { color: #dba617; }
</style>\n";

// Get the product ID from URL parameter or use a default
$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 0;

if ( ! $product_id ) {
    // Try to find a product with modifiers
    $products_with_modifiers = get_posts( array(
        'post_type' => 'product',
        'meta_query' => array(
            array(
                'key' => '_squarekit_modifiers',
                'compare' => 'EXISTS'
            )
        ),
        'posts_per_page' => 1,
        'fields' => 'ids'
    ) );
    
    if ( ! empty( $products_with_modifiers ) ) {
        $product_id = $products_with_modifiers[0];
    }
}

if ( ! $product_id ) {
    echo "<div class='debug-section error'>No product ID provided and no products with modifiers found. Please add ?product_id=XXX to the URL.</div>\n";
    exit;
}

echo "<div class='debug-section'>";
echo "<div class='debug-title'>Product Information</div>";
echo "<div class='debug-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span class='error'>Product not found with ID: $product_id</span>\n";
    exit;
}

echo "Product ID: $product_id<br>\n";
echo "Product Name: " . $product->get_name() . "<br>\n";
echo "Product Type: " . $product->get_type() . "<br>\n";
echo "Product Price: " . $product->get_price() . "<br>\n";
echo "Product Regular Price: " . $product->get_regular_price() . "<br>\n";
echo "Product Sale Price: " . $product->get_sale_price() . "<br>\n";

if ( function_exists( 'wc_price' ) ) {
    echo "Formatted Price: " . wc_price( $product->get_price() ) . "<br>\n";
}

echo "</div></div>\n";

// Check stored modifier data
echo "<div class='debug-section'>";
echo "<div class='debug-title'>Stored Modifier Data</div>";
echo "<div class='debug-data'>";

$modifiers = get_post_meta( $product_id, '_squarekit_modifiers', true );
$frontend_modifiers = get_post_meta( $product_id, '_squarekit_modifiers_frontend', true );

echo "<h4>Raw Modifier Data (_squarekit_modifiers):</h4>\n";
if ( ! empty( $modifiers ) ) {
    echo "<pre>" . print_r( $modifiers, true ) . "</pre>\n";
} else {
    echo "<span class='warning'>No modifier data found</span><br>\n";
}

echo "<h4>Frontend Modifier Data (_squarekit_modifiers_frontend):</h4>\n";
if ( ! empty( $frontend_modifiers ) ) {
    echo "<pre>" . print_r( $frontend_modifiers, true ) . "</pre>\n";
} else {
    echo "<span class='warning'>No frontend modifier data found</span><br>\n";
}

echo "</div></div>\n";

// Analyze price data structure
if ( ! empty( $modifiers ) ) {
    echo "<div class='debug-section'>";
    echo "<div class='debug-title'>Price Analysis</div>";
    echo "<div class='debug-data'>";
    
    foreach ( $modifiers as $set_index => $set ) {
        echo "<h4>Modifier Set $set_index: " . ( $set['set_name'] ?? 'Unnamed' ) . "</h4>\n";
        echo "Single Choice: " . ( $set['single_choice'] ? 'Yes' : 'No' ) . "<br>\n";
        echo "Required: " . ( $set['required'] ? 'Yes' : 'No' ) . "<br>\n";
        
        if ( ! empty( $set['options'] ) ) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr><th>Option Name</th><th>Price (Raw)</th><th>Price (Formatted)</th><th>Square ID</th></tr>\n";
            
            foreach ( $set['options'] as $option_index => $option ) {
                $name = $option['name'] ?? 'Unnamed';
                $price = $option['price'] ?? 0;
                $square_id = $option['square_modifier_id'] ?? 'N/A';
                
                echo "<tr>";
                echo "<td>" . esc_html( $name ) . "</td>";
                echo "<td>" . esc_html( $price ) . "</td>";
                
                if ( function_exists( 'wc_price' ) ) {
                    echo "<td>" . wc_price( $price ) . "</td>";
                } else {
                    echo "<td>$" . number_format( $price, 2 ) . "</td>";
                }
                
                echo "<td>" . esc_html( $square_id ) . "</td>";
                echo "</tr>\n";
            }
            
            echo "</table>\n";
        } else {
            echo "<span class='warning'>No options found in this set</span><br>\n";
        }
    }
    
    echo "</div></div>\n";
}

// Test JavaScript data attributes
echo "<div class='debug-section'>";
echo "<div class='debug-title'>JavaScript Data Attributes Test</div>";
echo "<div class='debug-data'>";

if ( ! empty( $modifiers ) ) {
    echo "<h4>Simulated Frontend HTML Output:</h4>\n";
    
    foreach ( $modifiers as $set_index => $set ) {
        $set_name = $set['set_name'] ?? 'Unnamed Set';
        $is_single = $set['single_choice'] ?? false;
        
        echo "<div class='squarekit-modifier-set'>\n";
        echo "<h4>" . esc_html( $set_name ) . "</h4>\n";
        
        $input_type = $is_single ? 'radio' : 'checkbox';
        $name_attr = $is_single ? 'squarekit_modifier_' . $set_index : 'squarekit_modifier_' . $set_index . '[]';
        
        foreach ( $set['options'] as $option_index => $option ) {
            $option_id = 'squarekit_modifier_' . $set_index . '_' . $option_index;
            $option_name = $option['name'] ?? '';
            $option_price = $option['price'] ?? 0;
            
            if ( empty( $option_name ) ) {
                continue;
            }
            
            echo "<label class='squarekit-modifier-option' style='display: block; margin: 5px 0;'>\n";
            echo "<input type='" . esc_attr( $input_type ) . "' name='" . esc_attr( $name_attr ) . "' value='" . esc_attr( $option_index ) . "' data-price='" . esc_attr( $option_price ) . "' data-set='" . esc_attr( $set_index ) . "' />\n";
            echo "<span class='squarekit-modifier-label'>" . esc_html( $option_name ) . "</span>\n";
            
            if ( ! empty( $option_price ) ) {
                if ( function_exists( 'wc_price' ) ) {
                    echo "<span class='squarekit-modifier-price'> (+" . wc_price( $option_price ) . ")</span>\n";
                } else {
                    echo "<span class='squarekit-modifier-price'> (+$" . number_format( $option_price, 2 ) . ")</span>\n";
                }
            }
            
            echo "</label>\n";
        }
        
        echo "</div>\n";
    }
} else {
    echo "<span class='warning'>No modifiers to display</span><br>\n";
}

echo "</div></div>\n";

// Check for potential issues
echo "<div class='debug-section'>";
echo "<div class='debug-title'>Potential Issues Analysis</div>";
echo "<div class='debug-data'>";

$issues = array();

if ( empty( $modifiers ) ) {
    $issues[] = "No modifier data found for this product";
}

if ( ! empty( $modifiers ) ) {
    foreach ( $modifiers as $set_index => $set ) {
        if ( empty( $set['options'] ) ) {
            $issues[] = "Modifier set $set_index has no options";
            continue;
        }
        
        foreach ( $set['options'] as $option_index => $option ) {
            if ( empty( $option['name'] ) ) {
                $issues[] = "Option $option_index in set $set_index has no name";
            }
            
            if ( ! isset( $option['price'] ) ) {
                $issues[] = "Option $option_index in set $set_index has no price field";
            } elseif ( ! is_numeric( $option['price'] ) ) {
                $issues[] = "Option $option_index in set $set_index has non-numeric price: " . $option['price'];
            }
        }
    }
}

if ( empty( $issues ) ) {
    echo "<span class='success'>No obvious data structure issues found</span><br>\n";
} else {
    echo "<span class='error'>Issues found:</span><br>\n";
    foreach ( $issues as $issue ) {
        echo "• " . esc_html( $issue ) . "<br>\n";
    }
}

echo "</div></div>\n";

echo "<p><strong>Next Steps:</strong> Check the JavaScript console for price calculation errors and verify the base product price calculation logic.</p>\n";

?>
