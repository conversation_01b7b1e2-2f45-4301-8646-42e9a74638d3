<?php
/**
 * Test New Import Architecture
 * 
 * This file tests the new dedicated Product Importer class
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;
    
    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }
    
    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Test New Import Architecture</h2>\n";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.debug-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.debug-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.issue { background: #ffe6e6; border-left: 3px solid #d63638; }
.success { background: #e6ffe6; border-left: 3px solid #00a32a; }
.warning { background: #fff3cd; border-left: 3px solid #ffc107; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>\n";

$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 175;
$use_new_importer = isset( $_GET['new_importer'] ) && $_GET['new_importer'] === '1';

echo "<div class='debug-section'>";
echo "<div class='debug-title'>Testing Import Architecture for Product ID: $product_id</div>";
echo "<div class='debug-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span style='color: red;'>Product not found with ID: $product_id</span>\n";
    exit;
}

echo "Product: " . $product->get_name() . "<br>\n";
echo "Current Type: " . $product->get_type() . "<br>\n";

$square_item_id = get_post_meta( $product_id, '_square_id', true );
if ( ! $square_item_id ) {
    echo "<span style='color: red;'>No Square ID found for this product</span>\n";
    exit;
}

echo "Square Item ID: " . $square_item_id . "<br>\n";

echo "</div></div>\n";

if ( $use_new_importer ) {
    echo "<div class='debug-section'>";
    echo "<div class='debug-title'>🚀 Using New Product Importer Architecture</div>";
    echo "<div class='debug-data'>";
    
    try {
        // Load the new Product Importer
        if ( ! class_exists( 'SquareKit_Product_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
        }
        
        echo "✅ Product Importer class loaded<br>\n";
        
        $product_importer = new SquareKit_Product_Importer();
        echo "✅ Product Importer instance created<br>\n";
        
        // Import the product
        echo "<h4>Starting Import Process...</h4>\n";
        $start_time = microtime( true );
        
        $import_result = $product_importer->import_product( $square_item_id, $product_id );
        
        $end_time = microtime( true );
        $execution_time = round( ( $end_time - $start_time ) * 1000, 2 );
        
        if ( is_wp_error( $import_result ) ) {
            echo "<div class='issue'>❌ Import failed: " . $import_result->get_error_message() . "</div>\n";
        } else {
            echo "<div class='success'>✅ Import completed successfully in {$execution_time}ms!</div>\n";
            
            echo "<h4>Import Statistics:</h4>\n";
            echo "<table>\n";
            echo "<tr><th>Metric</th><th>Value</th></tr>\n";
            echo "<tr><td>Success</td><td>" . ( $import_result['success'] ? 'Yes' : 'No' ) . "</td></tr>\n";
            echo "<tr><td>Product ID</td><td>" . ( $import_result['product_id'] ?? 'N/A' ) . "</td></tr>\n";
            echo "<tr><td>Variations Imported</td><td>" . ( $import_result['variations_imported'] ?? 0 ) . "</td></tr>\n";
            echo "<tr><td>Attributes Imported</td><td>" . ( $import_result['attributes_imported'] ?? 0 ) . "</td></tr>\n";
            echo "<tr><td>Modifiers Imported</td><td>" . ( $import_result['modifiers_imported'] ?? 0 ) . "</td></tr>\n";
            echo "<tr><td>Images Imported</td><td>" . ( $import_result['images_imported'] ?? 0 ) . "</td></tr>\n";
            echo "<tr><td>Execution Time</td><td>{$execution_time}ms</td></tr>\n";
            echo "</table>\n";
            
            if ( ! empty( $import_result['errors'] ) ) {
                echo "<h4>Import Errors:</h4>\n";
                echo "<ul>\n";
                foreach ( $import_result['errors'] as $error ) {
                    echo "<li class='issue'>" . esc_html( $error ) . "</li>\n";
                }
                echo "</ul>\n";
            }
        }
        
    } catch ( Exception $e ) {
        echo "<div class='issue'>❌ Exception during import: " . $e->getMessage() . "</div>\n";
        echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    }
    
    echo "</div></div>\n";
    
    // Refresh product data
    wp_cache_delete( $product_id, 'posts' );
    $product = wc_get_product( $product_id );
}

// Analyze current product state
echo "<div class='debug-section'>";
echo "<div class='debug-title'>📊 Current Product State Analysis</div>";
echo "<div class='debug-data'>";

echo "<h4>Basic Information:</h4>\n";
echo "<table>\n";
echo "<tr><th>Property</th><th>Value</th></tr>\n";
echo "<tr><td>Name</td><td>" . $product->get_name() . "</td></tr>\n";
echo "<tr><td>Type</td><td>" . $product->get_type() . "</td></tr>\n";
echo "<tr><td>Status</td><td>" . $product->get_status() . "</td></tr>\n";
echo "<tr><td>SKU</td><td>" . ( $product->get_sku() ?: 'No SKU' ) . "</td></tr>\n";
echo "<tr><td>Price</td><td>$" . number_format( $product->get_price(), 2 ) . "</td></tr>\n";
echo "</table>\n";

// Check Square tracking meta
echo "<h4>Square Tracking Meta:</h4>\n";
$square_id = get_post_meta( $product_id, '_square_id', true );
$square_variation_id = get_post_meta( $product_id, '_square_variation_id', true );
$square_version = get_post_meta( $product_id, '_square_version', true );
$square_last_sync = get_post_meta( $product_id, '_square_last_sync', true );

echo "<table>\n";
echo "<tr><th>Meta Field</th><th>Value</th><th>Status</th></tr>\n";
echo "<tr><td>_square_id</td><td>" . ( $square_id ?: 'Not set' ) . "</td><td>" . ( $square_id ? '✅' : '❌' ) . "</td></tr>\n";
echo "<tr><td>_square_variation_id</td><td>" . ( $square_variation_id ?: 'Not set' ) . "</td><td>" . ( $square_variation_id ? '✅' : '⚠️' ) . "</td></tr>\n";
echo "<tr><td>_square_version</td><td>" . ( $square_version ?: 'Not set' ) . "</td><td>" . ( $square_version ? '✅' : '⚠️' ) . "</td></tr>\n";
echo "<tr><td>_square_last_sync</td><td>" . ( $square_last_sync ?: 'Not set' ) . "</td><td>" . ( $square_last_sync ? '✅' : '⚠️' ) . "</td></tr>\n";
echo "</table>\n";

// Check attributes
echo "<h4>WooCommerce Attributes:</h4>\n";
$attributes = $product->get_attributes();
if ( ! empty( $attributes ) ) {
    echo "<div class='success'>✅ Found " . count( $attributes ) . " attributes:</div>\n";
    echo "<table>\n";
    echo "<tr><th>Attribute</th><th>Values</th><th>Used for Variations</th><th>Visible</th></tr>\n";
    foreach ( $attributes as $attribute ) {
        echo "<tr>";
        echo "<td>" . $attribute->get_name() . "</td>";
        echo "<td>" . implode( ', ', $attribute->get_options() ) . "</td>";
        echo "<td>" . ( $attribute->get_variation() ? 'Yes' : 'No' ) . "</td>";
        echo "<td>" . ( $attribute->get_visible() ? 'Yes' : 'No' ) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<div class='issue'>❌ No attributes found</div>\n";
}

// Check variations
if ( $product->is_type( 'variable' ) ) {
    echo "<h4>Product Variations:</h4>\n";
    $variations = $product->get_available_variations();
    
    if ( ! empty( $variations ) ) {
        echo "<div class='success'>✅ Found " . count( $variations ) . " variations:</div>\n";
        echo "<table>\n";
        echo "<tr><th>Variation ID</th><th>Attributes</th><th>Price</th><th>SKU</th><th>Square ID</th></tr>\n";
        
        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            $var_square_id = get_post_meta( $variation->get_id(), '_square_variation_id', true );
            
            echo "<tr>";
            echo "<td>" . $variation->get_id() . "</td>";
            echo "<td>";
            
            $variation_attributes = $variation->get_variation_attributes();
            if ( ! empty( $variation_attributes ) ) {
                foreach ( $variation_attributes as $attr_name => $attr_value ) {
                    echo str_replace( 'attribute_', '', $attr_name ) . ": " . $attr_value . "<br>";
                }
            } else {
                echo "<span style='color: red;'>No attributes</span>";
            }
            
            echo "</td>";
            echo "<td>$" . number_format( $variation->get_price(), 2 ) . "</td>";
            echo "<td>" . ( $variation->get_sku() ?: 'No SKU' ) . "</td>";
            echo "<td>" . ( $var_square_id ? $var_square_id : '<span style="color: red;">Missing</span>' ) . "</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    } else {
        echo "<div class='issue'>❌ Variable product but no variations found</div>\n";
    }
}

echo "</div></div>\n";

// Action buttons
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🎯 Actions</div>";
echo "<div class='debug-data'>";

if ( ! $use_new_importer ) {
    echo "<p><a href='?product_id={$product_id}&new_importer=1' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🚀 Test New Import Architecture</a></p>\n";
}

echo "<p><a href='?product_id={$product_id}' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🔍 Refresh Analysis</a></p>\n";

echo "<p><a href='/wp-admin/post.php?post={$product_id}&action=edit' style='background: #00a32a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>✏️ Edit Product in WooCommerce</a></p>\n";

echo "</div></div>\n";

?>
