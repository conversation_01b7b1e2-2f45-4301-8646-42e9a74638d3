<?php
/**
 * Debug Square Variations Structure
 * 
 * This file examines the actual structure of Square variations to understand
 * how to properly map them to WooCommerce attributes.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

echo "<h1>Debug Square Variations Structure</h1>\n";
echo "<pre>\n";

// Load required classes
if ( ! class_exists( 'SquareKit_Settings' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
}

if ( ! class_exists( 'SquareKit_Square_API' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/api/class-squarekit-square-api.php' );
}

// Check if Square is connected
$settings = new SquareKit_Settings();
$is_connected = $settings->is_connected();

if ( ! $is_connected ) {
    echo "❌ Square is not connected. Please configure Square API credentials first.\n";
    exit;
}

echo "✅ Square API is connected\n\n";

try {
    $square_api = new SquareKit_Square_API();
    
    // Get the Ceremonial Macha product specifically
    echo "=== Fetching Ceremonial Macha Product ===\n";
    $catalog = $square_api->get_catalog( array( 'types' => 'ITEM', 'limit' => 10 ) );
    
    if ( is_wp_error( $catalog ) ) {
        echo "❌ Failed to fetch catalog: " . $catalog->get_error_message() . "\n";
        exit;
    }
    
    $ceremonial_macha = null;
    foreach ( $catalog as $item ) {
        if ( $item['type'] === 'ITEM' && isset( $item['item_data']['name'] ) && $item['item_data']['name'] === 'Ceremonial Macha' ) {
            $ceremonial_macha = $item;
            break;
        }
    }
    
    if ( ! $ceremonial_macha ) {
        echo "❌ Ceremonial Macha product not found\n";
        exit;
    }
    
    echo "✅ Found Ceremonial Macha product\n";
    echo "Square ID: " . $ceremonial_macha['id'] . "\n\n";
    
    // Examine the complete structure
    echo "=== Complete Product Structure ===\n";
    echo "Raw JSON structure:\n";
    echo json_encode( $ceremonial_macha, JSON_PRETTY_PRINT ) . "\n\n";
    
    // Focus on variations
    $variations = $ceremonial_macha['item_data']['variations'] ?? array();
    echo "=== Variations Analysis ===\n";
    echo "Number of variations: " . count( $variations ) . "\n\n";
    
    foreach ( $variations as $i => $variation ) {
        echo "--- Variation " . ($i + 1) . " ---\n";
        echo "ID: " . $variation['id'] . "\n";
        echo "Type: " . $variation['type'] . "\n";
        
        $variation_data = $variation['item_variation_data'] ?? array();
        echo "Name: " . ( $variation_data['name'] ?? 'N/A' ) . "\n";
        echo "SKU: " . ( $variation_data['sku'] ?? 'N/A' ) . "\n";
        echo "Ordinal: " . ( $variation_data['ordinal'] ?? 'N/A' ) . "\n";
        
        if ( isset( $variation_data['price_money'] ) ) {
            $price = $variation_data['price_money'];
            echo "Price: " . ( $price['amount'] / 100 ) . " " . $price['currency'] . "\n";
        }
        
        // Check for item option values
        if ( isset( $variation_data['item_option_values'] ) ) {
            echo "Item Option Values: " . count( $variation_data['item_option_values'] ) . "\n";
            foreach ( $variation_data['item_option_values'] as $j => $option_value ) {
                echo "  Option Value " . ($j + 1) . ":\n";
                echo "    item_option_id: " . ( $option_value['item_option_id'] ?? 'N/A' ) . "\n";
                echo "    item_option_value_id: " . ( $option_value['item_option_value_id'] ?? 'N/A' ) . "\n";
                echo "    value: " . ( $option_value['value'] ?? 'N/A' ) . "\n";
            }
        } else {
            echo "Item Option Values: None\n";
        }
        
        echo "Raw variation data:\n";
        echo json_encode( $variation, JSON_PRETTY_PRINT ) . "\n\n";
    }
    
    // Check if there are any item options in the catalog
    echo "=== Item Options in Catalog ===\n";
    $all_catalog = $square_api->get_catalog( array( 'types' => 'ITEM_OPTION', 'limit' => 50 ) );
    
    if ( is_wp_error( $all_catalog ) ) {
        echo "❌ Failed to fetch item options: " . $all_catalog->get_error_message() . "\n";
    } else {
        $item_options = array_filter( $all_catalog, function( $obj ) {
            return $obj['type'] === 'ITEM_OPTION';
        });
        
        echo "Total item options in catalog: " . count( $item_options ) . "\n";
        
        if ( ! empty( $item_options ) ) {
            foreach ( $item_options as $option ) {
                echo "Option: " . ( $option['item_option_data']['name'] ?? 'Unknown' ) . " (ID: " . $option['id'] . ")\n";
                $values = $option['item_option_data']['values'] ?? array();
                echo "  Values: " . count( $values ) . "\n";
                foreach ( $values as $value ) {
                    echo "    - " . ( $value['item_option_value_data']['name'] ?? $value['name'] ?? 'Unknown' ) . " (ID: " . $value['id'] . ")\n";
                }
            }
        }
    }
    
    // Check what WooCommerce currently has for this product
    echo "\n=== Current WooCommerce Product State ===\n";
    global $wpdb;
    $wc_product_id = $wpdb->get_var( $wpdb->prepare(
        "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = '_square_id' AND meta_value = %s",
        $ceremonial_macha['id']
    ) );
    
    if ( $wc_product_id ) {
        echo "WooCommerce Product ID: {$wc_product_id}\n";
        
        $product = wc_get_product( $wc_product_id );
        if ( $product ) {
            echo "Product Type: " . $product->get_type() . "\n";
            echo "Product Status: " . $product->get_status() . "\n";
            
            $attributes = $product->get_attributes();
            echo "Attributes: " . count( $attributes ) . "\n";
            foreach ( $attributes as $attribute_name => $attribute ) {
                echo "  - {$attribute_name}: " . implode( ', ', $attribute->get_options() ) . "\n";
                echo "    Visible: " . ( $attribute->get_visible() ? 'Yes' : 'No' ) . "\n";
                echo "    Variation: " . ( $attribute->get_variation() ? 'Yes' : 'No' ) . "\n";
            }
            
            if ( $product->is_type( 'variable' ) ) {
                $variations = $product->get_children();
                echo "Variations: " . count( $variations ) . "\n";
                foreach ( $variations as $variation_id ) {
                    $variation = wc_get_product( $variation_id );
                    if ( $variation ) {
                        echo "  - " . $variation->get_name() . " (Price: " . $variation->get_price() . ")\n";
                        $variation_attributes = $variation->get_attributes();
                        echo "    Attributes: " . json_encode( $variation_attributes ) . "\n";
                    }
                }
            }
        }
    } else {
        echo "Product not found in WooCommerce\n";
    }
    
} catch ( Exception $e ) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>
