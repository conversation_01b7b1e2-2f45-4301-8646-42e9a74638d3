# Comprehensive Implementation Plan: Square Attributes/Variations Import Fix

## Executive Summary
This plan addresses the critical issues in Square-to-WooCommerce attribute and variation import by implementing a complete rewrite of the import system with proper data structure handling, batch processing, and correct mapping between Square's hierarchical attribute system and WooCommerce's taxonomy-based system.

## Phase 1: Foundation Enhancement (API & Data Structures)

### Task 1: Square API Enhancement
**Objective**: Enhance Square API class to properly fetch and structure related catalog objects

**Implementation Steps**:
1. **Add Batch Catalog Fetching**
   - Implement `get_catalog_with_relations()` method
   - Fetch ITEM, ITEM_OPTION_SET, ITEM_OPTION, MODIFIER_LIST types in single call
   - Cache related objects for efficient lookup

2. **Enhanced Item Retrieval**
   - Modify `get_catalog_item()` to include related objects
   - Add `get_catalog_item_with_relations()` method
   - Implement proper object relationship mapping

3. **Option Set Resolution**
   - Add `resolve_option_set_by_option_id()` method
   - Implement `get_complete_option_hierarchy()` method
   - Cache option-to-option-set mappings

**Files to Modify**:
- `includes/api/class-squarekit-square-api.php`

**Expected Outcome**: Efficient batch fetching of all related Square objects with proper relationship mapping.

### Task 2: Attribute Mapping System Redesign
**Objective**: Create proper mapping between Square option sets and WooCommerce attributes

**Implementation Steps**:
1. **Create Mapping Storage System**
   - Add database table for Square-to-WooCommerce mappings
   - Implement `SquareKit_Attribute_Mapper` class
   - Store option set ID → attribute ID mappings
   - Store option ID → term ID mappings

2. **Dynamic Attribute Creation**
   - Implement `create_wc_attribute_from_square_option_set()` method
   - Add automatic term creation from Square options
   - Handle attribute slug generation and conflicts

3. **Mapping Persistence**
   - Store mappings in dedicated database table
   - Implement mapping cache for performance
   - Add mapping validation and cleanup

**Files to Create**:
- `includes/class-squarekit-attribute-mapper.php`
- `includes/database/class-squarekit-attribute-mappings-table.php`

**Expected Outcome**: Robust mapping system that correctly links Square option sets to WooCommerce attributes.

## Phase 2: Core Import Logic Rewrite

### Task 3: Complete Variation Import Rewrite
**Objective**: Rewrite variation import logic with proper Square data structure handling

**Implementation Steps**:
1. **Remove Conflicting Code**
   - Remove old variation import code (lines 687-797)
   - Clean up conflicting methods
   - Consolidate to single import path

2. **Implement New Import Flow**
   ```
   1. Fetch Square item with all related objects
   2. Resolve option sets and create WooCommerce attributes
   3. Create attribute terms from Square options
   4. Create product with proper attributes
   5. Create variations with correct attribute assignments
   6. Set pricing, SKU, and inventory data
   ```

3. **Proper Attribute Resolution**
   - Map `item_option_values` to correct WooCommerce attributes
   - Resolve option IDs to option sets and terms
   - Handle missing or invalid option data gracefully

**Files to Modify**:
- `includes/integrations/class-squarekit-woocommerce.php`

**Methods to Implement**:
- `import_product_with_enhanced_variations()`
- `resolve_square_option_to_wc_attribute()`
- `create_variation_with_proper_attributes()`

**Expected Outcome**: Clean, efficient variation import that correctly handles Square's hierarchical structure.

### Task 4: Enhanced Modifier Import
**Objective**: Improve modifier import to handle all Square modifier structures

**Implementation Steps**:
1. **Proper Modifier List Processing**
   - Fetch complete modifier list data
   - Handle modifier pricing and SKU data
   - Support single/multiple selection types

2. **WooCommerce Integration**
   - Store modifiers in WooCommerce-compatible format
   - Create proper frontend modifier display
   - Handle modifier pricing in cart/checkout

3. **Performance Optimization**
   - Batch process modifier lists
   - Cache modifier data
   - Minimize API calls

**Expected Outcome**: Complete modifier import with proper pricing and frontend integration.

## Phase 3: Database & Performance Optimization

### Task 5: Database Schema Enhancement
**Objective**: Optimize database storage and retrieval of Square mapping data

**Implementation Steps**:
1. **Create Mapping Tables**
   ```sql
   CREATE TABLE squarekit_attribute_mappings (
       id INT AUTO_INCREMENT PRIMARY KEY,
       square_option_set_id VARCHAR(255) NOT NULL,
       wc_attribute_id INT NOT NULL,
       square_option_id VARCHAR(255),
       wc_term_id INT,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       INDEX(square_option_set_id),
       INDEX(wc_attribute_id)
   );
   ```

2. **Implement Caching Layer**
   - Add Redis/Memcached support for mapping cache
   - Implement fallback to database cache
   - Cache Square API responses

3. **Batch Operations**
   - Implement batch attribute creation
   - Batch term creation and assignment
   - Batch variation creation

**Expected Outcome**: Optimized database operations with significant performance improvements.

## Phase 4: Error Handling & Validation

### Task 6: Comprehensive Error Handling
**Objective**: Add robust error handling and validation throughout the import process

**Implementation Steps**:
1. **Data Validation**
   - Validate Square API responses
   - Check for required option sets and options
   - Validate WooCommerce attribute creation

2. **Error Recovery**
   - Implement rollback mechanisms
   - Handle partial import failures
   - Provide detailed error reporting

3. **Logging Enhancement**
   - Add detailed import logging
   - Track mapping creation and updates
   - Log performance metrics

**Expected Outcome**: Robust import system with comprehensive error handling and recovery.

## Phase 5: Testing & Validation

### Task 7: Comprehensive Testing Framework
**Objective**: Create thorough testing for various Square product configurations

**Implementation Steps**:
1. **Unit Tests**
   - Test attribute mapping logic
   - Test variation creation
   - Test modifier import

2. **Integration Tests**
   - Test complete import flow
   - Test with various Square product types
   - Test error scenarios

3. **Performance Tests**
   - Test with large product catalogs
   - Measure import speed improvements
   - Test memory usage

**Test Scenarios**:
- Simple products with no variations
- Variable products with single attribute
- Variable products with multiple attributes
- Products with modifiers
- Products with complex option sets
- Error scenarios (missing data, API failures)

## Implementation Timeline

### Week 1: Foundation
- [ ] Square API Enhancement (Task 1)
- [ ] Attribute Mapping System (Task 2)

### Week 2: Core Logic
- [ ] Variation Import Rewrite (Task 3)
- [ ] Modifier Import Enhancement (Task 4)

### Week 3: Optimization
- [ ] Database Schema Enhancement (Task 5)
- [ ] Error Handling Implementation (Task 6)

### Week 4: Testing & Validation
- [ ] Testing Framework (Task 7)
- [ ] Performance optimization
- [ ] Documentation and deployment

## Success Metrics

1. **Functionality**
   - 100% of Square variable products import with correct attributes
   - All variation pricing, SKUs, and inventory data preserved
   - Modifiers import with correct pricing and structure

2. **Performance**
   - 80% reduction in API calls during import
   - 60% faster import times for large catalogs
   - Reduced memory usage during batch imports

3. **Reliability**
   - Zero data corruption during imports
   - Proper error handling for all failure scenarios
   - Complete rollback capability for failed imports

## Risk Mitigation

1. **Backup Strategy**: Full database backup before any import operations
2. **Gradual Rollout**: Test with small product sets before full catalog import
3. **Monitoring**: Real-time monitoring of import operations and performance
4. **Rollback Plan**: Ability to revert to previous import system if needed

This comprehensive plan addresses all identified issues while providing a robust, scalable solution for Square-to-WooCommerce attribute and variation import.
