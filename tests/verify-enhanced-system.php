<?php
/**
 * Quick Verification of Enhanced Square Import System
 * 
 * This file provides a quick check to verify the enhanced import system is working.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

echo "<h1>Enhanced Square Import System Verification</h1>\n";
echo "<pre>\n";

// Check if WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    echo "❌ WooCommerce is not active. Please activate WooCommerce first.\n";
    exit;
}

echo "✅ WooCommerce is active\n";

// Check if SquareKit classes exist
$classes_to_check = array(
    'SquareKit_Square_API',
    'SquareKit_Attribute_Mapper', 
    'SquareKit_Attribute_Mappings_Table',
    'SquareKit_WooCommerce'
);

echo "\n=== Class Availability Check ===\n";
$all_classes_available = true;

foreach ( $classes_to_check as $class_name ) {
    if ( class_exists( $class_name ) ) {
        echo "✅ {$class_name} is available\n";
    } else {
        echo "❌ {$class_name} is NOT available\n";
        $all_classes_available = false;
    }
}

if ( ! $all_classes_available ) {
    echo "\n❌ Some required classes are missing. Please check the implementation.\n";
    exit;
}

echo "\n=== Database Table Check ===\n";
try {
    $mappings_table = new SquareKit_Attribute_Mappings_Table();
    
    if ( $mappings_table->table_exists() ) {
        echo "✅ Attribute mappings table exists\n";
        
        $stats = $mappings_table->get_mapping_stats();
        echo "📊 Current mappings: {$stats['total_mappings']}\n";
    } else {
        echo "⚠️  Attribute mappings table does not exist. Creating...\n";
        $result = $mappings_table->create_table();
        if ( $result ) {
            echo "✅ Attribute mappings table created successfully\n";
        } else {
            echo "❌ Failed to create attribute mappings table\n";
        }
    }
} catch ( Exception $e ) {
    echo "❌ Database table check failed: " . $e->getMessage() . "\n";
}

echo "\n=== Square API Connection Check ===\n";
try {
    if ( ! class_exists( 'SquareKit_Settings' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
    }
    
    $settings = new SquareKit_Settings();
    $is_connected = $settings->is_connected();
    
    if ( $is_connected ) {
        echo "✅ Square API is connected\n";
        
        // Test enhanced API methods
        $square_api = new SquareKit_Square_API();
        
        // Check if enhanced methods exist
        $enhanced_methods = array(
            'get_catalog_item_with_relations',
            'get_catalog_with_relations',
            'resolve_option_set_by_option_id',
            'get_complete_option_hierarchy',
            'batch_resolve_option_names'
        );
        
        echo "\n=== Enhanced API Methods Check ===\n";
        foreach ( $enhanced_methods as $method ) {
            if ( method_exists( $square_api, $method ) ) {
                echo "✅ {$method}() is available\n";
            } else {
                echo "❌ {$method}() is NOT available\n";
            }
        }
        
    } else {
        echo "⚠️  Square API is not connected. Please configure Square credentials to test API functionality.\n";
    }
} catch ( Exception $e ) {
    echo "❌ Square API check failed: " . $e->getMessage() . "\n";
}

echo "\n=== Enhanced Import Methods Check ===\n";
try {
    $wc_integration = new SquareKit_WooCommerce();
    
    $enhanced_import_methods = array(
        'import_product_with_enhanced_variations',
        'import_enhanced_variations',
        'create_enhanced_variation',
        'import_enhanced_modifiers'
    );
    
    foreach ( $enhanced_import_methods as $method ) {
        if ( method_exists( $wc_integration, $method ) ) {
            echo "✅ {$method}() is available\n";
        } else {
            echo "❌ {$method}() is NOT available\n";
        }
    }
} catch ( Exception $e ) {
    echo "❌ Enhanced import methods check failed: " . $e->getMessage() . "\n";
}

echo "\n=== Attribute Mapper Check ===\n";
try {
    $attribute_mapper = new SquareKit_Attribute_Mapper();
    
    $mapper_methods = array(
        'map_option_set_to_attribute',
        'map_option_to_term'
    );
    
    foreach ( $mapper_methods as $method ) {
        if ( method_exists( $attribute_mapper, $method ) ) {
            echo "✅ {$method}() is available\n";
        } else {
            echo "❌ {$method}() is NOT available\n";
        }
    }
} catch ( Exception $e ) {
    echo "❌ Attribute mapper check failed: " . $e->getMessage() . "\n";
}

echo "\n=== System Status Summary ===\n";

$system_ready = true;
$issues = array();

// Check all critical components
if ( ! class_exists( 'WooCommerce' ) ) {
    $system_ready = false;
    $issues[] = "WooCommerce not active";
}

foreach ( $classes_to_check as $class_name ) {
    if ( ! class_exists( $class_name ) ) {
        $system_ready = false;
        $issues[] = "{$class_name} class missing";
    }
}

try {
    $mappings_table = new SquareKit_Attribute_Mappings_Table();
    if ( ! $mappings_table->table_exists() ) {
        $issues[] = "Attribute mappings table missing (can be auto-created)";
    }
} catch ( Exception $e ) {
    $system_ready = false;
    $issues[] = "Database table check failed";
}

if ( $system_ready && empty( $issues ) ) {
    echo "🎉 Enhanced Square Import System is READY!\n";
    echo "✅ All components are properly installed and configured.\n";
    echo "✅ You can now import Square products with enhanced attribute/variation handling.\n";
} else {
    echo "⚠️  Enhanced Square Import System has some issues:\n";
    foreach ( $issues as $issue ) {
        echo "  - {$issue}\n";
    }
    
    if ( $system_ready ) {
        echo "\n✅ Core system is functional, but some optional features may not work.\n";
    } else {
        echo "\n❌ Core system is not functional. Please address the issues above.\n";
    }
}

echo "\n=== Next Steps ===\n";
echo "1. If system is ready, test with: test-enhanced-import.php\n";
echo "2. Configure Square API credentials if not already done\n";
echo "3. Test import with a few Square products first\n";
echo "4. Monitor import logs for any issues\n";

echo "</pre>\n";
?>
