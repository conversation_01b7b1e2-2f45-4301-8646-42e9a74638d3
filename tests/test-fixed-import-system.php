<?php
/**
 * Test Fixed Square Import System
 * 
 * Comprehensive testing tool to verify the fixed Square import system
 * is working correctly with proper attributes, variations, and modifiers.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

echo "<h1>Fixed Square Import System Test</h1>\n";
echo "<pre>\n";

// Check if WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    echo "❌ WooCommerce is not active. Please activate WooCommerce first.\n";
    exit;
}

echo "✅ WooCommerce is active\n";

// Check if SquareKit classes exist
$required_classes = array(
    'SquareKit_Square_API',
    'SquareKit_WooCommerce',
    'SquareKit_Attribute_Importer',
    'SquareKit_Variation_Importer',
    'SquareKit_Modifier_Importer',
    'SquareKit_Price_Calculator'
);

echo "\n=== Class Availability Check ===\n";
$all_classes_available = true;

foreach ( $required_classes as $class_name ) {
    if ( class_exists( $class_name ) ) {
        echo "✅ {$class_name} is available\n";
    } else {
        echo "❌ {$class_name} is NOT available\n";
        $all_classes_available = false;
    }
}

if ( ! $all_classes_available ) {
    echo "\n❌ Some required classes are missing. Please check the implementation.\n";
    exit;
}

// Check Square API connection
echo "\n=== Square API Connection Check ===\n";
try {
    if ( ! class_exists( 'SquareKit_Settings' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
    }
    
    $settings = new SquareKit_Settings();
    $is_connected = $settings->is_connected();
    
    if ( ! $is_connected ) {
        echo "⚠️  Square API is not connected. Please configure Square credentials first.\n";
        exit;
    }
    
    echo "✅ Square API is connected\n";
    
} catch ( Exception $e ) {
    echo "❌ Square API check failed: " . $e->getMessage() . "\n";
    exit;
}

// Initialize Square API and WooCommerce integration
$square_api = new SquareKit_Square_API();
$wc_integration = new SquareKit_WooCommerce();

// Get Square catalog items for testing
echo "\n=== Fetching Square Catalog Items ===\n";
try {
    $catalog = $square_api->get_catalog( array( 'types' => 'ITEM' ) );
    $items = $catalog['objects'] ?? array();
    
    if ( empty( $items ) ) {
        echo "⚠️  No items found in Square catalog.\n";
        echo "\n=== Alternative Testing Options ===\n";
        echo "Since no Square catalog items were found, here are your options:\n\n";

        echo "1. **Add Test Products to Square:**\n";
        echo "   - Log into your Square Dashboard\n";
        echo "   - Go to Items & Orders → Items\n";
        echo "   - Create a few test products with:\n";
        echo "     * Basic product (simple)\n";
        echo "     * Variable product with options (size, color, etc.)\n";
        echo "     * Product with modifiers (add-ons)\n\n";

        echo "2. **Test with Mock Data:**\n";
        echo "   - We can test the import system with simulated Square data\n";
        echo "   - This will verify all the new import handlers work correctly\n\n";

        echo "3. **Check Square Environment:**\n";
        echo "   - Verify you're connected to the right Square environment\n";
        echo "   - Sandbox vs Production may have different catalogs\n\n";

        // Test with mock data instead
        echo "=== Testing with Mock Square Data ===\n";
        echo "Creating simulated Square product data for testing...\n";

        $mock_item = array(
            'id' => 'TEST_ITEM_' . time(),
            'type' => 'ITEM',
            'item_data' => array(
                'name' => 'Test Coffee Product',
                'description' => 'A test coffee product for import testing',
                'category_id' => 'TEST_CATEGORY_' . time(),
                'variations' => array(
                    array(
                        'id' => 'TEST_VAR_1_' . time(),
                        'type' => 'ITEM_VARIATION',
                        'item_variation_data' => array(
                            'name' => 'Small',
                            'sku' => 'TEST-COFFEE-SM',
                            'price_money' => array(
                                'amount' => 500,
                                'currency' => 'USD'
                            ),
                            'item_option_values' => array(
                                array(
                                    'item_option_id' => 'TEST_OPTION_SIZE',
                                    'item_option_value_id' => 'TEST_VALUE_SMALL'
                                )
                            )
                        )
                    ),
                    array(
                        'id' => 'TEST_VAR_2_' . time(),
                        'type' => 'ITEM_VARIATION',
                        'item_variation_data' => array(
                            'name' => 'Large',
                            'sku' => 'TEST-COFFEE-LG',
                            'price_money' => array(
                                'amount' => 750,
                                'currency' => 'USD'
                            ),
                            'item_option_values' => array(
                                array(
                                    'item_option_id' => 'TEST_OPTION_SIZE',
                                    'item_option_value_id' => 'TEST_VALUE_LARGE'
                                )
                            )
                        )
                    )
                ),
                'modifier_list_info' => array(
                    array(
                        'modifier_list_id' => 'TEST_MODIFIER_LIST_' . time(),
                        'enabled' => true
                    )
                )
            )
        );

        echo "✅ Mock Square product data created\n";
        echo "  Product: " . $mock_item['item_data']['name'] . "\n";
        echo "  Variations: " . count( $mock_item['item_data']['variations'] ) . "\n";
        echo "  Has modifiers: " . ( ! empty( $mock_item['item_data']['modifier_list_info'] ) ? 'Yes' : 'No' ) . "\n";

        $test_item = $mock_item;
        $using_mock_data = true;
    } else {
        $using_mock_data = false;

        echo "✅ Found " . count( $items ) . " items in Square catalog\n";
    
    // Find a variable product for testing
    $test_item = null;
    foreach ( $items as $item ) {
        if ( $item['type'] === 'ITEM' && ! empty( $item['item_data']['variations'] ) ) {
            $variations = $item['item_data']['variations'];
            if ( count( $variations ) > 1 ) {
                // Check if it has item options (new system)
                foreach ( $variations as $variation ) {
                    if ( ! empty( $variation['item_variation_data']['item_option_values'] ) ) {
                        $test_item = $item;
                        break 2;
                    }
                }
            }
        }
    }
    
    if ( ! $test_item ) {
        echo "⚠️  No suitable variable product with item options found for testing.\n";
        echo "Looking for any variable product...\n";
        
        foreach ( $items as $item ) {
            if ( $item['type'] === 'ITEM' && ! empty( $item['item_data']['variations'] ) ) {
                $variations = $item['item_data']['variations'];
                if ( count( $variations ) > 1 ) {
                    $test_item = $item;
                    break;
                }
            }
        }
    }
    
    if ( ! $test_item ) {
        echo "⚠️  No variable products found for testing. Testing with simple product...\n";
        $test_item = $items[0];
    }
    
        echo "✅ Selected test item: " . ( $test_item['item_data']['name'] ?? 'Unknown' ) . "\n";
        echo "  Square ID: " . $test_item['id'] . "\n";
        echo "  Variations: " . count( $test_item['item_data']['variations'] ?? array() ) . "\n";
    }

} catch ( Exception $e ) {
    echo "❌ Failed to fetch catalog: " . $e->getMessage() . "\n";
    exit;
}

// Test the fixed import system
echo "\n=== Testing Fixed Import System ===\n";

if ( $using_mock_data ) {
    echo "Using mock Square data for testing...\n";
} else {
    echo "Using real Square catalog data for testing...\n";
}

// Check if product already exists
$existing_product_id = $wc_integration->find_wc_product_by_square_id( $test_item['id'] );
if ( $existing_product_id ) {
    echo "⚠️  Product already exists (ID: {$existing_product_id}). Deleting for clean test...\n";
    wp_delete_post( $existing_product_id, true );
}

try {
    // Create mock related objects if using mock data
    $related_objects = array();
    if ( $using_mock_data ) {
        $related_objects = array(
            array(
                'id' => 'TEST_OPTION_SIZE',
                'type' => 'ITEM_OPTION',
                'item_option_data' => array(
                    'name' => 'Size',
                    'display_name' => 'Size'
                )
            ),
            array(
                'id' => 'TEST_VALUE_SMALL',
                'type' => 'ITEM_OPTION_VAL',
                'item_option_value_data' => array(
                    'name' => 'Small',
                    'item_option_id' => 'TEST_OPTION_SIZE'
                )
            ),
            array(
                'id' => 'TEST_VALUE_LARGE',
                'type' => 'ITEM_OPTION_VAL',
                'item_option_value_data' => array(
                    'name' => 'Large',
                    'item_option_id' => 'TEST_OPTION_SIZE'
                )
            )
        );
    }

    // Test the enhanced import method
    $result = $wc_integration->import_product_with_enhanced_variations( $test_item, array() );
    
    if ( is_wp_error( $result ) ) {
        echo "❌ Import failed: " . $result->get_error_message() . "\n";
        exit;
    }
    
    $product_id = $result;
    echo "✅ Import succeeded! Product ID: {$product_id}\n";
    
} catch ( Exception $e ) {
    echo "❌ Import exception: " . $e->getMessage() . "\n";
    exit;
}

// Validate the imported product
echo "\n=== Validating Imported Product ===\n";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "❌ Imported product not found\n";
    exit;
}

echo "✅ Product found: " . $product->get_name() . "\n";
echo "  Type: " . $product->get_type() . "\n";
echo "  Status: " . $product->get_status() . "\n";

// Check attributes for variable products
if ( $product->is_type( 'variable' ) ) {
    echo "\n--- Variable Product Validation ---\n";
    
    $attributes = $product->get_attributes();
    echo "  Attributes: " . count( $attributes ) . "\n";
    
    foreach ( $attributes as $attribute ) {
        echo "    - " . $attribute->get_name() . " (ID: " . $attribute->get_id() . ")\n";
        echo "      Options: " . count( $attribute->get_options() ) . "\n";
        echo "      Used for variations: " . ( $attribute->get_variation() ? 'Yes' : 'No' ) . "\n";
    }
    
    // Check variations
    $variations = $product->get_available_variations();
    echo "  Variations: " . count( $variations ) . "\n";
    
    $variation_prices = array();
    foreach ( $variations as $variation_data ) {
        $variation = wc_get_product( $variation_data['variation_id'] );
        if ( $variation ) {
            $price = $variation->get_price();
            if ( $price !== '' ) {
                $variation_prices[] = floatval( $price );
            }
            echo "    - Variation ID: " . $variation->get_id() . ", Price: " . ( $price !== '' ? wc_price( $price ) : 'No price' ) . "\n";
        }
    }
    
    // Check price range
    if ( ! empty( $variation_prices ) ) {
        $min_price = min( $variation_prices );
        $max_price = max( $variation_prices );
        echo "  Price range: " . wc_price( $min_price ) . " - " . wc_price( $max_price ) . "\n";
        
        // Validate price range calculation
        if ( ! class_exists( 'SquareKit_Price_Calculator' ) ) {
            require_once( dirname( __FILE__ ) . '/includes/utils/class-squarekit-price-calculator.php' );
        }
        
        $price_summary = SquareKit_Price_Calculator::get_product_price_summary( $product_id );
        if ( isset( $price_summary['price_validation'] ) ) {
            $validation = $price_summary['price_validation'];
            if ( $validation['valid'] ) {
                echo "  ✅ Price validation: Passed\n";
            } else {
                echo "  ⚠️  Price validation issues:\n";
                foreach ( $validation['issues'] as $issue ) {
                    echo "    - {$issue}\n";
                }
            }
        }
    }
}

// Check modifiers
echo "\n--- Modifier Validation ---\n";
$modifiers = get_post_meta( $product_id, '_squarekit_modifiers', true );
if ( is_array( $modifiers ) && ! empty( $modifiers ) ) {
    echo "  ✅ Modifiers found: " . count( $modifiers ) . " sets\n";
    
    foreach ( $modifiers as $index => $modifier_set ) {
        $set_name = $modifier_set['name'] ?? $modifier_set['set_name'] ?? 'Unknown';
        $options_count = count( $modifier_set['options'] ?? array() );
        echo "    - Set {$index}: {$set_name} ({$options_count} options)\n";
        
        // Check for proper structure
        if ( isset( $modifier_set['set_name'] ) || isset( $modifier_set['name'] ) ) {
            echo "      ✅ Has proper name structure\n";
        } else {
            echo "      ❌ Missing name structure\n";
        }
        
        if ( isset( $modifier_set['single_choice'] ) || isset( $modifier_set['single'] ) ) {
            echo "      ✅ Has proper choice type structure\n";
        } else {
            echo "      ❌ Missing choice type structure\n";
        }
    }
} else {
    echo "  ⚠️  No modifiers found\n";
}

// Check Square ID mapping
echo "\n--- Square ID Mapping Validation ---\n";
$square_id = get_post_meta( $product_id, '_square_id', true );
if ( $square_id === $test_item['id'] ) {
    echo "  ✅ Square ID mapping correct: {$square_id}\n";
} else {
    echo "  ❌ Square ID mapping incorrect. Expected: " . $test_item['id'] . ", Got: {$square_id}\n";
}

// Test frontend display (check for errors)
echo "\n=== Testing Frontend Display ===\n";
ob_start();
try {
    $wc_integration->display_product_modifiers();
    $output = ob_get_clean();
    echo "  ✅ Frontend modifier display executed without errors\n";
} catch ( Exception $e ) {
    ob_end_clean();
    echo "  ❌ Frontend display error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Summary ===\n";
echo "✅ Fixed import system test completed successfully!\n";
echo "✅ Product imported with ID: {$product_id}\n";
echo "✅ All major components validated\n";

if ( $product->is_type( 'variable' ) ) {
    echo "✅ Variable product with proper attributes and variations\n";
}

if ( is_array( $modifiers ) && ! empty( $modifiers ) ) {
    echo "✅ Modifiers imported with proper structure\n";
}

echo "\n=== Next Steps ===\n";
echo "1. Visit the product in WooCommerce admin to verify the display\n";
echo "2. Check the frontend product page to ensure modifiers display correctly\n";
echo "3. Test adding the product to cart with different variations/modifiers\n";
echo "4. Monitor the logs for any additional issues\n";

echo "</pre>\n";
?>
