<?php
/**
 * SquareKit Import Manager Browser Test
 *
 * Comprehensive browser-based test for the Import Manager functionality.
 * Access via: /wp-content/plugins/squarekit/test-import-manager-browser.php
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Load WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../../../wp-load.php',
    '../../../../../wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not load WordPress. Please access this file through your WordPress installation.');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this test page.');
}

// Set content type
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SquareKit Import Manager Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f1f1f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #23282d;
            border-bottom: 3px solid #0073aa;
            padding-bottom: 10px;
        }
        h2 {
            color: #0073aa;
            margin-top: 30px;
        }
        .test-section {
            background: #f9f9f9;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #0073aa;
            border-radius: 4px;
        }
        .success {
            color: #46b450;
            font-weight: bold;
        }
        .error {
            color: #dc3232;
            font-weight: bold;
        }
        .warning {
            color: #ffb900;
            font-weight: bold;
        }
        .info {
            color: #0073aa;
            font-weight: bold;
        }
        pre {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }
        .status-success { background: #46b450; }
        .status-error { background: #dc3232; }
        .status-warning { background: #ffb900; }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-result.success { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-result.error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .test-result.warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .button {
            background: #0073aa;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .button:hover {
            background: #005a87;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #0073aa;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 SquareKit Import Manager Test Suite</h1>
        <p><strong>Test Environment:</strong> <?php echo home_url(); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
        <p><strong>Test Time:</strong> <?php echo current_time('Y-m-d H:i:s'); ?></p>

        <?php
        // Initialize test results
        $test_results = array(
            'total_tests' => 0,
            'passed_tests' => 0,
            'failed_tests' => 0,
            'warnings' => 0
        );

        /**
         * Test helper function
         */
        function run_test($test_name, $test_function, &$results) {
            $results['total_tests']++;
            echo "<div class='test-section'>";
            echo "<h3>🔬 {$test_name}</h3>";
            
            try {
                $result = $test_function();
                if ($result === true) {
                    echo "<div class='test-result success'>";
                    echo "<span class='status-indicator status-success'></span>";
                    echo "<span class='success'>✅ PASSED</span>";
                    echo "</div>";
                    $results['passed_tests']++;
                } elseif (is_array($result) && isset($result['warning'])) {
                    echo "<div class='test-result warning'>";
                    echo "<span class='status-indicator status-warning'></span>";
                    echo "<span class='warning'>⚠️ WARNING: {$result['warning']}</span>";
                    echo "</div>";
                    $results['warnings']++;
                } else {
                    echo "<div class='test-result error'>";
                    echo "<span class='status-indicator status-error'></span>";
                    echo "<span class='error'>❌ FAILED: {$result}</span>";
                    echo "</div>";
                    $results['failed_tests']++;
                }
            } catch (Exception $e) {
                echo "<div class='test-result error'>";
                echo "<span class='status-indicator status-error'></span>";
                echo "<span class='error'>❌ EXCEPTION: {$e->getMessage()}</span>";
                echo "</div>";
                $results['failed_tests']++;
            }
            
            echo "</div>";
        }

        // Test 1: Class Availability
        run_test("Class Availability Check", function() {
            $required_classes = array(
                'SquareKit_Import_Manager',
                'SquareKit_Import_Bridge',
                'SquareKit_WooCommerce',
                'SquareKit_Settings',
                'SquareKit_Logger'
            );
            
            $missing_classes = array();
            foreach ($required_classes as $class) {
                if (!class_exists($class)) {
                    $missing_classes[] = $class;
                }
            }
            
            if (!empty($missing_classes)) {
                return "Missing classes: " . implode(', ', $missing_classes);
            }
            
            echo "<p class='info'>All required classes are available ✓</p>";
            return true;
        }, $test_results);

        // Test 2: Import Manager Initialization
        run_test("Import Manager Initialization", function() {
            $import_manager = new SquareKit_Import_Manager();
            
            if (!$import_manager) {
                return "Failed to create Import Manager instance";
            }
            
            echo "<p class='info'>Import Manager initialized successfully ✓</p>";
            return true;
        }, $test_results);

        // Test 3: Method Availability Testing
        run_test("Import Methods Availability", function() {
            $import_manager = new SquareKit_Import_Manager();
            $test_results = $import_manager->test_import_methods();
            
            echo "<div class='stats-grid'>";
            echo "<div class='stat-card'>";
            echo "<div class='stat-value'>" . ($test_results['legacy_available'] ? 'YES' : 'NO') . "</div>";
            echo "<div class='stat-label'>Legacy Method Available</div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<div class='stat-value'>" . ($test_results['swever_available'] ? 'YES' : 'NO') . "</div>";
            echo "<div class='stat-label'>SWEVER Method Available</div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<div class='stat-value'>" . ($test_results['bridge_available'] ? 'YES' : 'NO') . "</div>";
            echo "<div class='stat-label'>Bridge Available</div>";
            echo "</div>";
            
            echo "<div class='stat-card'>";
            echo "<div class='stat-value'>" . strtoupper($test_results['recommended_method']) . "</div>";
            echo "<div class='stat-label'>Recommended Method</div>";
            echo "</div>";
            echo "</div>";
            
            if (!$test_results['legacy_available'] && !$test_results['swever_available']) {
                return "No import methods are available";
            }
            
            return true;
        }, $test_results);

        // Test 4: Input Validation
        run_test("Input Validation", function() {
            $import_manager = new SquareKit_Import_Manager();
            
            // Test valid Square ID
            $valid_id = 'ABCD1234567890EFGH';
            $result1 = $import_manager->import_product($valid_id);
            
            // Test invalid Square ID (too short)
            $invalid_id = 'short';
            $result2 = $import_manager->import_product($invalid_id);
            
            // Test valid item data
            $valid_data = array(
                'id' => 'TEST123456789',
                'item_data' => array('name' => 'Test Product')
            );
            $result3 = $import_manager->import_product($valid_data);
            
            // Test invalid item data
            $invalid_data = array('missing_required_fields' => true);
            $result4 = $import_manager->import_product($invalid_data);
            
            echo "<p class='info'>Validation tests completed:</p>";
            echo "<ul>";
            echo "<li>Valid ID: " . (is_wp_error($result1) ? "Validation passed (expected API error)" : "Unexpected success") . "</li>";
            echo "<li>Invalid ID: " . (is_wp_error($result2) ? "Correctly rejected ✓" : "Should have been rejected") . "</li>";
            echo "<li>Valid data: " . (is_wp_error($result3) ? "Validation passed (expected API error)" : "Unexpected success") . "</li>";
            echo "<li>Invalid data: " . (is_wp_error($result4) ? "Correctly rejected ✓" : "Should have been rejected") . "</li>";
            echo "</ul>";
            
            // Check if validation is working correctly
            if (!is_wp_error($result2) || !is_wp_error($result4)) {
                return "Input validation is not working correctly";
            }
            
            return true;
        }, $test_results);

        // Test 5: Preferences Management
        run_test("Preferences Management", function() {
            $import_manager = new SquareKit_Import_Manager();
            
            // Get default preferences
            $default_prefs = $import_manager->get_preferences();
            echo "<p class='info'>Default preferences:</p>";
            echo "<pre>" . json_encode($default_prefs, JSON_PRETTY_PRINT) . "</pre>";
            
            // Update preferences
            $new_prefs = array(
                'preferred_method' => 'swever',
                'fallback_enabled' => false
            );
            $update_result = $import_manager->update_preferences($new_prefs);
            
            if (!$update_result) {
                return "Failed to update preferences";
            }
            
            // Verify preferences were updated
            $updated_prefs = $import_manager->get_preferences();
            if ($updated_prefs['preferred_method'] !== 'swever') {
                return "Preferences were not updated correctly";
            }
            
            echo "<p class='info'>Updated preferences:</p>";
            echo "<pre>" . json_encode($updated_prefs, JSON_PRETTY_PRINT) . "</pre>";
            
            return true;
        }, $test_results);

        // Test 6: Statistics and Monitoring
        run_test("Statistics and Monitoring", function() {
            $import_manager = new SquareKit_Import_Manager();
            $stats = $import_manager->get_import_statistics();
            
            echo "<p class='info'>Import statistics:</p>";
            echo "<pre>" . json_encode($stats, JSON_PRETTY_PRINT) . "</pre>";
            
            if (!is_array($stats) || !isset($stats['swever_available'])) {
                return "Statistics format is incorrect";
            }
            
            return true;
        }, $test_results);

        // Test 7: Batch Processing Structure
        run_test("Batch Processing Structure", function() {
            $import_manager = new SquareKit_Import_Manager();
            
            // Test with empty array (should not fail)
            $empty_batch = $import_manager->import_products_batch(array());
            
            if (!is_array($empty_batch) || !isset($empty_batch['total'])) {
                return "Batch processing structure is incorrect";
            }
            
            echo "<p class='info'>Empty batch result:</p>";
            echo "<pre>" . json_encode($empty_batch, JSON_PRETTY_PRINT) . "</pre>";
            
            if ($empty_batch['total'] !== 0) {
                return "Empty batch should have total of 0";
            }
            
            return true;
        }, $test_results);

        // Display final results
        echo "<div class='test-section'>";
        echo "<h2>📊 Test Results Summary</h2>";
        echo "<div class='stats-grid'>";
        
        echo "<div class='stat-card'>";
        echo "<div class='stat-value'>{$test_results['total_tests']}</div>";
        echo "<div class='stat-label'>Total Tests</div>";
        echo "</div>";
        
        echo "<div class='stat-card'>";
        echo "<div class='stat-value success'>{$test_results['passed_tests']}</div>";
        echo "<div class='stat-label'>Passed</div>";
        echo "</div>";
        
        echo "<div class='stat-card'>";
        echo "<div class='stat-value error'>{$test_results['failed_tests']}</div>";
        echo "<div class='stat-label'>Failed</div>";
        echo "</div>";
        
        echo "<div class='stat-card'>";
        echo "<div class='stat-value warning'>{$test_results['warnings']}</div>";
        echo "<div class='stat-label'>Warnings</div>";
        echo "</div>";
        
        echo "</div>";
        
        $success_rate = $test_results['total_tests'] > 0 ? 
            round(($test_results['passed_tests'] / $test_results['total_tests']) * 100, 1) : 0;
        
        echo "<h3>Overall Success Rate: <span class='" . 
            ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'error')) . 
            "'>{$success_rate}%</span></h3>";
        
        if ($test_results['failed_tests'] === 0) {
            echo "<div class='test-result success'>";
            echo "<h3>🎉 All Tests Passed!</h3>";
            echo "<p>The Import Manager is working correctly and ready for use.</p>";
            echo "</div>";
        } else {
            echo "<div class='test-result error'>";
            echo "<h3>⚠️ Some Tests Failed</h3>";
            echo "<p>Please review the failed tests above and ensure all dependencies are properly loaded.</p>";
            echo "</div>";
        }
        
        echo "</div>";
        ?>

        <div class="test-section">
            <h2>🚀 Next Steps</h2>
            <p>If all tests passed, you can now:</p>
            <ul>
                <li><strong>Use the Import Manager</strong> in your code with confidence</li>
                <li><strong>Test with real Square data</strong> (requires API connection)</li>
                <li><strong>Proceed to Phase 2</strong>: File Refactoring</li>
            </ul>
            
            <h3>Quick Usage Example:</h3>
            <pre><?php echo htmlspecialchars('<?php
// Initialize Import Manager
$import_manager = new SquareKit_Import_Manager();

// Import by Square item ID (SWEVER method)
$result = $import_manager->import_product("SQUARE_ITEM_ID_123");

// Import by Square item data (Legacy method)
$item_data = array("id" => "ITEM_123", "item_data" => array(...));
$result = $import_manager->import_product($item_data);

// Check result
if (is_wp_error($result)) {
    echo "Import failed: " . $result->get_error_message();
} else {
    echo "Import successful! Product ID: " . $result["product_id"];
    echo "Method used: " . $result["method"];
}
?>'); ?></pre>
        </div>

        <div class="test-section">
            <h2>🔗 Useful Links</h2>
            <a href="<?php echo admin_url('admin.php?page=squarekit'); ?>" class="button">Square Kit Dashboard</a>
            <a href="<?php echo admin_url('admin.php?page=squarekit-settings'); ?>" class="button">Settings</a>
            <a href="<?php echo admin_url('admin.php?page=squarekit-products'); ?>" class="button">Products</a>
            <a href="javascript:location.reload();" class="button">Refresh Tests</a>
        </div>
    </div>
</body>
</html>
