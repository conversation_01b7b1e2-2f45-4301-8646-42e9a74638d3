<?php
/**
 * Verify Frontend Data
 * 
 * This file checks what data is actually being displayed on the frontend
 * vs what's stored in the database.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;
    
    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }
    
    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Frontend Data Verification</h2>\n";
echo "<style>
.verify-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.verify-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.verify-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.mismatch { background: #ffe6e6; border-left: 3px solid #d63638; }
.match { background: #e6ffe6; border-left: 3px solid #00a32a; }
</style>\n";

$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 175;

echo "<div class='verify-section'>";
echo "<div class='verify-title'>Checking Product ID: $product_id</div>";
echo "<div class='verify-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span style='color: red;'>Product not found with ID: $product_id</span>\n";
    exit;
}

echo "Product: " . $product->get_name() . "<br>\n";
echo "URL: <a href='" . get_permalink( $product_id ) . "' target='_blank'>" . get_permalink( $product_id ) . "</a><br>\n";

echo "</div></div>\n";

// Check what the frontend display method would output
echo "<div class='verify-section'>";
echo "<div class='verify-title'>Frontend Display Method Output</div>";
echo "<div class='verify-data'>";

$modifiers = get_post_meta( $product_id, '_squarekit_modifiers', true );
if ( ! empty( $modifiers ) ) {
    echo "<h4>What display_product_modifiers() would output:</h4>\n";
    
    // Simulate the frontend display method
    foreach ( $modifiers as $set_index => $set ) {
        if ( empty( $set['options'] ) ) continue;

        $set_name = $set['name'] ?? $set['set_name'] ?? '';
        $is_single = $set['single'] ?? $set['single_choice'] ?? false;

        if ( empty( $set_name ) ) {
            continue;
        }

        echo "<div class='squarekit-modifier-set'>\n";
        echo "<h4>" . esc_html( $set_name ) . "</h4>\n";

        $input_type = $is_single ? 'radio' : 'checkbox';
        $name_attr = $is_single ? 'squarekit_modifier_' . $set_index : 'squarekit_modifier_' . $set_index . '[]';

        foreach ( $set['options'] as $option_index => $option ) {
            $option_id = 'squarekit_modifier_' . $set_index . '_' . $option_index;
            $option_name = $option['name'] ?? '';
            $option_price = $option['price'] ?? 0;

            if ( empty( $option_name ) ) {
                continue;
            }

            echo "<label class='squarekit-modifier-option' style='display: block; margin: 5px 0; padding: 8px; border: 1px solid #ddd;'>\n";
            echo "<input type='" . esc_attr( $input_type ) . "' name='" . esc_attr( $name_attr ) . "' value='" . esc_attr( $option_index ) . "' data-price='" . esc_attr( $option_price ) . "' data-set='" . esc_attr( $set_index ) . "' />\n";
            echo "<span class='squarekit-modifier-label'>" . esc_html( $option_name ) . "</span>\n";
            
            if ( ! empty( $option_price ) ) {
                echo "<span class='squarekit-modifier-price'> (+" . wc_price( $option_price ) . ")</span>\n";
            }
            
            echo "</label>\n";
        }
        echo "</div>\n";
    }
} else {
    echo "<span style='color: orange;'>No modifiers found for this product</span>\n";
}

echo "</div></div>\n";

// Check JavaScript localization
echo "<div class='verify-section'>";
echo "<div class='verify-title'>JavaScript Localization Check</div>";
echo "<div class='verify-data'>";

// Simulate what enqueue_frontend_scripts() would pass
$base_price = 0;
if ( $product->is_type( 'variable' ) ) {
    $variations = $product->get_available_variations();
    if ( ! empty( $variations ) ) {
        $prices = array();
        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            if ( $variation && $variation->get_price() !== '' ) {
                $prices[] = floatval( $variation->get_price() );
            }
        }
        if ( ! empty( $prices ) ) {
            $base_price = min( $prices );
        }
    }
} else {
    $base_price = floatval( $product->get_price() );
}

$js_data = array(
    'ajax_url' => admin_url( 'admin-ajax.php' ),
    'nonce' => wp_create_nonce( 'squarekit_frontend' ),
    'base_price' => $base_price,
    'currency_symbol' => get_woocommerce_currency_symbol(),
    'currency_position' => get_option( 'woocommerce_currency_pos' ),
    'price_decimal_sep' => wc_get_price_decimal_separator(),
    'price_thousand_sep' => wc_get_price_thousand_separator(),
    'price_decimals' => wc_get_price_decimals(),
);

echo "<h4>JavaScript Data (squarekit_frontend):</h4>\n";
echo "<pre>" . print_r( $js_data, true ) . "</pre>\n";

echo "</div></div>\n";

// Data attributes check
echo "<div class='verify-section'>";
echo "<div class='verify-title'>Data Attributes Verification</div>";
echo "<div class='verify-data'>";

if ( ! empty( $modifiers ) ) {
    echo "<h4>data-price attributes that JavaScript will read:</h4>\n";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>Option Name</th><th>data-price Value</th><th>Expected Calculation</th></tr>\n";
    
    foreach ( $modifiers as $set_index => $set ) {
        if ( empty( $set['options'] ) ) continue;
        
        foreach ( $set['options'] as $option_index => $option ) {
            $option_name = $option['name'] ?? '';
            $option_price = $option['price'] ?? 0;
            $expected_total = $base_price + $option_price;
            
            if ( empty( $option_name ) ) continue;
            
            echo "<tr>";
            echo "<td>" . esc_html( $option_name ) . "</td>";
            echo "<td>" . esc_html( $option_price ) . "</td>";
            echo "<td>$" . number_format( $base_price, 2 ) . " + $" . number_format( $option_price, 2 ) . " = $" . number_format( $expected_total, 2 ) . "</td>";
            echo "</tr>\n";
        }
    }
    
    echo "</table>\n";
}

echo "</div></div>\n";

// Cache check
echo "<div class='verify-section'>";
echo "<div class='verify-title'>Cache & Meta Check</div>";
echo "<div class='verify-data'>";

echo "<h4>Product Meta Data:</h4>\n";
$all_meta = get_post_meta( $product_id );
$relevant_meta = array();

foreach ( $all_meta as $key => $value ) {
    if ( strpos( $key, 'squarekit' ) !== false || strpos( $key, '_price' ) !== false ) {
        $relevant_meta[$key] = $value;
    }
}

echo "<pre>" . print_r( $relevant_meta, true ) . "</pre>\n";

echo "</div></div>\n";

echo "<div class='verify-section match'>";
echo "<div class='verify-title'>✅ Verification Complete</div>";
echo "<div class='verify-data'>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>Visit the actual product page: <a href='" . get_permalink( $product_id ) . "' target='_blank'>" . get_permalink( $product_id ) . "</a></li>";
echo "<li>Check if the displayed prices match the data above</li>";
echo "<li>Open browser console and run <code>squareKitDebug()</code></li>";
echo "<li>Select modifiers and verify price calculations</li>";
echo "</ul>";
echo "</div></div>\n";

?>
