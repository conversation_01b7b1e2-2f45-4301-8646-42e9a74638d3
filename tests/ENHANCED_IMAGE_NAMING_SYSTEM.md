# Enhanced Image Naming System for SquareKit

## Overview

The SquareKit plugin now includes an enhanced image naming system that automatically renames imported product images using the actual product name instead of generic Square-provided filenames like "original.jpeg".

## Problem Solved

### Before (Issues):
- ❌ Images imported with generic names: "original.jpeg", "original-1.jpeg"
- ❌ Poor SEO due to non-descriptive filenames
- ❌ Difficult media library management
- ❌ No connection between image filename and product

### After (Solution):
- ✅ Images named after products: "ceremonial_matcha_tea.jpg"
- ✅ SEO-friendly filenames improve search visibility
- ✅ Easy media library organization and management
- ✅ Clear connection between images and products

## Filename Format Specifications

### 1. Primary Image Format
```
{sanitized-product-name}.{extension}
```
**Example**: "Ceremonial Matcha Tea" → `ceremonial_matcha_tea.jpg`

### 2. Multiple Images Format
```
{sanitized-product-name}-{position}.{extension}
```
**Examples**: 
- Main image: `ceremonial_matcha_tea.jpg`
- Gallery image 1: `ceremonial_matcha_tea-1.jpg`
- Gallery image 2: `ceremonial_matcha_tea-2.jpg`

### 3. Sanitization Rules

#### Character Replacement:
- **Spaces** → underscores (`_`)
- **Special characters** → underscores (`_`)
- **Multiple consecutive underscores** → single underscore (`_`)
- **Leading/trailing underscores** → removed

#### Length Limits:
- **Maximum length**: 50 characters (before extension)
- **Minimum length**: 2 characters
- **Fallback**: "product_image" if name is too short or empty

#### Case Conversion:
- **All lowercase** for consistency and compatibility

## Implementation Details

### Core Methods Added

#### 1. `generate_product_image_filename()`
```php
protected function generate_product_image_filename($url, $product_name = '', $position = 0, $image_type = '')
```
- **Purpose**: Main filename generation logic
- **Parameters**: URL, product name, image position, MIME type
- **Returns**: Sanitized, unique filename

#### 2. `sanitize_filename()`
```php
protected function sanitize_filename($product_name)
```
- **Purpose**: Convert product name to web-safe filename
- **Features**: HTML tag removal, character sanitization, length limiting
- **Returns**: Clean filename base

#### 3. `get_image_extension()`
```php
protected function get_image_extension($url, $image_type = '')
```
- **Purpose**: Determine correct file extension
- **Sources**: URL path, MIME type
- **Fallback**: 'jpg' if unable to determine

#### 4. `ensure_unique_filename()`
```php
protected function ensure_unique_filename($filename)
```
- **Purpose**: Prevent filename conflicts
- **Method**: Numbered suffixes (-1, -2, etc.)
- **Returns**: Guaranteed unique filename

### Updated Methods

#### 1. `import_image_from_url()`
- **New parameters**: `$product_name`, `$position`
- **Enhanced**: Uses product context for naming
- **Backward compatible**: Works without new parameters

#### 2. `import_product_gallery()` & `import_product_gallery_enhanced()`
- **Enhanced**: Automatically passes product name and position
- **Improved**: Better filename consistency across gallery

#### 3. `import_image_from_data_url()`
- **Enhanced**: Supports product-based naming for test images
- **Consistent**: Same naming logic as URL imports

## Filename Examples

### Real-World Examples

| Product Name | Main Image | Gallery Images |
|--------------|------------|----------------|
| "Ceremonial Matcha Tea" | `ceremonial_matcha_tea.jpg` | `ceremonial_matcha_tea-1.jpg`, `ceremonial_matcha_tea-2.jpg` |
| "Coffee & Espresso Blend!" | `coffee___espresso_blend_.jpg` | `coffee___espresso_blend_-1.jpg` |
| "Organic Green Tea (Premium)" | `organic_green_tea__premium_.jpg` | `organic_green_tea__premium_-1.jpg` |
| "Very Long Product Name That Exceeds Limit" | `very_long_product_name_that_should_be_truncated_.jpg` | `very_long_product_name_that_should_be_truncated_-1.jpg` |

### Special Cases

| Input | Output | Reason |
|-------|--------|---------|
| "" (empty) | `product_image.jpg` | Fallback for empty names |
| "A" | `product_image.jpg` | Fallback for too short names |
| "123 Numbers" | `123_numbers.jpg` | Numbers preserved |
| "Special @#$% Chars" | `special_____chars.jpg` | Special chars replaced |

## SEO Benefits

### Before vs After

#### Before (Generic Names):
```
original.jpeg
original-1.jpeg
original-2.jpeg
```
- ❌ No SEO value
- ❌ No search relevance
- ❌ Poor user experience

#### After (Product Names):
```
ceremonial_matcha_tea.jpg
ceremonial_matcha_tea-1.jpg
ceremonial_matcha_tea-2.jpg
```
- ✅ High SEO value
- ✅ Search engine friendly
- ✅ Improved discoverability
- ✅ Better user experience

### SEO Impact:
- **Image search ranking**: Improved visibility in Google Images
- **Alt text synergy**: Filename complements alt text
- **Site structure**: Better organized media library
- **User experience**: Meaningful filenames in downloads

## Testing and Validation

### Test File: `test-image-naming-system.php`

#### Test Coverage:
1. **Filename Sanitization**: Various product name formats
2. **Extension Detection**: URL and MIME type handling
3. **Complete Generation**: Full filename creation process
4. **Real Import**: Actual product image import testing
5. **Uniqueness**: Conflict prevention validation

#### Expected Results:
- ✅ All sanitization tests pass
- ✅ Extensions correctly detected
- ✅ Filenames follow expected patterns
- ✅ Real imports use product names
- ✅ No filename conflicts

### Manual Testing Steps:

1. **Run test file**: `http://your-site.com/wp-content/plugins/squarekit/test-image-naming-system.php`
2. **Import Square products** with images
3. **Check media library** for proper filenames
4. **Verify SEO impact** in search results

## Backward Compatibility

### Existing Functionality Preserved:
- ✅ All existing image import methods work unchanged
- ✅ Fallback to URL-based naming when product name unavailable
- ✅ No breaking changes to existing imports
- ✅ Optional parameters maintain compatibility

### Migration:
- **Existing images**: Keep current names (no automatic renaming)
- **New imports**: Use enhanced naming system
- **Re-imports**: Will use new naming if product name available

## Error Handling and Fallbacks

### Fallback Scenarios:

1. **No product name**: Use URL filename or generate unique name
2. **Invalid characters**: Replace with underscores
3. **Name too long**: Truncate to 50 characters
4. **Name too short**: Use "product_image" fallback
5. **File conflicts**: Add numbered suffix
6. **Extension unknown**: Default to 'jpg'

### Error Prevention:
- **Input validation**: All inputs sanitized
- **Length limits**: Prevent filesystem issues
- **Character filtering**: Ensure web-safe filenames
- **Uniqueness checks**: Prevent overwrites

## Performance Considerations

### Optimizations:
- **Minimal overhead**: Filename generation is lightweight
- **Caching**: Product names retrieved once per gallery
- **Efficient checks**: Quick file existence validation
- **Batch processing**: Gallery imports optimized

### Impact:
- **Import speed**: Negligible performance impact
- **Memory usage**: No significant increase
- **Database queries**: No additional queries for naming
- **File operations**: Standard WordPress media handling

## Future Enhancements

### Potential Improvements:
1. **Custom naming patterns**: User-configurable filename formats
2. **Bulk renaming**: Tool to rename existing images
3. **Advanced sanitization**: More sophisticated character handling
4. **Multilingual support**: Unicode character handling
5. **Category prefixes**: Include product category in filename

### Configuration Options:
- Enable/disable enhanced naming
- Custom sanitization rules
- Filename length limits
- Position suffix format

## Troubleshooting

### Common Issues:

#### 1. Images still have generic names
- **Check**: Product name available during import
- **Solution**: Ensure product is created before image import

#### 2. Filenames have many underscores
- **Cause**: Product name has many special characters
- **Solution**: Normal behavior, improves web compatibility

#### 3. Filename conflicts
- **Behavior**: Automatic numbering (-1, -2, etc.)
- **Solution**: Working as designed

#### 4. Very short filenames
- **Cause**: Product name too short or empty
- **Solution**: Uses "product_image" fallback

### Debug Steps:
1. Run `test-image-naming-system.php`
2. Check WordPress error logs
3. Verify product names are set
4. Test with simple product names first

The enhanced image naming system provides significant SEO and organizational benefits while maintaining full backward compatibility with existing SquareKit functionality.
