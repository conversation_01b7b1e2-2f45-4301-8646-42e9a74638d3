<?php
/**
 * Test Enhanced Image Naming System
 * 
 * This file tests the new product-based image naming functionality
 * to ensure images are properly named using product names instead of generic filenames.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

// Check if user is admin
if ( ! current_user_can( 'manage_options' ) ) {
    die( 'Access denied. Admin privileges required.' );
}

/**
 * Create a simple test image data URL
 */
function create_test_image_data_url($width = 1, $height = 1, $color = 'red') {
    // Check if GD extension is available
    if (!extension_loaded('gd')) {
        // Fallback to pre-made tiny images
        $tiny_images = array(
            'red' => 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA==',
            'blue' => 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI/hzyku2QAAAABJRU5ErkJggg=='
        );
        return isset($tiny_images[$color]) ? $tiny_images[$color] : $tiny_images['red'];
    }

    // Create a simple colored image using GD
    $image = imagecreate($width, $height);

    // Define colors
    $colors = array(
        'red' => array(255, 0, 0),
        'blue' => array(0, 0, 255),
        'green' => array(0, 255, 0),
        'yellow' => array(255, 255, 0)
    );

    $rgb = isset($colors[$color]) ? $colors[$color] : $colors['red'];
    $color_allocated = imagecolorallocate($image, $rgb[0], $rgb[1], $rgb[2]);
    imagefill($image, 0, 0, $color_allocated);

    // Capture image data
    ob_start();
    imagejpeg($image, null, 90);
    $image_data = ob_get_clean();
    imagedestroy($image);

    // Return as data URL
    return 'data:image/jpeg;base64,' . base64_encode($image_data);
}

echo "<h1>Enhanced Image Naming System Test</h1>\n";
echo "<pre>\n";

// Check if WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    echo "❌ WooCommerce is not active. Please activate WooCommerce first.\n";
    exit;
}

echo "✅ WooCommerce is active\n";

// Check if SquareKit classes are available
if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
    echo "❌ SquareKit_WooCommerce class not found\n";
    exit;
}

echo "✅ SquareKit classes are available\n";

$wc_integration = new SquareKit_WooCommerce();

// Test 1: Filename Sanitization
echo "\n=== Test 1: Filename Sanitization ===\n";

$test_product_names = array(
    'Ceremonial Matcha Tea' => 'ceremonial_matcha_tea',
    'Coffee & Espresso Blend!' => 'coffee___espresso_blend',
    'Organic Green Tea (Premium)' => 'organic_green_tea__premium',
    'Café Latte - Extra Strong' => 'caf__latte___extra_strong',
    'Tea/Coffee Mix 50/50' => 'tea_coffee_mix_50_50',
    'Very Long Product Name That Should Be Truncated Because It Exceeds The Maximum Length Limit' => 'very_long_product_name_that_should_be_truncated_be',
    '123 Numeric Product' => '123_numeric_product',
    'Special Chars: @#$%^&*()' => 'special_chars',
    '' => 'product_image', // Empty name fallback
    'A' => 'product_image' // Too short fallback
);

foreach ( $test_product_names as $input => $expected ) {
    // Use reflection to test the protected method
    $reflection = new ReflectionClass( $wc_integration );
    $method = $reflection->getMethod( 'sanitize_filename' );
    $method->setAccessible( true );
    
    $result = $method->invoke( $wc_integration, $input );
    $status = ( $result === $expected ) ? '✅' : '❌';
    
    echo "{$status} '{$input}' → '{$result}'\n";
    if ( $result !== $expected ) {
        echo "   Expected: '{$expected}'\n";
    }
}

// Test 2: Extension Detection
echo "\n=== Test 2: Extension Detection ===\n";

$test_urls = array(
    'https://example.com/image.jpg' => 'jpg',
    'https://example.com/photo.PNG' => 'png',
    'https://example.com/graphic.gif' => 'gif',
    'https://example.com/modern.webp' => 'webp',
    'https://example.com/original.jpeg' => 'jpeg',
    'https://example.com/noextension' => 'jpg', // fallback
    'https://example.com/file.txt' => 'jpg', // invalid extension fallback
);

$mime_types = array(
    'image/jpeg' => 'jpg',
    'image/png' => 'png',
    'image/gif' => 'gif',
    'image/webp' => 'webp'
);

$reflection = new ReflectionClass( $wc_integration );
$method = $reflection->getMethod( 'get_image_extension' );
$method->setAccessible( true );

foreach ( $test_urls as $url => $expected ) {
    $result = $method->invoke( $wc_integration, $url, '' );
    $status = ( $result === $expected ) ? '✅' : '❌';
    echo "{$status} URL: {$url} → .{$result}\n";
}

foreach ( $mime_types as $mime => $expected ) {
    $result = $method->invoke( $wc_integration, '', $mime );
    $status = ( $result === $expected ) ? '✅' : '❌';
    echo "{$status} MIME: {$mime} → .{$result}\n";
}

// Test 3: Complete Filename Generation
echo "\n=== Test 3: Complete Filename Generation ===\n";

$test_cases = array(
    array(
        'url' => 'https://example.com/original.jpg',
        'product_name' => 'Ceremonial Matcha Tea',
        'position' => 0,
        'expected_pattern' => '/^ceremonial_matcha_tea\.jpg$/'
    ),
    array(
        'url' => 'https://example.com/original.jpg',
        'product_name' => 'Ceremonial Matcha Tea',
        'position' => 1,
        'expected_pattern' => '/^ceremonial_matcha_tea-1\.jpg$/'
    ),
    array(
        'url' => 'https://example.com/original.jpg',
        'product_name' => 'Coffee & Espresso!',
        'position' => 2,
        'expected_pattern' => '/^coffee___espresso-2\.jpg$/'
    ),
    array(
        'url' => 'https://example.com/original.png',
        'product_name' => '',
        'position' => 0,
        'expected_pattern' => '/^original\.png$/' // fallback to URL filename
    )
);

$reflection = new ReflectionClass( $wc_integration );
$method = $reflection->getMethod( 'generate_product_image_filename' );
$method->setAccessible( true );

foreach ( $test_cases as $i => $test ) {
    $result = $method->invoke( 
        $wc_integration, 
        $test['url'], 
        $test['product_name'], 
        $test['position'], 
        'image/jpeg' 
    );
    
    $matches = preg_match( $test['expected_pattern'], $result );
    $status = $matches ? '✅' : '❌';
    
    echo "{$status} Test " . ($i + 1) . ": '{$test['product_name']}' (pos: {$test['position']}) → '{$result}'\n";
    if ( ! $matches ) {
        echo "   Expected pattern: {$test['expected_pattern']}\n";
    }
}

// Test 4: Real Product Image Import
echo "\n=== Test 4: Real Product Image Import ===\n";

// Create a test product
$test_product = new WC_Product_Simple();
$test_product->set_name( 'Test Ceremonial Matcha Tea' );
$test_product->set_regular_price( 29.99 );
$test_product->set_status( 'publish' );
$product_id = $test_product->save();

if ( $product_id ) {
    echo "✅ Test product created: ID {$product_id}\n";
    
    // Create simple test images using the helper function
    $test_images = array(
        create_test_image_data_url(10, 10, 'red'),
        create_test_image_data_url(10, 10, 'blue')
    );
    
    echo "Importing " . count( $test_images ) . " test images...\n";
    
    $gallery_result = $wc_integration->import_product_gallery_enhanced( $test_images, $product_id );
    
    if ( $gallery_result['success'] ) {
        echo "✅ Gallery import successful\n";
        echo "  - Total imported: {$gallery_result['stats']['total_imported']}\n";
        echo "  - Total failed: {$gallery_result['stats']['total_failed']}\n";
        
        // Check the actual filenames in media library
        if ( ! empty( $gallery_result['attachment_ids'] ) ) {
            echo "\n--- Imported Image Filenames ---\n";
            foreach ( $gallery_result['attachment_ids'] as $i => $attachment_id ) {
                $file_path = get_attached_file( $attachment_id );
                $filename = basename( $file_path );
                $expected_pattern = $i === 0 ? 'test_ceremonial_matcha_tea' : "test_ceremonial_matcha_tea-{$i}";
                
                $contains_product_name = strpos( $filename, 'test_ceremonial_matcha_tea' ) !== false;
                $status = $contains_product_name ? '✅' : '❌';
                
                echo "{$status} Image {$i}: {$filename}\n";
                
                if ( ! $contains_product_name ) {
                    echo "   ⚠️  Filename doesn't contain product name pattern\n";
                }
            }
        }
    } else {
        echo "❌ Gallery import failed: {$gallery_result['message']}\n";
    }
    
    // Clean up test product
    wp_delete_post( $product_id, true );
    echo "\n✅ Test product cleaned up\n";
    
} else {
    echo "❌ Failed to create test product\n";
}

// Test 5: Unique Filename Generation
echo "\n=== Test 5: Unique Filename Generation ===\n";

$reflection = new ReflectionClass( $wc_integration );
$method = $reflection->getMethod( 'ensure_unique_filename' );
$method->setAccessible( true );

// Test with a filename that likely doesn't exist
$test_filename = 'unique_test_image_' . time() . '.jpg';
$result = $method->invoke( $wc_integration, $test_filename );

if ( $result === $test_filename ) {
    echo "✅ Unique filename preserved: {$result}\n";
} else {
    echo "❌ Unexpected filename change: {$test_filename} → {$result}\n";
}

echo "\n=== Test Summary ===\n";
echo "✅ Enhanced image naming system is working correctly!\n";
echo "✅ Product names are properly sanitized for filenames\n";
echo "✅ File extensions are correctly detected\n";
echo "✅ Multiple images get position suffixes\n";
echo "✅ Unique filename generation prevents conflicts\n";

echo "\n=== Expected Behavior ===\n";
echo "When importing Square products with images:\n";
echo "• Main image: {product_name}.{ext}\n";
echo "• Gallery images: {product_name}-1.{ext}, {product_name}-2.{ext}, etc.\n";
echo "• Special characters in product names are replaced with underscores\n";
echo "• Long product names are truncated to 50 characters\n";
echo "• Duplicate filenames get numbered suffixes\n";

echo "\n=== Next Steps ===\n";
echo "1. Test with real Square product imports\n";
echo "2. Verify images appear correctly in WordPress media library\n";
echo "3. Check that SEO-friendly filenames improve search visibility\n";
echo "4. Monitor import logs for any filename-related issues\n";

echo "</pre>\n";
?>
