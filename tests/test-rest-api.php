<?php
class Test_SquareKit_REST_API extends WP_UnitTestCase {
    public function test_endpoints_registered() {
        $routes = rest_get_server()->get_routes();
        $this->assertArrayHasKey('/sws/v1/settings', $routes);
        $this->assertArrayHasKey('/sws/v1/inventory', $routes);
        $this->assertArrayHasKey('/sws/v1/orders', $routes);
        $this->assertArrayHasKey('/sws/v1/customers', $routes);
    }

    public function test_get_settings() {
        $request = new WP_REST_Request('GET', '/sws/v1/settings');
        $response = rest_do_request($request);
        $this->assertEquals(200, $response->get_status());
        $data = $response->get_data();
        $this->assertArrayHasKey('settings', $data);
    }
} 