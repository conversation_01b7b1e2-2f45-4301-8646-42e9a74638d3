<?php
/**
 * Debug Square Variation Structure
 * 
 * This file examines the exact structure of Square variation data
 * to understand why attributes aren't being created
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;
    
    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }
    
    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Debug Square Variation Structure</h2>\n";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.debug-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.debug-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.issue { background: #ffe6e6; border-left: 3px solid #d63638; }
.success { background: #e6ffe6; border-left: 3px solid #00a32a; }
.warning { background: #fff3cd; border-left: 3px solid #ffc107; }
pre { background: #f5f5f5; padding: 10px; overflow-x: auto; font-size: 12px; }
</style>\n";

$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 175;

echo "<div class='debug-section'>";
echo "<div class='debug-title'>Square Variation Structure Analysis for Product ID: $product_id</div>";
echo "<div class='debug-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span style='color: red;'>Product not found with ID: $product_id</span>\n";
    exit;
}

$square_item_id = get_post_meta( $product_id, '_square_id', true );
if ( ! $square_item_id ) {
    echo "<span style='color: red;'>No Square ID found for this product</span>\n";
    exit;
}

echo "Product: " . $product->get_name() . "<br>\n";
echo "Square Item ID: " . $square_item_id . "<br>\n";

echo "</div></div>\n";

// Get Square API data
echo "<div class='debug-section'>";
echo "<div class='debug-title'>Fetching Square Item Data</div>";
echo "<div class='debug-data'>";

// Load Square API
if ( ! class_exists( 'SquareKit_Square_API' ) ) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
}

$square_api = new SquareKit_Square_API();

try {
    // Get the catalog object with relations
    $response = $square_api->get_catalog_item_with_relations( $square_item_id );
    
    if ( is_wp_error( $response ) ) {
        echo "<div class='issue'>Error fetching Square data: " . $response->get_error_message() . "</div>\n";
    } else {
        $square_item = $response['item'] ?? null;
        $related_objects = $response['related_objects'] ?? array();
        
        if ( ! $square_item ) {
            echo "<div class='issue'>No item data found in Square response</div>\n";
        } else {
            echo "<div class='success'>✅ Successfully fetched Square data</div>\n";
            
            // Analyze variations structure
            if ( isset( $square_item['item_data']['variations'] ) ) {
                $variations = $square_item['item_data']['variations'];
                echo "<h4>Variations Structure Analysis:</h4>\n";
                echo "Number of variations: " . count( $variations ) . "<br>\n";
                
                echo "<h4>Detailed Variation Structure:</h4>\n";
                foreach ( $variations as $i => $variation ) {
                    echo "<h5>Variation " . ($i + 1) . " (ID: " . ($variation['id'] ?? 'No ID') . "):</h5>\n";
                    
                    // Check for item option values
                    if ( isset( $variation['item_variation_data']['item_option_values'] ) ) {
                        $option_values = $variation['item_variation_data']['item_option_values'];
                        echo "<div class='success'>✅ Has item_option_values (" . count( $option_values ) . " values)</div>\n";
                        
                        echo "<h6>Item Option Values Structure:</h6>\n";
                        foreach ( $option_values as $j => $option_value ) {
                            echo "<strong>Option Value " . ($j + 1) . ":</strong><br>\n";
                            echo "<pre>" . print_r( $option_value, true ) . "</pre>\n";
                            
                            // Check what fields are available
                            echo "<strong>Available Fields:</strong><br>\n";
                            echo "<ul>\n";
                            foreach ( array_keys( $option_value ) as $field ) {
                                echo "<li><code>{$field}</code>: " . ( is_array( $option_value[$field] ) ? 'Array' : $option_value[$field] ) . "</li>\n";
                            }
                            echo "</ul>\n";
                        }
                    } else {
                        echo "<div class='warning'>⚠️ No item_option_values found</div>\n";
                    }
                    
                    // Show pricing info
                    if ( isset( $variation['item_variation_data']['price_money']['amount'] ) ) {
                        $price = $variation['item_variation_data']['price_money']['amount'] / 100;
                        echo "<strong>Price:</strong> $" . number_format( $price, 2 ) . "<br>\n";
                    }
                    
                    echo "<hr>\n";
                }
                
                // Analyze related objects
                echo "<h4>Related Objects Analysis:</h4>\n";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
                echo "<tr><th>Object Type</th><th>Count</th><th>Sample IDs</th></tr>\n";
                
                foreach ( $related_objects as $type => $objects ) {
                    $sample_ids = array_slice( array_keys( $objects ), 0, 3 );
                    echo "<tr>";
                    echo "<td>{$type}</td>";
                    echo "<td>" . count( $objects ) . "</td>";
                    echo "<td>" . implode( ', ', $sample_ids ) . "</td>";
                    echo "</tr>\n";
                }
                echo "</table>\n";
                
                // Show detailed related objects
                if ( ! empty( $related_objects['item_options'] ) ) {
                    echo "<h4>Item Options (Option Sets) Details:</h4>\n";
                    foreach ( $related_objects['item_options'] as $option_id => $option ) {
                        echo "<h5>Option Set: " . ($option['item_option_data']['name'] ?? 'Unnamed') . " (ID: {$option_id})</h5>\n";
                        echo "<pre>" . print_r( $option, true ) . "</pre>\n";
                    }
                }
                
                if ( ! empty( $related_objects['item_option_values'] ) ) {
                    echo "<h4>Item Option Values Details:</h4>\n";
                    foreach ( $related_objects['item_option_values'] as $value_id => $value ) {
                        echo "<h5>Option Value: " . ($value['item_option_value_data']['name'] ?? 'Unnamed') . " (ID: {$value_id})</h5>\n";
                        echo "<pre>" . print_r( $value, true ) . "</pre>\n";
                    }
                }
                
            } else {
                echo "<div class='issue'>❌ No variations found in Square data</div>\n";
            }
        }
    }
    
} catch ( Exception $e ) {
    echo "<div class='issue'>Exception: " . $e->getMessage() . "</div>\n";
}

echo "</div></div>\n";

// Test attribute extraction logic
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🧪 Test Attribute Extraction Logic</div>";
echo "<div class='debug-data'>";

if ( isset( $variations ) && ! empty( $variations ) ) {
    echo "<h4>Testing extract_option_values_from_variations logic:</h4>\n";
    
    $option_values_by_set = array();
    
    foreach ( $variations as $variation ) {
        $item_option_values = $variation['item_variation_data']['item_option_values'] ?? array();
        
        echo "<h5>Processing variation: " . ($variation['id'] ?? 'No ID') . "</h5>\n";
        echo "Found " . count( $item_option_values ) . " item option values<br>\n";
        
        foreach ( $item_option_values as $option_value ) {
            echo "<strong>Processing option value:</strong><br>\n";
            echo "<pre>" . print_r( $option_value, true ) . "</pre>\n";
            
            $option_id = $option_value['item_option_id'] ?? '';
            $option_value_id = $option_value['item_option_value_id'] ?? '';
            
            echo "Extracted option_id: " . ($option_id ?: 'MISSING') . "<br>\n";
            echo "Extracted option_value_id: " . ($option_value_id ?: 'MISSING') . "<br>\n";
            
            if ( empty( $option_id ) || empty( $option_value_id ) ) {
                echo "<div class='issue'>❌ Missing required fields - this option value will be skipped</div>\n";
                
                // Check what fields are actually available
                echo "<strong>Available fields in this option value:</strong><br>\n";
                foreach ( array_keys( $option_value ) as $field ) {
                    echo "- {$field}<br>\n";
                }
            } else {
                echo "<div class='success'>✅ Valid option value found</div>\n";
                
                // Group by option set (item_option_id represents the option set)
                if ( ! isset( $option_values_by_set[ $option_id ] ) ) {
                    $option_values_by_set[ $option_id ] = array();
                }

                // Store unique option values for this set
                if ( ! in_array( $option_value_id, $option_values_by_set[ $option_id ] ) ) {
                    $option_values_by_set[ $option_id ][] = $option_value_id;
                }
            }
            
            echo "<hr>\n";
        }
    }
    
    echo "<h4>Final Extraction Result:</h4>\n";
    if ( empty( $option_values_by_set ) ) {
        echo "<div class='issue'>❌ No option values extracted - this explains why no attributes are created!</div>\n";
    } else {
        echo "<div class='success'>✅ Extracted option values by set:</div>\n";
        echo "<pre>" . print_r( $option_values_by_set, true ) . "</pre>\n";
    }
}

echo "</div></div>\n";

?>
