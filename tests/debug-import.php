<?php
/**
 * Debug script for import functionality
 * 
 * This script helps debug the import all products functionality
 * by testing each component step by step.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Load WordPress if not already loaded
    require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );
}

// Only allow admin users
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Access denied.' );
}

// Initialize logger
if ( ! class_exists( 'SquareKit_Logger' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-logger.php' );
}
$logger = SquareKit_Logger::get_instance();

echo "<h1>SquareKit Import Debug</h1>\n";
echo "<pre>\n";

// Test 1: Check if database table exists
echo "=== Test 1: Database Table Check ===\n";
global $wpdb;
$table_name = $wpdb->prefix . 'squarekit_bulk_operations';
$table_exists = $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) ) === $table_name;

if ( $table_exists ) {
    echo "✓ Table exists: $table_name\n";
    
    // Check table structure
    $columns = $wpdb->get_results( "DESCRIBE $table_name" );
    echo "Table columns:\n";
    foreach ( $columns as $column ) {
        echo "  - {$column->Field} ({$column->Type})\n";
    }
} else {
    echo "✗ Table missing: $table_name\n";
    echo "Creating table...\n";
    
    // Try to create the table
    if ( ! class_exists( 'SquareKit_Activator' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/class-squarekit-activator.php' );
    }
    SquareKit_Activator::activate();
    
    // Check again
    $table_exists = $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) ) === $table_name;
    echo $table_exists ? "✓ Table created successfully\n" : "✗ Failed to create table\n";
}

echo "\n";

// Test 2: Check Square connection
echo "=== Test 2: Square Connection Check ===\n";
if ( ! class_exists( 'SquareKit_Settings' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
}

$settings = new SquareKit_Settings();
$is_connected = $settings->is_connected();

if ( $is_connected ) {
    echo "✓ Connected to Square\n";
    echo "Environment: " . $settings->get_environment() . "\n";
} else {
    echo "✗ Not connected to Square\n";
    echo "Please configure Square connection in settings.\n";
}

echo "\n";

// Test 3: Test Square API
if ( $is_connected ) {
    echo "=== Test 3: Square API Test ===\n";

    if ( ! class_exists( 'SquareKit_Square_API' ) ) {
        require_once( dirname( __FILE__ ) . '/includes/api/class-squarekit-square-api.php' );
    }

    try {
        $square_api = new SquareKit_Square_API();
        echo "✓ Square API instance created\n";

        // Test catalog retrieval (simple method)
        echo "Testing simple catalog retrieval...\n";
        $catalog = $square_api->get_catalog( array( 'types' => 'ITEM' ) );

        if ( is_wp_error( $catalog ) ) {
            echo "✗ Simple catalog retrieval failed: " . $catalog->get_error_message() . "\n";
        } else {
            $count = is_array( $catalog ) ? count( $catalog ) : 0;
            echo "✓ Simple catalog retrieved successfully: $count items\n";
        }

        // Test catalog items retrieval (complex method used by products page)
        echo "Testing complex catalog items retrieval...\n";
        $catalog_items = $square_api->get_catalog_items( array() );

        if ( is_wp_error( $catalog_items ) ) {
            echo "✗ Complex catalog items retrieval failed: " . $catalog_items->get_error_message() . "\n";
        } else {
            $products = isset( $catalog_items['products'] ) ? $catalog_items['products'] : array();
            $count = count( $products );
            echo "✓ Complex catalog items retrieved successfully: $count processed products\n";

            if ( $count > 0 ) {
                echo "Sample product data:\n";
                $sample = $products[0];
                echo "  - ID: " . ( $sample['id'] ?? 'N/A' ) . "\n";
                echo "  - Name: " . ( $sample['name'] ?? 'N/A' ) . "\n";
                echo "  - Type: " . ( $sample['type'] ?? 'N/A' ) . "\n";
                echo "  - Imported: " . ( $sample['imported'] ? 'Yes' : 'No' ) . "\n";
                echo "  - Synced: " . ( $sample['synced'] ? 'Yes' : 'No' ) . "\n";
            }
        }

    } catch ( Exception $e ) {
        echo "✗ Exception in Square API test: " . $e->getMessage() . "\n";
    }

    echo "\n";
}

// Test 4: Test Bulk Operations Class
echo "=== Test 4: Bulk Operations Class Test ===\n";

if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-bulk-operations.php' );
}

try {
    $bulk_ops = new SquareKit_Bulk_Operations();
    echo "✓ Bulk Operations instance created\n";
    
    if ( $is_connected && $table_exists ) {
        echo "Testing start_operation...\n";
        
        $operation_data = array(
            'import_all' => true,
            'include_images' => true,
            'include_categories' => true,
            'include_variations' => true
        );
        
        $operation_id = $bulk_ops->start_operation( 'import_products', $operation_data );
        
        if ( is_wp_error( $operation_id ) ) {
            echo "✗ start_operation failed: " . $operation_id->get_error_message() . "\n";
        } else {
            echo "✓ start_operation succeeded: Operation ID $operation_id\n";
            
            // Get operation details
            $operation = $bulk_ops->get_operation( $operation_id );
            if ( $operation ) {
                echo "Operation details:\n";
                echo "  - Type: {$operation->operation_type}\n";
                echo "  - Status: {$operation->operation_status}\n";
                echo "  - Total items: {$operation->total_items}\n";
                echo "  - Created by: {$operation->created_by}\n";
            }
        }
    } else {
        echo "Skipping start_operation test (missing requirements)\n";
    }
    
} catch ( Exception $e ) {
    echo "✗ Exception in Bulk Operations test: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Check recent logs
echo "=== Test 5: Recent Logs ===\n";
$recent_logs = $logger->get_recent_logs( 10 );

if ( empty( $recent_logs ) ) {
    echo "No recent logs found.\n";
} else {
    echo "Recent logs:\n";
    foreach ( array_slice( $recent_logs, -5 ) as $log_line ) {
        echo "  " . trim( $log_line ) . "\n";
    }
}

echo "\n";

// Test 6: WordPress AJAX test
echo "=== Test 6: WordPress AJAX Setup ===\n";

// Check if AJAX actions are registered
$wp_filter = $GLOBALS['wp_filter'];
$ajax_action = 'wp_ajax_squarekit_import_all_products';

if ( isset( $wp_filter[$ajax_action] ) ) {
    echo "✓ AJAX action registered: $ajax_action\n";
} else {
    echo "✗ AJAX action not registered: $ajax_action\n";
}

// Check nonce
$nonce = wp_create_nonce( 'squarekit-admin' );
echo "Generated nonce: $nonce\n";

echo "\n=== Debug Complete ===\n";
echo "</pre>\n";

// Add a button to test the AJAX call
?>
<h2>Test AJAX Call</h2>
<p><strong>Note:</strong> If you see permission errors above, you need to re-authorize your Square application with the updated scopes.</p>
<p>Current required scopes: <code>MERCHANT_PROFILE_READ PAYMENTS_READ PAYMENTS_WRITE ORDERS_READ ORDERS_WRITE CUSTOMERS_READ CUSTOMERS_WRITE INVENTORY_READ INVENTORY_WRITE ITEMS_READ ITEMS_WRITE CATALOG_READ CATALOG_WRITE</code></p>
<button id="test-import" onclick="testImport()">Test Import All Products</button>
<div id="test-result"></div>

<script>
function testImport() {
    const button = document.getElementById('test-import');
    const result = document.getElementById('test-result');
    
    button.disabled = true;
    button.textContent = 'Testing...';
    result.innerHTML = '<p>Testing AJAX call...</p>';
    
    fetch('<?php echo admin_url( 'admin-ajax.php' ); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'squarekit_import_all_products',
            nonce: '<?php echo $nonce; ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        result.innerHTML = '<h3>AJAX Response:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        result.innerHTML = '<h3>AJAX Error:</h3><pre>' + error.toString() + '</pre>';
    })
    .finally(() => {
        button.disabled = false;
        button.textContent = 'Test Import All Products';
    });
}
</script>
