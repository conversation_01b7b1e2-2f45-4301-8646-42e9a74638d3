<?php
/**
 * Square API Connection Test
 * 
 * Tests Square API connectivity and finds valid item IDs for testing
 */

// WordPress environment
define( 'WP_USE_THEMES', false );
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Plugin includes
require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
require_once( dirname( __FILE__ ) . '/includes/class-squarekit-logger.php' );
require_once( dirname( __FILE__ ) . '/includes/api/class-squarekit-square-api.php' );

echo "<h1>Square API Connection Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .code { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 3px solid #ccc; }
    .item { background: #f9f9f9; padding: 10px; margin: 5px 0; border: 1px solid #ddd; }
</style>\n";

try {
    // Step 1: Check settings
    echo "<h2>Step 1: Settings Check</h2>\n";
    $settings = new SquareKit_Settings();
    
    $environment = $settings->get_environment();
    $access_token = $settings->get_access_token();
    
    echo "<div class='info'>Environment: <strong>{$environment}</strong></div>\n";
    echo "<div class='info'>Access Token: " . ( $access_token ? '<span class="success">✅ Present</span>' : '<span class="error">❌ Missing</span>' ) . "</div>\n";
    
    if ( ! $access_token ) {
        echo "<div class='error'>❌ No access token found. Please connect to Square first.</div>\n";
        exit;
    }
    
    // Step 2: Test API connection
    echo "<h2>Step 2: API Connection Test</h2>\n";
    $square_api = new SquareKit_Square_API();
    
    // Test basic connectivity
    echo "<p>Testing basic API connectivity...</p>\n";
    $locations = $square_api->get_locations();
    
    if ( is_wp_error( $locations ) ) {
        echo "<div class='error'>❌ API Connection Failed: " . $locations->get_error_message() . "</div>\n";
        exit;
    } else {
        echo "<div class='success'>✅ API Connection Successful</div>\n";
        echo "<div class='info'>Found " . count( $locations ) . " location(s)</div>\n";
    }
    
    // Step 3: Test catalog access
    echo "<h2>Step 3: Catalog Access Test</h2>\n";
    echo "<p>Fetching catalog items...</p>\n";
    
    $catalog = $square_api->get_catalog( array( 
        'types' => 'ITEM',
        'limit' => 10
    ) );
    
    if ( is_wp_error( $catalog ) ) {
        echo "<div class='error'>❌ Catalog Access Failed: " . $catalog->get_error_message() . "</div>\n";
        exit;
    }
    
    if ( empty( $catalog ) ) {
        echo "<div class='warning'>⚠️ No items found in catalog</div>\n";
        exit;
    }
    
    echo "<div class='success'>✅ Catalog Access Successful</div>\n";
    echo "<div class='info'>Found " . count( $catalog ) . " item(s)</div>\n";
    
    // Step 4: Display available items
    echo "<h2>Step 4: Available Items for Testing</h2>\n";
    
    foreach ( $catalog as $item ) {
        if ( $item['type'] !== 'ITEM' ) continue;
        
        $item_data = $item['item_data'] ?? array();
        $name = $item_data['name'] ?? 'Unnamed Item';
        $variations = $item_data['variations'] ?? array();
        $variation_count = count( $variations );
        
        echo "<div class='item'>\n";
        echo "<strong>Name:</strong> {$name}<br>\n";
        echo "<strong>ID:</strong> <code>{$item['id']}</code><br>\n";
        echo "<strong>Variations:</strong> {$variation_count}<br>\n";
        
        if ( $variation_count > 0 ) {
            echo "<strong>Type:</strong> Variable Product<br>\n";
        } else {
            echo "<strong>Type:</strong> Simple Product<br>\n";
        }
        
        echo "</div>\n";
    }
    
    // Step 5: Test specific item retrieval
    echo "<h2>Step 5: Test Item Retrieval</h2>\n";
    
    if ( ! empty( $catalog ) ) {
        $test_item = $catalog[0];
        $test_id = $test_item['id'];
        
        echo "<p>Testing retrieval of item: <code>{$test_id}</code></p>\n";
        
        $item_with_relations = $square_api->get_catalog_item_with_relations( $test_id );
        
        if ( is_wp_error( $item_with_relations ) ) {
            echo "<div class='error'>❌ Item Retrieval Failed: " . $item_with_relations->get_error_message() . "</div>\n";
        } else {
            echo "<div class='success'>✅ Item Retrieval Successful</div>\n";
            echo "<div class='code'>";
            echo "<strong>Retrieved Item Structure:</strong><br>\n";
            echo "Main Object: " . ( isset( $item_with_relations['object'] ) ? '✅' : '❌' ) . "<br>\n";
            echo "Related Objects: " . ( isset( $item_with_relations['related_objects'] ) ? '✅ (' . count( $item_with_relations['related_objects'] ) . ')' : '❌' ) . "<br>\n";
            echo "</div>\n";
        }
    }
    
    // Step 6: Recommendations
    echo "<h2>Step 6: Recommendations</h2>\n";
    
    if ( ! empty( $catalog ) ) {
        $recommended_item = null;
        $recommended_simple = null;
        
        foreach ( $catalog as $item ) {
            if ( $item['type'] !== 'ITEM' ) continue;
            
            $item_data = $item['item_data'] ?? array();
            $variations = $item_data['variations'] ?? array();
            
            if ( count( $variations ) > 1 && ! $recommended_item ) {
                $recommended_item = $item;
            }
            
            if ( count( $variations ) <= 1 && ! $recommended_simple ) {
                $recommended_simple = $item;
            }
        }
        
        if ( $recommended_item ) {
            $name = $recommended_item['item_data']['name'] ?? 'Variable Product';
            echo "<div class='success'>✅ Recommended Variable Product for Testing:</div>\n";
            echo "<div class='code'>";
            echo "<strong>Name:</strong> {$name}<br>\n";
            echo "<strong>ID:</strong> <code>{$recommended_item['id']}</code><br>\n";
            echo "<strong>Variations:</strong> " . count( $recommended_item['item_data']['variations'] ?? array() ) . "<br>\n";
            echo "</div>\n";
        }
        
        if ( $recommended_simple ) {
            $name = $recommended_simple['item_data']['name'] ?? 'Simple Product';
            echo "<div class='success'>✅ Recommended Simple Product for Testing:</div>\n";
            echo "<div class='code'>";
            echo "<strong>Name:</strong> {$name}<br>\n";
            echo "<strong>ID:</strong> <code>{$recommended_simple['id']}</code><br>\n";
            echo "</div>\n";
        }
    }
    
} catch ( Exception $e ) {
    echo "<div class='error'>❌ Exception: " . $e->getMessage() . "</div>\n";
    echo "<div class='code'>File: " . $e->getFile() . "<br>Line: " . $e->getLine() . "</div>\n";
}

echo "<h2>Next Steps</h2>\n";
echo "<p>1. Use one of the recommended item IDs above in your test-live-import.php</p>\n";
echo "<p>2. Update the test configuration with a valid Square item ID</p>\n";
echo "<p>3. Re-run the import test</p>\n";
?>
