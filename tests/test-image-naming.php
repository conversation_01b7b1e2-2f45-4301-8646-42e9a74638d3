<?php
/**
 * Test Image Naming Fix
 * 
 * Verifies that imported images are using product names instead of "original.jpg"
 */

// WordPress environment
define( 'WP_USE_THEMES', false );
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

echo "<h1>Image Naming Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .warning { color: orange; }
    .info { color: blue; }
    .image-item { background: #f9f9f9; padding: 10px; margin: 5px 0; border: 1px solid #ddd; }
    .filename { font-family: monospace; background: #f0f0f0; padding: 2px 4px; }
</style>\n";

try {
    // Find recently imported products with Square IDs
    $recent_products = get_posts( array(
        'post_type' => 'product',
        'meta_query' => array(
            array(
                'key' => '_square_item_id',
                'compare' => 'EXISTS'
            )
        ),
        'posts_per_page' => 10,
        'orderby' => 'date',
        'order' => 'DESC'
    ) );

    if ( empty( $recent_products ) ) {
        echo "<p class='warning'>⚠️ No products with Square IDs found. Please run an import first.</p>\n";
        exit;
    }

    echo "<h2>Recent Square-Imported Products</h2>\n";
    echo "<p>Checking image naming for recently imported products...</p>\n";

    $total_images = 0;
    $properly_named = 0;
    $generic_named = 0;

    foreach ( $recent_products as $post ) {
        $product = wc_get_product( $post->ID );
        if ( ! $product ) continue;

        $square_id = get_post_meta( $post->ID, '_square_item_id', true );
        $product_name = $product->get_name();

        echo "<div class='image-item'>\n";
        echo "<h3>{$product_name}</h3>\n";
        echo "<p><strong>Product ID:</strong> {$post->ID} | <strong>Square ID:</strong> {$square_id}</p>\n";

        // Check featured image
        $featured_image_id = $product->get_image_id();
        if ( $featured_image_id ) {
            $attachment = get_post( $featured_image_id );
            if ( $attachment ) {
                $filename = basename( get_attached_file( $featured_image_id ) );
                $total_images++;

                echo "<p><strong>Featured Image:</strong> <span class='filename'>{$filename}</span>";

                // Check if filename uses product name or is generic
                $sanitized_product_name = sanitize_title( $product_name );
                if ( strpos( strtolower( $filename ), strtolower( $sanitized_product_name ) ) !== false ) {
                    echo " <span class='success'>✅ Properly named</span>";
                    $properly_named++;
                } elseif ( preg_match( '/^(original|image|square-image|temp|download)/', strtolower( $filename ) ) ) {
                    echo " <span class='error'>❌ Generic naming</span>";
                    $generic_named++;
                } else {
                    echo " <span class='info'>ℹ️ Custom naming</span>";
                }
                echo "</p>\n";
            }
        }

        // Check gallery images
        $gallery_ids = $product->get_gallery_image_ids();
        if ( ! empty( $gallery_ids ) ) {
            echo "<p><strong>Gallery Images:</strong></p>\n";
            echo "<ul>\n";
            foreach ( $gallery_ids as $index => $gallery_id ) {
                $attachment = get_post( $gallery_id );
                if ( $attachment ) {
                    $filename = basename( get_attached_file( $gallery_id ) );
                    $total_images++;

                    echo "<li><span class='filename'>{$filename}</span>";

                    // Check if filename uses product name or is generic
                    $sanitized_product_name = sanitize_title( $product_name );
                    if ( strpos( strtolower( $filename ), strtolower( $sanitized_product_name ) ) !== false ) {
                        echo " <span class='success'>✅ Properly named</span>";
                        $properly_named++;
                    } elseif ( preg_match( '/^(original|image|square-image|temp|download)/', strtolower( $filename ) ) ) {
                        echo " <span class='error'>❌ Generic naming</span>";
                        $generic_named++;
                    } else {
                        echo " <span class='info'>ℹ️ Custom naming</span>";
                    }
                    echo "</li>\n";
                }
            }
            echo "</ul>\n";
        }

        if ( ! $featured_image_id && empty( $gallery_ids ) ) {
            echo "<p class='info'>ℹ️ No images found for this product</p>\n";
        }

        echo "</div>\n";
    }

    // Summary
    echo "<h2>Summary</h2>\n";
    echo "<div class='image-item'>\n";
    echo "<p><strong>Total Images Checked:</strong> {$total_images}</p>\n";
    echo "<p><strong>Properly Named:</strong> <span class='success'>{$properly_named}</span></p>\n";
    echo "<p><strong>Generic Named:</strong> <span class='error'>{$generic_named}</span></p>\n";

    if ( $total_images > 0 ) {
        $percentage = round( ( $properly_named / $total_images ) * 100, 1 );
        echo "<p><strong>Success Rate:</strong> {$percentage}%</p>\n";

        if ( $percentage >= 80 ) {
            echo "<p class='success'>✅ Image naming fix is working well!</p>\n";
        } elseif ( $percentage >= 50 ) {
            echo "<p class='warning'>⚠️ Image naming is partially working. Some images may have been imported before the fix.</p>\n";
        } else {
            echo "<p class='error'>❌ Image naming fix may not be working properly.</p>\n";
        }
    }
    echo "</div>\n";

    // Test the image handler directly
    echo "<h2>Direct Image Handler Test</h2>\n";
    
    if ( class_exists( 'SquareKit_Image_Handler' ) || file_exists( SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-image-handler.php' ) ) {
        if ( ! class_exists( 'SquareKit_Image_Handler' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-image-handler.php';
        }
        
        $image_handler = new SquareKit_Image_Handler();
        
        // Test filename generation
        $test_cases = array(
            array( 'product_name' => 'Ceremonial Macha', 'position' => 0, 'expected_pattern' => 'ceremonial-macha' ),
            array( 'product_name' => 'Jamaica Blue Mountain Gr 1', 'position' => 1, 'expected_pattern' => 'jamaica-blue-mountain-gr-1-1' ),
            array( 'product_name' => 'Test Product with Special Characters!@#', 'position' => 0, 'expected_pattern' => 'test-product-with-special-characters' )
        );
        
        echo "<div class='image-item'>\n";
        echo "<h3>Filename Generation Test</h3>\n";
        
        foreach ( $test_cases as $test ) {
            $generated_filename = $image_handler->generate_product_image_filename( 
                'https://example.com/test.jpg', 
                $test['product_name'], 
                $test['position'], 
                'image/jpeg' 
            );
            
            echo "<p><strong>Product:</strong> {$test['product_name']}<br>\n";
            echo "<strong>Position:</strong> {$test['position']}<br>\n";
            echo "<strong>Generated:</strong> <span class='filename'>{$generated_filename}</span><br>\n";
            
            if ( strpos( strtolower( $generated_filename ), $test['expected_pattern'] ) !== false ) {
                echo "<span class='success'>✅ Correct pattern</span></p>\n";
            } else {
                echo "<span class='error'>❌ Unexpected pattern</span></p>\n";
            }
        }
        echo "</div>\n";
        
    } else {
        echo "<p class='error'>❌ SquareKit_Image_Handler class not found</p>\n";
    }

} catch ( Exception $e ) {
    echo "<div class='error'>❌ Exception: " . $e->getMessage() . "</div>\n";
}

echo "<h2>Next Steps</h2>\n";
echo "<p>1. If generic naming is still found, those images were likely imported before the fix</p>\n";
echo "<p>2. Run a new import to test the current naming system</p>\n";
echo "<p>3. Check the WordPress Media Library to see the actual filenames</p>\n";
?>
