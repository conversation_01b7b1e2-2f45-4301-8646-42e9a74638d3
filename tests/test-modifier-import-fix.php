<?php
/**
 * Test Modifier Import Fix
 * 
 * This test verifies that the modifier import regression has been fixed.
 * Run this after the fixes to ensure modifiers are properly imported and persisted.
 */

// WordPress environment - adjust path for Local by Flywheel
$wp_load_paths = [
    dirname(__FILE__) . '/../../../wp-load.php',  // Standard WordPress
    dirname(__FILE__) . '/../../../../wp-load.php', // Local by Flywheel
    dirname(__FILE__) . '/../../../../../wp-load.php' // Alternative structure
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die("❌ Could not find wp-load.php. Please run this file from a web browser or adjust the path.");
}

// Check if SquareKit plugin is loaded
if (!defined('SQUAREKIT_PLUGIN_DIR')) {
    die("❌ SquareKit plugin is not loaded. Please ensure the plugin is active.");
}

// Include required classes
if (!class_exists('SquareKit_Product_Importer')) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
}
if (!class_exists('SquareKit_Square_API')) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
}

echo "<h1>🔧 Modifier Import Fix Test</h1>\n";

// Auto-find a product with modifiers or use manual override
$test_product_id = $_GET['product_id'] ?? null;

if (!$test_product_id) {
    echo "<h2>🔍 Auto-Finding Product with Modifiers</h2>\n";

    try {
        $square_api = new SquareKit_Square_API();

        // Get catalog items
        echo "Fetching Square catalog...<br>\n";
        $catalog_response = $square_api->get_catalog(array('types' => 'ITEM'));

        if (is_wp_error($catalog_response)) {
            echo "❌ Failed to fetch catalog: " . $catalog_response->get_error_message() . "<br>\n";
            exit;
        }

        $items = $catalog_response['objects'] ?? array();
        echo "✅ Found " . count($items) . " products in catalog<br>\n";

        // Find first product with modifiers
        $found_product = null;
        $checked_count = 0;
        $max_check = 50; // Check up to 50 products

        foreach ($items as $item) {
            if ($checked_count >= $max_check) break;
            $checked_count++;

            $modifier_list_info = $item['item_data']['modifier_list_info'] ?? array();
            if (!empty($modifier_list_info)) {
                $found_product = $item;
                break;
            }
        }

        if (!$found_product) {
            echo "❌ No products with modifiers found in first {$checked_count} products.<br>\n";
            echo "Please add <code>?product_id=YOUR_SQUARE_PRODUCT_ID</code> to test a specific product.<br>\n";
            exit;
        }

        $test_product_id = $found_product['id'];
        $product_name = $found_product['item_data']['name'] ?? 'Unknown';
        $modifier_count = count($found_product['item_data']['modifier_list_info']);

        echo "✅ Auto-selected: <strong>{$product_name}</strong><br>\n";
        echo "Square ID: <code>{$test_product_id}</code><br>\n";
        echo "Modifier Lists: {$modifier_count}<br>\n";

    } catch (Exception $e) {
        echo "❌ Error finding product: " . $e->getMessage() . "<br>\n";
        exit;
    }
} else {
    echo "<h2>📋 Manual Test Configuration</h2>\n";
    echo "Using provided Square Product ID: <code>{$test_product_id}</code><br>\n";
}

echo "<h2>🎯 Test Objective</h2>\n";
echo "Expected: Product should import with modifiers that persist in database<br>\n";

try {
    // Step 1: Test Square API data fetching
    echo "<h2>🔍 Step 1: Fetching Square Data</h2>\n";

    // Initialize Square API if not already done
    if (!isset($square_api)) {
        $square_api = new SquareKit_Square_API();
    }

    $square_data = $square_api->get_catalog_item_with_relations($test_product_id);
    
    if (is_wp_error($square_data)) {
        echo "❌ Failed to fetch Square data: " . $square_data->get_error_message() . "<br>\n";
        exit;
    }
    
    echo "✅ Successfully fetched Square data<br>\n";
    echo "Item name: " . ($square_data['item']['item_data']['name'] ?? 'Unknown') . "<br>\n";
    
    // Check for modifier lists
    $modifier_lists = $square_data['related_objects']['modifier_lists'] ?? array();
    echo "Modifier lists found: " . count($modifier_lists) . "<br>\n";
    
    if (empty($modifier_lists)) {
        echo "⚠️ No modifier lists found for this product. Please test with a product that has modifiers.<br>\n";
        exit;
    }
    
    foreach ($modifier_lists as $list) {
        echo "  - " . ($list['modifier_list_data']['name'] ?? 'Unknown') . " (ID: {$list['id']})<br>\n";
    }
    
    // Step 2: Test product import
    echo "<h2>🚀 Step 2: Testing Product Import</h2>\n";
    
    $importer = new SquareKit_Product_Importer();
    
    // Configure import with modifiers enabled
    $import_config = array(
        'name' => true,
        'description' => true,
        'images' => true,
        'categories' => true,
        'variations' => true,
        'modifiers' => true,  // CRITICAL: Ensure modifiers are enabled
        'attributesDisabled' => false
    );
    
    echo "Import configuration: " . json_encode($import_config) . "<br>\n";
    
    // Import the product
    $result = $importer->import_product_swever_style(
        $test_product_id,
        $import_config,
        false // Not update-only
    );
    
    if (is_wp_error($result)) {
        echo "❌ Import failed: " . $result->get_error_message() . "<br>\n";
        exit;
    }
    
    echo "✅ Product import completed<br>\n";
    echo "WooCommerce Product ID: {$result['product_id']}<br>\n";
    echo "Created: " . ($result['created'] ? 'Yes' : 'No (updated existing)') . "<br>\n";
    
    // Step 3: Verify modifier persistence
    echo "<h2>🔍 Step 3: Verifying Modifier Persistence</h2>\n";
    
    $product_id = $result['product_id'];
    
    // Check SWEVER-compatible format
    $swever_modifiers = get_post_meta($product_id, '_squarekit_modifier_sets', true);
    echo "SWEVER modifier sets: " . (empty($swever_modifiers) ? '❌ EMPTY' : '✅ Found ' . count($swever_modifiers) . ' sets') . "<br>\n";
    
    if (!empty($swever_modifiers)) {
        foreach ($swever_modifiers as $index => $set) {
            echo "  Set {$index}: {$set['set_name']} (Square ID: {$set['square_mod_list_id']}, Options: " . count($set['options']) . ")<br>\n";
        }
    }
    
    // Check legacy format
    $legacy_modifiers = get_post_meta($product_id, '_squarekit_modifiers', true);
    echo "Legacy modifier sets: " . (empty($legacy_modifiers) ? '❌ EMPTY' : '✅ Found ' . count($legacy_modifiers) . ' sets') . "<br>\n";
    
    // Check modifier count
    $modifier_count = get_post_meta($product_id, '_squarekit_modifier_count', true);
    echo "Modifier count meta: " . ($modifier_count ? "✅ {$modifier_count}" : '❌ Missing') . "<br>\n";
    
    // Step 4: Test admin interface display
    echo "<h2>🖥️ Step 4: Testing Admin Interface</h2>\n";
    
    if (!empty($swever_modifiers)) {
        echo "✅ Modifiers should display in admin interface<br>\n";
        echo "Expected behavior:<br>\n";
        echo "  - Modifier set names should show: ";
        foreach ($swever_modifiers as $set) {
            echo "'{$set['set_name']}' ";
        }
        echo "<br>\n";
        echo "  - Square IDs should show in modifier list ID field<br>\n";
        echo "  - Options should be populated with names and prices<br>\n";
    } else {
        echo "❌ No modifiers to display in admin interface<br>\n";
    }
    
    // Step 5: Test frontend display
    echo "<h2>🌐 Step 5: Testing Frontend Display</h2>\n";
    
    $product_url = get_permalink($product_id);
    echo "Product URL: <a href='{$product_url}' target='_blank'>{$product_url}</a><br>\n";
    
    if (!empty($swever_modifiers)) {
        echo "✅ Modifiers should display on frontend under 'Customization Options'<br>\n";
        echo "Expected behavior:<br>\n";
        echo "  - Section should appear below variations/attributes<br>\n";
        echo "  - Each modifier set should show as radio buttons or checkboxes<br>\n";
        echo "  - Prices should be displayed correctly<br>\n";
    } else {
        echo "❌ No modifiers to display on frontend<br>\n";
    }
    
    echo "<h2>📊 Test Summary</h2>\n";
    
    if (!empty($swever_modifiers) && !empty($legacy_modifiers) && $modifier_count > 0) {
        echo "🎉 <strong>SUCCESS!</strong> Modifier import fix is working correctly.<br>\n";
        echo "✅ Modifiers are properly imported and persisted to database<br>\n";
        echo "✅ Both SWEVER and legacy formats are saved<br>\n";
        echo "✅ Modifier count is tracked<br>\n";
    } else {
        echo "❌ <strong>FAILURE!</strong> Modifier import is still broken.<br>\n";
        echo "Issues found:<br>\n";
        if (empty($swever_modifiers)) echo "  - SWEVER modifier sets not saved<br>\n";
        if (empty($legacy_modifiers)) echo "  - Legacy modifier sets not saved<br>\n";
        if (!$modifier_count) echo "  - Modifier count not saved<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception: " . $e->getMessage() . "<br>\n";
    echo "Stack trace:<br>\n<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<br>\n<em>Test completed at " . date('Y-m-d H:i:s') . "</em>\n";
