# Enhanced Square Kit Image Import System

## Overview

The Square Kit plugin now includes a comprehensive, enhanced image import system that addresses the previous issues with Square product image imports. This system provides robust error handling, performance optimizations, detailed logging, and comprehensive testing tools.

## Key Features

### 🚀 Performance Enhancements
- **Batch Image Retrieval**: Efficiently fetches multiple images from Square's API in single requests
- **Image Caching**: Caches image URLs to reduce redundant API calls
- **Duplicate Detection**: Prevents importing the same image multiple times
- **Retry Logic**: Automatically retries failed downloads with exponential backoff
- **Parallel Processing**: Optimized for handling large image galleries

### 🛡️ Robust Error Handling
- **Comprehensive Validation**: Validates URLs, file types, and sizes before import
- **Detailed Error Logging**: Tracks all import failures with specific error messages
- **Graceful Degradation**: Continues importing other images even if some fail
- **HTTP Status Handling**: Properly handles various HTTP response codes

### 📊 Advanced Logging & Debugging
- **Success/Failure Tracking**: Maintains detailed statistics on import performance
- **Debug Tools**: Built-in debugging utilities for troubleshooting import issues
- **Performance Metrics**: Tracks import speeds and success rates
- **Admin Interface**: Easy-to-use testing interface in WordPress admin

### 🔧 Enhanced API Integration
- **Optimized Square API Usage**: Uses batch retrieval and proper image mapping
- **Image Map Caching**: Efficiently maps Square image IDs to URLs
- **API Rate Limiting**: Respects Square's API limits with proper throttling

## Architecture

### Core Classes

#### SquareKit_Square_API (Enhanced)
- `batch_retrieve_catalog_images()` - Fetch multiple images in one API call
- `get_image_urls_by_ids()` - Get image URLs with caching
- `create_comprehensive_image_map()` - Build efficient image ID to URL mapping
- `get_image_cache_stats()` - Monitor cache performance

#### SquareKit_WooCommerce (Enhanced)
- `import_product_images_enhanced()` - Main enhanced image import method
- `import_product_gallery_enhanced()` - Advanced gallery import with error handling
- `debug_image_import()` - Debug individual image imports
- `get_image_import_stats()` - Get detailed import statistics

### Data Flow

1. **Catalog Retrieval**: Fetch both ITEM and IMAGE objects from Square
2. **Image Mapping**: Create comprehensive image ID to URL mapping
3. **Product Processing**: Extract image IDs from product data
4. **Enhanced Import**: Use optimized import methods with error handling
5. **Logging**: Track all import attempts and results

## Usage Examples

### Basic Product Import with Images
```php
$wc = new SquareKit_WooCommerce();
$square_api = new SquareKit_Square_API();

// Get catalog with images
$catalog = $square_api->get_catalog(['types' => 'ITEM,IMAGE']);
$image_map = $square_api->create_comprehensive_image_map($catalog);

// Import product with images
$result = $wc->import_product_from_square($item, $image_map);
```

### Debug Image Import Issues
```php
$wc = new SquareKit_WooCommerce();
$debug_info = $wc->debug_image_import('https://example.com/image.jpg');

// Check results
if (!$debug_info['checks']['url_valid']) {
    echo "Invalid URL";
}
if (!$debug_info['checks']['http_accessible']) {
    echo "Cannot access image: " . $debug_info['http_error'];
}
```

### Get Import Statistics
```php
$wc = new SquareKit_WooCommerce();
$stats = $wc->get_image_import_stats(7); // Last 7 days

echo "Success rate: " . $stats['success_rate_percent'] . "%";
echo "Total attempts: " . $stats['total_attempts'];
```

## Configuration

### Settings
The enhanced image import system respects existing Square Kit settings:
- Image optimization settings
- Maximum image dimensions
- Quality settings
- Timeout configurations

### Environment Variables
For optimal performance, consider setting:
```php
// Increase memory limit for large images
ini_set('memory_limit', '512M');

// Increase execution time for bulk imports
ini_set('max_execution_time', 300);
```

## Testing

### Admin Interface
Navigate to **Square Kit > Image Tests** in WordPress admin to:
- Test image URL validation
- Debug import issues
- View import statistics
- Run comprehensive tests

### Command Line Testing
Run the comprehensive test suite:
```bash
php wp-content/plugins/squarekit/test-image-imports.php
```

### Test Coverage
The test suite covers:
- ✅ Single image imports
- ✅ Gallery imports with multiple images
- ✅ Error handling for invalid URLs
- ✅ HTTP error responses (404, 500, etc.)
- ✅ Image format validation
- ✅ Duplicate detection
- ✅ Performance metrics
- ✅ Cache functionality

## Troubleshooting

### Common Issues

#### Images Not Importing
1. **Check Square API Connection**: Verify OAuth credentials are valid
2. **Test Image URLs**: Use the debug tool to test specific image URLs
3. **Review Error Logs**: Check WordPress error logs for detailed messages
4. **Verify Permissions**: Ensure uploads directory is writable

#### Poor Performance
1. **Check Memory Limits**: Increase PHP memory limit if needed
2. **Monitor API Calls**: Use cache statistics to optimize API usage
3. **Review Image Sizes**: Large images may need optimization
4. **Check Network**: Slow downloads may indicate network issues

#### Duplicate Images
1. **Clear Cache**: Use `$square_api->clear_image_cache()` if needed
2. **Check Source URLs**: Verify images have consistent URLs
3. **Review Import Logic**: Ensure proper duplicate detection

### Debug Tools

#### Image Import Debugger
```php
$debug_info = $wc->debug_image_import($image_url);
print_r($debug_info);
```

#### Cache Statistics
```php
$cache_stats = $square_api->get_image_cache_stats();
echo "Cached images: " . $cache_stats['cached_images'];
```

#### Import Statistics
```php
$stats = $wc->get_image_import_stats(30); // Last 30 days
echo "Success rate: " . $stats['success_rate_percent'] . "%";
```

## Performance Optimization

### Best Practices
1. **Use Batch Operations**: Import multiple products at once for efficiency
2. **Monitor Cache**: Keep image cache populated for faster imports
3. **Optimize Images**: Enable image optimization in settings
4. **Schedule Imports**: Use bulk operations for large catalogs

### Monitoring
- Track import success rates through admin interface
- Monitor API usage to stay within Square's limits
- Review error logs regularly for patterns
- Use performance metrics to optimize import timing

## API Reference

### Enhanced Methods

#### SquareKit_Square_API
- `batch_retrieve_catalog_images(array $image_ids)` - Batch fetch images
- `get_image_urls_by_ids(array $image_ids)` - Get URLs with caching
- `create_comprehensive_image_map(array $catalog)` - Create image mapping
- `clear_image_cache()` - Clear image URL cache
- `get_image_cache_stats()` - Get cache statistics

#### SquareKit_WooCommerce
- `import_product_images_enhanced(array $image_ids, int $product_id, array $image_map)` - Enhanced import
- `import_product_gallery_enhanced(array $image_urls, int $product_id)` - Enhanced gallery import
- `debug_image_import(string $url)` - Debug image import
- `get_image_import_stats(int $days)` - Get import statistics
- `log_image_import_success(string $url, int $attachment_id, int $product_id, array $metadata)` - Log success
- `log_image_import_error(string $url, string $error)` - Log errors

## Migration from Previous Version

The enhanced image import system is backward compatible. Existing imports will continue to work, but new imports will automatically use the enhanced methods.

### Recommended Steps
1. **Test Current Setup**: Use the admin testing interface
2. **Review Statistics**: Check import success rates
3. **Update Bulk Operations**: Ensure they use enhanced methods
4. **Monitor Performance**: Track improvements in import speed and reliability

## Support

For issues with the enhanced image import system:
1. Use the built-in debugging tools
2. Review the comprehensive error logs
3. Check the troubleshooting guide above
4. Contact support with specific error messages and debug output
