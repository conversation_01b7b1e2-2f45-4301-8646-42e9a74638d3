<?php
/**
 * Debug OAuth Settings
 * 
 * Temporary debug file to check OAuth configuration
 * Delete this file after debugging
 */

// Load WordPress
require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );

// Check if user is admin
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Access denied' );
}

// Load SquareKit settings
require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );

$settings = new SquareKit_Settings();

echo '<h1>SquareKit OAuth Debug Information</h1>';

echo '<h2>Current Settings</h2>';
echo '<p><strong>Environment:</strong> ' . esc_html( $settings->get_environment() ) . '</p>';

$environment = $settings->get_environment();
$app_id = $settings->get( $environment . '_application_id', 'NOT SET' );
echo '<p><strong>Application ID:</strong> ' . esc_html( $app_id ) . '</p>';
echo '<p><strong>Client Secret:</strong> ' . ( ! empty( $settings->get( $environment . '_client_secret', '' ) ) ? 'SET' : 'NOT SET' ) . '</p>';
echo '<p><strong>Access Token:</strong> ' . ( ! empty( $settings->get( $environment . '_access_token', '' ) ) ? 'SET' : 'NOT SET' ) . '</p>';
echo '<p><strong>Location ID:</strong> ' . esc_html( $settings->get( 'location_id', 'NOT SET' ) ) . '</p>';

echo '<h2>Application ID Analysis</h2>';
if ( $app_id !== 'NOT SET' ) {
    if ( strpos( $app_id, 'sandbox-' ) === 0 ) {
        echo '<p><span style="color: green;">✓</span> <strong>Application ID Format:</strong> Sandbox format detected</p>';
        if ( $environment === 'sandbox' ) {
            echo '<p><span style="color: green;">✓</span> <strong>Environment Match:</strong> Sandbox ID with Sandbox environment - CORRECT</p>';
        } else {
            echo '<p><span style="color: red;">✗</span> <strong>Environment Mismatch:</strong> Sandbox ID with Production environment - INCORRECT</p>';
        }
    } elseif ( strpos( $app_id, 'sq0idp-' ) === 0 ) {
        echo '<p><span style="color: blue;">ℹ</span> <strong>Application ID Format:</strong> Production format detected</p>';
        if ( $environment === 'production' ) {
            echo '<p><span style="color: green;">✓</span> <strong>Environment Match:</strong> Production ID with Production environment - CORRECT</p>';
        } else {
            echo '<p><span style="color: red;">✗</span> <strong>Environment Mismatch:</strong> Production ID with Sandbox environment - INCORRECT</p>';
        }
    } else {
        echo '<p><span style="color: orange;">⚠</span> <strong>Application ID Format:</strong> Unknown format - please verify</p>';
    }
} else {
    echo '<p><span style="color: red;">✗</span> <strong>Application ID:</strong> Not set</p>';
}

echo '<h2>Both Environment Credentials</h2>';
echo '<h3>Sandbox Credentials</h3>';
$sandbox_app_id = $settings->get( 'sandbox_application_id', 'NOT SET' );
echo '<p><strong>Sandbox Application ID:</strong> ' . esc_html( $sandbox_app_id ) . '</p>';
echo '<p><strong>Sandbox Client Secret:</strong> ' . ( ! empty( $settings->get( 'sandbox_client_secret', '' ) ) ? 'SET' : 'NOT SET' ) . '</p>';

echo '<h3>Production Credentials</h3>';
$production_app_id = $settings->get( 'production_application_id', 'NOT SET' );
echo '<p><strong>Production Application ID:</strong> ' . esc_html( $production_app_id ) . '</p>';
echo '<p><strong>Production Client Secret:</strong> ' . ( ! empty( $settings->get( 'production_client_secret', '' ) ) ? 'SET' : 'NOT SET' ) . '</p>';

echo '<h2>OAuth URLs</h2>';
$redirect_uri = admin_url( 'admin.php?page=squarekit-wizard&step=connect' );
echo '<p><strong>Redirect URI:</strong> <code>' . esc_html( $redirect_uri ) . '</code></p>';

$client_id = $settings->get( $environment . '_application_id', '' );
if ( ! empty( $client_id ) ) {
    $oauth_base_url = ( $environment === 'sandbox' ? 'https://connect.squareupsandbox.com' : 'https://connect.squareup.com' );
    $oauth_url = $oauth_base_url . '/oauth2/authorize?client_id=' . esc_attr( $client_id ) . '&scope=MERCHANT_PROFILE_READ+PAYMENTS_READ+PAYMENTS_WRITE+ORDERS_READ+ORDERS_WRITE+CUSTOMERS_READ+CUSTOMERS_WRITE+INVENTORY_READ+INVENTORY_WRITE&session=false&state=' . wp_create_nonce( 'squarekit_oauth' ) . '&redirect_uri=' . urlencode( $redirect_uri );
    echo '<p><strong>OAuth Base URL:</strong> ' . esc_html( $oauth_base_url ) . ' (' . esc_html( $environment ) . ')</p>';
    echo '<p><strong>OAuth URL:</strong> <a href="' . esc_url( $oauth_url ) . '" target="_blank">Test OAuth Flow</a></p>';
    echo '<p><strong>Full OAuth URL:</strong><br><code>' . esc_html( $oauth_url ) . '</code></p>';
} else {
    echo '<p><strong>OAuth URL:</strong> Cannot generate - Application ID not set</p>';
}

echo '<h2>URL Parameters (if this is an OAuth callback)</h2>';
if ( ! empty( $_GET ) ) {
    foreach ( $_GET as $key => $value ) {
        echo '<p><strong>' . esc_html( $key ) . ':</strong> ' . esc_html( $value ) . '</p>';
    }
} else {
    echo '<p>No URL parameters present</p>';
}

echo '<h2>Nonce Verification</h2>';
if ( isset( $_GET['state'] ) ) {
    $nonce_valid = wp_verify_nonce( $_GET['state'], 'squarekit_oauth' );
    echo '<p><strong>Nonce Valid:</strong> ' . ( $nonce_valid ? 'YES' : 'NO' ) . '</p>';
} else {
    echo '<p>No state parameter to verify</p>';
}

echo '<h2>Instructions</h2>';
echo '<ol>';
echo '<li>Make sure the redirect URI above is registered in your Square Developer Console</li>';
echo '<li>Verify your Application ID and Client Secret are correct</li>';
echo '<li>Test the OAuth flow using the link above</li>';
echo '<li>Check the debug log at /wp-content/debug.log after testing</li>';
echo '</ol>';

echo '<p><strong>Remember to delete this debug file when done!</strong></p>';
?>
