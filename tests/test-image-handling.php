<?php
/**
 * Test file for enhanced image handling functionality
 *
 * @package SquareKit
 * @subpackage SquareKit/tests
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Test class for image handling functionality
 */
class SquareKit_Image_Handling_Test {

    /**
     * Test image import from URL
     */
    public function test_image_import_from_url() {
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        
        // Test with a sample image URL
        $test_url = 'https://via.placeholder.com/300x200/007cba/ffffff?text=Test+Image';
        
        // Test the import function
        $attachment_id = $wc->import_image_from_url($test_url);
        
        if ($attachment_id) {
            echo "✓ Image import successful. Attachment ID: " . $attachment_id . "\n";
            
            // Test if image exists
            $attachment_url = wp_get_attachment_url($attachment_id);
            if ($attachment_url) {
                echo "✓ Image URL retrieved: " . $attachment_url . "\n";
            } else {
                echo "✗ Failed to get attachment URL\n";
            }
        } else {
            echo "✗ Image import failed\n";
        }
    }

    /**
     * Test gallery import
     */
    public function test_gallery_import() {
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        
        // Test with multiple image URLs
        $test_urls = array(
            'https://via.placeholder.com/300x200/007cba/ffffff?text=Main+Image',
            'https://via.placeholder.com/300x200/28a745/ffffff?text=Gallery+1',
            'https://via.placeholder.com/300x200/dc3545/ffffff?text=Gallery+2',
        );
        
        // Create a test product
        $product = new WC_Product_Simple();
        $product->set_name('Test Product for Gallery');
        $product_id = $product->save();
        
        // Test gallery import
        $attachment_ids = $wc->import_product_gallery($test_urls, $product_id);
        
        if (!empty($attachment_ids)) {
            echo "✓ Gallery import successful. " . count($attachment_ids) . " images imported.\n";
            
            // Check if main image is set
            $product = wc_get_product($product_id);
            $main_image_id = $product->get_image_id();
            if ($main_image_id) {
                echo "✓ Main image set: " . $main_image_id . "\n";
            } else {
                echo "✗ Main image not set\n";
            }
            
            // Check gallery images
            $gallery_ids = $product->get_gallery_image_ids();
            if (!empty($gallery_ids)) {
                echo "✓ Gallery images set: " . implode(', ', $gallery_ids) . "\n";
            } else {
                echo "✗ Gallery images not set\n";
            }
        } else {
            echo "✗ Gallery import failed\n";
        }
        
        // Clean up test product
        wp_delete_post($product_id, true);
    }

    /**
     * Test image optimization
     */
    public function test_image_optimization() {
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        
        // Create a test image (this would need GD library)
        if (function_exists('imagecreatetruecolor')) {
            $width = 2000;
            $height = 1500;
            $image = imagecreatetruecolor($width, $height);
            
            // Fill with a color
            $color = imagecolorallocate($image, 0, 124, 186);
            imagefill($image, 0, 0, $color);
            
            // Output to buffer
            ob_start();
            imagejpeg($image, null, 90);
            $image_data = ob_get_clean();
            imagedestroy($image);
            
            // Test optimization
            $optimized_data = $wc->optimize_image($image_data, 'image/jpeg');
            
            if ($optimized_data !== $image_data) {
                echo "✓ Image optimization successful\n";
                echo "Original size: " . strlen($image_data) . " bytes\n";
                echo "Optimized size: " . strlen($optimized_data) . " bytes\n";
            } else {
                echo "✗ Image optimization failed or not needed\n";
            }
        } else {
            echo "⚠ GD library not available for optimization test\n";
        }
    }

    /**
     * Run all tests
     */
    public function run_all_tests() {
        echo "Running SquareKit Image Handling Tests\n";
        echo "=====================================\n\n";
        
        $this->test_image_import_from_url();
        echo "\n";
        
        $this->test_gallery_import();
        echo "\n";
        
        $this->test_image_optimization();
        echo "\n";
        
        echo "Tests completed.\n";
    }
}

// Run tests if this file is executed directly
if (defined('WP_CLI') && WP_CLI) {
    $test = new SquareKit_Image_Handling_Test();
    $test->run_all_tests();
} 