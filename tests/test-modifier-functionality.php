<?php
/**
 * SquareKit Modifier Functionality Test
 * 
 * Test the new SWEVER-compatible modifier functionality
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Load WordPress if not already loaded
    require_once dirname( __FILE__ ) . '/../../../wp-load.php';
}

// Check if WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    die( 'WooCommerce is required for this test.' );
}

// Load SquareKit classes
require_once dirname( __FILE__ ) . '/includes/integrations/class-squarekit-woocommerce.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SquareKit Modifier Functionality Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: 600;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .button {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .button:hover {
            background: #5a6fd8;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🧪 SquareKit Modifier Functionality Test</h1>
        <p>Testing SWEVER-compatible modifier implementation</p>
        <p><strong>Test Environment:</strong> <?php echo home_url(); ?></p>
        <p><strong>WordPress Version:</strong> <?php echo get_bloginfo( 'version' ); ?></p>
        <p><strong>Test Time:</strong> <?php echo current_time( 'Y-m-d H:i:s' ); ?></p>
    </div>

    <div class="test-container">
        <?php
        
        // Test 1: Class Availability
        echo '<div class="test-section">';
        echo '<h3>🔬 Class Availability Check</h3>';
        
        $required_classes = array(
            'SquareKit_WooCommerce',
            'WC_Product',
            'WC_Product_Simple'
        );
        
        $all_classes_available = true;
        foreach ( $required_classes as $class ) {
            if ( class_exists( $class ) ) {
                echo "<div class='status success'>✓ {$class} is available</div>";
            } else {
                echo "<div class='status error'>✗ {$class} is missing</div>";
                $all_classes_available = false;
            }
        }
        
        if ( $all_classes_available ) {
            echo '<div class="status success">All required classes are available ✓</div>';
        } else {
            echo '<div class="status error">Some required classes are missing ✗</div>';
        }
        echo '</div>';

        // Test 2: Create Test Product with Modifiers
        echo '<div class="test-section">';
        echo '<h3>🔬 Test Product Creation</h3>';
        
        try {
            // Create a test product
            $product = new WC_Product_Simple();
            $product->set_name( 'Test Product with Modifiers' );
            $product->set_regular_price( 10.00 );
            $product->set_status( 'publish' );
            $product_id = $product->save();
            
            if ( $product_id ) {
                echo "<div class='status success'>✓ Test product created with ID: {$product_id}</div>";
                
                // Add test modifier data in SWEVER format
                $test_modifiers = array(
                    array(
                        'set_name' => 'Entourage Effect',
                        'square_mod_list_id' => 'WEYL7DNTWBB4MQOIXMYWPK2J',
                        'single_choice' => true,
                        'source' => 'square',
                        'options' => array(
                            array(
                                'name' => 'Light',
                                'price' => 10.00,
                                'stock' => '',
                                'square_mod_id' => 'FUHJVLUB7R7WDTI'
                            ),
                            array(
                                'name' => 'Strong',
                                'price' => 20.00,
                                'stock' => '',
                                'square_mod_id' => 'WYEIDMWETUFAT5'
                            ),
                            array(
                                'name' => 'Very Strong',
                                'price' => 30.00,
                                'stock' => '',
                                'square_mod_id' => '4KF77S2YWR643A'
                            ),
                            array(
                                'name' => 'Double Dragon',
                                'price' => 40.00,
                                'stock' => '',
                                'square_mod_id' => 'QBF37L6UX3ZBG5'
                            )
                        )
                    ),
                    array(
                        'set_name' => 'Size',
                        'square_mod_list_id' => '4FKHEXTJ4JA34P',
                        'single_choice' => true,
                        'source' => 'square',
                        'options' => array(
                            array(
                                'name' => 'Small',
                                'price' => 0.00,
                                'stock' => '',
                                'square_mod_id' => 'QN5HTH7RV7R2TM'
                            ),
                            array(
                                'name' => 'Large',
                                'price' => 5.00,
                                'stock' => '',
                                'square_mod_id' => '22VUNKQN3MZHZC'
                            )
                        )
                    )
                );
                
                update_post_meta( $product_id, '_squarekit_modifier_sets', $test_modifiers );
                echo '<div class="status success">✓ Test modifier data added to product</div>';
                
                echo '<div class="test-data">';
                echo '<strong>Test Modifier Data:</strong><br>';
                echo '<pre>' . print_r( $test_modifiers, true ) . '</pre>';
                echo '</div>';
                
                echo "<div class='status info'>📝 <a href='" . admin_url( "post.php?post={$product_id}&action=edit" ) . "' target='_blank'>Edit Product in Admin</a></div>";
                echo "<div class='status info'>👁️ <a href='" . get_permalink( $product_id ) . "' target='_blank'>View Product on Frontend</a></div>";
                
            } else {
                echo '<div class="status error">✗ Failed to create test product</div>';
            }
            
        } catch ( Exception $e ) {
            echo '<div class="status error">✗ Error creating test product: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';

        // Test 3: WooCommerce Integration Test
        echo '<div class="test-section">';
        echo '<h3>🔬 WooCommerce Integration Test</h3>';
        
        try {
            $wc_integration = new SquareKit_WooCommerce();
            echo '<div class="status success">✓ SquareKit_WooCommerce instance created</div>';
            
            // Test migration function
            $migrated_count = $wc_integration->migrate_existing_modifiers();
            echo "<div class='status info'>📊 Migrated {$migrated_count} products to new format</div>";
            
        } catch ( Exception $e ) {
            echo '<div class="status error">✗ Error testing WooCommerce integration: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';

        // Test 4: Frontend Display Test
        if ( isset( $product_id ) && $product_id ) {
            echo '<div class="test-section">';
            echo '<h3>🔬 Frontend Display Test</h3>';
            
            // Simulate frontend display
            global $product;
            $product = wc_get_product( $product_id );
            
            if ( $product ) {
                echo '<div class="status success">✓ Product loaded for frontend test</div>';
                
                // Test modifier display
                ob_start();
                $wc_integration->display_product_modifiers();
                $modifier_html = ob_get_clean();
                
                if ( ! empty( $modifier_html ) ) {
                    echo '<div class="status success">✓ Modifier HTML generated successfully</div>';
                    echo '<div class="test-data">';
                    echo '<strong>Generated Modifier HTML:</strong><br>';
                    echo '<textarea rows="10" cols="80" readonly>' . esc_textarea( $modifier_html ) . '</textarea>';
                    echo '</div>';
                } else {
                    echo '<div class="status error">✗ No modifier HTML generated</div>';
                }
            } else {
                echo '<div class="status error">✗ Could not load product for frontend test</div>';
            }
            echo '</div>';
        }

        ?>
        
        <div class="test-section">
            <h3>🚀 Next Steps</h3>
            <p>If all tests passed, the modifier functionality should be working correctly:</p>
            <ul>
                <li><strong>Backend:</strong> Admin panel should show full modifier management interface</li>
                <li><strong>Frontend:</strong> Product pages should display beautiful modifier options</li>
                <li><strong>Cart:</strong> Selected modifiers should adjust prices and appear in cart</li>
                <li><strong>Checkout:</strong> Modifiers should be saved to orders</li>
            </ul>
            
            <h4>Test Actions:</h4>
            <a href="<?php echo admin_url( 'edit.php?post_type=product' ); ?>" class="button">View All Products</a>
            <a href="<?php echo admin_url( 'admin.php?page=squarekit-settings' ); ?>" class="button secondary">SquareKit Settings</a>
            <button onclick="location.reload()" class="button secondary">Refresh Tests</button>
        </div>
    </div>

    <div style="text-align: center; margin-top: 30px; color: #666;">
        <p>🔗 <strong>Useful Links:</strong> 
        <a href="<?php echo admin_url(); ?>">Dashboard</a> | 
        <a href="<?php echo admin_url( 'admin.php?page=squarekit-settings' ); ?>">Settings</a> | 
        <a href="<?php echo admin_url( 'edit.php?post_type=product' ); ?>">Products</a> | 
        <a href="javascript:location.reload()">Refresh Tests</a>
        </p>
    </div>
</body>
</html>
