<?php
/**
 * Verify Class Loading
 * 
 * Quick test to verify that the new importer classes are properly loaded.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

echo "<h1>Class Loading Verification</h1>\n";
echo "<pre>\n";

// Test if SquareKit plugin is active
if ( ! defined( 'SQUAREKIT_PLUGIN_DIR' ) ) {
    echo "❌ SquareKit plugin is not active or not properly loaded.\n";
    echo "Please make sure the SquareKit plugin is activated.\n";
    exit;
}

echo "✅ SquareKit plugin is active\n";
echo "Plugin directory: " . SQUAREKIT_PLUGIN_DIR . "\n";

// Check if new class files exist
$new_files = array(
    'includes/importers/class-squarekit-attribute-importer.php',
    'includes/importers/class-squarekit-variation-importer.php', 
    'includes/importers/class-squarekit-modifier-importer.php',
    'includes/utils/class-squarekit-price-calculator.php'
);

echo "\n=== File Existence Check ===\n";
$all_files_exist = true;
foreach ( $new_files as $file ) {
    $full_path = SQUAREKIT_PLUGIN_DIR . $file;
    if ( file_exists( $full_path ) ) {
        echo "✅ {$file} exists\n";
    } else {
        echo "❌ {$file} NOT found at {$full_path}\n";
        $all_files_exist = false;
    }
}

if ( ! $all_files_exist ) {
    echo "\n❌ Some files are missing. Please check that all new files were created properly.\n";
    exit;
}

// Check if classes are loaded
$new_classes = array(
    'SquareKit_Attribute_Importer',
    'SquareKit_Variation_Importer',
    'SquareKit_Modifier_Importer', 
    'SquareKit_Price_Calculator'
);

echo "\n=== Class Loading Check ===\n";
$classes_loaded = 0;
foreach ( $new_classes as $class ) {
    if ( class_exists( $class ) ) {
        echo "✅ {$class} is loaded\n";
        $classes_loaded++;
    } else {
        echo "❌ {$class} is NOT loaded\n";
    }
}

// If classes aren't loaded, try manual loading
if ( $classes_loaded < count( $new_classes ) ) {
    echo "\n=== Manual Loading Attempt ===\n";
    
    foreach ( $new_files as $index => $file ) {
        $class = $new_classes[$index];
        $full_path = SQUAREKIT_PLUGIN_DIR . $file;
        
        if ( ! class_exists( $class ) ) {
            echo "Attempting to load {$class} from {$file}...\n";
            
            try {
                require_once $full_path;
                
                if ( class_exists( $class ) ) {
                    echo "✅ Successfully loaded {$class}\n";
                    $classes_loaded++;
                } else {
                    echo "❌ File loaded but class {$class} not found\n";
                }
            } catch ( Exception $e ) {
                echo "❌ Error loading {$file}: " . $e->getMessage() . "\n";
            } catch ( ParseError $e ) {
                echo "❌ Parse error in {$file}: " . $e->getMessage() . "\n";
            } catch ( Error $e ) {
                echo "❌ Fatal error in {$file}: " . $e->getMessage() . "\n";
            }
        }
    }
}

// Test class instantiation
echo "\n=== Class Instantiation Test ===\n";
foreach ( $new_classes as $class ) {
    if ( class_exists( $class ) ) {
        try {
            $instance = new $class();
            echo "✅ {$class} can be instantiated\n";
        } catch ( Exception $e ) {
            echo "❌ Error instantiating {$class}: " . $e->getMessage() . "\n";
        } catch ( Error $e ) {
            echo "❌ Fatal error instantiating {$class}: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️  {$class} not available for instantiation test\n";
    }
}

echo "\n=== Summary ===\n";
if ( $classes_loaded === count( $new_classes ) ) {
    echo "✅ All {$classes_loaded} new classes are properly loaded!\n";
    echo "✅ Ready to run the full import system test\n";
    echo "\nNext step: Run test-fixed-import-system.php\n";
} else {
    echo "❌ Only {$classes_loaded}/" . count( $new_classes ) . " classes are loaded.\n";
    echo "\n🔧 Troubleshooting steps:\n";
    echo "1. Deactivate and reactivate the SquareKit plugin\n";
    echo "2. Check for PHP syntax errors in the new files\n";
    echo "3. Verify file permissions\n";
    echo "4. Check WordPress error logs\n";
}

echo "</pre>\n";
?>
