<?php
/**
 * Test Price Calculation Fix
 * 
 * This file tests the fixed price calculation logic to ensure modifiers
 * display and calculate correctly.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;

    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }

    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Testing Price Calculation Fix</h2>\n";
echo "<style>
.test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.test-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.test-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.success { color: #00a32a; }
.error { color: #d63638; }
.warning { color: #dba617; }
.fix-highlight { background: #e6ffe6; padding: 5px; border-left: 3px solid #00a32a; }
</style>\n";

// Get the product ID from URL parameter or use a default
$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 0;

if ( ! $product_id ) {
    // Try to find a product with modifiers
    $products_with_modifiers = get_posts( array(
        'post_type' => 'product',
        'meta_query' => array(
            array(
                'key' => '_squarekit_modifiers',
                'compare' => 'EXISTS'
            )
        ),
        'posts_per_page' => 1,
        'fields' => 'ids'
    ) );
    
    if ( ! empty( $products_with_modifiers ) ) {
        $product_id = $products_with_modifiers[0];
    }
}

if ( ! $product_id ) {
    echo "<div class='test-section error'>No product ID provided and no products with modifiers found. Please add ?product_id=XXX to the URL.</div>\n";
    exit;
}

echo "<div class='test-section'>";
echo "<div class='test-title'>Testing Product: $product_id</div>";
echo "<div class='test-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span class='error'>Product not found with ID: $product_id</span>\n";
    exit;
}

echo "Product Name: " . $product->get_name() . "<br>\n";
echo "Product Type: " . $product->get_type() . "<br>\n";
echo "Product Price: " . $product->get_price() . "<br>\n";

echo "</div></div>\n";

// Test the base price calculation logic
echo "<div class='test-section'>";
echo "<div class='test-title'>Base Price Calculation Test</div>";
echo "<div class='test-data'>";

$base_price = 0;
if ( $product->is_type( 'variable' ) ) {
    echo "<h4>Variable Product Base Price Calculation:</h4>\n";
    $variations = $product->get_available_variations();
    if ( ! empty( $variations ) ) {
        $prices = array();
        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            if ( $variation && $variation->get_price() !== '' ) {
                $price = floatval( $variation->get_price() );
                $prices[] = $price;
                echo "Variation " . $variation->get_id() . ": $" . number_format( $price, 2 ) . "<br>\n";
            }
        }
        if ( ! empty( $prices ) ) {
            $base_price = min( $prices );
            echo "<div class='fix-highlight'><strong>Calculated Base Price: $" . number_format( $base_price, 2 ) . "</strong></div>\n";
        }
    } else {
        echo "<span class='warning'>No variations found</span><br>\n";
    }
} else {
    echo "<h4>Simple Product Base Price:</h4>\n";
    $base_price = floatval( $product->get_price() );
    echo "<div class='fix-highlight'><strong>Base Price: $" . number_format( $base_price, 2 ) . "</strong></div>\n";
}

echo "</div></div>\n";

// Test the JavaScript localization data
echo "<div class='test-section'>";
echo "<div class='test-title'>JavaScript Localization Data Test</div>";
echo "<div class='test-data'>";

$localization_data = array(
    'base_price' => $base_price,
    'currency_symbol' => get_woocommerce_currency_symbol(),
    'currency_position' => get_option( 'woocommerce_currency_pos' ),
    'price_decimal_sep' => wc_get_price_decimal_separator(),
    'price_thousand_sep' => wc_get_price_thousand_separator(),
    'price_decimals' => wc_get_price_decimals(),
);

echo "<h4>Data passed to JavaScript:</h4>\n";
echo "<pre>" . print_r( $localization_data, true ) . "</pre>\n";

echo "</div></div>\n";

// Test modifier data and pricing
$modifiers = get_post_meta( $product_id, '_squarekit_modifiers', true );
if ( ! empty( $modifiers ) ) {
    echo "<div class='test-section'>";
    echo "<div class='test-title'>Modifier Pricing Test</div>";
    echo "<div class='test-data'>";
    
    foreach ( $modifiers as $set_index => $set ) {
        $set_name = $set['set_name'] ?? 'Unnamed Set';
        echo "<h4>$set_name (Set $set_index):</h4>\n";
        
        if ( ! empty( $set['options'] ) ) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
            echo "<tr><th>Option</th><th>Price</th><th>Formatted</th><th>Total with Base</th></tr>\n";
            
            foreach ( $set['options'] as $option_index => $option ) {
                $name = $option['name'] ?? 'Unnamed';
                $price = floatval( $option['price'] ?? 0 );
                $total_with_base = $base_price + $price;
                
                echo "<tr>";
                echo "<td>" . esc_html( $name ) . "</td>";
                echo "<td>$" . number_format( $price, 2 ) . "</td>";
                echo "<td>" . wc_price( $price ) . "</td>";
                echo "<td class='fix-highlight'>$" . number_format( $total_with_base, 2 ) . "</td>";
                echo "</tr>\n";
            }
            
            echo "</table>\n";
        }
    }
    
    echo "</div></div>\n";
}

// Test JavaScript price formatting
echo "<div class='test-section'>";
echo "<div class='test-title'>JavaScript Price Formatting Test</div>";
echo "<div class='test-data'>";

echo "<h4>Test Price Formatting Logic:</h4>\n";

$test_prices = array( 5.00, 10.00, 25.00, 65.00 );
foreach ( $test_prices as $test_price ) {
    echo "Price: $" . number_format( $test_price, 2 ) . " → WC Formatted: " . wc_price( $test_price ) . "<br>\n";
}

echo "</div></div>\n";

// JavaScript test code
echo "<div class='test-section'>";
echo "<div class='test-title'>Frontend JavaScript Test</div>";
echo "<div class='test-data'>";

echo "<h4>JavaScript Test Code:</h4>\n";
echo "<p>Open browser console and run: <code>squareKitDebug()</code> to see pricing debug info.</p>\n";

echo "<script>
// Simulate the localization data
window.squarekit_frontend = " . json_encode( $localization_data ) . ";

// Test price formatting function
function testPriceFormatting(price) {
    let decimals = parseInt(squarekit_frontend.price_decimals) || 2;
    let decimalSep = squarekit_frontend.price_decimal_sep || '.';
    let thousandSep = squarekit_frontend.price_thousand_sep || ',';
    let currencySymbol = squarekit_frontend.currency_symbol || '$';
    let currencyPosition = squarekit_frontend.currency_position || 'left';
    
    // Format the number
    let formattedNumber = price.toFixed(decimals);
    
    // Add thousand separators if needed
    if (thousandSep && price >= 1000) {
        let parts = formattedNumber.split(decimalSep);
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandSep);
        formattedNumber = parts.join(decimalSep);
    }
    
    // Add currency symbol based on position
    switch (currencyPosition) {
        case 'left':
            return currencySymbol + formattedNumber;
        case 'right':
            return formattedNumber + currencySymbol;
        case 'left_space':
            return currencySymbol + ' ' + formattedNumber;
        case 'right_space':
            return formattedNumber + ' ' + currencySymbol;
        default:
            return currencySymbol + formattedNumber;
    }
}

console.log('Price formatting tests:');
console.log('5.00 →', testPriceFormatting(5.00));
console.log('10.00 →', testPriceFormatting(10.00));
console.log('25.00 →', testPriceFormatting(25.00));
console.log('65.00 →', testPriceFormatting(65.00));
</script>\n";

echo "</div></div>\n";

echo "<div class='test-section success'>";
echo "<div class='test-title'>✅ Fixes Implemented</div>";
echo "<div class='test-data'>";
echo "<ul>";
echo "<li><strong>Base Price Calculation:</strong> Now properly calculates minimum variation price for variable products</li>";
echo "<li><strong>JavaScript Data:</strong> Base price and currency settings are passed from PHP to JavaScript</li>";
echo "<li><strong>Price Display:</strong> Uses proper WooCommerce price formatting instead of text parsing</li>";
echo "<li><strong>Price Updates:</strong> Properly updates price display while preserving original HTML structure</li>";
echo "<li><strong>Debug Tools:</strong> Added console debugging functions for troubleshooting</li>";
echo "</ul>";
echo "</div></div>\n";

echo "<p><strong>Next Steps:</strong> Test on the actual product page and check browser console for debug information.</p>\n";

?>
