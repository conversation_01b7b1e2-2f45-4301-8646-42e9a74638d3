<?php
/**
 * Test script to debug the Square import issue
 * Run this to test the Jamaica Blue Mountain product import
 */

// WordPress environment
require_once __DIR__ . '/../../../wp-load.php';

// Load SquareKit classes
require_once __DIR__ . '/includes/importers/class-squarekit-product-importer.php';

echo "=== SquareKit Import Debug Test ===\n";
echo "Testing Jamaica Blue Mountain product import...\n\n";

// Test the specific product that was in the debug output
$square_item_id = '26TOQGLJ7VBL6H5MNCVJNSH5';

try {
    // Create importer instance
    $importer = new SquareKit_Product_Importer();
    
    echo "1. Testing product import for Square ID: {$square_item_id}\n";
    
    // Attempt to import the product
    $result = $importer->import_product( $square_item_id );
    
    if ( is_wp_error( $result ) ) {
        echo "❌ Import failed with error:\n";
        echo "   Code: " . $result->get_error_code() . "\n";
        echo "   Message: " . $result->get_error_message() . "\n";
    } else {
        echo "✅ Import successful!\n";
        echo "   Result: " . print_r( $result, true ) . "\n";
    }
    
} catch ( Exception $e ) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
    echo "   Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Test Complete ===\n";
