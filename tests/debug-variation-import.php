<?php
/**
 * Debug Variation Import Issues
 * 
 * This file investigates why Square item options (Size: Small, Medium, Large)
 * are not being imported as WooCommerce variations.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $current_dir = __FILE__;
    $wp_config_found = false;
    
    // Go up directories until we find wp-config.php
    for ( $i = 0; $i < 10; $i++ ) {
        $current_dir = dirname( $current_dir );
        if ( file_exists( $current_dir . '/wp-config.php' ) ) {
            require_once( $current_dir . '/wp-config.php' );
            $wp_config_found = true;
            break;
        }
    }
    
    if ( ! $wp_config_found ) {
        die( 'WordPress installation not found. Please ensure this file is in a WordPress plugin directory.' );
    }
}

echo "<h2>Debug Variation Import Issues</h2>\n";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
.debug-title { color: #333; font-weight: bold; margin-bottom: 10px; }
.debug-data { background: #fff; padding: 10px; border-left: 3px solid #0073aa; }
.issue { background: #ffe6e6; border-left: 3px solid #d63638; }
.success { background: #e6ffe6; border-left: 3px solid #00a32a; }
.warning { background: #fff3cd; border-left: 3px solid #ffc107; }
</style>\n";

$product_id = isset( $_GET['product_id'] ) ? intval( $_GET['product_id'] ) : 175;

echo "<div class='debug-section'>";
echo "<div class='debug-title'>Analyzing Product ID: $product_id</div>";
echo "<div class='debug-data'>";

$product = wc_get_product( $product_id );
if ( ! $product ) {
    echo "<span style='color: red;'>Product not found with ID: $product_id</span>\n";
    exit;
}

echo "Product: " . $product->get_name() . "<br>\n";
echo "Product Type: " . $product->get_type() . "<br>\n";
echo "Has Variations: " . ( $product->is_type( 'variable' ) ? 'Yes' : 'No' ) . "<br>\n";

if ( $product->is_type( 'variable' ) ) {
    $variations = $product->get_available_variations();
    echo "Number of Variations: " . count( $variations ) . "<br>\n";
}

echo "</div></div>\n";

// Check Square data stored in meta
echo "<div class='debug-section'>";
echo "<div class='debug-title'>Square Import Data Analysis</div>";
echo "<div class='debug-data'>";

$square_item_id = get_post_meta( $product_id, '_square_item_id', true );
$square_variation_id = get_post_meta( $product_id, '_square_variation_id', true );

echo "<h4>Square Meta Data:</h4>\n";
echo "Square Item ID: " . ( $square_item_id ?: 'Not found' ) . "<br>\n";
echo "Square Variation ID: " . ( $square_variation_id ?: 'Not found' ) . "<br>\n";

// Check all Square-related meta
$all_meta = get_post_meta( $product_id );
$square_meta = array();

foreach ( $all_meta as $key => $value ) {
    if ( strpos( $key, 'square' ) !== false || strpos( $key, '_square' ) !== false ) {
        $square_meta[$key] = $value;
    }
}

if ( ! empty( $square_meta ) ) {
    echo "<h4>All Square-related Meta:</h4>\n";
    echo "<pre>" . print_r( $square_meta, true ) . "</pre>\n";
}

echo "</div></div>\n";

// Check WooCommerce attributes
echo "<div class='debug-section'>";
echo "<div class='debug-title'>WooCommerce Attributes Analysis</div>";

$attributes = $product->get_attributes();
if ( ! empty( $attributes ) ) {
    echo "<div class='debug-data success'>";
    echo "<h4>Product Attributes Found:</h4>\n";
    foreach ( $attributes as $attribute ) {
        echo "Attribute: " . $attribute->get_name() . "<br>\n";
        echo "Values: " . implode( ', ', $attribute->get_options() ) . "<br>\n";
        echo "Used for Variations: " . ( $attribute->get_variation() ? 'Yes' : 'No' ) . "<br>\n";
        echo "Visible: " . ( $attribute->get_visible() ? 'Yes' : 'No' ) . "<br>\n";
        echo "<hr>\n";
    }
    echo "</div>";
} else {
    echo "<div class='debug-data issue'>";
    echo "<h4>❌ No Product Attributes Found</h4>\n";
    echo "<p>This indicates that Square item options were not imported as WooCommerce attributes.</p>\n";
    echo "</div>";
}

echo "</div>\n";

// Check variations
if ( $product->is_type( 'variable' ) ) {
    echo "<div class='debug-section'>";
    echo "<div class='debug-title'>Variations Analysis</div>";
    echo "<div class='debug-data'>";
    
    $variations = $product->get_available_variations();
    
    if ( ! empty( $variations ) ) {
        echo "<h4>Variations Found:</h4>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>Variation ID</th><th>Attributes</th><th>Price</th><th>Square Data</th></tr>\n";
        
        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            $variation_square_id = get_post_meta( $variation->get_id(), '_square_variation_id', true );
            
            echo "<tr>";
            echo "<td>" . $variation->get_id() . "</td>";
            echo "<td>";
            
            $variation_attributes = $variation->get_variation_attributes();
            if ( ! empty( $variation_attributes ) ) {
                foreach ( $variation_attributes as $attr_name => $attr_value ) {
                    echo $attr_name . ": " . $attr_value . "<br>";
                }
            } else {
                echo "No attributes";
            }
            
            echo "</td>";
            echo "<td>$" . number_format( $variation->get_price(), 2 ) . "</td>";
            echo "<td>" . ( $variation_square_id ?: 'No Square ID' ) . "</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    } else {
        echo "<div class='issue'><h4>❌ No Variations Found</h4></div>\n";
    }
    
    echo "</div></div>\n";
}

// Check import logs or errors
echo "<div class='debug-section'>";
echo "<div class='debug-title'>Import Process Analysis</div>";
echo "<div class='debug-data'>";

echo "<h4>Possible Issues:</h4>\n";
echo "<ul>\n";

if ( empty( $attributes ) ) {
    echo "<li class='issue'>❌ <strong>No attributes imported:</strong> Square item options were not converted to WooCommerce attributes</li>\n";
}

if ( $product->is_type( 'variable' ) && empty( $variations ) ) {
    echo "<li class='issue'>❌ <strong>Variable product with no variations:</strong> Product is marked as variable but has no variations</li>\n";
}

if ( ! $square_item_id ) {
    echo "<li class='warning'>⚠️ <strong>No Square item ID:</strong> Product may not have been imported from Square</li>\n";
}

echo "</ul>\n";

echo "<h4>Expected Behavior:</h4>\n";
echo "<ul>\n";
echo "<li>✅ Square item options (Size: Small, Medium, Large) should become WooCommerce attributes</li>\n";
echo "<li>✅ Each Square variation should become a WooCommerce variation</li>\n";
echo "<li>✅ Variations should have proper attributes and pricing</li>\n";
echo "<li>✅ Square modifiers should be separate from variations</li>\n";
echo "</ul>\n";

echo "</div></div>\n";

// Recommendations
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🔧 Recommendations</div>";
echo "<div class='debug-data'>";

echo "<h4>Next Steps:</h4>\n";
echo "<ol>\n";
echo "<li><strong>Re-import the product:</strong> Try importing this specific product again from Square</li>\n";
echo "<li><strong>Check Square API data:</strong> Verify the product has item options in Square</li>\n";
echo "<li><strong>Review import logs:</strong> Check for any errors during the import process</li>\n";
echo "<li><strong>Manual attribute creation:</strong> Temporarily create attributes manually to test functionality</li>\n";
echo "</ol>\n";

echo "<h4>Import Logic Check:</h4>\n";
echo "<p>The variation import logic exists in the codebase, but it may not be executing properly for this product.</p>\n";
echo "<p>Key files to investigate:</p>\n";
echo "<ul>\n";
echo "<li><code>includes/importers/class-squarekit-variation-importer.php</code></li>\n";
echo "<li><code>includes/importers/class-squarekit-attribute-importer.php</code></li>\n";
echo "<li><code>includes/integrations/class-squarekit-woocommerce.php</code> (import_product_from_square method)</li>\n";
echo "</ul>\n";

echo "</div></div>\n";

// Test manual attribute creation
echo "<div class='debug-section'>";
echo "<div class='debug-title'>🧪 Test Manual Attribute Creation</div>";
echo "<div class='debug-data'>";

echo "<p><strong>For testing purposes, you can manually create the Size attribute:</strong></p>\n";
echo "<ol>\n";
echo "<li>Go to WooCommerce → Products → Attributes</li>\n";
echo "<li>Create a new attribute called 'Size'</li>\n";
echo "<li>Add terms: Small, Medium, Large</li>\n";
echo "<li>Edit the product and add the Size attribute</li>\n";
echo "<li>Create variations for each size with the correct prices</li>\n";
echo "</ol>\n";

echo "<p>This will help verify that the variation system works correctly while we fix the import logic.</p>\n";

echo "</div></div>\n";

?>
