<?php
/**
 * Test Enhanced Square Import System
 * 
 * This file tests the new enhanced Square-to-WooCommerce import system
 * with proper attribute/variation handling.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

echo "<h1>Enhanced Square Import System Test</h1>\n";
echo "<pre>\n";

// Load required classes
if ( ! class_exists( 'SquareKit_Square_API' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/api/class-squarekit-square-api.php' );
}

if ( ! class_exists( 'SquareKit_Attribute_Mapper' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-attribute-mapper.php' );
}

if ( ! class_exists( 'SquareKit_Attribute_Mappings_Table' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/database/class-squarekit-attribute-mappings-table.php' );
}

if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/integrations/class-squarekit-woocommerce.php' );
}

if ( ! class_exists( 'SquareKit_Settings' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
}

// Check if Square is connected
$settings = new SquareKit_Settings();
$is_connected = $settings->is_connected();

if ( ! $is_connected ) {
    echo "❌ Square is not connected. Please configure Square API credentials first.\n";
    exit;
}

echo "✅ Square API is connected\n\n";

try {
    // Test 1: Check if required classes exist
    echo "=== Test 1: Class Availability Check ===\n";

    $required_classes = array(
        'SquareKit_Square_API' => 'includes/api/class-squarekit-square-api.php',
        'SquareKit_Attribute_Mapper' => 'includes/class-squarekit-attribute-mapper.php',
        'SquareKit_Attribute_Mappings_Table' => 'includes/database/class-squarekit-attribute-mappings-table.php',
        'SquareKit_WooCommerce' => 'includes/integrations/class-squarekit-woocommerce.php'
    );

    foreach ( $required_classes as $class_name => $file_path ) {
        if ( class_exists( $class_name ) ) {
            echo "✅ {$class_name} is available\n";
        } else {
            echo "❌ {$class_name} is not available (expected at {$file_path})\n";
        }
    }
    echo "\n";

    // Test 2: Initialize database table
    echo "=== Test 2: Database Table Initialization ===\n";
    $mappings_table = new SquareKit_Attribute_Mappings_Table();
    
    if ( $mappings_table->table_exists() ) {
        echo "✅ Attribute mappings table exists\n";
    } else {
        echo "⚠️  Creating attribute mappings table...\n";
        $result = $mappings_table->create_table();
        if ( $result ) {
            echo "✅ Attribute mappings table created successfully\n";
        } else {
            echo "❌ Failed to create attribute mappings table\n";
            exit;
        }
    }
    
    $stats = $mappings_table->get_mapping_stats();
    echo "📊 Mapping Statistics:\n";
    echo "  Total mappings: {$stats['total_mappings']}\n";
    echo "  Option set mappings: {$stats['option_set_mappings']}\n";
    echo "  Option mappings: {$stats['option_mappings']}\n";
    echo "  Recent mappings: {$stats['recent_mappings']}\n\n";

    // Test 3: Square API Enhanced Methods
    echo "=== Test 3: Enhanced Square API Methods ===\n";
    $square_api = new SquareKit_Square_API();
    
    // Test catalog with relations
    echo "Testing get_catalog_with_relations()...\n";
    $catalog_with_relations = $square_api->get_catalog_with_relations( array(
        'types' => 'ITEM,ITEM_OPTION,MODIFIER_LIST',
        'limit' => 10
    ) );
    
    if ( is_wp_error( $catalog_with_relations ) ) {
        echo "❌ get_catalog_with_relations failed: " . $catalog_with_relations->get_error_message() . "\n";
    } else {
        echo "✅ get_catalog_with_relations succeeded\n";
        echo "  Items: " . count( $catalog_with_relations['items'] ) . "\n";
        echo "  Item Options: " . count( $catalog_with_relations['item_options'] ) . "\n";
        echo "  Item Option Values: " . count( $catalog_with_relations['item_option_values'] ) . "\n";
        echo "  Modifier Lists: " . count( $catalog_with_relations['modifier_lists'] ) . "\n";
    }
    echo "\n";

    // Test 4: Attribute Mapper
    echo "=== Test 4: Attribute Mapper ===\n";
    $attribute_mapper = new SquareKit_Attribute_Mapper();
    
    // Test with a sample item option if available
    if ( ! is_wp_error( $catalog_with_relations ) && ! empty( $catalog_with_relations['item_options'] ) ) {
        $sample_option = array_values( $catalog_with_relations['item_options'] )[0];
        echo "Testing attribute mapping with item option: " . ( $sample_option['item_option_data']['name'] ?? 'Unknown' ) . "\n";

        $attribute_id = $attribute_mapper->map_option_set_to_attribute( $sample_option, true );
        if ( $attribute_id ) {
            echo "✅ Successfully mapped item option to WooCommerce attribute ID: {$attribute_id}\n";

            // Test option value mapping if values are available
            $option_values = $sample_option['item_option_data']['values'] ?? array();
            if ( ! empty( $option_values ) ) {
                $sample_option_value = $option_values[0];
                $term_id = $attribute_mapper->map_option_to_term( $sample_option_value, $attribute_id, true );
                if ( $term_id ) {
                    echo "✅ Successfully mapped option value to WooCommerce term ID: {$term_id}\n";
                } else {
                    echo "❌ Failed to map option value to WooCommerce term\n";
                }
            }
        } else {
            echo "❌ Failed to map item option to WooCommerce attribute\n";
        }
    } else {
        if ( is_wp_error( $catalog_with_relations ) ) {
            echo "⚠️  Catalog fetch failed, cannot test option sets\n";
        } else {
            echo "⚠️  No option sets available for testing\n";
        }
    }
    echo "\n";

    // Test 5: Enhanced Product Import
    echo "=== Test 5: Enhanced Product Import ===\n";
    $wc_integration = new SquareKit_WooCommerce();
    
    // Find a variable product to test with
    $variable_item = null;
    if ( ! is_wp_error( $catalog_with_relations ) && ! empty( $catalog_with_relations['items'] ) ) {
        foreach ( $catalog_with_relations['items'] as $item ) {
        $variations = $item['item_data']['variations'] ?? array();
        if ( count( $variations ) > 1 ) {
            $variable_item = $item;
            break;
        }
    }
    }

    if ( $variable_item ) {
        echo "Testing enhanced import with variable product: " . ( $variable_item['item_data']['name'] ?? 'Unknown' ) . "\n";
        echo "Product has " . count( $variable_item['item_data']['variations'] ) . " variations\n";
        
        // Test the enhanced import method
        $result = $wc_integration->import_product_with_enhanced_variations( $variable_item );
        
        if ( is_wp_error( $result ) ) {
            echo "❌ Enhanced import failed: " . $result->get_error_message() . "\n";
        } else {
            echo "✅ Enhanced import succeeded. Product ID: {$result}\n";
            
            // Verify the imported product
            $product = wc_get_product( $result );
            if ( $product && $product->is_type( 'variable' ) ) {
                $attributes = $product->get_attributes();
                $variations = $product->get_children();
                
                echo "  Product type: " . $product->get_type() . "\n";
                echo "  Attributes: " . count( $attributes ) . "\n";
                echo "  Variations: " . count( $variations ) . "\n";
                
                foreach ( $attributes as $attribute_name => $attribute ) {
                    echo "    - {$attribute_name}: " . implode( ', ', $attribute->get_options() ) . "\n";
                }
            }
        }
    } else {
        echo "⚠️  No variable products found for testing\n";
    }
    echo "\n";

    // Test 6: Performance Metrics
    echo "=== Test 6: Performance Metrics ===\n";
    $start_time = microtime( true );
    $start_memory = memory_get_usage();
    
    // Simulate batch processing
    $batch_size = 5;
    $processed = 0;
    
    if ( ! is_wp_error( $catalog_with_relations ) && ! empty( $catalog_with_relations['items'] ) ) {
        foreach ( array_slice( $catalog_with_relations['items'], 0, $batch_size ) as $item ) {
        $variations = $item['item_data']['variations'] ?? array();
        if ( count( $variations ) > 0 ) {
            // Just test the data processing without actual import
            foreach ( $variations as $variation ) {
                $option_values = $variation['item_variation_data']['item_option_values'] ?? array();
                foreach ( $option_values as $option_value ) {
                    $option_id = $option_value['item_option_id'] ?? '';
                    if ( $option_id && isset( $catalog_with_relations['item_options'][$option_id] ) ) {
                        $processed++;
                    }
                }
            }
        }
    }
    }

    $end_time = microtime( true );
    $end_memory = memory_get_usage();
    
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 );
    $memory_used = round( ( $end_memory - $start_memory ) / 1024 / 1024, 2 );
    
    echo "📈 Performance Metrics:\n";
    echo "  Execution time: {$execution_time}ms\n";
    echo "  Memory used: {$memory_used}MB\n";
    echo "  Option values processed: {$processed}\n";
    echo "\n";
    
    echo "🎉 All tests completed successfully!\n";
    
} catch ( Exception $e ) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>
