<?php
/**
 * Test Actual Square Product Import
 * 
 * This file tests importing an actual Square product to verify the enhanced system works end-to-end.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Find WordPress root directory
    $wp_root = dirname( dirname( dirname( dirname( __FILE__ ) ) ) );
    if ( file_exists( $wp_root . '/wp-load.php' ) ) {
        require_once( $wp_root . '/wp-load.php' );
    } else {
        // Alternative path for different WordPress structures
        $wp_root = dirname( dirname( dirname( dirname( dirname( __FILE__ ) ) ) ) );
        if ( file_exists( $wp_root . '/wp-load.php' ) ) {
            require_once( $wp_root . '/wp-load.php' );
        } else {
            die( 'WordPress not found. Please run this file from within WordPress admin or ensure wp-load.php is accessible.' );
        }
    }
}

echo "<h1>Actual Square Product Import Test</h1>\n";
echo "<pre>\n";

// Check if WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    echo "❌ WooCommerce is not active. Please activate WooCommerce first.\n";
    exit;
}

// Load required classes
if ( ! class_exists( 'SquareKit_Settings' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
}

if ( ! class_exists( 'SquareKit_Square_API' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/api/class-squarekit-square-api.php' );
}

if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/integrations/class-squarekit-woocommerce.php' );
}

// Check if Square is connected
$settings = new SquareKit_Settings();
$is_connected = $settings->is_connected();

if ( ! $is_connected ) {
    echo "❌ Square is not connected. Please configure Square API credentials first.\n";
    exit;
}

echo "✅ Square API is connected\n\n";

try {
    $square_api = new SquareKit_Square_API();
    $wc_integration = new SquareKit_WooCommerce();
    
    // Step 1: Get Square catalog items
    echo "=== Step 1: Fetching Square Catalog ===\n";
    $catalog = $square_api->get_catalog( array( 'types' => 'ITEM', 'limit' => 5 ) );
    
    if ( is_wp_error( $catalog ) ) {
        echo "❌ Failed to fetch catalog: " . $catalog->get_error_message() . "\n";
        exit;
    }
    
    if ( empty( $catalog ) ) {
        echo "⚠️  No items found in Square catalog. Please add some products to Square first.\n";
        exit;
    }
    
    echo "✅ Found " . count( $catalog ) . " items in Square catalog\n";
    
    // Step 2: Find a suitable item to import
    echo "\n=== Step 2: Selecting Item for Import ===\n";
    $selected_item = null;
    $item_type = 'simple';
    
    foreach ( $catalog as $item ) {
        if ( $item['type'] !== 'ITEM' ) {
            continue;
        }
        
        $variations = $item['item_data']['variations'] ?? array();
        $item_name = $item['item_data']['name'] ?? 'Unknown';
        
        echo "Item: {$item_name} (Variations: " . count( $variations ) . ")\n";
        
        if ( count( $variations ) > 1 ) {
            // Prefer variable products for testing
            $selected_item = $item;
            $item_type = 'variable';
            echo "  → Selected as variable product for testing\n";
            break;
        } elseif ( ! $selected_item && count( $variations ) === 1 ) {
            // Fallback to simple product
            $selected_item = $item;
            $item_type = 'simple';
            echo "  → Available as simple product\n";
        }
    }
    
    if ( ! $selected_item ) {
        echo "❌ No suitable items found for import testing\n";
        exit;
    }
    
    $item_name = $selected_item['item_data']['name'] ?? 'Unknown';
    echo "\n✅ Selected item: {$item_name} (Type: {$item_type})\n";
    
    // Step 3: Check if item already exists in WooCommerce
    echo "\n=== Step 3: Checking Existing Products ===\n";
    $square_id = $selected_item['id'];
    $existing_product_id = $wc_integration->find_wc_product_by_square_id( $square_id );
    
    if ( $existing_product_id ) {
        echo "⚠️  Product already exists in WooCommerce (ID: {$existing_product_id})\n";
        echo "Proceeding with update instead of new import...\n";
    } else {
        echo "✅ Product does not exist in WooCommerce, will create new product\n";
    }
    
    // Step 4: Test enhanced import
    echo "\n=== Step 4: Testing Enhanced Import ===\n";
    
    if ( $item_type === 'variable' ) {
        echo "Testing enhanced variable product import...\n";
        
        // Get complete item data with relations
        $complete_data = $square_api->get_catalog_item_with_relations( $square_id );
        if ( is_wp_error( $complete_data ) ) {
            echo "❌ Failed to get complete item data: " . $complete_data->get_error_message() . "\n";
            exit;
        }
        
        echo "✅ Retrieved complete item data with relations\n";
        echo "  Related item options: " . count( $complete_data['related_objects']['item_options'] ) . "\n";
        echo "  Related option values: " . count( $complete_data['related_objects']['item_option_values'] ) . "\n";
        echo "  Related modifier lists: " . count( $complete_data['related_objects']['modifier_lists'] ) . "\n";
        
        // Test the enhanced import method
        $result = $wc_integration->import_product_with_enhanced_variations( $selected_item, array(), $existing_product_id );
        
        if ( is_wp_error( $result ) ) {
            echo "❌ Enhanced import failed: " . $result->get_error_message() . "\n";
        } else {
            echo "✅ Enhanced import succeeded! Product ID: {$result}\n";
            
            // Verify the imported product
            $product = wc_get_product( $result );
            if ( $product ) {
                echo "\n=== Import Verification ===\n";
                echo "Product name: " . $product->get_name() . "\n";
                echo "Product type: " . $product->get_type() . "\n";
                echo "Product status: " . $product->get_status() . "\n";
                
                if ( $product->is_type( 'variable' ) ) {
                    $attributes = $product->get_attributes();
                    $variations = $product->get_children();
                    
                    echo "Attributes: " . count( $attributes ) . "\n";
                    foreach ( $attributes as $attribute_name => $attribute ) {
                        echo "  - {$attribute_name}: " . implode( ', ', $attribute->get_options() ) . "\n";
                    }
                    
                    echo "Variations: " . count( $variations ) . "\n";
                    foreach ( $variations as $variation_id ) {
                        $variation = wc_get_product( $variation_id );
                        if ( $variation ) {
                            echo "  - " . $variation->get_name() . " (Price: " . $variation->get_price() . ")\n";
                        }
                    }
                }
                
                // Check Square ID mapping
                $stored_square_id = get_post_meta( $result, '_square_id', true );
                if ( $stored_square_id === $square_id ) {
                    echo "✅ Square ID mapping stored correctly\n";
                } else {
                    echo "❌ Square ID mapping issue (Expected: {$square_id}, Got: {$stored_square_id})\n";
                }
            }
        }
        
    } else {
        echo "Testing simple product import...\n";
        
        // For simple products, use the regular import method
        $result = $wc_integration->import_product_from_square( $selected_item, array(), $existing_product_id );
        
        if ( is_wp_error( $result ) ) {
            echo "❌ Simple product import failed: " . $result->get_error_message() . "\n";
        } else {
            echo "✅ Simple product import succeeded! Product ID: {$result}\n";
        }
    }
    
    echo "\n🎉 Import test completed!\n";
    
} catch ( Exception $e ) {
    echo "❌ Error during import test: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>
