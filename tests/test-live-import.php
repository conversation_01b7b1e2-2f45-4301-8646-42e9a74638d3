<?php
/**
 * Live Import Test for SquareKit
 * 
 * This test can be run in a WordPress environment to test the complete
 * Square-to-WooCommerce import workflow with the Jamaica Blue Mountain product.
 * 
 * Usage: Place this file in the plugin root and access via browser or WP-CLI
 */

// Ensure WordPress environment
if ( ! defined( 'ABSPATH' ) ) {
    // Try to load WordPress
    $wp_load_paths = array(
        __DIR__ . '/../../../wp-load.php',
        __DIR__ . '/../../../../wp-load.php',
        __DIR__ . '/../../../../../wp-load.php'
    );
    
    $wp_loaded = false;
    foreach ( $wp_load_paths as $path ) {
        if ( file_exists( $path ) ) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if ( ! $wp_loaded ) {
        die( 'WordPress environment not found. Please run this from within WordPress.' );
    }
}

// Ensure WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    die( 'WooCommerce is not active. Please activate WooCommerce before running this test.' );
}

echo "<h1>SquareKit Live Import Test - Variation Name Strategy</h1>\n";
echo "<p>Testing the complete Square-to-WooCommerce import workflow for products without Square Options...</p>\n";

// Auto-detect a valid Square item for testing (specifically looking for variation name strategy products)
echo "<h2>Step 0: Auto-Detect Variation Name Strategy Product</h2>\n";

$square_api = new SquareKit_Square_API();
$catalog = $square_api->get_catalog( array( 'types' => 'ITEM', 'limit' => 10 ) );

if ( is_wp_error( $catalog ) ) {
    die( '<p style="color: red;">❌ Failed to fetch Square catalog: ' . $catalog->get_error_message() . '</p>' );
}

if ( empty( $catalog ) ) {
    die( '<p style="color: red;">❌ No items found in Square catalog. Please add some products to Square first.</p>' );
}

// Find a variation name strategy product (variations without Square Options)
$test_item = null;
$variation_name_strategy_items = array();
$regular_variable_items = array();

foreach ( $catalog as $item ) {
    if ( $item['type'] === 'ITEM' ) {
        $variations = $item['item_data']['variations'] ?? array();
        if ( count( $variations ) > 1 ) {
            // Check if this uses variation name strategy (no item_option_values)
            $uses_variation_name_strategy = true;
            foreach ( $variations as $variation ) {
                $option_values = $variation['item_variation_data']['item_option_values'] ?? array();
                if ( ! empty( $option_values ) ) {
                    $uses_variation_name_strategy = false;
                    break;
                }
            }

            if ( $uses_variation_name_strategy ) {
                $variation_name_strategy_items[] = $item;
            } else {
                $regular_variable_items[] = $item;
            }
        }
    }
}

// Prefer variation name strategy items for this test
if ( ! empty( $variation_name_strategy_items ) ) {
    $test_item = $variation_name_strategy_items[0];
    echo "<p>✅ Found variation name strategy product (like Monster Energy)</p>\n";
} elseif ( ! empty( $regular_variable_items ) ) {
    $test_item = $regular_variable_items[0];
    echo "<p>⚠️ Using regular variable product (has Square Options)</p>\n";
} else {
    // Fallback to any item
    foreach ( $catalog as $item ) {
        if ( $item['type'] === 'ITEM' ) {
            $test_item = $item;
            break;
        }
    }
    echo "<p>⚠️ Using simple product (no variations)</p>\n";
}

if ( ! $test_item ) {
    die( '<p style="color: red;">❌ No valid items found for testing.</p>' );
}

$item_data = $test_item['item_data'];
$variations = $item_data['variations'] ?? array();

// Build test configuration from actual Square data
$test_config = array(
    'square_item_id' => $test_item['id'],
    'expected_name' => $item_data['name'] ?? 'Test Product',
    'expected_variations' => count( $variations ),
    'expected_attributes' => array(), // Will be determined during import
    'expected_prices' => array()
);

echo "<p>✅ Auto-detected test item: <strong>{$test_config['expected_name']}</strong></p>\n";
echo "<ul>\n";
echo "<li><strong>Square ID:</strong> {$test_config['square_item_id']}</li>\n";
echo "<li><strong>Variations:</strong> {$test_config['expected_variations']}</li>\n";

// Show variation details
if ( ! empty( $variations ) ) {
    echo "<li><strong>Variation Details:</strong><ul>\n";
    foreach ( $variations as $variation ) {
        $var_name = $variation['item_variation_data']['name'] ?? 'Unnamed';
        $var_sku = $variation['item_variation_data']['sku'] ?? 'No SKU';
        $has_options = ! empty( $variation['item_variation_data']['item_option_values'] ?? array() );
        echo "<li>{$var_name} (SKU: {$var_sku}) - Has Options: " . ( $has_options ? 'YES' : 'NO' ) . "</li>\n";
    }
    echo "</ul></li>\n";
}

echo "</ul>\n";

echo "<h2>Test Configuration</h2>\n";
echo "<ul>\n";
echo "<li><strong>Square Item ID:</strong> {$test_config['square_item_id']}</li>\n";
echo "<li><strong>Expected Product:</strong> {$test_config['expected_name']}</li>\n";
echo "<li><strong>Expected Variations:</strong> {$test_config['expected_variations']}</li>\n";
echo "</ul>\n";

try {
    echo "<h2>Step 1: Initialize Import System</h2>\n";
    
    // Load the product importer
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
    
    $importer = new SquareKit_Product_Importer();
    echo "<p>✅ Product importer initialized successfully</p>\n";
    
    echo "<h2>Step 2: Test SWEVER-Style Import</h2>\n";
    
    // Configure import settings
    $import_config = array(
        'name' => true,
        'description' => true,
        'price' => true,
        'sku' => true,
        'categories' => true,
        'images' => true,
        'modifiers' => true,
        'attributesDisabled' => false
    );
    
    echo "<p>Starting import for Square item: {$test_config['square_item_id']}</p>\n";
    
    // Perform the import using SWEVER-style architecture
    $import_result = $importer->import_product_swever_style( 
        $test_config['square_item_id'], 
        $import_config, 
        false // Not update-only
    );
    
    if ( is_wp_error( $import_result ) ) {
        echo "<p>❌ Import failed: " . $import_result->get_error_message() . "</p>\n";
        throw new Exception( 'Import failed: ' . $import_result->get_error_message() );
    }
    
    $product_id = $import_result['product_id'];
    $created = $import_result['created'];
    
    echo "<p>✅ Import completed successfully!</p>\n";
    echo "<ul>\n";
    echo "<li><strong>Product ID:</strong> {$product_id}</li>\n";
    echo "<li><strong>Action:</strong> " . ( $created ? 'Created' : 'Updated' ) . "</li>\n";
    echo "<li><strong>Square ID:</strong> {$import_result['square_id']}</li>\n";
    echo "</ul>\n";
    
    echo "<h2>Step 3: Validate Import Results</h2>\n";
    
    // Get the imported product
    $product = wc_get_product( $product_id );
    
    if ( ! $product ) {
        throw new Exception( 'Imported product not found' );
    }
    
    echo "<p>✅ Product retrieved successfully</p>\n";
    
    // Validate basic product data
    echo "<h3>Basic Product Validation</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Name:</strong> " . $product->get_name() . 
         ( $product->get_name() === $test_config['expected_name'] ? ' ✅' : ' ❌' ) . "</li>\n";
    echo "<li><strong>Type:</strong> " . $product->get_type() . 
         ( $product->get_type() === 'variable' ? ' ✅' : ' ❌' ) . "</li>\n";
    echo "<li><strong>Status:</strong> " . $product->get_status() . 
         ( $product->get_status() === 'publish' ? ' ✅' : ' ❌' ) . "</li>\n";
    echo "</ul>\n";
    
    // Validate attributes
    echo "<h3>Attribute Validation</h3>\n";
    $attributes = $product->get_attributes();
    echo "<p><strong>Total Attributes:</strong> " . count( $attributes ) . "</p>\n";
    
    foreach ( $attributes as $attribute ) {
        $attr_name = $attribute->get_name();
        $attr_options = $attribute->get_options();
        echo "<ul>\n";
        echo "<li><strong>{$attr_name}:</strong> " . implode( ', ', $attr_options ) . "</li>\n";
        echo "</ul>\n";
    }
    
    // Validate variations
    echo "<h3>Variation Validation</h3>\n";
    if ( $product->is_type( 'variable' ) ) {
        $variations = $product->get_children();
        echo "<p><strong>Total Variations:</strong> " . count( $variations ) . 
             ( count( $variations ) === $test_config['expected_variations'] ? ' ✅' : ' ❌' ) . "</p>\n";
        
        echo "<ul>\n";
        foreach ( $variations as $variation_id ) {
            $variation = wc_get_product( $variation_id );
            if ( $variation ) {
                $price = $variation->get_price();
                $attributes = $variation->get_attributes();
                $attr_string = '';
                foreach ( $attributes as $key => $value ) {
                    $attr_string .= "{$key}: {$value} ";
                }
                echo "<li><strong>Variation {$variation_id}:</strong> {$attr_string} - \${$price}</li>\n";
            }
        }
        echo "</ul>\n";
    }
    
    // Display validation results if available
    if ( isset( $import_result['validation'] ) ) {
        echo "<h3>Detailed Validation Report</h3>\n";
        $validation = $import_result['validation'];
        
        echo "<p><strong>Overall Status:</strong> " . 
             ( $validation['is_valid'] ? '<span style="color: green;">VALID ✅</span>' : '<span style="color: red;">INVALID ❌</span>' ) . "</p>\n";
        
        $summary = $validation['summary'] ?? array();
        echo "<ul>\n";
        echo "<li><strong>Total Checks:</strong> " . ( $summary['total_checks'] ?? 0 ) . "</li>\n";
        echo "<li><strong>Errors:</strong> <span style='color: red;'>" . ( $summary['errors'] ?? 0 ) . "</span></li>\n";
        echo "<li><strong>Warnings:</strong> <span style='color: orange;'>" . ( $summary['warnings'] ?? 0 ) . "</span></li>\n";
        echo "<li><strong>Successes:</strong> <span style='color: green;'>" . ( $summary['successes'] ?? 0 ) . "</span></li>\n";
        echo "</ul>\n";
        
        // Show errors if any
        if ( ! empty( $validation['errors'] ) ) {
            echo "<h4>Validation Errors</h4>\n";
            echo "<ul style='color: red;'>\n";
            foreach ( $validation['errors'] as $error ) {
                echo "<li><strong>{$error['code']}:</strong> {$error['message']}</li>\n";
            }
            echo "</ul>\n";
        }
        
        // Show warnings if any
        if ( ! empty( $validation['warnings'] ) ) {
            echo "<h4>Validation Warnings</h4>\n";
            echo "<ul style='color: orange;'>\n";
            foreach ( $validation['warnings'] as $warning ) {
                echo "<li><strong>{$warning['code']}:</strong> {$warning['message']}</li>\n";
            }
            echo "</ul>\n";
        }
    }
    
    echo "<h2>Step 4: Test Frontend Functionality</h2>\n";
    
    // Check if product is purchasable
    echo "<ul>\n";
    echo "<li><strong>Purchasable:</strong> " . ( $product->is_purchasable() ? 'Yes ✅' : 'No ❌' ) . "</li>\n";
    echo "<li><strong>In Stock:</strong> " . ( $product->is_in_stock() ? 'Yes ✅' : 'No ❌' ) . "</li>\n";
    echo "<li><strong>Product URL:</strong> <a href='" . $product->get_permalink() . "' target='_blank'>View Product</a></li>\n";
    echo "</ul>\n";
    
    echo "<h2>🎉 Test Results Summary</h2>\n";
    echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;'>\n";
    echo "<p><strong>✅ SUCCESS!</strong> The Square-to-WooCommerce import system is working correctly.</p>\n";
    echo "<ul>\n";
    echo "<li>Product imported successfully with ID: {$product_id}</li>\n";
    echo "<li>All SWEVER-style architecture components functioning</li>\n";
    echo "<li>Option resolution system working</li>\n";
    echo "<li>Attributes and variations created properly</li>\n";
    echo "<li>Validation system providing detailed feedback</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch ( Exception $e ) {
    echo "<h2>❌ Test Failed</h2>\n";
    echo "<div style='background: #fff2f2; padding: 15px; border-left: 4px solid #dc3232;'>\n";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
    echo "</div>\n";
    
    // Show import statistics if available
    if ( isset( $importer ) ) {
        $stats = $importer->get_import_stats();
        echo "<h3>Import Statistics</h3>\n";
        echo "<ul>\n";
        foreach ( $stats as $key => $value ) {
            if ( is_array( $value ) ) {
                echo "<li><strong>{$key}:</strong> " . count( $value ) . " items</li>\n";
            } else {
                echo "<li><strong>{$key}:</strong> {$value}</li>\n";
            }
        }
        echo "</ul>\n";
    }
}

echo "<hr>\n";
echo "<p><em>Test completed at " . date( 'Y-m-d H:i:s' ) . "</em></p>\n";
