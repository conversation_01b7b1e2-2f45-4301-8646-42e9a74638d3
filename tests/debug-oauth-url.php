<?php
/**
 * Debug OAuth URL Generation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // Load WordPress if not already loaded
    require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );
}

// Only allow admin users
if ( ! current_user_can( 'manage_options' ) ) {
    wp_die( 'Access denied.' );
}

echo "<h1>OAuth URL Debug</h1>\n";
echo "<pre>\n";

// Load settings
if ( ! class_exists( 'SquareKit_Settings' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-settings.php' );
}

$settings = new SquareKit_Settings();
$environment = $settings->get_environment();

echo "=== Current Settings ===\n";
echo "Environment: $environment\n";

// Get client ID
$client_id = '';
if ( class_exists( 'SquareKit_Secure_Storage' ) ) {
    require_once( dirname( __FILE__ ) . '/includes/class-squarekit-secure-storage.php' );
    $secure_credentials = SquareKit_Secure_Storage::get_secure_credentials( $environment );
    if ( $secure_credentials !== false ) {
        $client_id = $secure_credentials['application_id'];
        echo "Using secure credentials\n";
    }
}

if ( empty( $client_id ) ) {
    $client_id = $settings->get( $environment . '_application_id', '' );
    echo "Using settings credentials\n";
}

echo "Client ID: " . ( ! empty( $client_id ) ? substr( $client_id, 0, 20 ) . '...' : 'EMPTY' ) . "\n";

// Generate OAuth URL
$redirect_uri_raw = admin_url( 'admin.php?page=squarekit-wizard&step=connect' );
$oauth_base_url = ( $environment === 'sandbox' ? 'https://connect.squareupsandbox.com' : 'https://connect.squareup.com' );

echo "\n=== URL Components ===\n";
echo "Redirect URI (raw): $redirect_uri_raw\n";
echo "Redirect URI (encoded): " . urlencode( $redirect_uri_raw ) . "\n";
echo "OAuth Base URL: $oauth_base_url\n";
echo "Environment: $environment\n";

// Generate state nonce
$state_nonce = wp_create_nonce( 'squarekit_oauth' );
echo "State nonce: $state_nonce\n";

// Build complete OAuth URL
$scopes = array(
    'MERCHANT_PROFILE_READ',
    'PAYMENTS_READ',
    'PAYMENTS_WRITE',
    'ORDERS_READ',
    'ORDERS_WRITE',
    'CUSTOMERS_READ',
    'CUSTOMERS_WRITE',
    'INVENTORY_READ',
    'INVENTORY_WRITE',
    'ITEMS_READ',
    'ITEMS_WRITE',
    'CATALOG_READ',
    'CATALOG_WRITE'
);

$scope_string = implode( '+', $scopes );
echo "\nScopes: $scope_string\n";

$oauth_url = $oauth_base_url . '/oauth2/authorize?' . http_build_query( array(
    'client_id' => $client_id,
    'scope' => $scope_string,
    'session' => 'false',
    'state' => $state_nonce,
    'redirect_uri' => $redirect_uri_raw
) );

echo "\n=== Generated OAuth URL ===\n";
echo "Complete URL:\n";
echo $oauth_url . "\n";

echo "\n=== URL Validation ===\n";

// Check if client ID is valid format
if ( empty( $client_id ) ) {
    echo "❌ Client ID is empty\n";
} elseif ( strpos( $client_id, 'sandbox-' ) === 0 && $environment === 'sandbox' ) {
    echo "✅ Client ID format looks correct for sandbox\n";
} elseif ( strpos( $client_id, 'sq0idp-' ) === 0 && $environment === 'production' ) {
    echo "✅ Client ID format looks correct for production\n";
} else {
    echo "⚠️  Client ID format might be incorrect\n";
    echo "   Expected: sandbox-* for sandbox or sq0idp-* for production\n";
    echo "   Got: " . substr( $client_id, 0, 20 ) . "...\n";
}

// Check redirect URI
if ( strpos( $redirect_uri_raw, '.local' ) !== false ) {
    echo "⚠️  Using .local domain - Square OAuth might have issues with this\n";
    echo "   Consider using ngrok or a public domain for testing\n";
} else {
    echo "✅ Redirect URI domain looks good\n";
}

// Check OAuth base URL
if ( $oauth_base_url === 'https://connect.squareupsandbox.com' && $environment === 'sandbox' ) {
    echo "✅ OAuth base URL is correct for sandbox\n";
} elseif ( $oauth_base_url === 'https://connect.squareup.com' && $environment === 'production' ) {
    echo "✅ OAuth base URL is correct for production\n";
} else {
    echo "❌ OAuth base URL mismatch\n";
}

echo "\n=== Alternative URLs to Try ===\n";

// Alternative with different redirect URI
$alt_redirect = str_replace( '.local', '.test', $redirect_uri_raw );
$alt_oauth_url = $oauth_base_url . '/oauth2/authorize?' . http_build_query( array(
    'client_id' => $client_id,
    'scope' => $scope_string,
    'session' => 'false',
    'state' => $state_nonce,
    'redirect_uri' => $alt_redirect
) );

echo "With .test domain:\n";
echo $alt_oauth_url . "\n\n";

// Check if we can reach Square's OAuth endpoint
echo "=== Connectivity Test ===\n";
$response = wp_remote_get( $oauth_base_url, array( 'timeout' => 10 ) );

if ( is_wp_error( $response ) ) {
    echo "❌ Cannot reach Square OAuth endpoint: " . $response->get_error_message() . "\n";
} else {
    $response_code = wp_remote_retrieve_response_code( $response );
    echo "✅ Square OAuth endpoint reachable (HTTP $response_code)\n";
}

echo "\n=== Recommendations ===\n";

if ( strpos( $redirect_uri_raw, '.local' ) !== false ) {
    echo "1. Try using ngrok to create a public tunnel:\n";
    echo "   - Install ngrok: https://ngrok.com/\n";
    echo "   - Run: ngrok http 80\n";
    echo "   - Use the ngrok URL in your Square app settings\n\n";
}

echo "2. Make sure your Square app redirect URI exactly matches:\n";
echo "   $redirect_uri_raw\n\n";

echo "3. Check your Square Developer Dashboard:\n";
echo "   - Sandbox: https://developer.squareup.com/apps\n";
echo "   - Make sure the redirect URI is added to your app\n\n";

echo "</pre>\n";
?>

<h2>Test OAuth URL</h2>
<p>Click the button below to test the OAuth URL:</p>
<a href="<?php echo esc_url( $oauth_url ); ?>" target="_blank" class="button button-primary">Test OAuth URL</a>

<h2>Manual URL</h2>
<p>If the button doesn't work, copy and paste this URL into a new browser tab:</p>
<textarea style="width: 100%; height: 100px;" readonly><?php echo esc_html( $oauth_url ); ?></textarea>
