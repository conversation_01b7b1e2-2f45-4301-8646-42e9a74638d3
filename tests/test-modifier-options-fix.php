<?php
/**
 * Test Modifier Options Fix
 * 
 * Quick test to verify that modifier options (individual modifiers within sets) 
 * are now being imported correctly.
 */

// WordPress environment - adjust path for Local by Flywheel
$wp_load_paths = [
    dirname(__FILE__) . '/../../../wp-load.php',  // Standard WordPress
    dirname(__FILE__) . '/../../../../wp-load.php', // Local by Flywheel
    dirname(__FILE__) . '/../../../../../wp-load.php' // Alternative structure
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die("❌ Could not find wp-load.php. Please run this file from a web browser.");
}

// Check if SquareKit plugin is loaded
if (!defined('SQUAREKIT_PLUGIN_DIR')) {
    die("❌ SquareKit plugin is not loaded. Please ensure the plugin is active.");
}

// Include required classes
if (!class_exists('SquareKit_Product_Importer')) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
}
if (!class_exists('SquareKit_Square_API')) {
    require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
}

echo "<h1>🔧 Modifier Options Fix Test</h1>\n";
echo "<p>Testing that individual modifier options are now being imported correctly.</p>\n";

// Get product ID from URL or find one automatically
$test_product_id = $_GET['product_id'] ?? null;

try {
    $square_api = new SquareKit_Square_API();
    
    if (!$test_product_id) {
        echo "<h2>🔍 Finding Product with Modifiers</h2>\n";
        
        $catalog_response = $square_api->get_catalog(array('types' => 'ITEM'));
        if (is_wp_error($catalog_response)) {
            echo "❌ Failed to fetch catalog: " . $catalog_response->get_error_message() . "<br>\n";
            exit;
        }
        
        $items = $catalog_response['objects'] ?? array();
        $found_product = null;
        
        foreach ($items as $item) {
            $modifier_list_info = $item['item_data']['modifier_list_info'] ?? array();
            if (!empty($modifier_list_info)) {
                $found_product = $item;
                break;
            }
        }
        
        if (!$found_product) {
            echo "❌ No products with modifiers found.<br>\n";
            exit;
        }
        
        $test_product_id = $found_product['id'];
        echo "✅ Auto-selected: " . ($found_product['item_data']['name'] ?? 'Unknown') . "<br>\n";
    }
    
    echo "<h2>📋 Testing Product: <code>{$test_product_id}</code></h2>\n";
    
    // Step 1: Check Square API data structure
    echo "<h3>🔍 Step 1: Checking Square API Data</h3>\n";
    
    $square_data = $square_api->get_catalog_item_with_relations($test_product_id);
    if (is_wp_error($square_data)) {
        echo "❌ Failed to fetch Square data: " . $square_data->get_error_message() . "<br>\n";
        exit;
    }
    
    $modifier_lists = $square_data['related_objects']['modifier_lists'] ?? array();
    echo "Modifier lists found: " . count($modifier_lists) . "<br>\n";
    
    if (empty($modifier_lists)) {
        echo "❌ No modifier lists found for this product.<br>\n";
        exit;
    }
    
    // Show the actual Square data structure
    foreach ($modifier_lists as $list) {
        $list_name = $list['modifier_list_data']['name'] ?? 'Unknown';
        $modifiers = $list['modifier_list_data']['modifiers'] ?? array();
        
        echo "<strong>{$list_name}</strong> (ID: {$list['id']})<br>\n";
        echo "  Individual modifiers: " . count($modifiers) . "<br>\n";
        
        if (!empty($modifiers)) {
            foreach ($modifiers as $modifier) {
                $mod_name = $modifier['modifier_data']['name'] ?? 'Unknown';
                $price = 0;
                if (isset($modifier['modifier_data']['price_money']['amount'])) {
                    $price = $modifier['modifier_data']['price_money']['amount'] / 100;
                }
                echo "    - {$mod_name} (\${$price})<br>\n";
            }
        } else {
            echo "    ❌ No individual modifiers found in this list!<br>\n";
        }
    }
    
    // Step 2: Test import
    echo "<h3>🚀 Step 2: Testing Import with Fixed Mapping</h3>\n";
    
    $importer = new SquareKit_Product_Importer();
    
    $import_config = array(
        'name' => true,
        'description' => true,
        'images' => true,
        'categories' => true,
        'variations' => true,
        'modifiers' => true,
        'attributesDisabled' => false
    );
    
    $result = $importer->import_product_swever_style(
        $test_product_id,
        $import_config,
        false
    );
    
    if (is_wp_error($result)) {
        echo "❌ Import failed: " . $result->get_error_message() . "<br>\n";
        exit;
    }
    
    $wc_product_id = $result['product_id'];
    echo "✅ Import completed! WooCommerce Product ID: {$wc_product_id}<br>\n";
    
    // Step 3: Check imported modifier options
    echo "<h3>🔍 Step 3: Checking Imported Modifier Options</h3>\n";
    
    $swever_modifiers = get_post_meta($wc_product_id, '_squarekit_modifier_sets', true);
    
    if (empty($swever_modifiers)) {
        echo "❌ No modifier sets found in database!<br>\n";
        exit;
    }
    
    echo "✅ Found " . count($swever_modifiers) . " modifier set(s) in database:<br>\n";
    
    $total_options = 0;
    foreach ($swever_modifiers as $index => $set) {
        $set_name = $set['set_name'] ?? 'Unknown';
        $options = $set['options'] ?? array();
        $option_count = count($options);
        $total_options += $option_count;
        
        echo "<strong>Set {$index}: {$set_name}</strong><br>\n";
        echo "  Options: {$option_count}<br>\n";
        
        if (!empty($options)) {
            foreach ($options as $option) {
                $opt_name = $option['name'] ?? 'Unknown';
                $opt_price = $option['price'] ?? 0;
                echo "    - {$opt_name} (\${$opt_price})<br>\n";
            }
        } else {
            echo "    ❌ No options found in this set!<br>\n";
        }
    }
    
    // Step 4: Results
    echo "<h3>🎯 Test Results</h3>\n";
    
    if ($total_options > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; color: #155724;'>\n";
        echo "<h4>🎉 SUCCESS! Modifier Options Fix is Working!</h4>\n";
        echo "<ul>\n";
        echo "<li>✅ Modifier sets are imported with names</li>\n";
        echo "<li>✅ Individual modifier options are imported ({$total_options} total)</li>\n";
        echo "<li>✅ Option names and prices are preserved</li>\n";
        echo "<li>✅ Data is properly stored in database</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        $admin_url = admin_url("post.php?post={$wc_product_id}&action=edit");
        echo "<p><a href='{$admin_url}' target='_blank'>🔧 Check Admin Interface</a> - Options should now be visible!</p>\n";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; color: #721c24;'>\n";
        echo "<h4>❌ FAILURE! Modifier Options Still Missing!</h4>\n";
        echo "<p>Modifier sets are imported but individual options are still empty.</p>\n";
        echo "<p>The fix needs more investigation.</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception: " . $e->getMessage() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<br>\n<hr>\n";
echo "<em>Test completed at " . date('Y-m-d H:i:s') . "</em>\n";
