# Phase 2: Module Breakdown Analysis
## 5,435-Line File Refactoring Plan

Based on analysis of `includes/integrations/class-squarekit-woocommerce.php`, here's the detailed breakdown:

---

## 📊 **Current File Structure Analysis**

### **Total Methods**: 95 methods
### **Total Lines**: 5,435 lines
### **Average Method Size**: ~57 lines per method

---

## 🎯 **Proposed Module Structure**

### **1. Product Sync Module** (~1,200 lines)
**File**: `includes/sync/class-squarekit-product-sync.php`

**Methods to Extract** (Lines 623-4820):
- `import_products_from_square()` (623-852)
- `import_product_from_square()` (3675-3839)
- `import_single_product_from_square()` (4285-4334)
- `sync_single_product_with_square()` (4335-4389)
- `import_product_with_enhanced_variations()` (4706-4819)
- `sync_product_to_square()` (168-231)
- `delete_product_from_square()` (232-248)
- `prepare_square_catalog_item()` (249-330)

**Responsibilities**:
- Core product import/export logic
- Product synchronization with Square
- Basic product CRUD operations

---

### **2. Inventory Sync Module** (~900 lines)
**File**: `includes/sync/class-squarekit-inventory-sync.php`

**Methods to Extract** (Lines 974-1570):
- `import_inventory_from_square()` (974-1095)
- `sync_inventory_to_square()` (1096-1189)
- `check_inventory_conflict()` (1190-1234)
- `update_local_inventory_tracking()` (1235-1251)
- `handle_order_*_inventory()` methods (1306-1452)
- `ajax_sync_inventory_manual()` (1528-1569)
- `ajax_resolve_inventory_conflict()` (1453-1527)

**Responsibilities**:
- Inventory synchronization
- Stock level management
- Inventory conflict resolution
- Order-based inventory updates

---

### **3. Image Handler Module** (~800 lines)
**File**: `includes/sync/class-squarekit-image-handler.php`

**Methods to Extract** (Lines 2115-2895):
- `import_image_from_url()` (2115-2236)
- `import_image_from_data_url()` (2452-2525)
- `import_product_images()` (4390-4419)
- `import_product_images_enhanced()` (4420-4511)
- `import_product_gallery()` (2713-2756)
- `import_product_gallery_enhanced()` (2757-2879)
- `optimize_image()` (2237-2309)
- `generate_product_image_filename()` (2310-2344)
- Image logging and stats methods (2526-2693)

**Responsibilities**:
- Image import/export operations
- Image optimization and processing
- Gallery management
- Image filename generation and sanitization

---

### **4. Variation & Attribute Handler Module** (~700 lines)
**File**: `includes/sync/class-squarekit-variation-handler.php`

**Methods to Extract** (Lines 3840-4284):
- `import_item_option_sets_from_square()` (3840-3881)
- `import_single_option_set()` (3882-3933)
- `create_wc_attribute_from_option_set()` (3934-3961)
- `import_variations_with_option_sets()` (4019-4037)
- `create_attributes_from_variations()` (4038-4118)
- `create_wc_variations_from_square()` (4191-4284)
- `import_enhanced_variations()` (4820-4955)
- `create_enhanced_variation()` (4956-5065)

**Responsibilities**:
- Product variations import/export
- Attribute creation and management
- Option sets handling
- Variation-specific logic

---

### **5. Order Sync Module** (~600 lines)
**File**: `includes/sync/class-squarekit-order-sync.php`

**Methods to Extract** (Lines 331-868):
- `sync_order_to_square()` (331-375)
- `prepare_square_order_data()` (376-404)
- `import_orders_from_square()` (869-925)
- `update_wc_order_status_from_square()` (3140-3160)
- `update_order_fulfillment_from_square()` (3337-3406)
- `handle_fulfillment_webhook()` (3407-3460)
- Fulfillment mapping methods (3230-3336)

**Responsibilities**:
- Order synchronization
- Order status management
- Fulfillment handling
- Webhook processing

---

### **6. Customer Sync Module** (~500 lines)
**File**: `includes/sync/class-squarekit-customer-sync.php`

**Methods to Extract** (Lines 405-1610):
- `sync_customer_to_square()` (405-434)
- `prepare_square_customer_data()` (435-454)
- `import_customers_from_square()` (1611-1671)
- `determine_customer_role()` (455-485)
- `apply_customer_role()` (578-622)
- `apply_role_mapping_to_all_customers()` (3161-3208)
- Customer role evaluation methods (486-577)

**Responsibilities**:
- Customer synchronization
- Customer role management
- Customer data mapping

---

### **7. Category & SKU Manager Module** (~400 lines)
**File**: `includes/sync/class-squarekit-category-sku-manager.php`

**Methods to Extract** (Lines 926-2114):
- `import_categories_from_square()` (926-973)
- `bulk_import_categories()` (1919-2002)
- Category hierarchy methods (1715-1918)
- SKU import and validation methods (3471-3674)
- `resolve_sku_conflict()` (3560-3648)

**Responsibilities**:
- Category import/management
- SKU validation and conflict resolution
- Category hierarchy handling

---

### **8. Modifier Handler Module** (~400 lines)
**File**: `includes/sync/class-squarekit-modifier-handler.php`

**Methods to Extract** (Lines 2928-3139):
- `display_product_modifiers()` (2928-2999)
- `add_modifiers_to_cart_item()` (3057-3101)
- `display_modifiers_in_cart()` (3102-3121)
- `add_modifiers_to_order_item()` (3122-3139)
- `import_modifiers_from_square()` (4575-4621)
- `process_square_modifier_list()` (4622-4678)
- `import_enhanced_modifiers()` (5111-5195)

**Responsibilities**:
- Modifier display and management
- Cart integration
- Order integration
- Modifier import/export

---

### **9. Sync Coordinator Module** (~300 lines)
**File**: `includes/sync/class-squarekit-sync-coordinator.php`

**Methods to Extract** (Lines 49-120 + utility methods):
- `init()` (71-120)
- `add_modifiers_tab()` (121-135)
- `add_modifiers_panel()` (136-152)
- `save_modifiers_meta()` (153-167)
- Utility and helper methods
- Coordination logic

**Responsibilities**:
- Orchestrates all sync operations
- Manages module dependencies
- Provides unified interface
- Handles WordPress integration hooks

---

## 📋 **Remaining Main Class** (~635 lines)

**File**: `includes/integrations/class-squarekit-woocommerce.php` (refactored)

**Keeps**:
- Constructor and initialization
- Module loading and dependency injection
- Public API methods that delegate to modules
- WordPress hook registration
- Import Manager integration (our new bridge)

---

## 🎯 **Benefits of This Structure**

1. **Maintainability**: Each module has a single responsibility
2. **Testability**: Modules can be tested independently
3. **Reusability**: Modules can be used by other parts of the plugin
4. **Performance**: Only load modules when needed
5. **Collaboration**: Multiple developers can work on different modules
6. **Debugging**: Easier to isolate issues to specific modules

---

## 📊 **File Size Reduction**

| Module | Lines | Percentage |
|--------|-------|------------|
| Product Sync | ~1,200 | 22% |
| Inventory Sync | ~900 | 17% |
| Image Handler | ~800 | 15% |
| Variation Handler | ~700 | 13% |
| Order Sync | ~600 | 11% |
| Customer Sync | ~500 | 9% |
| Category/SKU Manager | ~400 | 7% |
| Modifier Handler | ~400 | 7% |
| **Main Class (Remaining)** | **~635** | **12%** |
| **Total** | **5,435** | **100%** |

---

## 🚀 **Implementation Order**

1. **Product Sync Module** (most critical, highest usage)
2. **Inventory Sync Module** (high impact, complex logic)
3. **Image Handler Module** (independent, good test case)
4. **Variation Handler Module** (complex but isolated)
5. **Order Sync Module** (important for e-commerce)
6. **Customer Sync Module** (straightforward)
7. **Category/SKU Manager** (utility functions)
8. **Modifier Handler Module** (frontend integration)
9. **Sync Coordinator** (orchestration layer)

This breakdown transforms the monolithic 5,435-line file into 9 focused, maintainable modules averaging 400-1,200 lines each! 🎯
