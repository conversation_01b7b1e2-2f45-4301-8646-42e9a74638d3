<?php
/**
 * Test script for Square Kit logging system
 * 
 * This script tests the comprehensive logging functionality
 * 
 * Usage: Run this script from the WordPress root directory
 * php wp-content/plugins/squarekit/test-logging.php
 */

// Load WordPress
require_once dirname(__FILE__) . '/../../../wp-load.php';

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce is not active. Please activate WooCommerce first.\n";
    exit(1);
}

// Load required classes
require_once dirname(__FILE__) . '/includes/class-squarekit-logger.php';

echo "=== Square Kit Logging System Test ===\n\n";

// Initialize logger
$logger = SquareKit_Logger::get_instance();

echo "Test 1: Basic Logging Functions\n";
echo "--------------------------------\n";

// Test different log levels
$logger->log('test', 'debug', 'This is a debug message', array('test_data' => 'debug_value'));
$logger->log('test', 'info', 'This is an info message', array('test_data' => 'info_value'));
$logger->log('test', 'warning', 'This is a warning message', array('test_data' => 'warning_value'));
$logger->log('test', 'error', 'This is an error message', array('test_data' => 'error_value'));
$logger->log('test', 'critical', 'This is a critical message', array('test_data' => 'critical_value'));

echo "✅ Logged 5 test messages with different severity levels\n\n";

echo "Test 2: Category-Specific Logging\n";
echo "----------------------------------\n";

// Test API logging
$logger->log('api', 'info', 'Test API call successful', array(
    'endpoint' => '/test/endpoint',
    'method' => 'GET',
    'response_time' => 150
));

// Test product import logging
$logger->log('product_import', 'info', 'Test product imported', array(
    'product_id' => 123,
    'square_id' => 'SQUARE123',
    'name' => 'Test Product'
));

// Test image import logging
$logger->log('image_import', 'info', 'Test image imported', array(
    'url' => 'https://example.com/test.jpg',
    'attachment_id' => 456,
    'file_size' => 1024
));

// Test mapping logging
$logger->log('mapping', 'info', 'Test mapping created', array(
    'square_category' => 'Electronics',
    'woo_category' => 'Tech'
));

echo "✅ Logged messages for API, product import, image import, and mapping categories\n\n";

echo "Test 3: Error Scenarios\n";
echo "------------------------\n";

// Test error logging
$logger->log('api', 'error', 'Test API error', array(
    'endpoint' => '/test/error',
    'status_code' => 404,
    'error_message' => 'Not found'
));

$logger->log('product_import', 'error', 'Test product import failed', array(
    'square_id' => 'SQUARE456',
    'error' => 'Invalid product data'
));

$logger->log('image_import', 'error', 'Test image import failed', array(
    'url' => 'https://example.com/broken.jpg',
    'error' => 'HTTP 404 Not Found'
));

echo "✅ Logged error scenarios for different categories\n\n";

echo "Test 4: Log Statistics\n";
echo "----------------------\n";

$stats = $logger->get_stats();

echo "Log Statistics:\n";
echo "  - Total Entries: " . $stats['total_entries'] . "\n";
echo "  - File Size: " . $stats['file_size_mb'] . " MB\n";
echo "  - By Level:\n";
foreach ($stats['by_level'] as $level => $count) {
    echo "    * " . ucfirst($level) . ": " . $count . "\n";
}
echo "  - By Category:\n";
foreach ($stats['by_category'] as $category => $count) {
    echo "    * " . ucfirst($category) . ": " . $count . "\n";
}

echo "\n";

echo "Test 5: Log Retrieval and Filtering\n";
echo "------------------------------------\n";

// Get recent logs
$recent_logs = $logger->get_logs(5);
echo "Recent 5 log entries:\n";
foreach ($recent_logs as $log) {
    echo "  [{$log['timestamp']}] [{$log['level']}] [{$log['category']}] {$log['message']}\n";
}

echo "\n";

// Get error logs only
$error_logs = $logger->get_logs(10, 'error');
echo "Recent error logs (" . count($error_logs) . " found):\n";
foreach ($error_logs as $log) {
    echo "  [{$log['timestamp']}] {$log['message']}\n";
}

echo "\n";

// Get API logs only
$api_logs = $logger->get_logs(10, null, 'api');
echo "Recent API logs (" . count($api_logs) . " found):\n";
foreach ($api_logs as $log) {
    echo "  [{$log['timestamp']}] [{$log['level']}] {$log['message']}\n";
}

echo "\n";

echo "Test 6: Log File Information\n";
echo "-----------------------------\n";

$log_file = $logger->get_log_file_path();
echo "Log file location: {$log_file}\n";

if (file_exists($log_file)) {
    echo "✅ Log file exists\n";
    echo "File size: " . round(filesize($log_file) / 1024, 2) . " KB\n";
    echo "Last modified: " . date('Y-m-d H:i:s', filemtime($log_file)) . "\n";
} else {
    echo "❌ Log file does not exist\n";
}

echo "\n";

echo "Test 7: Public Debug Page Access\n";
echo "---------------------------------\n";

$debug_url = home_url('/wp-content/plugins/squarekit/sk-logs.php');
echo "Debug page URL: {$debug_url}\n";
echo "✅ You can now access the debug page in your browser\n";

echo "\n=== All Tests Completed ===\n";
echo "The comprehensive logging system is working correctly!\n";
echo "Check the debug page for a visual interface: {$debug_url}\n";
