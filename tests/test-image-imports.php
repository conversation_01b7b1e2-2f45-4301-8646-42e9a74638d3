<?php
/**
 * Test script for Square Kit image import functionality
 * 
 * This script tests the enhanced image import features including:
 * - Single image imports
 * - Gallery imports
 * - Error handling
 * - Performance optimizations
 * - Debugging tools
 * 
 * Usage: Run this script from the WordPress root directory
 * php wp-content/plugins/squarekit/test-image-imports.php
 */

// Load WordPress
require_once dirname(__FILE__) . '/../../../wp-load.php';

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    echo "❌ WooCommerce is not active. Please activate WooCommerce first.\n";
    exit(1);
}

// Load required classes
require_once dirname(__FILE__) . '/includes/class-squarekit-settings.php';
require_once dirname(__FILE__) . '/includes/api/class-squarekit-square-api.php';
require_once dirname(__FILE__) . '/includes/integrations/class-squarekit-woocommerce.php';

echo "=== Square Kit Image Import Tests ===\n\n";

// Initialize classes
$settings = new SquareKit_Settings();
$square_api = new SquareKit_Square_API();
$wc_integration = new SquareKit_WooCommerce();

// Helper function to create a test image data URL
function create_test_image_data_url($width = 100, $height = 100, $color = 'blue') {
    // Create a simple colored rectangle as base64 data URL
    $colors = array(
        'blue' => array(0, 100, 200),
        'red' => array(200, 50, 50),
        'green' => array(50, 200, 50),
        'yellow' => array(200, 200, 50)
    );

    $rgb = isset($colors[$color]) ? $colors[$color] : $colors['blue'];

    // Create a minimal PNG data URL (this is a 1x1 blue pixel PNG)
    $png_data = base64_encode(
        pack('H*', '89504e470d0a1a0a0000000d49484452000000010000000108060000001f15c4890000000a4944415478da6300010000050001') .
        pack('C3', $rgb[0], $rgb[1], $rgb[2]) .
        pack('H*', '0d0a4945444ae426082')
    );

    return "data:image/png;base64,{$png_data}";
}

// Helper function to check if external URLs are accessible
function check_external_connectivity() {
    $test_response = wp_remote_head('https://httpbin.org/status/200', array(
        'timeout' => 5,
        'sslverify' => false
    ));

    return !is_wp_error($test_response) && wp_remote_retrieve_response_code($test_response) === 200;
}

$external_connectivity = check_external_connectivity();
if (!$external_connectivity) {
    echo "⚠️  External connectivity limited - using local test data\n\n";
}

// Test 1: Check Square API connection
echo "Test 1: Square API Connection\n";
echo "------------------------------\n";

$is_connected = $square_api->check_token();
if ($is_connected) {
    echo "✅ Square API connection successful\n";
} else {
    echo "❌ Square API connection failed\n";
    echo "Please configure your Square API credentials first.\n\n";
    exit(1);
}

// Test 2: Test image URL validation and debugging
echo "\nTest 2: Image URL Validation and Debugging\n";
echo "-------------------------------------------\n";

// Use different test URLs based on connectivity
if ($external_connectivity) {
    $test_urls = array(
        'https://picsum.photos/300/200', // More reliable image service
        'https://httpbin.org/status/404', // Will return 404
        'invalid-url',
        'https://picsum.photos/300/200.jpg',
        'https://httpbin.org/status/500' // Will return 500 error
    );
} else {
    $test_urls = array(
        create_test_image_data_url(300, 200, 'blue'), // Valid data URL
        'https://nonexistent.example.com/404.jpg', // Will fail
        'invalid-url',
        create_test_image_data_url(300, 200, 'red'), // Another valid data URL
        'https://nonexistent.example.com/500.jpg' // Will fail
    );
}

foreach ($test_urls as $url) {
    echo "Testing URL: {$url}\n";
    $debug_info = $wc_integration->debug_image_import($url);
    
    echo "  - URL Valid: " . ($debug_info['checks']['url_valid'] ? '✅' : '❌') . "\n";
    echo "  - HTTP Accessible: " . ($debug_info['checks']['http_accessible'] ? '✅' : '❌') . "\n";
    
    if (isset($debug_info['http_status'])) {
        echo "  - HTTP Status: {$debug_info['http_status']}\n";
    }
    
    if (isset($debug_info['content_type'])) {
        echo "  - Content Type: {$debug_info['content_type']}\n";
        echo "  - Is Image: " . ($debug_info['checks']['is_image'] ? '✅' : '❌') . "\n";
    }
    
    if (isset($debug_info['http_error'])) {
        echo "  - Error: {$debug_info['http_error']}\n";
    }
    
    echo "\n";
}

// Test 3: Test single image import
echo "Test 3: Single Image Import\n";
echo "----------------------------\n";

$test_image_url = $external_connectivity ? 'https://picsum.photos/400/300' : create_test_image_data_url(400, 300, 'green');
echo "Importing test image: {$test_image_url}\n";

$attachment_id = $wc_integration->import_image_from_url($test_image_url);

if ($attachment_id) {
    echo "✅ Image import successful! Attachment ID: {$attachment_id}\n";
    
    // Verify the attachment exists
    $attachment_url = wp_get_attachment_url($attachment_id);
    if ($attachment_url) {
        echo "✅ Attachment URL: {$attachment_url}\n";
    } else {
        echo "❌ Failed to get attachment URL\n";
    }
    
    // Check metadata
    $metadata = wp_get_attachment_metadata($attachment_id);
    if ($metadata) {
        echo "✅ Image metadata generated successfully\n";
        echo "  - Width: {$metadata['width']}px\n";
        echo "  - Height: {$metadata['height']}px\n";
        echo "  - File: {$metadata['file']}\n";
    }
} else {
    echo "❌ Image import failed\n";
}

// Test 4: Test gallery import with multiple images
echo "\nTest 4: Gallery Import (Multiple Images)\n";
echo "-----------------------------------------\n";

// Create a test product first
$test_product = new WC_Product_Simple();
$test_product->set_name('Test Product for Image Import');
$test_product->set_description('This is a test product for testing image imports.');
$test_product->set_regular_price(19.99);
$test_product->set_status('publish');
$product_id = $test_product->save();

if ($product_id) {
    echo "✅ Test product created with ID: {$product_id}\n";
    
    if ($external_connectivity) {
        $gallery_urls = array(
            'https://picsum.photos/500/400?random=1',
            'https://picsum.photos/500/400?random=2',
            'https://picsum.photos/500/400?random=3',
            'https://httpbin.org/status/404', // This should fail
            'https://picsum.photos/500/400?random=4'
        );
    } else {
        $gallery_urls = array(
            create_test_image_data_url(500, 400, 'blue'),
            create_test_image_data_url(500, 400, 'red'),
            create_test_image_data_url(500, 400, 'green'),
            'https://nonexistent.example.com/404.jpg', // This should fail
            create_test_image_data_url(500, 400, 'yellow')
        );
    }
    
    echo "Importing gallery with " . count($gallery_urls) . " images...\n";
    
    $gallery_result = $wc_integration->import_product_gallery_enhanced($gallery_urls, $product_id);
    
    echo "Gallery import result:\n";
    echo "  - Success: " . ($gallery_result['success'] ? '✅' : '❌') . "\n";
    echo "  - Message: {$gallery_result['message']}\n";
    echo "  - Total Requested: {$gallery_result['stats']['total_requested']}\n";
    echo "  - Total Imported: {$gallery_result['stats']['total_imported']}\n";
    echo "  - Total Failed: {$gallery_result['stats']['total_failed']}\n";
    echo "  - Total Skipped: {$gallery_result['stats']['total_skipped']}\n";
    
    if (!empty($gallery_result['failed_urls'])) {
        echo "  - Failed URLs:\n";
        foreach ($gallery_result['failed_urls'] as $failed_url) {
            echo "    * {$failed_url}\n";
        }
    }
    
    if (!empty($gallery_result['attachment_ids'])) {
        echo "  - Imported Attachment IDs: " . implode(', ', $gallery_result['attachment_ids']) . "\n";
        
        // Check if main image was set
        $product = wc_get_product($product_id);
        $main_image_id = $product->get_image_id();
        if ($main_image_id) {
            echo "  - Main product image set: ✅ (ID: {$main_image_id})\n";
        } else {
            echo "  - Main product image set: ❌\n";
        }
        
        // Check gallery images
        $gallery_ids = $product->get_gallery_image_ids();
        if (!empty($gallery_ids)) {
            echo "  - Gallery images set: ✅ (" . count($gallery_ids) . " images)\n";
        } else {
            echo "  - Gallery images set: ❌\n";
        }
    }
} else {
    echo "❌ Failed to create test product\n";
}

// Test 5: Test image import statistics
echo "\nTest 5: Image Import Statistics\n";
echo "--------------------------------\n";

$stats = $wc_integration->get_image_import_stats(1); // Last 1 day

if (isset($stats['error'])) {
    echo "❌ Failed to get statistics: {$stats['error']}\n";
} else {
    echo "Image import statistics (last {$stats['period_days']} day(s)):\n";
    echo "  - Total Attempts: {$stats['total_attempts']}\n";
    echo "  - Successful Imports: {$stats['successful_imports']}\n";
    echo "  - Failed Imports: {$stats['failed_imports']}\n";
    echo "  - Success Rate: {$stats['success_rate_percent']}%\n";
    
    if (!empty($stats['recent_errors'])) {
        echo "  - Recent Errors:\n";
        foreach (array_slice($stats['recent_errors'], 0, 3) as $error) {
            $timestamp = isset($error['timestamp']) ? $error['timestamp'] : 'Unknown time';
            $error_msg = isset($error['error']) ? $error['error'] : 'Unknown error';
            echo "    * {$timestamp}: {$error_msg}\n";
        }
    }
}

// Test 6: Test Square API image caching
echo "\nTest 6: Square API Image Caching\n";
echo "---------------------------------\n";

$cache_stats = $square_api->get_image_cache_stats();
echo "Image cache statistics:\n";
echo "  - Cached Images: {$cache_stats['cached_images']}\n";
echo "  - Cache Size: {$cache_stats['cache_size_kb']} KB\n";

if (!empty($cache_stats['cached_ids'])) {
    echo "  - Sample Cached IDs: " . implode(', ', array_slice($cache_stats['cached_ids'], 0, 3)) . "\n";
}

// Cleanup
echo "\nTest 7: Cleanup\n";
echo "---------------\n";

if (isset($product_id) && $product_id) {
    // Delete test product and its images
    $product = wc_get_product($product_id);
    if ($product) {
        // Delete product images
        $image_id = $product->get_image_id();
        if ($image_id) {
            wp_delete_attachment($image_id, true);
        }
        
        $gallery_ids = $product->get_gallery_image_ids();
        foreach ($gallery_ids as $gallery_id) {
            wp_delete_attachment($gallery_id, true);
        }
        
        // Delete product
        $product->delete(true);
        echo "✅ Test product and images cleaned up\n";
    }
}

// Delete standalone test image
if (isset($attachment_id) && $attachment_id) {
    wp_delete_attachment($attachment_id, true);
    echo "✅ Standalone test image cleaned up\n";
}

echo "\n=== All Tests Completed ===\n";
echo "Check the error logs for detailed information about the import process.\n";
