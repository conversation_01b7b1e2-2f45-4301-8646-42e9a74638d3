<?php
/**
 * Test the Square Import Fix for Variation Name Strategy
 * 
 * This file tests the fix for the SWEVER import path
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // For testing outside WordPress, define basic constants
    define( 'ABSPATH', dirname( __FILE__ ) . '/' );
    define( 'SQUAREKIT_PLUGIN_DIR', dirname( __FILE__ ) . '/' );
}

/**
 * Test the variation mapping logic
 */
function test_variation_mapping() {
    echo "<h2>Testing Variation Mapping Logic</h2>\n";
    
    // Simulate Monster Energy product data (variation name strategy)
    $square_item = array(
        'id' => 'KK3Q37655RZJKODXUBCNX7I2',
        'item_data' => array(
            'name' => 'Monster Energy Ultra- 350ml',
            'variations' => array(
                array(
                    'id' => 'var_regular',
                    'item_variation_data' => array(
                        'name' => 'Regular',
                        'sku' => '454536',
                        'price_money' => array('amount' => 1499),
                        'item_option_values' => array() // Empty - this is the key!
                    )
                ),
                array(
                    'id' => 'var_big',
                    'item_variation_data' => array(
                        'name' => 'Big',
                        'sku' => '576578',
                        'price_money' => array('amount' => 20000),
                        'item_option_values' => array() // Empty
                    )
                ),
                array(
                    'id' => 'var_extra_large',
                    'item_variation_data' => array(
                        'name' => 'Extra Large',
                        'sku' => '677868',
                        'price_money' => array('amount' => 150000)
                        // No item_option_values at all
                    )
                )
            )
        )
    );
    
    // Simulate the mapping logic from SquareKit_Square_Import
    $wc_product_data = array(
        'square_id' => $square_item['id'],
        'name' => $square_item['item_data']['name'],
        'type' => 'variable',
        'variations' => array(),
        'attributes' => array()
    );
    
    // Map variations using the new logic
    foreach ( $square_item['item_data']['variations'] as $variation ) {
        $wc_variation = array(
            'variation_square_id' => $variation['id'],
            'name' => $variation['item_variation_data']['name'] ?? '',
            'sku' => $variation['item_variation_data']['sku'] ?? '',
            'price' => 0,
            'attributes' => array()
        );

        // Set price
        if ( isset( $variation['item_variation_data']['price_money']['amount'] ) ) {
            $wc_variation['price'] = $variation['item_variation_data']['price_money']['amount'] / 100;
        }

        // Map attributes using the new logic
        if ( isset( $variation['item_variation_data']['item_option_values'] ) && ! empty( $variation['item_variation_data']['item_option_values'] ) ) {
            // Item Options Strategy: Use Square options
            foreach ( $variation['item_variation_data']['item_option_values'] as $option_value ) {
                if ( isset( $option_value['option_name'] ) && isset( $option_value['option_value'] ) ) {
                    $wc_variation['attributes'][] = array(
                        'name' => $option_value['option_name'],
                        'option' => $option_value['option_value']
                    );
                }
            }
        } else {
            // Variation Name Strategy: Use variation name as default attribute
            $variation_name = $variation['item_variation_data']['name'] ?? '';
            if ( ! empty( $variation_name ) ) {
                $wc_variation['attributes'][] = array(
                    'name' => 'Product Options',
                    'option' => $variation_name
                );
            }
        }

        $wc_product_data['variations'][] = $wc_variation;
    }
    
    // Create product-level attributes
    $attributes_by_name = array();

    // Collect all unique attributes from variations
    foreach ( $wc_product_data['variations'] as $variation ) {
        foreach ( $variation['attributes'] as $attribute ) {
            $attr_name = $attribute['name'];
            $attr_option = $attribute['option'];

            if ( ! isset( $attributes_by_name[$attr_name] ) ) {
                $attributes_by_name[$attr_name] = array();
            }

            if ( ! in_array( $attr_option, $attributes_by_name[$attr_name] ) ) {
                $attributes_by_name[$attr_name][] = $attr_option;
            }
        }
    }

    // Convert to product attribute format
    foreach ( $attributes_by_name as $attr_name => $options ) {
        $wc_product_data['attributes'][] = array(
            'name' => $attr_name,
            'options' => $options,
            'visible' => true,
            'variation' => true
        );
    }
    
    // Display results
    echo "<h3>Mapped Product Data:</h3>\n";
    echo "<p><strong>Product Name:</strong> " . $wc_product_data['name'] . "</p>\n";
    echo "<p><strong>Product Type:</strong> " . $wc_product_data['type'] . "</p>\n";
    
    echo "<h4>Product Attributes:</h4>\n";
    foreach ( $wc_product_data['attributes'] as $attribute ) {
        echo "<p><strong>" . $attribute['name'] . ":</strong> " . implode(', ', $attribute['options']) . "</p>\n";
    }
    
    echo "<h4>Variations:</h4>\n";
    foreach ( $wc_product_data['variations'] as $variation ) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
        echo "<p><strong>Name:</strong> " . $variation['name'] . "</p>\n";
        echo "<p><strong>SKU:</strong> " . $variation['sku'] . "</p>\n";
        echo "<p><strong>Price:</strong> $" . number_format($variation['price'], 2) . "</p>\n";
        echo "<p><strong>Attributes:</strong></p>\n";
        echo "<ul>\n";
        foreach ( $variation['attributes'] as $attr ) {
            echo "<li>" . $attr['name'] . " = " . $attr['option'] . "</li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
    }
    
    // Validation
    echo "<h3>Validation:</h3>\n";
    $all_variations_have_attributes = true;
    foreach ( $wc_product_data['variations'] as $variation ) {
        if ( empty( $variation['attributes'] ) ) {
            $all_variations_have_attributes = false;
            break;
        }
    }
    
    if ( $all_variations_have_attributes ) {
        echo "<p style='color: green;'>✅ SUCCESS: All variations have attributes</p>\n";
    } else {
        echo "<p style='color: red;'>❌ FAILURE: Some variations have no attributes</p>\n";
    }
    
    if ( ! empty( $wc_product_data['attributes'] ) ) {
        echo "<p style='color: green;'>✅ SUCCESS: Product has attributes</p>\n";
    } else {
        echo "<p style='color: red;'>❌ FAILURE: Product has no attributes</p>\n";
    }
}

/**
 * Run the test
 */
function run_test() {
    echo "<h1>Square Import Fix Test</h1>\n";
    echo "<p>Testing the fix for variation name strategy products in the SWEVER import path.</p>\n";
    
    test_variation_mapping();
    
    echo "<h2>Summary</h2>\n";
    echo "<p>This test simulates the Monster Energy product import using the new logic.</p>\n";
    echo "<p>If successful, variations should have 'Product Options' attributes with their names as values.</p>\n";
}

// Run test if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    run_test();
}
