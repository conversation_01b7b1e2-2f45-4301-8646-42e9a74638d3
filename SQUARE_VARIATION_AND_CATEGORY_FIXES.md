# Square Kit - Variable Product & Category Import Fixes

## Issues Identified

### 1. Variable Product Detection Issue

**Problem:** Products with multiple variations created using Square's "Variation Name Strategy" were being imported as simple products instead of variable products.

**Root Cause:** The plugin's logic only considered products as "variable" if ALL variations had `item_option_values` populated. However, Square supports two strategies for creating variations:

1. **Item Options Strategy** - Uses standardized options (Size, Color, etc.) with `item_option_values` populated
2. **Variation Name Strategy** - Uses unique variation names with empty `item_option_values`

**Example:** Prime Lemonade product (ID: `EXYIY4RKJG7BQGTXE2YXQ35L`) has 3 variations:
- Regular
- Huge  
- The Biggest

These were created using variation names, not item options, so `item_option_values` was empty, causing the plugin to incorrectly import it as a simple product.

### 2. Category Handling Issue

**Problem:** When importing products with categories that already exist in WooCommerce, the import would fail with "category already exists" error and products would be filed under "uncategorized".

**Root Cause:** The category creation logic didn't check for existing categories before attempting to create new ones, and didn't gracefully handle the case where a category already existed.

## Solutions Implemented

### 1. Variable Product Detection Fix

**Files Modified:**
- `includes/importers/class-squarekit-square-import.php`
- `includes/importers/class-squarekit-product-importer.php`

**Changes:**
- Updated `determine_product_type()` method to treat ANY Square item with multiple variations as a variable product
- Updated `should_be_variable_product()` method with the same logic
- Added comprehensive documentation explaining both Square variation strategies

**New Logic:**
```php
// If only one variation, it's always simple
if ( count( $variations ) <= 1 ) {
    return 'simple';
}

// Multiple variations should be treated as variable product
// This handles both Square variation strategies:
// 1. Item Options Strategy: variations have item_option_values populated
// 2. Variation Name Strategy: variations have unique names but empty item_option_values
return 'variable';
```

### 2. Category Handling Fix

**File Modified:**
- `includes/sync/class-squarekit-category-sync.php`

**Changes:**
- Added check for existing categories by name before attempting to create new ones
- Added graceful handling of `term_exists` error (race condition protection)
- Ensured Square ID mapping is set for both new and existing categories
- Added proper parent relationship handling for existing categories

**New Logic:**
```php
// Check if category already exists by name first
$existing_category = get_term_by( 'name', $category_name, 'product_cat' );

if ( $existing_category ) {
    // Category already exists, use it and link to Square ID
    $wc_category_id = $existing_category->term_id;
    
    // Update parent if needed and different
    if ( $parent_wc_id && $existing_category->parent != $parent_wc_id ) {
        wp_update_term( $wc_category_id, 'product_cat', array( 'parent' => $parent_wc_id ) );
    }
} else {
    // Create new category with proper error handling
    // ...
}
```

## Testing

A test file `test-variation-fixes.php` has been created to verify the fixes work correctly. The test covers:

1. **Prime Lemonade Style Products** - Multiple variations with empty `item_option_values`
2. **Ceremonial Matcha Style Products** - Multiple variations with populated `item_option_values`  
3. **Single Variation Products** - Should remain simple products
4. **Category Handling** - Existing categories should be reused and linked to Square IDs

## Impact

### Before Fix:
- Prime Lemonade and similar products imported as simple products (losing variation data)
- Category import failures caused products to be uncategorized
- Manual cleanup required for affected products

### After Fix:
- All multi-variation Square products correctly import as variable products
- Existing WooCommerce categories are gracefully reused and linked to Square
- No more "uncategorized" products due to category conflicts
- Maintains compatibility with both Square variation strategies

## Backward Compatibility

These fixes are fully backward compatible:
- Existing simple products remain unchanged
- Existing variable products continue to work
- No database schema changes required
- All existing functionality preserved

## Square Documentation Reference

According to Square's official documentation:
- [Define Item Variations Using Options](https://developer.squareup.com/docs/catalog-api/item-options)
- Both "Item Options Strategy" and "Variation Name Strategy" are valid approaches
- Applications should support both methods for maximum compatibility

## Files Changed

1. `includes/importers/class-squarekit-square-import.php` - Lines 441-466
2. `includes/importers/class-squarekit-product-importer.php` - Lines 321-346  
3. `includes/sync/class-squarekit-category-sync.php` - Lines 273-315

## Phase 2: Default Attribute Creation (COMPLETED)

### 3. Missing Attribute Issue ✅

**Problem:** Variable products were created but variations had no attributes, making them unpurchasable.

**Root Cause:** Square products using "Variation Name Strategy" don't have `item_option_values`, so no WooCommerce attributes were created. WooCommerce requires attributes for variation dropdowns.

**Solution:** Implemented automatic creation of default "Product Options" attribute for variation name strategy products.

### 4. Implementation Details

**New Files Created:**
- `includes/importers/class-squarekit-default-attribute-handler.php` - Handles default attribute creation

**Key Features:**
- **Strategy Detection:** Automatically detects if Square product uses Variation Name Strategy
- **Default Attribute Creation:** Creates "Product Options" attribute with variation names as values
- **Smart Variation Linking:** Links each WooCommerce variation to appropriate attribute value
- **Backward Compatibility:** Maintains full compatibility with Item Options Strategy products

**Enhanced Logic:**
```php
// Detect strategy and create appropriate attributes
if ($default_handler->uses_variation_name_strategy($square_item)) {
    // Create default "Product Options" attribute
    $default_attribute = $default_handler->create_default_product_options_attribute($square_item, $product_id);
    $attributes['product-options'] = $default_attribute;
} else {
    // Use existing Item Options Strategy logic
    $attributes = $this->process_item_options_strategy($square_item, $product);
}
```

**Variation Linking:**
```php
// Link variation to default attribute
$variation_name = $square_variation['item_variation_data']['name'];
$attribute_value = sanitize_title($variation_name);
$variation_attributes['product-options'] = $attribute_value;
```

### 5. Files Modified (Phase 2)

**New Files:**
- `includes/importers/class-squarekit-default-attribute-handler.php`

**Modified Files:**
- `includes/importers/class-squarekit-attribute-importer.php` - Added strategy detection and default attribute creation
- `includes/importers/class-squarekit-variation-importer.php` - Added smart variation attribute building

**Test Files:**
- `test-default-attributes.php` - Tests default attribute handler
- `test-complete-variation-fix.php` - Tests complete import process

### 6. Expected Results (After All Fixes)

**Before Fixes:**
- Prime Lemonade imported as simple product ❌
- Monster Energy variations had no attributes ❌
- Products not purchasable ❌
- Category import failures ❌

**After Fixes:**
- Prime Lemonade imports as variable product ✅
- Monster Energy has "Product Options" attribute ✅
- Variations: Regular, Big, Extra Large properly linked ✅
- Products fully purchasable with dropdown selection ✅
- Categories gracefully handle existing ones ✅

## Next Steps

1. **Test the complete solution:**
   - Run `test-complete-variation-fix.php` to verify full process
   - Test with Monster Energy Ultra product (ID: `KK3Q37655RZJKODXUBCNX7I2`)
   - Test with Prime Lemonade product (ID: `EXYIY4RKJG7BQGTXE2YXQ35L`)

2. **Production deployment:**
   - Re-import affected products to get proper attributes
   - Verify frontend purchasing works correctly
   - Monitor logs for any remaining issues

3. **Validation:**
   - Check that variation dropdowns appear on product pages
   - Verify customers can select and purchase variations
   - Confirm pricing displays correctly

The complete solution addresses all identified issues while maintaining full compatibility with existing functionality and Square's documented variation strategies.
