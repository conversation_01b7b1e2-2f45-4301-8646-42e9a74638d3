<?php
/**
 * Square Kit Debug Logs Viewer
 * 
 * Public debug page for viewing Square Kit plugin logs
 * Access: yoursite.com/wp-content/plugins/squarekit/sk-logs.php
 */

// Security check - only allow access if WordPress is loaded
if (!defined('ABSPATH')) {
    // Try multiple possible WordPress paths
    $possible_paths = array(
        dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php', // Standard WordPress
        dirname(dirname(dirname(__FILE__))) . '/wp-load.php', // Alternative structure
        dirname(dirname(dirname(dirname(dirname(__FILE__))))) . '/wp-load.php', // Deeper nesting
        $_SERVER['DOCUMENT_ROOT'] . '/wp-load.php', // Document root
    );

    $wp_loaded = false;
    foreach ($possible_paths as $wp_load_path) {
        if (file_exists($wp_load_path)) {
            require_once $wp_load_path;
            $wp_loaded = true;
            break;
        }
    }

    if (!$wp_loaded) {
        die('WordPress not found. Please access this file through your WordPress installation.');
    }
}

// Check if user has admin capabilities (optional security)
if (!current_user_can('manage_options')) {
    // For debugging purposes, we'll allow access but show a warning
    $show_warning = true;
} else {
    $show_warning = false;
}

// Load the logger
require_once dirname(__FILE__) . '/includes/class-squarekit-logger.php';
$logger = SquareKit_Logger::get_instance();

// Handle actions
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : '';
$message = '';

if ($action === 'clear' && current_user_can('manage_options')) {
    $logger->clear_logs();
    $message = 'Logs cleared successfully.';
}

// Get filter parameters
$level_filter = isset($_GET['level']) ? sanitize_text_field($_GET['level']) : '';
$category_filter = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : '';
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;

// Get logs and stats
$logs = $logger->get_logs($limit, $level_filter, $category_filter);
$stats = $logger->get_stats();

// Get unique categories and levels for filters
$categories = array_keys($stats['by_category']);
$levels = array_keys($stats['by_level']);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Square Kit Debug Logs</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .filters {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 500;
            color: #555;
        }

        select, input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .logs-container {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .log-entry {
            border-bottom: 1px solid #eee;
            padding: 15px 20px;
            transition: background-color 0.2s;
        }

        .log-entry:hover {
            background: #f8f9fa;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .log-level {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .level-debug {
            background: #e8f4fd;
            color: #1976d2;
        }

        .level-info {
            background: #e3f2fd;
            color: #0277bd;
        }

        .level-warning {
            background: #fff3e0;
            color: #f57c00;
        }

        .level-error {
            background: #ffebee;
            color: #d32f2f;
        }

        .level-critical {
            background: #fce4ec;
            color: #c2185b;
        }

        .log-category {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            color: #666;
        }

        .log-timestamp {
            color: #888;
            font-size: 12px;
        }

        .log-message {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .log-context {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        .log-meta {
            display: flex;
            gap: 15px;
            font-size: 11px;
            color: #888;
            margin-top: 8px;
        }

        .no-logs {
            text-align: center;
            padding: 40px;
            color: #888;
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .log-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .log-meta {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Square Kit Debug Logs</h1>
            <p>Real-time debugging and monitoring for Square Kit plugin operations</p>
        </div>

        <?php if ($show_warning): ?>
        <div class="warning">
            <strong>⚠️ Debug Access:</strong> You are viewing debug logs without admin privileges. 
            This page is intended for debugging purposes only.
        </div>
        <?php endif; ?>

        <?php if ($message): ?>
        <div class="success">
            <?php echo esc_html($message); ?>
        </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total_entries']); ?></div>
                <div class="stat-label">Total Log Entries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['file_size_mb']; ?> MB</div>
                <div class="stat-label">Log File Size</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($categories); ?></div>
                <div class="stat-label">Categories</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo isset($stats['by_level']['error']) ? $stats['by_level']['error'] : 0; ?></div>
                <div class="stat-label">Errors</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <form method="get" action="">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="level">Log Level:</label>
                        <select name="level" id="level">
                            <option value="">All Levels</option>
                            <?php foreach ($levels as $level): ?>
                                <option value="<?php echo esc_attr($level); ?>" <?php selected($level_filter, $level); ?>>
                                    <?php echo esc_html(ucfirst($level)); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="category">Category:</label>
                        <select name="category" id="category">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo esc_attr($category); ?>" <?php selected($category_filter, $category); ?>>
                                    <?php echo esc_html(ucfirst($category)); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="limit">Limit:</label>
                        <select name="limit" id="limit">
                            <option value="50" <?php selected($limit, 50); ?>>50</option>
                            <option value="100" <?php selected($limit, 100); ?>>100</option>
                            <option value="250" <?php selected($limit, 250); ?>>250</option>
                            <option value="500" <?php selected($limit, 500); ?>>500</option>
                        </select>
                    </div>

                    <button type="submit" class="btn btn-primary">Filter</button>
                    
                    <?php if (current_user_can('manage_options')): ?>
                    <a href="?action=clear" class="btn btn-danger" onclick="return confirm('Are you sure you want to clear all logs?')">
                        Clear Logs
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Logs -->
        <div class="logs-container">
            <?php if (empty($logs)): ?>
                <div class="no-logs">
                    <h3>No logs found</h3>
                    <p>No log entries match your current filters, or no logs have been generated yet.</p>
                </div>
            <?php else: ?>
                <?php foreach ($logs as $log): ?>
                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-<?php echo esc_attr(strtolower($log['level'])); ?>">
                                    <?php echo esc_html($log['level']); ?>
                                </span>
                                <span class="log-category"><?php echo esc_html($log['category']); ?></span>
                            </div>
                            <div class="log-timestamp"><?php echo esc_html($log['timestamp']); ?></div>
                        </div>
                        
                        <div class="log-message">
                            <?php echo esc_html($log['message']); ?>
                        </div>

                        <?php if (!empty($log['context'])): ?>
                            <div class="log-context">
                                <?php echo esc_html(json_encode($log['context'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)); ?>
                            </div>
                        <?php endif; ?>

                        <div class="log-meta">
                            <span>Memory: <?php echo esc_html($log['memory_mb']); ?> MB</span>
                            <span>User: <?php echo esc_html($log['user_id'] ?: 'Guest'); ?></span>
                            <span>URI: <?php echo esc_html($log['request_uri']); ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Auto-refresh every 30 seconds if no filters are applied
        <?php if (empty($level_filter) && empty($category_filter)): ?>
        setTimeout(function() {
            window.location.reload();
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
