<?php
/**
 * Debug Brazil Coffee Import Issue
 * 
 * Deep dive into the specific Square data for Brazil Yellow Cataui Coffee
 * to understand why it's being imported as Variable instead of Simple
 */

// Load WordPress
require_once '../../../wp-config.php';
require_once '../../../wp-load.php';

// Load SquareKit classes
require_once 'includes/class-squarekit-settings.php';
require_once 'includes/api/class-squarekit-square-api.php';

$settings = new SquareKit_Settings();
$is_connected = $settings->is_connected();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Brazil Coffee Import Issue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .debug-card { 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 15px 0; 
            background: #f9f9f9;
        }
        .error { border-color: #dc3545; background: #f8d7da; }
        .warning { border-color: #ffc107; background: #fff3cd; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007cba;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <h1>🔍 Debug Brazil Coffee Import Issue</h1>
    <p><strong>Debug Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <?php if (!$is_connected): ?>
        <div class="debug-card error">
            <h2>❌ Not Connected to Square</h2>
            <p>Please connect to Square first.</p>
        </div>
    <?php else: ?>
    
    <!-- Step 1: Get Raw Square Data -->
    <div class="debug-card info">
        <h2>📦 Step 1: Raw Square API Data</h2>
        
        <?php
        try {
            $square_api = new SquareKit_Square_API();
            $brazil_coffee_id = '2G64ELRH4LNWWI7TEHQTZIBX';
            
            // Get the specific item
            $item_response = $square_api->get_catalog_object($brazil_coffee_id);
            
            if (is_wp_error($item_response)) {
                echo "<div class='error'>Error: " . $item_response->get_error_message() . "</div>";
            } else {
                echo "<h3>Raw Square Data for Brazil Coffee:</h3>";
                echo "<div class='code-block'>" . esc_html(json_encode($item_response, JSON_PRETTY_PRINT)) . "</div>";
                
                // Analyze the data
                $item_data = $item_response['item_data'] ?? array();
                $variations = $item_data['variations'] ?? array();
                
                echo "<h3>Analysis:</h3>";
                echo "<ul>";
                echo "<li><strong>Variations Count:</strong> " . count($variations) . "</li>";
                echo "<li><strong>Has Item Options:</strong> " . (!empty($item_data['item_options']) ? 'Yes' : 'No') . "</li>";
                
                if (!empty($variations)) {
                    $first_variation = $variations[0];
                    $variation_data = $first_variation['item_variation_data'] ?? array();
                    
                    echo "<li><strong>First Variation ID:</strong> " . esc_html($first_variation['id']) . "</li>";
                    echo "<li><strong>Track Inventory:</strong> " . (!empty($variation_data['track_inventory']) ? 'Yes' : 'No') . "</li>";
                    echo "<li><strong>Has Option Values:</strong> " . (!empty($variation_data['item_option_values']) ? 'Yes' : 'No') . "</li>";
                    
                    if (!empty($variation_data['item_option_values'])) {
                        echo "<li><strong>Option Values:</strong> " . esc_html(json_encode($variation_data['item_option_values'])) . "</li>";
                    }
                }
                echo "</ul>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>Exception: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>
    
    <!-- Step 2: Test Our Product Type Logic -->
    <div class="debug-card warning">
        <h2>🧪 Step 2: Test Our Product Type Detection Logic</h2>
        
        <?php
        if (isset($item_response) && !is_wp_error($item_response)) {
            // Test our current logic
            $variations = $item_response['item_data']['variations'] ?? array();
            $variation_count = count($variations);
            
            echo "<h3>Current Logic Test:</h3>";
            echo "<ul>";
            echo "<li><strong>Variation Count:</strong> {$variation_count}</li>";
            
            if ($variation_count <= 1) {
                $expected_type = 'simple';
                echo "<li><strong>Logic Result:</strong> Simple (≤1 variation)</li>";
            } else {
                // Check if variations have option values
                $all_have_options = true;
                foreach ($variations as $variation) {
                    if (empty($variation['item_variation_data']['item_option_values'])) {
                        $all_have_options = false;
                        break;
                    }
                }
                $expected_type = $all_have_options ? 'variable' : 'simple';
                echo "<li><strong>Logic Result:</strong> " . ($all_have_options ? 'Variable (has options)' : 'Simple (no options)') . "</li>";
            }
            echo "</ul>";
            
            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>🎯 Expected Product Type: <strong>" . strtoupper($expected_type) . "</strong></h4>";
            echo "</div>";
        }
        ?>
    </div>
    
    <!-- Step 3: Check Current WooCommerce Product -->
    <div class="debug-card info">
        <h2>🛒 Step 3: Current WooCommerce Product Status</h2>
        
        <?php
        // Check if product exists in WooCommerce
        $existing_products = get_posts(array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_square_item_id',
                    'value' => $brazil_coffee_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        ));
        
        if (!empty($existing_products)) {
            $wc_product = wc_get_product($existing_products[0]->ID);
            echo "<h3>Existing WooCommerce Product:</h3>";
            echo "<ul>";
            echo "<li><strong>Product ID:</strong> " . $wc_product->get_id() . "</li>";
            echo "<li><strong>Product Type:</strong> " . $wc_product->get_type() . "</li>";
            echo "<li><strong>Stock Status:</strong> " . $wc_product->get_stock_status() . "</li>";
            echo "<li><strong>Manage Stock:</strong> " . ($wc_product->get_manage_stock() ? 'Yes' : 'No') . "</li>";
            echo "<li><strong>Stock Quantity:</strong> " . ($wc_product->get_stock_quantity() ?? 'N/A') . "</li>";
            echo "</ul>";
            
            echo "<a href='/wp-admin/post.php?post={$wc_product->get_id()}&action=edit' target='_blank' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>View in WooCommerce</a>";
            
            if ($wc_product->get_type() === 'variable') {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; color: #721c24;'>";
                echo "<h4>❌ PROBLEM CONFIRMED</h4>";
                echo "<p>This product is incorrectly imported as Variable when it should be Simple!</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; color: #155724;'>";
                echo "<h4>✅ CORRECT TYPE</h4>";
                echo "<p>Product is correctly imported as Simple.</p>";
                echo "</div>";
            }
        } else {
            echo "<p>No existing WooCommerce product found for this Square item.</p>";
            echo "<button onclick='importBrazilCoffee()' style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;'>Import Brazil Coffee Now</button>";
        }
        ?>
        
        <div id="import-results"></div>
    </div>
    
    <!-- Step 4: Debug Import Process -->
    <div class="debug-card warning">
        <h2>🔧 Step 4: Debug Import Process</h2>
        
        <p>Let's trace through the import process step by step:</p>
        
        <button onclick="debugImportProcess()" style="background: #ffc107; color: black; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">Debug Import Process</button>
        
        <div id="debug-results"></div>
        
        <h3>Key Questions to Answer:</h3>
        <ol>
            <li>Is our product type detection logic being called?</li>
            <li>What data is being passed to the product creation method?</li>
            <li>Is there a different code path being used?</li>
            <li>Are we using the right importer class?</li>
        </ol>
    </div>
    
    <?php endif; ?>
    
    <script>
    function importBrazilCoffee() {
        const resultsDiv = document.getElementById('import-results');
        resultsDiv.innerHTML = '<div style="background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">Importing Brazil Coffee...</div>';
        
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'squarekit_import_product',
                product_id: '2G64ELRH4LNWWI7TEHQTZIBX',
                nonce: '<?php echo wp_create_nonce('squarekit-admin'); ?>'
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                resultsDiv.innerHTML += '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;">✅ Import SUCCESS</div>';
                if (result.data.wc_product_id) {
                    resultsDiv.innerHTML += `<div style="background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">Product ID: ${result.data.wc_product_id}</div>`;
                    resultsDiv.innerHTML += `<div><a href="/wp-admin/post.php?post=${result.data.wc_product_id}&action=edit" target="_blank" style="background: #007cba; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;">View Product</a></div>`;
                    
                    // Reload page to see updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                }
            } else {
                resultsDiv.innerHTML += `<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; color: #721c24;">❌ Import FAILED: ${result.data.message}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML += `<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; color: #721c24;">❌ ERROR: ${error.message}</div>`;
        });
    }
    
    function debugImportProcess() {
        const resultsDiv = document.getElementById('debug-results');
        resultsDiv.innerHTML = '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">This would require adding debug logging to the import process...</div>';
        resultsDiv.innerHTML += '<div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;"><h4>Next Steps:</h4><ol><li>Add debug logging to the import process</li><li>Check which importer class is being used</li><li>Verify the product type detection is being called</li><li>Check if there are any overrides happening</li></ol></div>';
    }
    </script>
    
</body>
</html>
