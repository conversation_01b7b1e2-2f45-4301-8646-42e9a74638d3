<?php
/**
 * Complete test for the variation name strategy fix
 * Tests the full import process from Square data to purchasable WooCommerce product
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once '../../../wp-load.php';
}

// Include necessary files
require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-default-attribute-handler.php';
require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-attribute-importer.php';
require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-variation-importer.php';
require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-create-product.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Square Kit - Complete Variation Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Square Kit - Complete Variation Fix Test</h1>
    
    <div class="test-section">
        <h2>Test: Complete Import Process Simulation</h2>
        <p>Simulating the complete import process for Monster Energy Ultra product</p>
        
        <?php
        // Monster Energy Ultra data (variation name strategy)
        $monster_energy_data = array(
            'id' => 'KK3Q37655RZJKODXUBCNX7I2',
            'item_data' => array(
                'name' => 'Monster Energy Ultra- 350ml',
                'description' => 'Energy drink with multiple size options',
                'variations' => array(
                    array(
                        'id' => 'REGULAR_VAR',
                        'item_variation_data' => array(
                            'name' => 'Regular',
                            'pricing_type' => 'FIXED_PRICING',
                            'price_money' => array(
                                'amount' => 1499,
                                'currency' => 'USD'
                            ),
                            'item_option_values' => array() // Empty - variation name strategy
                        )
                    ),
                    array(
                        'id' => 'BIG_VAR',
                        'item_variation_data' => array(
                            'name' => 'Big',
                            'pricing_type' => 'FIXED_PRICING',
                            'price_money' => array(
                                'amount' => 20000,
                                'currency' => 'USD'
                            ),
                            'item_option_values' => array() // Empty - variation name strategy
                        )
                    ),
                    array(
                        'id' => 'EXTRA_LARGE_VAR',
                        'item_variation_data' => array(
                            'name' => 'Extra Large',
                            'pricing_type' => 'FIXED_PRICING',
                            'price_money' => array(
                                'amount' => 150000,
                                'currency' => 'USD'
                            ),
                            'item_option_values' => array() // Empty - variation name strategy
                        )
                    )
                )
            )
        );
        
        try {
            // Step 1: Create WooCommerce product
            echo '<h3>Step 1: Creating WooCommerce Product</h3>';
            
            $product = new WC_Product_Variable();
            $product->set_name($monster_energy_data['item_data']['name']);
            $product->set_description($monster_energy_data['item_data']['description'] ?? '');
            $product->set_status('publish');
            $product_id = $product->save();
            
            if ($product_id) {
                echo '<p class="success">✓ Created variable product with ID: ' . $product_id . '</p>';
            } else {
                echo '<p class="error">✗ Failed to create product</p>';
                return;
            }
            
            // Step 2: Process attributes
            echo '<h3>Step 2: Processing Attributes</h3>';
            
            $attribute_importer = new SquareKit_Attribute_Importer();
            $attributes = $attribute_importer->process_square_attributes($monster_energy_data, $product);
            
            if (!empty($attributes)) {
                echo '<p class="success">✓ Attributes processed successfully</p>';

                // Set attributes on product BEFORE creating variations
                $product->set_attributes($attributes);
                $product->save();

                // Refresh product object to ensure attributes are properly loaded
                $product = wc_get_product($product_id);

                foreach ($attributes as $slug => $attribute) {
                    echo '<p class="info">- Created attribute: ' . $attribute->get_name() . ' with values: ' . implode(', ', $attribute->get_options()) . '</p>';
                }

                // Verify attributes are set
                $saved_attributes = $product->get_attributes();
                if (!empty($saved_attributes)) {
                    echo '<p class="success">✓ Attributes successfully saved to product</p>';
                } else {
                    echo '<p class="error">✗ Attributes not saved to product</p>';
                }
            } else {
                echo '<p class="error">✗ No attributes created</p>';
            }
            
            // Step 3: Create variations
            echo '<h3>Step 3: Creating Variations</h3>';
            
            if (class_exists('SquareKit_Variation_Importer')) {
                $variation_importer = new SquareKit_Variation_Importer();
                
                // Simulate attribute mappings (empty for variation name strategy)
                $attribute_mappings = array();
                
                foreach ($monster_energy_data['item_data']['variations'] as $square_variation) {
                    // Use reflection to access private method
                    $reflection = new ReflectionClass($variation_importer);
                    $method = $reflection->getMethod('build_variation_attributes_smart');
                    $method->setAccessible(true);
                    
                    $variation_attributes = $method->invoke($variation_importer, $product_id, $square_variation, $attribute_mappings);
                    
                    if (!empty($variation_attributes)) {
                        // Create the variation
                        $variation = new WC_Product_Variation();
                        $variation->set_parent_id($product_id);
                        $variation->set_attributes($variation_attributes);
                        
                        // Set pricing
                        $price_data = $square_variation['item_variation_data']['price_money'] ?? array();
                        if (!empty($price_data['amount'])) {
                            $price = $price_data['amount'] / 100; // Convert cents to dollars
                            $variation->set_regular_price($price);
                            $variation->set_price($price);
                        }
                        
                        // Set other data
                        $variation->set_status('publish');
                        $variation->set_stock_status('instock');
                        $variation->set_manage_stock(false);
                        $variation->set_virtual(false);
                        $variation->set_downloadable(false);
                        $variation->update_meta_data('_square_variation_id', $square_variation['id']);
                        
                        $variation_id = $variation->save();
                        
                        if ($variation_id) {
                            $variation_name = $square_variation['item_variation_data']['name'];
                            echo '<p class="success">✓ Created variation: ' . $variation_name . ' (ID: ' . $variation_id . ') - $' . $price . '</p>';
                            echo '<p class="info">  Attributes: ' . json_encode($variation_attributes) . '</p>';
                        } else {
                            echo '<p class="error">✗ Failed to create variation: ' . $square_variation['item_variation_data']['name'] . '</p>';
                        }
                    } else {
                        echo '<p class="error">✗ No attributes generated for variation: ' . $square_variation['item_variation_data']['name'] . '</p>';
                    }
                }
            } else {
                echo '<p class="error">✗ SquareKit_Variation_Importer class not available</p>';
            }

            // Step 3.5: Sync product data to make it purchasable
            echo '<h3>Step 3.5: Syncing Product Data</h3>';

            // Refresh and sync the product
            $product = wc_get_product($product_id);
            if ($product && $product->is_type('variable')) {
                // Sync variation prices to parent product
                WC_Product_Variable::sync($product_id);

                // Clear any caches
                wc_delete_product_transients($product_id);

                // Force refresh product object
                wp_cache_delete($product_id, 'posts');
                $product = wc_get_product($product_id);

                echo '<p class="success">✓ Product data synchronized</p>';
            }

            // Step 4: Validate the final product
            echo '<h3>Step 4: Product Validation</h3>';
            
            // Refresh product object
            $product = wc_get_product($product_id);
            
            if ($product && $product->is_type('variable')) {
                echo '<p class="success">✓ Product is correctly set as variable</p>';
                
                $variations = $product->get_children();
                echo '<p class="info">Number of variations: ' . count($variations) . '</p>';
                
                $product_attributes = $product->get_attributes();
                echo '<p class="info">Number of attributes: ' . count($product_attributes) . '</p>';
                
                // Check if product is purchasable
                if ($product->is_purchasable()) {
                    echo '<p class="success">✓ Product is purchasable</p>';
                } else {
                    echo '<p class="error">✗ Product is not purchasable</p>';

                    // Debug why it's not purchasable
                    echo '<p class="info">Debug info:</p>';
                    echo '<p class="info">- Product status: ' . $product->get_status() . '</p>';
                    echo '<p class="info">- Product type: ' . $product->get_type() . '</p>';
                    echo '<p class="info">- Has variations: ' . (count($product->get_children()) > 0 ? 'Yes' : 'No') . '</p>';
                    echo '<p class="info">- Catalog visibility: ' . $product->get_catalog_visibility() . '</p>';

                    // Check if any variations are purchasable
                    $purchasable_variations = 0;
                    foreach ($product->get_children() as $variation_id) {
                        $variation = wc_get_product($variation_id);
                        if ($variation && $variation->is_purchasable()) {
                            $purchasable_variations++;
                        }
                    }
                    echo '<p class="info">- Purchasable variations: ' . $purchasable_variations . ' / ' . count($product->get_children()) . '</p>';
                }
                
                // Check variations
                $all_variations_valid = true;
                foreach ($variations as $variation_id) {
                    $variation = wc_get_product($variation_id);
                    if ($variation) {
                        $var_attributes = $variation->get_attributes();
                        if (empty($var_attributes)) {
                            echo '<p class="error">✗ Variation ' . $variation_id . ' has no attributes</p>';
                            $all_variations_valid = false;
                        } else {
                            echo '<p class="success">✓ Variation ' . $variation_id . ' has attributes: ' . json_encode($var_attributes) . '</p>';
                        }
                    }
                }
                
                if ($all_variations_valid) {
                    echo '<p class="success">✓ All variations have proper attributes</p>';
                } else {
                    echo '<p class="error">✗ Some variations are missing attributes</p>';
                }
                
                // Display price range
                $min_price = $product->get_variation_price('min');
                $max_price = $product->get_variation_price('max');
                if ($min_price && $max_price) {
                    if ($min_price === $max_price) {
                        echo '<p class="info">Price: $' . $min_price . '</p>';
                    } else {
                        echo '<p class="info">Price range: $' . $min_price . ' - $' . $max_price . '</p>';
                    }
                } else {
                    echo '<p class="warning">No price range available</p>';
                }
                
            } else {
                echo '<p class="error">✗ Product is not variable or not found</p>';
            }
            
            // Step 5: Frontend simulation
            echo '<h3>Step 5: Frontend Simulation</h3>';
            
            // Check if variation dropdown would work
            $available_variations = $product->get_available_variations();
            if (!empty($available_variations)) {
                echo '<p class="success">✓ Product has ' . count($available_variations) . ' available variations for frontend</p>';
                
                foreach ($available_variations as $variation) {
                    echo '<p class="info">- Variation ID: ' . $variation['variation_id'] . ', Attributes: ' . json_encode($variation['attributes']) . ', Price: ' . $variation['display_price'] . '</p>';
                }
            } else {
                echo '<p class="error">✗ No variations available for frontend display</p>';
            }
            
            // Clean up
            echo '<h3>Cleanup</h3>';
            wp_delete_post($product_id, true);
            echo '<p class="info">Test product deleted</p>';
            
        } catch (Exception $e) {
            echo '<p class="error">✗ Exception occurred: ' . $e->getMessage() . '</p>';
            echo '<pre>' . $e->getTraceAsString() . '</pre>';
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>Summary</h2>
        <p><strong>What this test validates:</strong></p>
        <ul>
            <li>✅ Variable product creation</li>
            <li>✅ Default "Product Options" attribute creation</li>
            <li>✅ Variation creation with proper attribute linking</li>
            <li>✅ Pricing assignment</li>
            <li>✅ Product purchasability</li>
            <li>✅ Frontend variation availability</li>
        </ul>
        
        <p><strong>Expected Results:</strong></p>
        <ul>
            <li>Product should be variable type</li>
            <li>Should have "Product Options" attribute with values: Regular, Big, Extra Large</li>
            <li>Should have 3 variations with correct pricing</li>
            <li>Each variation should be linked to appropriate attribute value</li>
            <li>Product should be purchasable on frontend</li>
            <li>Dropdown selection should work</li>
        </ul>
    </div>
    
</body>
</html>
