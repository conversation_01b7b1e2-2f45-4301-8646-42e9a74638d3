<?php
/**
 * Debug why variable products with default attributes aren't purchasable
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once '../../../wp-load.php';
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Purchasable Issue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Purchasable Issue</h1>
    
    <?php
    // Create a simple test to understand the issue
    echo '<h2>Creating Minimal Test Product</h2>';
    
    // Create product
    $product = new WC_Product_Variable();
    $product->set_name('Debug Test Product');
    $product->set_status('publish');
    $product_id = $product->save();
    
    echo '<p class="info">Created product ID: ' . $product_id . '</p>';
    
    // Create attribute manually
    $attribute = new WC_Product_Attribute();
    $attribute->set_name('Product Options');
    $attribute->set_options(array('Option 1', 'Option 2'));
    $attribute->set_visible(true);
    $attribute->set_variation(true);
    
    $product->set_attributes(array('product-options' => $attribute));
    $product->save();
    
    echo '<p class="success">✓ Added attribute to product</p>';
    
    // Create variations manually
    for ($i = 1; $i <= 2; $i++) {
        $variation = new WC_Product_Variation();
        $variation->set_parent_id($product_id);
        $variation->set_attributes(array('product-options' => 'option-' . $i));
        $variation->set_regular_price(10 + $i);
        $variation->set_price(10 + $i);
        $variation->set_status('publish');
        $variation->set_stock_status('instock');
        $variation->set_manage_stock(false);
        
        $variation_id = $variation->save();
        echo '<p class="success">✓ Created variation ' . $i . ' (ID: ' . $variation_id . ')</p>';
    }
    
    // Sync the product
    WC_Product_Variable::sync($product_id);
    echo '<p class="info">Synced product</p>';
    
    // Refresh product
    $product = wc_get_product($product_id);
    
    // Test purchasability
    echo '<h2>Purchasability Test</h2>';
    
    if ($product->is_purchasable()) {
        echo '<p class="success">✓ Product is purchasable</p>';
    } else {
        echo '<p class="error">✗ Product is not purchasable</p>';
    }
    
    // Check variations
    $variations = $product->get_available_variations();
    echo '<p class="info">Available variations: ' . count($variations) . '</p>';
    
    if (empty($variations)) {
        echo '<p class="error">✗ No available variations - this is the problem!</p>';
        
        // Debug why no variations are available
        $all_variations = $product->get_children();
        echo '<p class="info">Total variations: ' . count($all_variations) . '</p>';
        
        foreach ($all_variations as $variation_id) {
            $variation = wc_get_product($variation_id);
            if ($variation) {
                echo '<p class="info">Variation ' . $variation_id . ':</p>';
                echo '<p class="info">- Status: ' . $variation->get_status() . '</p>';
                echo '<p class="info">- Stock status: ' . $variation->get_stock_status() . '</p>';
                echo '<p class="info">- Price: ' . $variation->get_price() . '</p>';
                echo '<p class="info">- Attributes: ' . json_encode($variation->get_attributes()) . '</p>';
                echo '<p class="info">- Is purchasable: ' . ($variation->is_purchasable() ? 'Yes' : 'No') . '</p>';
                echo '<p class="info">- Variation is visible: ' . ($variation->variation_is_visible() ? 'Yes' : 'No') . '</p>';
            }
        }
    } else {
        echo '<p class="success">✓ Variations are available</p>';
        foreach ($variations as $variation) {
            echo '<p class="info">Variation: ' . json_encode($variation['attributes']) . ' - $' . $variation['display_price'] . '</p>';
        }
    }
    
    // Check product attributes
    echo '<h2>Product Attributes Check</h2>';
    $product_attributes = $product->get_attributes();
    
    if (empty($product_attributes)) {
        echo '<p class="error">✗ Product has no attributes</p>';
    } else {
        echo '<p class="success">✓ Product has attributes</p>';
        foreach ($product_attributes as $slug => $attribute) {
            echo '<p class="info">Attribute: ' . $attribute->get_name() . ' (' . $slug . ')</p>';
            echo '<p class="info">- Options: ' . implode(', ', $attribute->get_options()) . '</p>';
            echo '<p class="info">- Used for variations: ' . ($attribute->get_variation() ? 'Yes' : 'No') . '</p>';
            echo '<p class="info">- Visible: ' . ($attribute->get_visible() ? 'Yes' : 'No') . '</p>';
        }
    }
    
    // Test the specific issue with our attribute structure
    echo '<h2>Testing Our Attribute Structure</h2>';
    
    // Check if the attribute slug matches what variations expect
    $expected_slug = 'product-options';
    if (isset($product_attributes[$expected_slug])) {
        echo '<p class="success">✓ Product has expected attribute slug: ' . $expected_slug . '</p>';
    } else {
        echo '<p class="error">✗ Product missing expected attribute slug: ' . $expected_slug . '</p>';
        echo '<p class="info">Available slugs: ' . implode(', ', array_keys($product_attributes)) . '</p>';
    }
    
    // Check if variation attributes match product attributes
    echo '<h2>Attribute Matching Check</h2>';
    $all_variations = $product->get_children();
    
    foreach ($all_variations as $variation_id) {
        $variation = wc_get_product($variation_id);
        if ($variation) {
            $variation_attributes = $variation->get_attributes();
            echo '<p class="info">Variation ' . $variation_id . ' attributes: ' . json_encode($variation_attributes) . '</p>';
            
            // Check if variation attributes match product attributes
            foreach ($variation_attributes as $attr_slug => $attr_value) {
                if (isset($product_attributes[$attr_slug])) {
                    $product_attr = $product_attributes[$attr_slug];
                    $product_options = $product_attr->get_options();
                    
                    // Check if the variation's attribute value exists in product's attribute options
                    $value_exists = false;
                    foreach ($product_options as $option) {
                        if (sanitize_title($option) === $attr_value) {
                            $value_exists = true;
                            break;
                        }
                    }
                    
                    if ($value_exists) {
                        echo '<p class="success">✓ Variation attribute value "' . $attr_value . '" matches product options</p>';
                    } else {
                        echo '<p class="error">✗ Variation attribute value "' . $attr_value . '" does not match any product options</p>';
                        echo '<p class="info">Available options: ' . implode(', ', array_map('sanitize_title', $product_options)) . '</p>';
                    }
                } else {
                    echo '<p class="error">✗ Variation has attribute "' . $attr_slug . '" but product does not</p>';
                }
            }
        }
    }
    
    // Clean up
    wp_delete_post($product_id, true);
    echo '<p class="info">Test product deleted</p>';
    ?>
    
</body>
</html>
