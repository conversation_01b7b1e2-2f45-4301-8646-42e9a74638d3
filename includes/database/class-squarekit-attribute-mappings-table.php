<?php
/**
 * SquareKit Attribute Mappings Database Table
 *
 * Handles creation and management of the attribute mappings table
 *
 * @package SquareKit
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Attribute Mappings Table Class
 */
class SquareKit_Attribute_Mappings_Table {

    /**
     * Table name
     *
     * @var string
     */
    private $table_name;

    /**
     * Database version for this table
     *
     * @var string
     */
    private $db_version = '1.0.0';

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'squarekit_attribute_mappings';
    }

    /**
     * Create the attribute mappings table
     *
     * @return bool Success status
     */
    public function create_table() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE {$this->table_name} (
            id int(11) NOT NULL AUTO_INCREMENT,
            square_option_set_id varchar(255) DEFAULT NULL,
            wc_attribute_id int(11) DEFAULT NULL,
            square_option_id varchar(255) DEFAULT NULL,
            wc_term_id int(11) DEFAULT NULL,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY square_option_set_id (square_option_set_id),
            KEY wc_attribute_id (wc_attribute_id),
            KEY square_option_id (square_option_id),
            KEY wc_term_id (wc_term_id),
            UNIQUE KEY unique_option_set (square_option_set_id, wc_attribute_id),
            UNIQUE KEY unique_option (square_option_id, wc_term_id)
        ) $charset_collate;";

        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );

        // Check if table was created successfully
        if ( $wpdb->get_var( "SHOW TABLES LIKE '{$this->table_name}'" ) === $this->table_name ) {
            // Update database version
            update_option( 'squarekit_attribute_mappings_db_version', $this->db_version );
            return true;
        }

        return false;
    }

    /**
     * Check if table exists
     *
     * @return bool True if table exists
     */
    public function table_exists() {
        global $wpdb;
        return $wpdb->get_var( "SHOW TABLES LIKE '{$this->table_name}'" ) === $this->table_name;
    }

    /**
     * Get table name
     *
     * @return string Table name
     */
    public function get_table_name() {
        return $this->table_name;
    }

    /**
     * Drop the table (for uninstall)
     *
     * @return bool Success status
     */
    public function drop_table() {
        global $wpdb;
        
        $result = $wpdb->query( "DROP TABLE IF EXISTS {$this->table_name}" );
        
        if ( $result !== false ) {
            delete_option( 'squarekit_attribute_mappings_db_version' );
            return true;
        }
        
        return false;
    }

    /**
     * Check if table needs to be created or updated
     *
     * @return bool True if table needs creation/update
     */
    public function needs_update() {
        if ( ! $this->table_exists() ) {
            return true;
        }

        $current_version = get_option( 'squarekit_attribute_mappings_db_version', '0.0.0' );
        return version_compare( $current_version, $this->db_version, '<' );
    }

    /**
     * Initialize table (create if needed)
     *
     * @return bool Success status
     */
    public function init() {
        if ( $this->needs_update() ) {
            return $this->create_table();
        }
        return true;
    }

    /**
     * Get all mappings
     *
     * @return array Array of mapping records
     */
    public function get_all_mappings() {
        global $wpdb;
        
        return $wpdb->get_results( 
            "SELECT * FROM {$this->table_name} ORDER BY created_at DESC", 
            ARRAY_A 
        );
    }

    /**
     * Get option set mappings
     *
     * @return array Array of option set mapping records
     */
    public function get_option_set_mappings() {
        global $wpdb;
        
        return $wpdb->get_results( 
            "SELECT * FROM {$this->table_name} 
             WHERE square_option_id IS NULL 
             ORDER BY created_at DESC", 
            ARRAY_A 
        );
    }

    /**
     * Get option mappings
     *
     * @return array Array of option mapping records
     */
    public function get_option_mappings() {
        global $wpdb;
        
        return $wpdb->get_results( 
            "SELECT * FROM {$this->table_name} 
             WHERE square_option_id IS NOT NULL 
             ORDER BY created_at DESC", 
            ARRAY_A 
        );
    }

    /**
     * Delete mapping by Square option set ID
     *
     * @param string $square_option_set_id Square option set ID
     * @return bool Success status
     */
    public function delete_option_set_mapping( $square_option_set_id ) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array( 'square_option_set_id' => $square_option_set_id ),
            array( '%s' )
        );
        
        return $result !== false;
    }

    /**
     * Delete mapping by Square option ID
     *
     * @param string $square_option_id Square option ID
     * @return bool Success status
     */
    public function delete_option_mapping( $square_option_id ) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array( 'square_option_id' => $square_option_id ),
            array( '%s' )
        );
        
        return $result !== false;
    }

    /**
     * Clean up orphaned mappings (where WooCommerce attributes/terms no longer exist)
     *
     * @return int Number of cleaned up records
     */
    public function cleanup_orphaned_mappings() {
        global $wpdb;
        
        $cleaned_count = 0;
        
        // Clean up option set mappings with non-existent attributes
        $orphaned_attributes = $wpdb->get_results(
            "SELECT m.id, m.wc_attribute_id 
             FROM {$this->table_name} m
             LEFT JOIN {$wpdb->prefix}woocommerce_attribute_taxonomies a ON m.wc_attribute_id = a.attribute_id
             WHERE m.square_option_id IS NULL AND a.attribute_id IS NULL",
            ARRAY_A
        );
        
        foreach ( $orphaned_attributes as $orphaned ) {
            $wpdb->delete( $this->table_name, array( 'id' => $orphaned['id'] ), array( '%d' ) );
            $cleaned_count++;
        }
        
        // Clean up option mappings with non-existent terms
        $orphaned_terms = $wpdb->get_results(
            "SELECT m.id, m.wc_term_id 
             FROM {$this->table_name} m
             LEFT JOIN {$wpdb->terms} t ON m.wc_term_id = t.term_id
             WHERE m.square_option_id IS NOT NULL AND t.term_id IS NULL",
            ARRAY_A
        );
        
        foreach ( $orphaned_terms as $orphaned ) {
            $wpdb->delete( $this->table_name, array( 'id' => $orphaned['id'] ), array( '%d' ) );
            $cleaned_count++;
        }
        
        return $cleaned_count;
    }

    /**
     * Get mapping statistics
     *
     * @return array Statistics about mappings
     */
    public function get_mapping_stats() {
        global $wpdb;
        
        $stats = array();
        
        // Total mappings
        $stats['total_mappings'] = $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name}" );
        
        // Option set mappings
        $stats['option_set_mappings'] = $wpdb->get_var( 
            "SELECT COUNT(*) FROM {$this->table_name} WHERE square_option_id IS NULL" 
        );
        
        // Option mappings
        $stats['option_mappings'] = $wpdb->get_var( 
            "SELECT COUNT(*) FROM {$this->table_name} WHERE square_option_id IS NOT NULL" 
        );
        
        // Recent mappings (last 7 days)
        $stats['recent_mappings'] = $wpdb->get_var( 
            "SELECT COUNT(*) FROM {$this->table_name} 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)" 
        );
        
        return $stats;
    }
}
