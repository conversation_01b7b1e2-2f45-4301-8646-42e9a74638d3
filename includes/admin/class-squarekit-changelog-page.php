<?php
/**
 * SquareKit Changelog Admin Page
 * Displays version history and changelog information
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/admin
 * @since 1.1.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Changelog Page Class
 */
class SquareKit_Changelog_Page {

    /**
     * Version manager instance
     * @var SquareKit_Version_Manager
     */
    private $version_manager;

    /**
     * Constructor
     */
    public function __construct() {
        $this->version_manager = new SquareKit_Version_Manager();
        
        // Add admin menu
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        
        // Enqueue admin styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_styles' ) );
    }

    /**
     * Add admin menu item
     */
    public function add_admin_menu() {
        add_submenu_page(
            'squarekit-settings',
            __( 'Changelog', 'squarekit' ),
            __( 'Changelog', 'squarekit' ),
            'manage_options',
            'squarekit-changelog',
            array( $this, 'display_changelog_page' )
        );
    }

    /**
     * Enqueue admin styles
     */
    public function enqueue_admin_styles( $hook ) {
        if ( $hook !== 'squarekit_page_squarekit-changelog' ) {
            return;
        }

        wp_enqueue_style(
            'squarekit-changelog-admin',
            SQUAREKIT_PLUGIN_URL . 'assets/css/admin-changelog.css',
            array(),
            SQUAREKIT_VERSION
        );
    }

    /**
     * Display changelog page
     */
    public function display_changelog_page() {
        $version_info = $this->version_manager->get_version_info();
        $changelog = $this->version_manager->get_changelog();
        ?>
        <div class="wrap squarekit-changelog">
            <h1><?php esc_html_e( 'SquareKit Changelog', 'squarekit' ); ?></h1>
            
            <!-- Version Information -->
            <div class="squarekit-version-info">
                <div class="version-card current-version">
                    <h3><?php esc_html_e( 'Current Version', 'squarekit' ); ?></h3>
                    <div class="version-number"><?php echo esc_html( $version_info['current_version'] ); ?></div>
                    <div class="version-details">
                        <p><strong><?php esc_html_e( 'Database Version:', 'squarekit' ); ?></strong> <?php echo esc_html( $version_info['current_db_version'] ); ?></p>
                        <?php if ( $version_info['last_update'] ) : ?>
                            <p><strong><?php esc_html_e( 'Last Updated:', 'squarekit' ); ?></strong> <?php echo esc_html( date_i18n( get_option( 'date_format' ), strtotime( $version_info['last_update'] ) ) ); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="version-card system-info">
                    <h3><?php esc_html_e( 'System Information', 'squarekit' ); ?></h3>
                    <div class="system-details">
                        <p><strong><?php esc_html_e( 'WordPress:', 'squarekit' ); ?></strong> <?php echo esc_html( get_bloginfo( 'version' ) ); ?></p>
                        <p><strong><?php esc_html_e( 'WooCommerce:', 'squarekit' ); ?></strong> <?php echo esc_html( defined( 'WC_VERSION' ) ? WC_VERSION : 'Not installed' ); ?></p>
                        <p><strong><?php esc_html_e( 'PHP:', 'squarekit' ); ?></strong> <?php echo esc_html( PHP_VERSION ); ?></p>
                    </div>
                </div>
            </div>

            <!-- Changelog Entries -->
            <div class="squarekit-changelog-entries">
                <?php foreach ( $changelog as $version => $details ) : ?>
                    <div class="changelog-entry <?php echo esc_attr( $details['type'] ); ?>">
                        <div class="changelog-header">
                            <h2 class="version-title">
                                <span class="version-number">v<?php echo esc_html( $version ); ?></span>
                                <span class="version-name"><?php echo esc_html( $details['title'] ); ?></span>
                                <span class="version-type <?php echo esc_attr( $details['type'] ); ?>"><?php echo esc_html( ucfirst( $details['type'] ) ); ?></span>
                            </h2>
                            <div class="version-date"><?php echo esc_html( date_i18n( get_option( 'date_format' ), strtotime( $details['date'] ) ) ); ?></div>
                        </div>

                        <div class="changelog-content">
                            <?php if ( ! empty( $details['features'] ) ) : ?>
                                <div class="changelog-section features">
                                    <h4><?php esc_html_e( '✨ New Features', 'squarekit' ); ?></h4>
                                    <ul>
                                        <?php foreach ( $details['features'] as $feature ) : ?>
                                            <li><?php echo esc_html( $feature ); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $details['improvements'] ) ) : ?>
                                <div class="changelog-section improvements">
                                    <h4><?php esc_html_e( '🚀 Improvements', 'squarekit' ); ?></h4>
                                    <ul>
                                        <?php foreach ( $details['improvements'] as $improvement ) : ?>
                                            <li><?php echo esc_html( $improvement ); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $details['fixes'] ) ) : ?>
                                <div class="changelog-section fixes">
                                    <h4><?php esc_html_e( '🐛 Bug Fixes', 'squarekit' ); ?></h4>
                                    <ul>
                                        <?php foreach ( $details['fixes'] as $fix ) : ?>
                                            <li><?php echo esc_html( $fix ); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $details['security'] ) ) : ?>
                                <div class="changelog-section security">
                                    <h4><?php esc_html_e( '🔒 Security', 'squarekit' ); ?></h4>
                                    <ul>
                                        <?php foreach ( $details['security'] as $security ) : ?>
                                            <li><?php echo esc_html( $security ); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $details['deprecated'] ) ) : ?>
                                <div class="changelog-section deprecated">
                                    <h4><?php esc_html_e( '⚠️ Deprecated', 'squarekit' ); ?></h4>
                                    <ul>
                                        <?php foreach ( $details['deprecated'] as $deprecated ) : ?>
                                            <li><?php echo esc_html( $deprecated ); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <?php if ( ! empty( $details['removed'] ) ) : ?>
                                <div class="changelog-section removed">
                                    <h4><?php esc_html_e( '🗑️ Removed', 'squarekit' ); ?></h4>
                                    <ul>
                                        <?php foreach ( $details['removed'] as $removed ) : ?>
                                            <li><?php echo esc_html( $removed ); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Footer Information -->
            <div class="squarekit-changelog-footer">
                <div class="footer-section">
                    <h3><?php esc_html_e( 'Need Help?', 'squarekit' ); ?></h3>
                    <p><?php esc_html_e( 'If you encounter any issues after updating, please check our documentation or contact support.', 'squarekit' ); ?></p>
                    <div class="footer-links">
                        <a href="https://chimastudios.com/squarekit/docs" target="_blank" class="button button-secondary">
                            <?php esc_html_e( 'Documentation', 'squarekit' ); ?>
                        </a>
                        <a href="https://chimastudios.com/support" target="_blank" class="button button-secondary">
                            <?php esc_html_e( 'Support', 'squarekit' ); ?>
                        </a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3><?php esc_html_e( 'Version Management', 'squarekit' ); ?></h3>
                    <p><?php esc_html_e( 'SquareKit follows semantic versioning. Major versions may include breaking changes.', 'squarekit' ); ?></p>
                    <div class="version-legend">
                        <span class="legend-item major"><?php esc_html_e( 'Major', 'squarekit' ); ?></span>
                        <span class="legend-item minor"><?php esc_html_e( 'Minor', 'squarekit' ); ?></span>
                        <span class="legend-item patch"><?php esc_html_e( 'Patch', 'squarekit' ); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .squarekit-changelog {
            max-width: 1200px;
        }
        
        .squarekit-version-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .version-card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 1px 1px rgba(0,0,0,.04);
        }
        
        .version-card h3 {
            margin-top: 0;
            color: #23282d;
        }
        
        .version-number {
            font-size: 2em;
            font-weight: bold;
            color: #0073aa;
            margin: 10px 0;
        }
        
        .changelog-entry {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .changelog-header {
            background: #f9f9f9;
            border-bottom: 1px solid #eee;
            padding: 20px;
        }
        
        .version-title {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .version-number {
            font-size: 1.5em;
            color: #0073aa;
        }
        
        .version-name {
            font-size: 1.2em;
            color: #23282d;
        }
        
        .version-type {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            text-transform: uppercase;
            font-weight: bold;
        }
        
        .version-type.major {
            background: #d63384;
            color: white;
        }
        
        .version-type.minor {
            background: #0d6efd;
            color: white;
        }
        
        .version-type.patch {
            background: #198754;
            color: white;
        }
        
        .version-date {
            color: #666;
            margin-top: 5px;
        }
        
        .changelog-content {
            padding: 20px;
        }
        
        .changelog-section {
            margin: 20px 0;
        }
        
        .changelog-section h4 {
            margin: 0 0 10px 0;
            color: #23282d;
        }
        
        .changelog-section ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .changelog-section li {
            margin: 5px 0;
            line-height: 1.5;
        }
        
        .squarekit-changelog-footer {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 40px 0 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .footer-links {
            margin-top: 10px;
        }
        
        .footer-links .button {
            margin-right: 10px;
        }
        
        .version-legend {
            margin-top: 10px;
        }
        
        .legend-item {
            display: inline-block;
            padding: 4px 8px;
            margin-right: 10px;
            border-radius: 3px;
            font-size: 0.8em;
            text-transform: uppercase;
            font-weight: bold;
        }
        
        .legend-item.major {
            background: #d63384;
            color: white;
        }
        
        .legend-item.minor {
            background: #0d6efd;
            color: white;
        }
        
        .legend-item.patch {
            background: #198754;
            color: white;
        }
        </style>
        <?php
    }
}
