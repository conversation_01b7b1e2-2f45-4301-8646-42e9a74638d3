<?php
/**
 * SquareKit Webhook Settings Admin Page
 * Manages webhook configuration and monitoring
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/admin
 * @since 1.1.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Webhook Settings Page Class
 */
class SquareKit_Webhook_Settings {

    /**
     * Settings instance
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Webhook handler instance
     * @var SquareKit_Webhook_Handler
     */
    private $webhook_handler;

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = new SquareKit_Settings();
        $this->webhook_handler = new SquareKit_Webhook_Handler();
        
        // Add admin menu
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        
        // Handle form submissions
        add_action( 'admin_post_squarekit_save_webhook_settings', array( $this, 'save_webhook_settings' ) );
        
        // Enqueue admin styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_styles' ) );
    }

    /**
     * Add admin menu item
     */
    public function add_admin_menu() {
        add_submenu_page(
            'squarekit-settings',
            __( 'Webhooks', 'squarekit' ),
            __( 'Webhooks', 'squarekit' ),
            'manage_options',
            'squarekit-webhooks',
            array( $this, 'display_webhook_settings_page' )
        );
    }

    /**
     * Enqueue admin styles
     */
    public function enqueue_admin_styles( $hook ) {
        if ( $hook !== 'squarekit_page_squarekit-webhooks' ) {
            return;
        }

        wp_enqueue_style(
            'squarekit-webhook-admin',
            SQUAREKIT_PLUGIN_URL . 'assets/css/admin-webhooks.css',
            array(),
            SQUAREKIT_VERSION
        );
    }

    /**
     * Display webhook settings page
     */
    public function display_webhook_settings_page() {
        // Check for saved message
        $message = get_transient( 'squarekit_webhook_settings_message' );
        if ( $message ) {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html( $message ) . '</p></div>';
            delete_transient( 'squarekit_webhook_settings_message' );
        }

        $webhook_url = $this->webhook_handler->get_webhook_url();
        $webhook_status_info = $this->webhook_handler->get_webhook_status();
        $webhook_status = $webhook_status_info['enabled'] ?? false;
        $signature_key = $this->settings->get_webhook_signature_key();
        $webhook_stats = $this->webhook_handler->get_webhook_stats();
        ?>
        <div class="wrap squarekit-webhook-settings">
            <h1><?php esc_html_e( 'SquareKit Webhook Settings', 'squarekit' ); ?></h1>
            
            <!-- Webhook Configuration -->
            <div class="squarekit-settings-section">
                <h2><?php esc_html_e( 'Webhook Configuration', 'squarekit' ); ?></h2>
                
                <form method="post" action="<?php echo esc_url( admin_url( 'admin-post.php' ) ); ?>">
                    <?php wp_nonce_field( 'squarekit_webhook_settings', 'squarekit_webhook_nonce' ); ?>
                    <input type="hidden" name="action" value="squarekit_save_webhook_settings">
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="webhook_status"><?php esc_html_e( 'Enable Webhooks', 'squarekit' ); ?></label>
                            </th>
                            <td>
                                <label class="squarekit-toggle">
                                    <input type="checkbox" id="webhook_status" name="webhook_status" value="1" <?php checked( $webhook_status ); ?>>
                                    <span class="squarekit-toggle-slider"></span>
                                </label>
                                <p class="description">
                                    <?php esc_html_e( 'Enable webhook processing for real-time synchronization with Square.', 'squarekit' ); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="webhook_url"><?php esc_html_e( 'Webhook URL', 'squarekit' ); ?></label>
                            </th>
                            <td>
                                <input type="text" id="webhook_url" class="regular-text" value="<?php echo esc_attr( $webhook_url ); ?>" readonly>
                                <button type="button" class="button" onclick="copyToClipboard('<?php echo esc_js( $webhook_url ); ?>')">
                                    <?php esc_html_e( 'Copy', 'squarekit' ); ?>
                                </button>
                                <p class="description">
                                    <?php esc_html_e( 'Configure this URL in your Square Developer Dashboard under Webhooks.', 'squarekit' ); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="webhook_signature_key"><?php esc_html_e( 'Signature Key', 'squarekit' ); ?></label>
                            </th>
                            <td>
                                <input type="password" id="webhook_signature_key" name="webhook_signature_key" class="regular-text" value="<?php echo esc_attr( $signature_key ); ?>">
                                <p class="description">
                                    <?php esc_html_e( 'Enter the webhook signature key from your Square Developer Dashboard.', 'squarekit' ); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                    
                    <?php submit_button( __( 'Save Webhook Settings', 'squarekit' ) ); ?>
                </form>
            </div>

            <!-- Webhook Status -->
            <div class="squarekit-settings-section">
                <h2><?php esc_html_e( 'Webhook Status', 'squarekit' ); ?></h2>
                
                <div class="squarekit-status-grid">
                    <div class="status-card <?php echo $webhook_status ? 'status-enabled' : 'status-disabled'; ?>">
                        <h3><?php esc_html_e( 'Webhook Status', 'squarekit' ); ?></h3>
                        <div class="status-indicator">
                            <?php if ( $webhook_status ) : ?>
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php esc_html_e( 'Enabled', 'squarekit' ); ?>
                            <?php else : ?>
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php esc_html_e( 'Disabled', 'squarekit' ); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="status-card <?php echo ! empty( $signature_key ) ? 'status-enabled' : 'status-disabled'; ?>">
                        <h3><?php esc_html_e( 'Signature Key', 'squarekit' ); ?></h3>
                        <div class="status-indicator">
                            <?php if ( ! empty( $signature_key ) ) : ?>
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php esc_html_e( 'Configured', 'squarekit' ); ?>
                            <?php else : ?>
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php esc_html_e( 'Not Set', 'squarekit' ); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="status-card">
                        <h3><?php esc_html_e( 'Endpoint Test', 'squarekit' ); ?></h3>
                        <div class="status-indicator">
                            <button type="button" class="button" onclick="testWebhookEndpoint()">
                                <?php esc_html_e( 'Test Endpoint', 'squarekit' ); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Webhook Statistics -->
            <div class="squarekit-settings-section">
                <h2><?php esc_html_e( 'Webhook Statistics (Last 30 Days)', 'squarekit' ); ?></h2>
                
                <?php if ( empty( $webhook_stats ) || ( $webhook_stats['processed'] ?? 0 ) === 0 ) : ?>
                    <p><?php esc_html_e( 'No webhook events processed yet.', 'squarekit' ); ?></p>
                <?php else : ?>
                    <div class="squarekit-stats-grid">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo esc_html( $webhook_stats['processed'] ?? 0 ); ?></div>
                            <div class="stat-label"><?php esc_html_e( 'Total Processed', 'squarekit' ); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo esc_html( $webhook_stats['successful'] ?? 0 ); ?></div>
                            <div class="stat-label"><?php esc_html_e( 'Successful', 'squarekit' ); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo esc_html( $webhook_stats['failed'] ?? 0 ); ?></div>
                            <div class="stat-label"><?php esc_html_e( 'Failed', 'squarekit' ); ?></div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo esc_html( $webhook_stats['retries'] ?? 0 ); ?></div>
                            <div class="stat-label"><?php esc_html_e( 'Retries', 'squarekit' ); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Supported Events -->
            <div class="squarekit-settings-section">
                <h2><?php esc_html_e( 'Supported Webhook Events', 'squarekit' ); ?></h2>
                
                <div class="squarekit-events-grid">
                    <div class="event-category">
                        <h3><?php esc_html_e( 'Payment Events', 'squarekit' ); ?></h3>
                        <ul>
                            <li><code>payment.created</code> - <?php esc_html_e( 'New payment processed', 'squarekit' ); ?></li>
                            <li><code>payment.updated</code> - <?php esc_html_e( 'Payment status changed', 'squarekit' ); ?></li>
                            <li><code>refund.created</code> - <?php esc_html_e( 'Refund processed', 'squarekit' ); ?></li>
                            <li><code>refund.updated</code> - <?php esc_html_e( 'Refund status changed', 'squarekit' ); ?></li>
                        </ul>
                    </div>
                    
                    <div class="event-category">
                        <h3><?php esc_html_e( 'Order Events', 'squarekit' ); ?></h3>
                        <ul>
                            <li><code>order.created</code> - <?php esc_html_e( 'New order created', 'squarekit' ); ?></li>
                            <li><code>order.updated</code> - <?php esc_html_e( 'Order modified', 'squarekit' ); ?></li>
                            <li><code>order.fulfilled</code> - <?php esc_html_e( 'Order fulfilled', 'squarekit' ); ?></li>
                        </ul>
                    </div>
                    
                    <div class="event-category">
                        <h3><?php esc_html_e( 'Inventory Events', 'squarekit' ); ?></h3>
                        <ul>
                            <li><code>inventory.count.updated</code> - <?php esc_html_e( 'Stock levels changed', 'squarekit' ); ?></li>
                            <li><code>catalog.version.updated</code> - <?php esc_html_e( 'Product catalog updated', 'squarekit' ); ?></li>
                        </ul>
                    </div>
                    
                    <div class="event-category">
                        <h3><?php esc_html_e( 'Dispute Events', 'squarekit' ); ?></h3>
                        <ul>
                            <li><code>dispute.created</code> - <?php esc_html_e( 'Payment disputed', 'squarekit' ); ?></li>
                            <li><code>dispute.updated</code> - <?php esc_html_e( 'Dispute status changed', 'squarekit' ); ?></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Setup Instructions -->
            <div class="squarekit-settings-section">
                <h2><?php esc_html_e( 'Setup Instructions', 'squarekit' ); ?></h2>
                
                <div class="setup-steps">
                    <div class="setup-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4><?php esc_html_e( 'Configure Square Developer Dashboard', 'squarekit' ); ?></h4>
                            <p><?php esc_html_e( 'Log in to your Square Developer Dashboard and navigate to the Webhooks section.', 'squarekit' ); ?></p>
                        </div>
                    </div>
                    
                    <div class="setup-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4><?php esc_html_e( 'Add Webhook Endpoint', 'squarekit' ); ?></h4>
                            <p><?php esc_html_e( 'Add the webhook URL above as a new endpoint in Square.', 'squarekit' ); ?></p>
                        </div>
                    </div>
                    
                    <div class="setup-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4><?php esc_html_e( 'Configure Events', 'squarekit' ); ?></h4>
                            <p><?php esc_html_e( 'Select the webhook events you want to receive from the supported events list above.', 'squarekit' ); ?></p>
                        </div>
                    </div>
                    
                    <div class="setup-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4><?php esc_html_e( 'Copy Signature Key', 'squarekit' ); ?></h4>
                            <p><?php esc_html_e( 'Copy the webhook signature key from Square and paste it in the field above.', 'squarekit' ); ?></p>
                        </div>
                    </div>
                    
                    <div class="setup-step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h4><?php esc_html_e( 'Enable Webhooks', 'squarekit' ); ?></h4>
                            <p><?php esc_html_e( 'Enable webhooks in the settings above and save your configuration.', 'squarekit' ); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .squarekit-webhook-settings {
            max-width: 1200px;
        }
        
        .squarekit-settings-section {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin: 20px 0;
            padding: 20px;
        }
        
        .squarekit-settings-section h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .squarekit-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .status-card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            text-align: center;
        }
        
        .status-card.status-enabled {
            border-color: #46b450;
            background: #f0f9f0;
        }
        
        .status-card.status-disabled {
            border-color: #dc3232;
            background: #fdf0f0;
        }
        
        .status-indicator {
            font-size: 16px;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .status-enabled .status-indicator {
            color: #46b450;
        }
        
        .status-disabled .status-indicator {
            color: #dc3232;
        }
        
        .squarekit-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #0073aa;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-top: 5px;
        }
        
        .squarekit-events-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .event-category h3 {
            margin-top: 0;
            color: #0073aa;
        }
        
        .event-category ul {
            list-style: none;
            padding: 0;
        }
        
        .event-category li {
            margin: 10px 0;
            padding: 8px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .event-category code {
            background: #0073aa;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .setup-steps {
            margin: 20px 0;
        }
        
        .setup-step {
            display: flex;
            margin: 20px 0;
            align-items: flex-start;
        }
        
        .step-number {
            background: #0073aa;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            margin: 0 0 5px 0;
            color: #23282d;
        }
        
        .step-content p {
            margin: 0;
            color: #666;
        }
        
        .squarekit-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .squarekit-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .squarekit-toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .squarekit-toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        .squarekit-toggle input:checked + .squarekit-toggle-slider {
            background-color: #0073aa;
        }
        
        .squarekit-toggle input:checked + .squarekit-toggle-slider:before {
            transform: translateX(26px);
        }
        </style>

        <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('<?php esc_html_e( 'Webhook URL copied to clipboard!', 'squarekit' ); ?>');
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        }
        
        function testWebhookEndpoint() {
            const webhookUrl = '<?php echo esc_js( $webhook_url ); ?>';
            
            fetch(webhookUrl, {
                method: 'GET'
            })
            .then(response => {
                if (response.status === 405) {
                    alert('<?php esc_html_e( 'Webhook endpoint is working correctly!', 'squarekit' ); ?>');
                } else {
                    alert('<?php esc_html_e( 'Unexpected response from webhook endpoint.', 'squarekit' ); ?>');
                }
            })
            .catch(error => {
                alert('<?php esc_html_e( 'Error testing webhook endpoint: ', 'squarekit' ); ?>' + error.message);
            });
        }
        </script>
        <?php
    }

    /**
     * Save webhook settings
     */
    public function save_webhook_settings() {
        // Verify nonce
        if ( ! wp_verify_nonce( $_POST['squarekit_webhook_nonce'], 'squarekit_webhook_settings' ) ) {
            wp_die( __( 'Security check failed', 'squarekit' ) );
        }

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'Insufficient permissions', 'squarekit' ) );
        }

        // Save settings
        $webhook_status = isset( $_POST['webhook_status'] ) ? 1 : 0;
        $signature_key = sanitize_text_field( $_POST['webhook_signature_key'] ?? '' );

        // Use the webhook handler's methods
        if ( $webhook_status ) {
            $this->webhook_handler->enable_webhook_processing();
        } else {
            $this->webhook_handler->disable_webhook_processing();
        }

        if ( ! empty( $signature_key ) ) {
            $this->webhook_handler->update_webhook_signature_key( $signature_key );
        }

        // Set success message
        set_transient( 'squarekit_webhook_settings_message', __( 'Webhook settings saved successfully!', 'squarekit' ), 30 );

        // Redirect back to settings page
        wp_redirect( admin_url( 'admin.php?page=squarekit-webhooks' ) );
        exit;
    }
}
