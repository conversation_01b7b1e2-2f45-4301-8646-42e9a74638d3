<?php
/**
 * SquareKit Logger Class
 *
 * Handles custom logging for OAuth and other plugin operations
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Logger Class
 *
 * Provides comprehensive logging functionality for the plugin with severity levels
 * and structured logging for debugging and monitoring.
 */
class SquareKit_Logger {

    /**
     * Log severity levels
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * Log file path
     *
     * @var string
     */
    private $log_file;

    /**
     * Maximum log file size in bytes (10MB)
     *
     * @var int
     */
    private $max_file_size = 10485760;

    /**
     * Database logger instance
     *
     * @var SquareKit_DB
     */
    private $db;

    /**
     * Constructor
     */
    public function __construct() {
        // Set log file path in wp-content/uploads/squarekit-logs/
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/squarekit-logs';
        
        // Create log directory if it doesn't exist
        if ( ! file_exists( $log_dir ) ) {
            wp_mkdir_p( $log_dir );
            
            // Add .htaccess to protect log files
            $htaccess_content = "Order deny,allow\nDeny from all\n";
            file_put_contents( $log_dir . '/.htaccess', $htaccess_content );
        }
        
        $this->log_file = $log_dir . '/squarekit-debug.log';
        
        // Initialize database logger
        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }
        $this->db = new SquareKit_DB();
    }

    /**
     * Log OAuth events
     *
     * @param string $level Log level (info, warning, error, debug)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    public function log_oauth( $level, $message, $context = array() ) {
        $this->log( 'oauth', $level, $message, $context );
    }

    /**
     * Log API events
     *
     * @param string $level Log level (info, warning, error, debug)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    public function log_api( $level, $message, $context = array() ) {
        $this->log( 'api', $level, $message, $context );
    }

    /**
     * Log sync events
     *
     * @param string $level Log level (info, warning, error, debug)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    public function log_sync( $level, $message, $context = array() ) {
        $this->log( 'sync', $level, $message, $context );
    }

    /**
     * Main logging method
     *
     * @param string $type Log type (oauth, api, sync, etc.)
     * @param string $level Log level (info, warning, error, debug)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    public function log( $type, $level, $message, $context = array() ) {
        $timestamp = current_time( 'Y-m-d H:i:s' );
        $memory_usage = round(memory_get_usage() / 1024 / 1024, 2);

        // Format the log entry as structured JSON
        $log_entry = array(
            'timestamp' => $timestamp,
            'level' => strtoupper($level),
            'category' => $type,
            'message' => $message,
            'context' => $context,
            'memory_mb' => $memory_usage,
            'user_id' => get_current_user_id(),
            'request_uri' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'CLI'
        );

        // Convert to JSON for structured logging
        $json_entry = json_encode($log_entry, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $formatted_entry = $json_entry . PHP_EOL;

        // Write to file
        $this->write_to_file( $formatted_entry );

        // Also log critical errors to WordPress error log
        if ( $level === 'error' || $level === 'critical' ) {
            error_log( "SquareKit {$type}: {$message}" );
        }
    }

    /**
     * Write log entry to file
     *
     * @param string $log_entry Formatted log entry
     */
    private function write_to_file( $log_entry ) {
        // Ensure log file exists and is writable
        if ( ! file_exists( $this->log_file ) ) {
            touch( $this->log_file );
        }
        
        if ( is_writable( $this->log_file ) ) {
            file_put_contents( $this->log_file, $log_entry, FILE_APPEND | LOCK_EX );
        }
    }

    /**
     * Get recent log entries from file
     *
     * @param int $lines Number of lines to retrieve (default: 100)
     * @return array Log entries
     */
    public function get_recent_logs( $lines = 100 ) {
        if ( ! file_exists( $this->log_file ) ) {
            return array();
        }
        
        $file_lines = file( $this->log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES );
        
        if ( $file_lines === false ) {
            return array();
        }
        
        // Return last N lines
        return array_slice( $file_lines, -$lines );
    }

    /**
     * Clear log file
     *
     * @return bool Success status
     */
    public function clear_logs() {
        if ( file_exists( $this->log_file ) ) {
            return file_put_contents( $this->log_file, '' ) !== false;
        }
        return true;
    }

    /**
     * Get log file path
     *
     * @return string Log file path
     */
    public function get_log_file_path() {
        return $this->log_file;
    }

    /**
     * Get log file URL for download
     *
     * @return string Log file URL
     */
    public function get_log_file_url() {
        $upload_dir = wp_upload_dir();
        return str_replace( $upload_dir['basedir'], $upload_dir['baseurl'], $this->log_file );
    }

    /**
     * Get log file size
     *
     * @return string Formatted file size
     */
    public function get_log_file_size() {
        if ( ! file_exists( $this->log_file ) ) {
            return '0 B';
        }
        
        $size = filesize( $this->log_file );
        $units = array( 'B', 'KB', 'MB', 'GB' );
        
        for ( $i = 0; $size > 1024 && $i < count( $units ) - 1; $i++ ) {
            $size /= 1024;
        }
        
        return round( $size, 2 ) . ' ' . $units[ $i ];
    }

    /**
     * Get recent log entries
     *
     * @param int $limit Number of entries to retrieve
     * @param string $level_filter Filter by severity level
     * @param string $category_filter Filter by category
     * @return array Array of log entries
     */
    public function get_logs($limit = 100, $level_filter = null, $category_filter = null) {
        if (!file_exists($this->log_file)) {
            return array();
        }

        $logs = array();
        $handle = fopen($this->log_file, 'r');

        if ($handle) {
            // Read file in reverse order to get most recent entries first
            $lines = array();
            while (($line = fgets($handle)) !== false) {
                $lines[] = trim($line);
            }
            fclose($handle);

            $lines = array_reverse($lines);

            foreach ($lines as $line) {
                if (empty($line)) continue;

                $entry = json_decode($line, true);
                if (!$entry) continue;

                // Apply filters
                if ($level_filter && strtolower($entry['level']) !== strtolower($level_filter)) {
                    continue;
                }

                if ($category_filter && $entry['category'] !== $category_filter) {
                    continue;
                }

                $logs[] = $entry;

                if (count($logs) >= $limit) {
                    break;
                }
            }
        }

        return $logs;
    }

    /**
     * Get log statistics
     */
    public function get_stats() {
        $logs = $this->get_logs(1000); // Get last 1000 entries for stats

        $stats = array(
            'total_entries' => count($logs),
            'by_level' => array(),
            'by_category' => array(),
            'file_size' => file_exists($this->log_file) ? filesize($this->log_file) : 0,
            'file_size_mb' => file_exists($this->log_file) ? round(filesize($this->log_file) / 1024 / 1024, 2) : 0
        );

        foreach ($logs as $log) {
            $level = strtolower($log['level']);
            $category = $log['category'];

            if (!isset($stats['by_level'][$level])) {
                $stats['by_level'][$level] = 0;
            }
            $stats['by_level'][$level]++;

            if (!isset($stats['by_category'][$category])) {
                $stats['by_category'][$category] = 0;
            }
            $stats['by_category'][$category]++;
        }

        return $stats;
    }



    /**
     * Log debug message
     */
    public function debug($message, $context = array(), $category = 'general') {
        $this->log($category, self::LEVEL_DEBUG, $message, $context);
    }



    /**
     * Clean old log entries (keep only recent entries)
     *
     * @param int $days_to_keep Number of days to keep logs
     * @return int Number of entries removed
     */
    public function clean_old_logs($days_to_keep = 7) {
        if (!file_exists($this->log_file)) {
            return 0;
        }

        $cutoff_time = time() - ($days_to_keep * 24 * 60 * 60);
        $temp_file = $this->log_file . '.tmp';
        $kept_entries = 0;
        $removed_entries = 0;

        $handle = fopen($this->log_file, 'r');
        $temp_handle = fopen($temp_file, 'w');

        if ($handle && $temp_handle) {
            while (($line = fgets($handle)) !== false) {
                $entry = json_decode(trim($line), true);

                if ($entry && isset($entry['timestamp'])) {
                    $entry_time = strtotime($entry['timestamp']);

                    if ($entry_time >= $cutoff_time) {
                        fwrite($temp_handle, $line);
                        $kept_entries++;
                    } else {
                        $removed_entries++;
                    }
                }
            }

            fclose($handle);
            fclose($temp_handle);

            // Replace original file with cleaned version
            rename($temp_file, $this->log_file);

            // Log the cleanup
            $this->log('system', 'info', 'Log cleanup completed', array(
                'days_kept' => $days_to_keep,
                'entries_kept' => $kept_entries,
                'entries_removed' => $removed_entries
            ));
        }

        return $removed_entries;
    }



    /**
     * Log info message
     */
    public function info($message, $context = array(), $category = 'general') {
        $this->log(self::LEVEL_INFO, $message, $context, $category);
    }

    /**
     * Log warning message
     */
    public function warning($message, $context = array(), $category = 'general') {
        $this->log(self::LEVEL_WARNING, $message, $context, $category);
    }

    /**
     * Log error message
     */
    public function error($message, $context = array(), $category = 'general') {
        $this->log(self::LEVEL_ERROR, $message, $context, $category);
    }

    /**
     * Log critical message
     */
    public function critical($message, $context = array(), $category = 'general') {
        $this->log(self::LEVEL_CRITICAL, $message, $context, $category);
    }

    /**
     * Log API request/response
     */
    public function api($method, $endpoint, $request_data = null, $response_data = null, $error = null) {
        $context = array(
            'method' => $method,
            'endpoint' => $endpoint,
            'request_data' => $request_data,
            'response_data' => $response_data,
            'error' => $error
        );

        $level = $error ? self::LEVEL_ERROR : self::LEVEL_INFO;
        $message = $error ? "API Error: {$method} {$endpoint}" : "API Call: {$method} {$endpoint}";

        $this->log($level, $message, $context, 'api');
    }

    /**
     * Log product import operation
     */
    public function product_import($action, $product_data, $result = null, $error = null) {
        $context = array(
            'action' => $action,
            'product_data' => $product_data,
            'result' => $result,
            'error' => $error
        );

        $level = $error ? self::LEVEL_ERROR : self::LEVEL_INFO;
        $message = $error ? "Product Import Error: {$action}" : "Product Import: {$action}";

        $this->log($level, $message, $context, 'product_import');
    }

    /**
     * Log image import operation
     */
    public function image_import($action, $image_data, $result = null, $error = null) {
        $context = array(
            'action' => $action,
            'image_data' => $image_data,
            'result' => $result,
            'error' => $error
        );

        $level = $error ? self::LEVEL_ERROR : self::LEVEL_INFO;
        $message = $error ? "Image Import Error: {$action}" : "Image Import: {$action}";

        $this->log($level, $message, $context, 'image_import');
    }

    /**
     * Log mapping operation
     */
    public function mapping($action, $mapping_data, $result = null, $error = null) {
        $context = array(
            'action' => $action,
            'mapping_data' => $mapping_data,
            'result' => $result,
            'error' => $error
        );

        $level = $error ? self::LEVEL_ERROR : self::LEVEL_INFO;
        $message = $error ? "Mapping Error: {$action}" : "Mapping: {$action}";

        $this->log($level, $message, $context, 'mapping');
    }

    /**
     * Rotate log file if it exceeds maximum size
     */
    private function rotate_log_if_needed() {
        if (!file_exists($this->log_file)) {
            return;
        }

        if (filesize($this->log_file) > $this->max_file_size) {
            $backup_file = $this->log_file . '.old';

            // Remove old backup if exists
            if (file_exists($backup_file)) {
                unlink($backup_file);
            }

            // Move current log to backup
            rename($this->log_file, $backup_file);
        }
    }

    /**
     * Static method to get logger instance
     *
     * @return SquareKit_Logger
     */
    public static function get_instance() {
        static $instance = null;
        
        if ( $instance === null ) {
            $instance = new self();
        }
        
        return $instance;
    }
}
