<?php
/**
 * Fired during plugin activation
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since 1.0.0
 */
class SquareKit_Activator {

    /**
     * Run activation tasks
     *
     * @since 1.0.0
     */
    public static function activate() {
        // Create custom database tables
        self::create_tables();
        
        // Set default options
        self::set_default_options();
        
        // Set activation flag for redirect to wizard
        set_transient( 'squarekit_activation_redirect', true, 30 );
    }

    /**
     * Create custom database tables
     *
     * @since 1.0.0
     */
    private static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Logs table
        $logs_sql = "CREATE TABLE {$wpdb->prefix}squarekit_sync_logs (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            log_type varchar(50) NOT NULL,
            log_message text NOT NULL,
            log_data longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            <PERSON><PERSON><PERSON> log_type (log_type),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Inventory table
        $inventory_sql = "CREATE TABLE {$wpdb->prefix}squarekit_inventory (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            square_id varchar(255) NOT NULL,
            wc_product_id bigint(20) NOT NULL,
            variation_id bigint(20) DEFAULT 0,
            location_id varchar(255) NOT NULL,
            quantity int(11) NOT NULL DEFAULT 0,
            last_sync datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY square_location (square_id, location_id, variation_id),
            KEY wc_product_id (wc_product_id),
            KEY location_id (location_id)
        ) $charset_collate;";
        
        // Customers table
        $customers_sql = "CREATE TABLE {$wpdb->prefix}squarekit_customers (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            square_id varchar(255) NOT NULL,
            wc_user_id bigint(20) NOT NULL,
            loyalty_points int(11) DEFAULT 0,
            total_spent decimal(10,2) DEFAULT 0.00,
            order_count int(11) DEFAULT 0,
            last_order_date datetime,
            customer_note text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY square_id (square_id),
            UNIQUE KEY wc_user_id (wc_user_id),
            KEY loyalty_points (loyalty_points),
            KEY total_spent (total_spent)
        ) $charset_collate;";
        
        // Locations table
        $locations_sql = "CREATE TABLE {$wpdb->prefix}squarekit_locations (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            square_id varchar(255) NOT NULL,
            name varchar(255) NOT NULL,
            address text,
            phone varchar(50),
            business_hours text,
            pickup_enabled tinyint(1) DEFAULT 0,
            shipping_enabled tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY square_id (square_id),
            KEY name (name)
        ) $charset_collate;";
        
        // Bulk operations table
        $bulk_operations_sql = "CREATE TABLE {$wpdb->prefix}squarekit_bulk_operations (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            operation_type varchar(50) NOT NULL,
            operation_status varchar(20) NOT NULL DEFAULT 'pending',
            total_items int(11) NOT NULL DEFAULT 0,
            processed_items int(11) NOT NULL DEFAULT 0,
            successful_items int(11) NOT NULL DEFAULT 0,
            failed_items int(11) NOT NULL DEFAULT 0,
            progress_percentage decimal(5,2) NOT NULL DEFAULT 0.00,
            current_item_id bigint(20) DEFAULT NULL,
            current_item_name varchar(255) DEFAULT NULL,
            operation_data longtext,
            error_log longtext,
            started_at datetime DEFAULT CURRENT_TIMESTAMP,
            completed_at datetime DEFAULT NULL,
            created_by bigint(20) NOT NULL,
            PRIMARY KEY (id),
            KEY operation_type (operation_type),
            KEY operation_status (operation_status),
            KEY created_by (created_by),
            KEY started_at (started_at)
        ) $charset_collate;";

        // Fetched products cache table
        $fetched_products_sql = "CREATE TABLE {$wpdb->prefix}squarekit_fetched_products (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            square_id varchar(255) NOT NULL,
            product_data longtext NOT NULL,
            fetch_session_id varchar(255) NOT NULL,
            fetched_at datetime DEFAULT CURRENT_TIMESTAMP,
            expires_at datetime DEFAULT NULL,
            is_imported tinyint(1) NOT NULL DEFAULT 0,
            import_status varchar(20) NOT NULL DEFAULT 'not_imported',
            wc_product_id bigint(20) DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY square_session (square_id, fetch_session_id),
            KEY fetch_session_id (fetch_session_id),
            KEY fetched_at (fetched_at),
            KEY import_status (import_status),
            KEY is_imported (is_imported)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($logs_sql);
        dbDelta($inventory_sql);
        dbDelta($customers_sql);

        // Only create locations table if shipping locations are enabled
        $settings = get_option('square-kit_settings', array());
        if ( isset( $settings['enable_shipping_locations'] ) && $settings['enable_shipping_locations'] ) {
            dbDelta( $locations_sql );
        }

        // Create bulk operations table
        dbDelta($bulk_operations_sql);

        // Create fetched products cache table
        dbDelta($fetched_products_sql);
    }

    /**
     * Set default options
     *
     * @since 1.0.0
     */
    private static function set_default_options() {
        // Default settings
        $default_settings = array(
            'environment' => 'sandbox',
            'sync_products' => 'both',
            'sync_orders' => true,
            'sync_customers' => true,
            'enable_automatic_sync' => true,
            'cron_schedule' => 'hourly',
            'payment_methods' => array(
                'square' => true,
                'google_pay' => false,
                'apple_pay' => false,
                'afterpay' => false
            ),
            'webhook_status' => false,
            'setup_complete' => false
        );

        // Only add default settings if they don't exist
        if ( ! get_option( 'square-kit_settings' ) ) {
            add_option( 'square-kit_settings', $default_settings );
        }
    }
} 