<?php
/**
 * SquareKit Payment Exception Class
 * Custom exception for payment processing errors
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/exceptions
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Payment Exception
 * 
 * Provides enhanced error handling for payment processing with error codes,
 * user-friendly messages, and retry logic.
 */
class SquareKit_Payment_Exception extends Exception {

    /**
     * Error code for categorizing payment errors
     * @var string
     */
    private $error_code;

    /**
     * Whether the payment can be retried
     * @var bool
     */
    private $retry_allowed;

    /**
     * Additional error context data
     * @var array
     */
    private $error_context;

    /**
     * Constructor
     *
     * @param string $message Error message
     * @param string $error_code Error code for categorization
     * @param bool $retry_allowed Whether retry is allowed
     * @param array $error_context Additional context data
     * @param int $code Exception code
     * @param Throwable $previous Previous exception
     */
    public function __construct( 
        $message = '', 
        $error_code = 'UNKNOWN_ERROR', 
        $retry_allowed = true, 
        $error_context = array(), 
        $code = 0, 
        Throwable $previous = null 
    ) {
        parent::__construct( $message, $code, $previous );
        
        $this->error_code = $error_code;
        $this->retry_allowed = $retry_allowed;
        $this->error_context = $error_context;
    }

    /**
     * Get error code
     *
     * @return string
     */
    public function getErrorCode() {
        return $this->error_code;
    }

    /**
     * Check if retry is allowed
     *
     * @return bool
     */
    public function isRetryAllowed() {
        return $this->retry_allowed;
    }

    /**
     * Get error context
     *
     * @return array
     */
    public function getErrorContext() {
        return $this->error_context;
    }

    /**
     * Set error context
     *
     * @param array $context
     */
    public function setErrorContext( $context ) {
        $this->error_context = $context;
    }

    /**
     * Add context data
     *
     * @param string $key
     * @param mixed $value
     */
    public function addContext( $key, $value ) {
        $this->error_context[ $key ] = $value;
    }

    /**
     * Get user-friendly error message based on error code
     *
     * @return string
     */
    public function getUserMessage() {
        $user_messages = array(
            'VALIDATION_ERROR' => __( 'Please check your payment information and try again.', 'squarekit' ),
            'RATE_LIMIT_EXCEEDED' => __( 'Too many payment attempts. Please wait a moment before trying again.', 'squarekit' ),
            'CARD_DECLINED' => __( 'Your card was declined. Please try a different payment method.', 'squarekit' ),
            'INSUFFICIENT_FUNDS' => __( 'Insufficient funds. Please try a different payment method.', 'squarekit' ),
            'CVV_FAILURE' => __( 'Card security code verification failed. Please check your CVV and try again.', 'squarekit' ),
            'EXPIRED_CARD' => __( 'Your card has expired. Please use a different payment method.', 'squarekit' ),
            'INVALID_CARD' => __( 'Invalid card information. Please check your details and try again.', 'squarekit' ),
            'GENERIC_DECLINE' => __( 'Payment was declined. Please contact your bank or try a different payment method.', 'squarekit' ),
            'TEMPORARY_ERROR' => __( 'Temporary payment processing error. Please try again in a moment.', 'squarekit' ),
            'NETWORK_ERROR' => __( 'Network connection error. Please check your internet connection and try again.', 'squarekit' ),
            'API_ERROR' => __( 'Payment service temporarily unavailable. Please try again later.', 'squarekit' ),
            'CONFIGURATION_ERROR' => __( 'Payment gateway configuration error. Please contact support.', 'squarekit' ),
            'TECHNICAL_ERROR' => __( 'A technical error occurred. Please try again or contact support.', 'squarekit' ),
        );

        return $user_messages[ $this->error_code ] ?? $this->getMessage();
    }

    /**
     * Convert exception to array for logging
     *
     * @return array
     */
    public function toArray() {
        return array(
            'message' => $this->getMessage(),
            'error_code' => $this->error_code,
            'retry_allowed' => $this->retry_allowed,
            'context' => $this->error_context,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'trace' => $this->getTraceAsString(),
            'timestamp' => current_time( 'mysql' ),
        );
    }

    /**
     * Create exception from Square API error response
     *
     * @param array $error_response Square API error response
     * @return SquareKit_Payment_Exception
     */
    public static function fromSquareError( $error_response ) {
        $error_code = 'API_ERROR';
        $message = __( 'Payment processing failed.', 'squarekit' );
        $retry_allowed = true;
        $context = array();

        // Parse Square error structure
        if ( isset( $error_response['errors'] ) && is_array( $error_response['errors'] ) ) {
            $first_error = $error_response['errors'][0];
            
            if ( isset( $first_error['code'] ) ) {
                $error_code = $first_error['code'];
            }
            
            if ( isset( $first_error['detail'] ) ) {
                $message = $first_error['detail'];
            }
            
            $context = $first_error;
        }

        // Map Square error codes to our error codes
        $error_code_mapping = array(
            'CARD_DECLINED' => 'CARD_DECLINED',
            'CVV_FAILURE' => 'CVV_FAILURE',
            'EXPIRED_CARD' => 'EXPIRED_CARD',
            'INSUFFICIENT_FUNDS' => 'INSUFFICIENT_FUNDS',
            'INVALID_CARD_DATA' => 'INVALID_CARD',
            'GENERIC_DECLINE' => 'GENERIC_DECLINE',
            'TEMPORARY_ERROR' => 'TEMPORARY_ERROR',
        );

        $mapped_error_code = $error_code_mapping[ $error_code ] ?? 'API_ERROR';
        
        // Determine if retry is allowed based on error type
        $no_retry_errors = array( 'CARD_DECLINED', 'INSUFFICIENT_FUNDS', 'EXPIRED_CARD', 'GENERIC_DECLINE' );
        $retry_allowed = ! in_array( $mapped_error_code, $no_retry_errors, true );

        return new self( $message, $mapped_error_code, $retry_allowed, $context );
    }

    /**
     * Create validation error
     *
     * @param string $message
     * @param array $context
     * @return SquareKit_Payment_Exception
     */
    public static function validationError( $message, $context = array() ) {
        return new self( $message, 'VALIDATION_ERROR', true, $context );
    }

    /**
     * Create configuration error
     *
     * @param string $message
     * @param array $context
     * @return SquareKit_Payment_Exception
     */
    public static function configurationError( $message, $context = array() ) {
        return new self( $message, 'CONFIGURATION_ERROR', false, $context );
    }

    /**
     * Create network error
     *
     * @param string $message
     * @param array $context
     * @return SquareKit_Payment_Exception
     */
    public static function networkError( $message, $context = array() ) {
        return new self( $message, 'NETWORK_ERROR', true, $context );
    }
}
