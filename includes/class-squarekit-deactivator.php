<?php
/**
 * Fired during plugin deactivation
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since 1.0.0
 */
class SquareKit_Deactivator {

    /**
     * Run deactivation tasks
     *
     * @since 1.0.0
     */
    public static function deactivate() {
        // Clear scheduled events
        self::clear_scheduled_events();
        
        // Optionally clean up webhooks
        self::maybe_cleanup_webhooks();
    }

    /**
     * Clear scheduled events
     *
     * @since 1.0.0
     */
    private static function clear_scheduled_events() {
        // Clear the sync cron job
        wp_clear_scheduled_hook( 'squarekit_scheduled_sync' );
        
        // Clear any other scheduled events
        wp_clear_scheduled_hook( 'squarekit_process_webhook_queue' );
    }

    /**
     * Maybe clean up webhooks on Square
     *
     * @since 1.0.0
     */
    private static function maybe_cleanup_webhooks() {
        $settings = get_option( 'square-kit_settings', array() );
        
        // Check if we should clean up webhooks
        if ( isset( $settings['cleanup_webhooks_on_deactivate'] ) && $settings['cleanup_webhooks_on_deactivate'] ) {
            // If Square API class exists, try to delete webhooks
            if ( class_exists( 'SquareKit_Square_API' ) ) {
                $square_api = new SquareKit_Square_API();
                $square_api->delete_webhooks();
            }
        }
    }
} 