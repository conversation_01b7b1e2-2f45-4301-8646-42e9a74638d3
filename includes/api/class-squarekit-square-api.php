<?php
/**
 * The Square API integration.
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/api
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * The Square API integration class.
 *
 * @since 1.0.0
 */
class SquareKit_Square_API {

    /**
     * Settings instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_Settings
     */
    protected $settings;

    /**
     * DB instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_DB
     */
    protected $db;

    /**
     * Image URL cache
     *
     * @since 1.0.0
     * @access protected
     * @var array
     */
    protected $image_cache = array();

    /**
     * Logger instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_Logger
     */
    protected $logger;

    /**
     * Square API endpoints
     *
     * @since 1.0.0
     * @access protected
     * @var array
     */
    protected $endpoints = array(
        'sandbox' => 'https://connect.squareupsandbox.com/v2',
        'production' => 'https://connect.squareup.com/v2',
    );

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->settings = new SquareKit_Settings();
        $this->db = new SquareKit_DB();

        // Initialize logger
        if (!class_exists('SquareKit_Logger')) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }
        $this->logger = SquareKit_Logger::get_instance();
    }

    /**
     * Get API endpoint
     *
     * @since 1.0.0
     * @return string API endpoint
     */
    protected function get_api_endpoint() {
        $environment = $this->settings->get_environment();
        return $this->endpoints[ $environment ];
    }

    /**
     * Get API headers
     *
     * @since 1.0.0
     * @return array API headers
     */
    protected function get_api_headers() {
        $access_token = $this->settings->get_access_token();
        
        return array(
            'Authorization' => 'Bearer ' . $access_token,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Square-Version' => '2023-09-25', // Use the latest Square API version
        );
    }

    /**
     * Make API request
     *
     * @since 1.0.0
     * @param string $endpoint API endpoint
     * @param string $method Request method (GET, POST, PUT, DELETE)
     * @param array $data Request data
     * @return array|WP_Error Response data or error
     */
    protected function request( $endpoint, $method = 'GET', $data = array() ) {
        // Log API request start
        $this->logger->log('api', 'info', "Starting API request: {$method} {$endpoint}", array(
            'endpoint' => $endpoint,
            'method' => $method,
            'data_size' => !empty($data) ? strlen(json_encode($data)) : 0
        ));

        // Check if we have an access token
        if ( ! $this->settings->get_access_token() ) {
            $error = new WP_Error(
                'squarekit_missing_token',
                __( 'Missing Square access token.', 'squarekit' )
            );

            $this->logger->log('api', 'error', "API request failed: Missing access token", array(
                'endpoint' => $endpoint,
                'method' => $method
            ));

            return $error;
        }
        
        // Build request arguments
        $args = array(
            'method' => $method,
            'headers' => $this->get_api_headers(),
            'timeout' => 30,
        );
        
        // Add request body for POST, PUT methods
        if ( in_array( $method, array( 'POST', 'PUT' ) ) && ! empty( $data ) ) {
            $args['body'] = wp_json_encode( $data );
        }
        
        // Make request
        $response = wp_remote_request( $this->get_api_endpoint() . $endpoint, $args );
        
        // Check for errors
        if ( is_wp_error( $response ) ) {
            $this->logger->log('api', 'error', "API request failed: HTTP error", array(
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $response->get_error_message()
            ));
            return $response;
        }

        // Get response code
        $response_code = wp_remote_retrieve_response_code( $response );

        // Get response body
        $response_body = wp_remote_retrieve_body( $response );
        $response_data = json_decode( $response_body, true );

        // Check for API errors
        if ( $response_code >= 400 ) {
            $error_message = isset( $response_data['errors'][0]['detail'] ) ? $response_data['errors'][0]['detail'] : __( 'Unknown error.', 'squarekit' );

            $this->logger->log('api', 'error', "API request failed: {$error_message}", array(
                'endpoint' => $endpoint,
                'method' => $method,
                'status_code' => $response_code,
                'response_data' => $response_data
            ));

            return new WP_Error(
                'squarekit_api_error',
                $error_message,
                array(
                    'status' => $response_code,
                    'response' => $response_data,
                )
            );
        }

        // Log successful API response
        $this->logger->log('api', 'info', "API request successful: {$method} {$endpoint}", array(
            'endpoint' => $endpoint,
            'method' => $method,
            'status_code' => $response_code,
            'response_size' => strlen($response_body)
        ));

        return $response_data;
    }

    /**
     * Check if access token is valid
     *
     * @since 1.0.0
     * @return bool True if valid, false otherwise
     */
    public function check_token() {
        // Make a simple request to check if the token is valid
        $response = $this->request( '/locations' );
        
        return ! is_wp_error( $response );
    }

    /**
     * Get locations
     *
     * @since 1.0.0
     * @return array|WP_Error Locations or error
     */
    public function get_locations() {
        $response = $this->request( '/locations' );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['locations'] ) ? $response['locations'] : array();
    }

    /**
     * Get catalog
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Catalog or error
     */
    public function get_catalog( $args = array() ) {
        $endpoint = '/catalog/list';

        if ( ! empty( $args ) ) {
            $endpoint = add_query_arg( $args, $endpoint );
        }

        $response = $this->request( $endpoint );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $objects = isset( $response['objects'] ) ? $response['objects'] : array();

        // If we got images in the response, cache them
        if ( ! empty( $objects ) ) {
            foreach ( $objects as $object ) {
                if ( isset( $object['type'] ) && $object['type'] === 'IMAGE' &&
                     isset( $object['id'] ) && isset( $object['image_data']['url'] ) ) {

                    $this->image_cache[ $object['id'] ] = $object['image_data']['url'];
                }
            }
        }

        return $objects;
    }

    /**
     * Get catalog with full response including cursor for pagination
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Full catalog response with cursor or error
     */
    public function get_catalog_with_cursor( $args = array() ) {
        $endpoint = '/catalog/list';

        if ( ! empty( $args ) ) {
            $endpoint = add_query_arg( $args, $endpoint );
        }

        $response = $this->request( $endpoint );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $objects = isset( $response['objects'] ) ? $response['objects'] : array();

        // If we got images in the response, cache them
        if ( ! empty( $objects ) ) {
            foreach ( $objects as $object ) {
                if ( isset( $object['type'] ) && $object['type'] === 'IMAGE' &&
                     isset( $object['id'] ) && isset( $object['image_data']['url'] ) ) {

                    $this->image_cache[ $object['id'] ] = $object['image_data']['url'];
                }
            }
        }

        return array(
            'objects' => $objects,
            'cursor' => isset( $response['cursor'] ) ? $response['cursor'] : null
        );
    }

    /**
     * Get item option sets from Square
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Item option sets or error
     */
    public function get_item_option_sets( $args = array() ) {
        $endpoint = '/catalog/list';
        
        // Filter for item option sets only
        $args['types'] = 'ITEM_OPTION_SET';
        
        if ( ! empty( $args ) ) {
            $endpoint = add_query_arg( $args, $endpoint );
        }
        
        $response = $this->request( $endpoint );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['objects'] ) ? $response['objects'] : array();
    }

    /**
     * Get specific item option set by ID
     *
     * @since 1.0.0
     * @param string $option_set_id Square option set ID
     * @return array|WP_Error Item option set or error
     */
    public function get_item_option_set( $option_set_id ) {
        $response = $this->request( '/catalog/object/' . $option_set_id );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['object'] ) ? $response['object'] : array();
    }

    /**
     * Create item option set in Square
     *
     * @since 1.0.0
     * @param array $option_set Option set data
     * @return array|WP_Error Created option set or error
     */
    public function create_item_option_set( $option_set ) {
        $data = array(
            'idempotency_key' => uniqid( 'option_set_' . time() . '_' ),
            'object' => array(
                'type' => 'ITEM_OPTION_SET',
                'id' => '#' . uniqid( 'option_set_' ),
                'item_option_set_data' => $option_set,
            ),
        );
        
        $response = $this->request( '/catalog/object', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : array();
    }

    /**
     * Update item option set in Square
     *
     * @since 1.0.0
     * @param string $option_set_id Square option set ID
     * @param array $option_set Updated option set data
     * @return array|WP_Error Updated option set or error
     */
    public function update_item_option_set( $option_set_id, $option_set ) {
        $data = array(
            'idempotency_key' => uniqid( 'option_set_update_' . time() . '_' ),
            'object' => array(
                'type' => 'ITEM_OPTION_SET',
                'id' => $option_set_id,
                'item_option_set_data' => $option_set,
            ),
        );
        
        $response = $this->request( '/catalog/object', 'PUT', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : array();
    }

    /**
     * Delete item option set from Square
     *
     * @since 1.0.0
     * @param string $option_set_id Square option set ID
     * @return bool|WP_Error Success status or error
     */
    public function delete_item_option_set( $option_set_id ) {
        $response = $this->request( '/catalog/object/' . $option_set_id, 'DELETE' );
        
        return ! is_wp_error( $response );
    }

    /**
     * Get item options from Square
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Item options or error
     */
    public function get_item_options( $args = array() ) {
        $endpoint = '/catalog/list';
        
        // Filter for item options only
        $args['types'] = 'ITEM_OPTION';
        
        if ( ! empty( $args ) ) {
            $endpoint = add_query_arg( $args, $endpoint );
        }
        
        $response = $this->request( $endpoint );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['objects'] ) ? $response['objects'] : array();
    }

    /**
     * Get specific item option by ID
     *
     * @since 1.0.0
     * @param string $option_id Square option ID
     * @return array|WP_Error Item option or error
     */
    public function get_item_option( $option_id ) {
        $response = $this->request( '/catalog/object/' . $option_id );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['object'] ) ? $response['object'] : array();
    }

    /**
     * Create item option in Square
     *
     * @since 1.0.0
     * @param array $option Option data
     * @return array|WP_Error Created option or error
     */
    public function create_item_option( $option ) {
        $data = array(
            'idempotency_key' => uniqid( 'option_' . time() . '_' ),
            'object' => array(
                'type' => 'ITEM_OPTION',
                'id' => '#' . uniqid( 'option_' ),
                'item_option_data' => $option,
            ),
        );
        
        $response = $this->request( '/catalog/object', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : array();
    }

    /**
     * Update item option in Square
     *
     * @since 1.0.0
     * @param string $option_id Square option ID
     * @param array $option Updated option data
     * @return array|WP_Error Updated option or error
     */
    public function update_item_option( $option_id, $option ) {
        $data = array(
            'idempotency_key' => uniqid( 'option_update_' . time() . '_' ),
            'object' => array(
                'type' => 'ITEM_OPTION',
                'id' => $option_id,
                'item_option_data' => $option,
            ),
        );
        
        $response = $this->request( '/catalog/object', 'PUT', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : array();
    }

    /**
     * Delete item option from Square
     *
     * @since 1.0.0
     * @param string $option_id Square option ID
     * @return bool|WP_Error Success status or error
     */
    public function delete_item_option( $option_id ) {
        $response = $this->request( '/catalog/object/' . $option_id, 'DELETE' );
        
        return ! is_wp_error( $response );
    }

    /**
     * Search catalog objects with query parameters
     *
     * @since 1.0.0
     * @param array $search_params Search parameters
     * @return array|WP_Error Search results or error
     */
    public function search_catalog_objects( $search_params = array() ) {
        $endpoint = '/catalog/search';

        $data = array();

        if ( ! empty( $search_params['object_types'] ) ) {
            $data['object_types'] = $search_params['object_types'];
        }

        if ( ! empty( $search_params['query'] ) ) {
            $data['query'] = $search_params['query'];
        }

        if ( ! empty( $search_params['limit'] ) ) {
            $data['limit'] = $search_params['limit'];
        }

        if ( ! empty( $search_params['cursor'] ) ) {
            $data['cursor'] = $search_params['cursor'];
        }

        $response = $this->request( $endpoint, 'POST', $data );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return $response;
    }

    /**
     * Get categories from Square catalog
     *
     * @since 1.0.0
     * @return array|WP_Error Categories or error
     */
    public function get_categories() {
        $response = $this->request( '/catalog/list', 'GET', array(
            'types' => 'CATEGORY'
        ) );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return isset( $response['objects'] ) ? $response['objects'] : array();
    }

    /**
     * Create category in Square
     *
     * @since 1.0.0
     * @param array $category Category data
     * @return array|WP_Error Created category or error
     */
    public function create_category( $category ) {
        $data = array(
            'idempotency_key' => uniqid(),
            'object' => array(
                'type' => 'CATEGORY',
                'id' => '#' . uniqid(),
                'category_data' => array(
                    'name' => $category['name'],
                    'description' => isset( $category['description'] ) ? $category['description'] : '',
                ),
            ),
        );
        
        $response = $this->request( '/catalog/object', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : $response;
    }

    /**
     * Update category in Square
     *
     * @since 1.0.0
     * @param string $category_id Category ID
     * @param array $category Category data
     * @return array|WP_Error Updated category or error
     */
    public function update_category( $category_id, $category ) {
        $data = array(
            'idempotency_key' => uniqid(),
            'object' => array(
                'type' => 'CATEGORY',
                'id' => $category_id,
                'category_data' => array(
                    'name' => $category['name'],
                    'description' => isset( $category['description'] ) ? $category['description'] : '',
                ),
            ),
        );
        
        $response = $this->request( '/catalog/object', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : $response;
    }

    /**
     * Get catalog item
     *
     * @since 1.0.0
     * @param string $item_id Item ID
     * @param bool $include_related Whether to include related objects
     * @return array|WP_Error Item or error
     */
    public function get_catalog_item( $item_id, $include_related = false ) {
        $endpoint = '/catalog/object/' . $item_id;

        if ( $include_related ) {
            $endpoint = add_query_arg( 'include_related_objects', 'true', $endpoint );
        }

        $response = $this->request( $endpoint );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return isset( $response['object'] ) ? $response['object'] : array();
    }

    /**
     * Get catalog object (alias for get_catalog_item for compatibility)
     *
     * @since 1.0.0
     * @param string $object_id Object ID
     * @param bool $include_related Whether to include related objects
     * @return array|WP_Error Object or error
     */
    public function get_catalog_object( $object_id, $include_related = false ) {
        return $this->get_catalog_item( $object_id, $include_related );
    }

    /**
     * Fetch all ITEM_OPTION objects from Square API (SWEVER-style)
     * Follows pagination cursor to get complete list
     *
     * @since 1.0.0
     * @return array Array of item options indexed by ID
     */
    public function fetch_square_item_options() {
        $cursor = null;
        $options = array();

        do {
            $endpoint = '/catalog/list?types=ITEM_OPTION';
            if ( $cursor ) {
                $endpoint .= '&cursor=' . rawurlencode( $cursor );
            }

            $response = $this->request( $endpoint );

            if ( is_wp_error( $response ) ) {
                $this->logger->log( 'api', 'error', 'Failed to fetch item options: ' . $response->get_error_message() );
                break;
            }

            if ( isset( $response['objects'] ) && is_array( $response['objects'] ) ) {
                foreach ( $response['objects'] as $option ) {
                    if ( isset( $option['id'] ) && isset( $option['item_option_data']['name'] ) ) {
                        $options[$option['id']] = $option;
                    }
                }
            }

            $cursor = isset( $response['cursor'] ) ? $response['cursor'] : null;
        } while ( $cursor );

        return $options;
    }

    /**
     * Fetch all ITEM_OPTION_VAL objects from Square API (SWEVER-style)
     * Follows pagination cursor to get complete list
     *
     * @since 1.0.0
     * @return array Array of item option values indexed by ID
     */
    public function fetch_square_item_option_values() {
        $cursor = null;
        $option_values = array();

        do {
            $endpoint = '/catalog/list?types=ITEM_OPTION_VAL';
            if ( $cursor ) {
                $endpoint .= '&cursor=' . rawurlencode( $cursor );
            }

            $response = $this->request( $endpoint );

            if ( is_wp_error( $response ) ) {
                $this->logger->log( 'api', 'error', 'Failed to fetch item option values: ' . $response->get_error_message() );
                break;
            }

            if ( isset( $response['objects'] ) && is_array( $response['objects'] ) ) {
                foreach ( $response['objects'] as $option_value ) {
                    if ( isset( $option_value['id'] ) && isset( $option_value['item_option_value_data']['name'] ) ) {
                        $option_values[$option_value['id']] = $option_value;
                    }
                }
            }

            $cursor = isset( $response['cursor'] ) ? $response['cursor'] : null;
        } while ( $cursor );

        return $option_values;
    }

    /**
     * Get catalog item with all related objects (option sets, options, modifiers)
     *
     * @since 1.0.0
     * @param string $item_id Item ID
     * @return array|WP_Error Complete item data with related objects or error
     */
    public function get_catalog_item_with_relations( $item_id ) {
        $endpoint = '/catalog/object/' . $item_id;
        $endpoint = add_query_arg( 'include_related_objects', 'true', $endpoint );

        $response = $this->request( $endpoint );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $item = isset( $response['object'] ) ? $response['object'] : array();
        $related_objects = isset( $response['related_objects'] ) ? $response['related_objects'] : array();

        // Organize related objects by type for easier access
        $organized_relations = array(
            'item_options' => array(),
            'item_option_values' => array(),
            'modifier_lists' => array(),
            'modifiers' => array(),
            'categories' => array(),
            'taxes' => array(),
            'images' => array()
        );

        foreach ( $related_objects as $related_obj ) {
            $type = $related_obj['type'] ?? '';
            switch ( $type ) {
                case 'ITEM_OPTION':
                    $organized_relations['item_options'][$related_obj['id']] = $related_obj;
                    // Also extract item option values for easier access
                    if ( isset( $related_obj['item_option_data']['values'] ) ) {
                        foreach ( $related_obj['item_option_data']['values'] as $value ) {
                            $organized_relations['item_option_values'][$value['id']] = $value;
                        }
                    }
                    break;
                case 'MODIFIER_LIST':
                    $organized_relations['modifier_lists'][$related_obj['id']] = $related_obj;
                    break;
                case 'MODIFIER':
                    $organized_relations['modifiers'][$related_obj['id']] = $related_obj;
                    break;
                case 'CATEGORY':
                    $organized_relations['categories'][$related_obj['id']] = $related_obj;
                    break;
                case 'TAX':
                    $organized_relations['taxes'][$related_obj['id']] = $related_obj;
                    break;
                case 'IMAGE':
                    $organized_relations['images'][$related_obj['id']] = $related_obj;
                    break;
            }
        }

        // If item_options or item_option_values are missing, fetch them separately
        // This addresses the issue where Square API doesn't always include all related objects
        if ( empty( $organized_relations['item_options'] ) || empty( $organized_relations['item_option_values'] ) ) {
            $this->logger->log( 'api', 'info', 'Item options/values missing from related objects, fetching separately for item: ' . $item_id );

            // Extract option IDs from the item variations
            $missing_option_ids = array();
            $missing_option_value_ids = array();

            if ( isset( $item['item_data']['variations'] ) ) {
                foreach ( $item['item_data']['variations'] as $variation ) {
                    if ( isset( $variation['item_variation_data']['item_option_values'] ) ) {
                        foreach ( $variation['item_variation_data']['item_option_values'] as $option_value ) {
                            if ( isset( $option_value['item_option_id'] ) ) {
                                $option_id = $option_value['item_option_id'];
                                if ( ! isset( $organized_relations['item_options'][$option_id] ) ) {
                                    $missing_option_ids[] = $option_id;
                                }
                            }
                            if ( isset( $option_value['item_option_value_id'] ) ) {
                                $value_id = $option_value['item_option_value_id'];
                                if ( ! isset( $organized_relations['item_option_values'][$value_id] ) ) {
                                    $missing_option_value_ids[] = $value_id;
                                }
                            }
                        }
                    }
                }
            }

            // Fetch missing option objects
            $missing_option_ids = array_unique( $missing_option_ids );
            $missing_option_value_ids = array_unique( $missing_option_value_ids );

            foreach ( $missing_option_ids as $option_id ) {
                $option_object = $this->get_catalog_item( $option_id );
                if ( ! is_wp_error( $option_object ) && isset( $option_object['type'] ) && $option_object['type'] === 'ITEM_OPTION' ) {
                    $organized_relations['item_options'][$option_id] = $option_object;

                    // Also extract option values from this option
                    if ( isset( $option_object['item_option_data']['values'] ) ) {
                        foreach ( $option_object['item_option_data']['values'] as $value ) {
                            $organized_relations['item_option_values'][$value['id']] = $value;
                        }
                    }
                }
            }

            foreach ( $missing_option_value_ids as $option_value_id ) {
                if ( ! isset( $organized_relations['item_option_values'][$option_value_id] ) ) {
                    $option_value_object = $this->get_catalog_item( $option_value_id );
                    if ( ! is_wp_error( $option_value_object ) && isset( $option_value_object['type'] ) && $option_value_object['type'] === 'ITEM_OPTION_VAL' ) {
                        $organized_relations['item_option_values'][$option_value_id] = $option_value_object;
                    }
                }
            }
        }

        return array(
            'item' => $item,
            'related_objects' => $organized_relations
        );
    }

    /**
     * Get catalog with all related objects for efficient batch processing
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Catalog with organized related objects or error
     */
    public function get_catalog_with_relations( $args = array() ) {
        $defaults = array(
            'types' => 'ITEM,ITEM_OPTION,MODIFIER_LIST,MODIFIER,CATEGORY',
            'include_related_objects' => 'true',
            'limit' => 1000
        );

        $args = wp_parse_args( $args, $defaults );

        $endpoint = '/catalog/list';
        if ( ! empty( $args ) ) {
            $endpoint = add_query_arg( $args, $endpoint );
        }

        $response = $this->request( $endpoint );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $objects = isset( $response['objects'] ) ? $response['objects'] : array();

        // Organize objects by type
        $organized_catalog = array(
            'items' => array(),
            'item_options' => array(),
            'item_option_values' => array(),
            'modifier_lists' => array(),
            'modifiers' => array(),
            'categories' => array(),
            'taxes' => array(),
            'images' => array()
        );

        foreach ( $objects as $object ) {
            $type = $object['type'] ?? '';
            $id = $object['id'] ?? '';

            switch ( $type ) {
                case 'ITEM':
                    $organized_catalog['items'][$id] = $object;
                    break;
                case 'ITEM_OPTION':
                    $organized_catalog['item_options'][$id] = $object;
                    // Also extract item option values for easier access
                    if ( isset( $object['item_option_data']['values'] ) ) {
                        foreach ( $object['item_option_data']['values'] as $value ) {
                            $organized_catalog['item_option_values'][$value['id']] = $value;
                        }
                    }
                    break;
                case 'MODIFIER_LIST':
                    $organized_catalog['modifier_lists'][$id] = $object;
                    break;
                case 'MODIFIER':
                    $organized_catalog['modifiers'][$id] = $object;
                    break;
                case 'CATEGORY':
                    $organized_catalog['categories'][$id] = $object;
                    break;
                case 'TAX':
                    $organized_catalog['taxes'][$id] = $object;
                    break;
                case 'IMAGE':
                    $organized_catalog['images'][$id] = $object;
                    // Cache image URLs
                    if ( isset( $object['image_data']['url'] ) ) {
                        $this->image_cache[$id] = $object['image_data']['url'];
                    }
                    break;
            }
        }

        return $organized_catalog;
    }

    /**
     * Resolve option value ID to its parent option
     *
     * @since 1.0.0
     * @param string $option_value_id Square option value ID
     * @param array $catalog_data Optional pre-fetched catalog data
     * @return array|false Option data or false if not found
     */
    public function resolve_option_by_option_value_id( $option_value_id, $catalog_data = null ) {
        // If catalog data is provided, search within it
        if ( $catalog_data && isset( $catalog_data['item_options'] ) ) {
            foreach ( $catalog_data['item_options'] as $option ) {
                $values = $option['item_option_data']['values'] ?? array();
                foreach ( $values as $value ) {
                    if ( $value['id'] === $option_value_id ) {
                        return $option;
                    }
                }
            }
            return false;
        }

        // Fallback: fetch options and search
        $options = $this->get_catalog( array( 'types' => 'ITEM_OPTION' ) );
        if ( is_wp_error( $options ) ) {
            return false;
        }

        foreach ( $options as $option ) {
            if ( $option['type'] !== 'ITEM_OPTION' ) {
                continue;
            }
            $values = $option['item_option_data']['values'] ?? array();
            foreach ( $values as $value ) {
                if ( $value['id'] === $option_value_id ) {
                    return $option;
                }
            }
        }

        return false;
    }

    /**
     * Get complete option hierarchy for an item (option sets with their options)
     *
     * @since 1.0.0
     * @param array $item Square item data
     * @param array $catalog_data Optional pre-fetched catalog data
     * @return array Complete option hierarchy
     */
    public function get_complete_option_hierarchy( $item, $catalog_data = null ) {
        $hierarchy = array();

        // Get option set IDs from item
        $option_set_ids = $item['item_data']['item_option_set_ids'] ?? array();

        foreach ( $option_set_ids as $option_set_id ) {
            $option_set = null;

            // Try to get from provided catalog data first
            if ( $catalog_data && isset( $catalog_data['item_option_sets'][$option_set_id] ) ) {
                $option_set = $catalog_data['item_option_sets'][$option_set_id];
            } else {
                // Fallback to API call
                $option_set = $this->get_item_option_set( $option_set_id );
                if ( is_wp_error( $option_set ) ) {
                    continue;
                }
            }

            if ( empty( $option_set ) ) {
                continue;
            }

            $option_set_data = $option_set['item_option_set_data'] ?? array();
            $option_ids = $option_set_data['item_option_ids'] ?? array();

            $options = array();
            foreach ( $option_ids as $option_id ) {
                $option = null;

                // Try to get from provided catalog data first
                if ( $catalog_data && isset( $catalog_data['item_options'][$option_id] ) ) {
                    $option = $catalog_data['item_options'][$option_id];
                } else {
                    // Fallback to API call
                    $option = $this->get_catalog_item( $option_id );
                    if ( is_wp_error( $option ) ) {
                        continue;
                    }
                }

                if ( ! empty( $option ) ) {
                    $options[$option_id] = $option;
                }
            }

            $hierarchy[$option_set_id] = array(
                'option_set' => $option_set,
                'options' => $options
            );
        }

        return $hierarchy;
    }

    /**
     * Batch resolve option IDs to their names and option set names
     *
     * @since 1.0.0
     * @param array $option_ids Array of option IDs to resolve
     * @param array $catalog_data Optional pre-fetched catalog data
     * @return array Resolved option data with format: [option_id => [option_name, option_set_name, option_set_id]]
     */
    public function batch_resolve_option_names( $option_ids, $catalog_data = null ) {
        $resolved = array();

        foreach ( $option_ids as $option_id ) {
            $option = null;
            $option_set = null;

            // Get option data
            if ( $catalog_data && isset( $catalog_data['item_options'][$option_id] ) ) {
                $option = $catalog_data['item_options'][$option_id];
            } else {
                $option = $this->get_catalog_item( $option_id );
                if ( is_wp_error( $option ) ) {
                    continue;
                }
            }

            if ( empty( $option ) ) {
                continue;
            }

            $option_name = $option['item_option_data']['name'] ?? '';

            // Find parent option set
            $option_set = $this->resolve_option_set_by_option_id( $option_id, $catalog_data );

            $option_set_name = '';
            $option_set_id = '';
            if ( $option_set ) {
                $option_set_name = $option_set['item_option_set_data']['name'] ?? '';
                $option_set_id = $option_set['id'] ?? '';
            }

            $resolved[$option_id] = array(
                'option_name' => $option_name,
                'option_set_name' => $option_set_name,
                'option_set_id' => $option_set_id
            );
        }

        return $resolved;
    }

    /**
     * Create catalog item
     *
     * @since 1.0.0
     * @param array $item Item data
     * @return array|WP_Error Response or error
     */
    public function create_catalog_item( $item ) {
        $data = array(
            'idempotency_key' => uniqid( 'squarekit_' ),
            'object' => $item,
        );
        
        $response = $this->request( '/catalog/object', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : array();
    }

    /**
     * Update catalog item
     *
     * @since 1.0.0
     * @param array $item Item data
     * @return array|WP_Error Response or error
     */
    public function update_catalog_item( $item ) {
        $data = array(
            'idempotency_key' => uniqid( 'squarekit_' ),
            'object' => $item,
        );
        
        $response = $this->request( '/catalog/object', 'PUT', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['catalog_object'] ) ? $response['catalog_object'] : array();
    }

    /**
     * Delete catalog item
     *
     * @since 1.0.0
     * @param string $item_id Item ID
     * @return array|WP_Error Response or error
     */
    public function delete_catalog_item( $item_id ) {
        $response = $this->request( '/catalog/object/' . $item_id, 'DELETE' );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return $response;
    }

    /**
     * Get inventory
     *
     * @since 1.0.0
     * @param array $catalog_object_ids Catalog object IDs
     * @param array $location_ids Location IDs
     * @return array|WP_Error Inventory or error
     */
    public function get_inventory( $catalog_object_ids = array(), $location_ids = array() ) {
        $endpoint = '/inventory/counts/batch';
        
        $data = array();
        
        if ( ! empty( $catalog_object_ids ) ) {
            $data['catalog_object_ids'] = $catalog_object_ids;
        }
        
        if ( ! empty( $location_ids ) ) {
            $data['location_ids'] = $location_ids;
        }
        
        $response = $this->request( $endpoint, 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['counts'] ) ? $response['counts'] : array();
    }

    /**
     * Update inventory
     *
     * @since 1.0.0
     * @param array $inventory_changes Inventory changes
     * @return array|WP_Error Response or error
     */
    public function update_inventory( $inventory_changes ) {
        $data = array(
            'idempotency_key' => uniqid( 'squarekit_' ),
            'changes' => $inventory_changes,
        );
        
        $response = $this->request( '/inventory/changes/batch-create', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return $response;
    }

    /**
     * Get customers
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Customers or error
     */
    public function get_customers( $args = array() ) {
        $endpoint = '/customers/search';
        
        $response = $this->request( $endpoint, 'POST', $args );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['customers'] ) ? $response['customers'] : array();
    }

    /**
     * Get customer
     *
     * @since 1.0.0
     * @param string $customer_id Customer ID
     * @return array|WP_Error Customer or error
     */
    public function get_customer( $customer_id ) {
        $response = $this->request( '/customers/' . $customer_id );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['customer'] ) ? $response['customer'] : array();
    }

    /**
     * Create customer
     *
     * @since 1.0.0
     * @param array $customer Customer data
     * @return array|WP_Error Response or error
     */
    public function create_customer( $customer ) {
        $data = array(
            'idempotency_key' => uniqid( 'squarekit_' ),
            'given_name' => isset( $customer['given_name'] ) ? $customer['given_name'] : '',
            'family_name' => isset( $customer['family_name'] ) ? $customer['family_name'] : '',
            'email_address' => isset( $customer['email_address'] ) ? $customer['email_address'] : '',
            'phone_number' => isset( $customer['phone_number'] ) ? $customer['phone_number'] : '',
            'reference_id' => isset( $customer['reference_id'] ) ? $customer['reference_id'] : '',
            'note' => isset( $customer['note'] ) ? $customer['note'] : '',
        );
        
        // Remove empty values
        $data = array_filter( $data );
        
        $response = $this->request( '/customers', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['customer'] ) ? $response['customer'] : array();
    }

    /**
     * Update customer
     *
     * @since 1.0.0
     * @param string $customer_id Customer ID
     * @param array $customer Customer data
     * @return array|WP_Error Response or error
     */
    public function update_customer( $customer_id, $customer ) {
        $data = array_filter( $customer );
        
        $response = $this->request( '/customers/' . $customer_id, 'PUT', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['customer'] ) ? $response['customer'] : array();
    }

    /**
     * Delete customer
     *
     * @since 1.0.0
     * @param string $customer_id Customer ID
     * @return array|WP_Error Response or error
     */
    public function delete_customer( $customer_id ) {
        $response = $this->request( '/customers/' . $customer_id, 'DELETE' );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return $response;
    }

    /**
     * Create order
     *
     * @since 1.0.0
     * @param array $order Order data
     * @return array|WP_Error Response or error
     */
    public function create_order( $order ) {
        $data = array(
            'idempotency_key' => uniqid( 'squarekit_' ),
            'order' => $order,
        );
        
        $response = $this->request( '/orders', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['order'] ) ? $response['order'] : array();
    }

    /**
     * Get order
     *
     * @since 1.0.0
     * @param string $order_id Order ID
     * @return array|WP_Error Order or error
     */
    public function get_order( $order_id ) {
        $response = $this->request( '/orders/' . $order_id );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['order'] ) ? $response['order'] : array();
    }

    /**
     * Update order
     *
     * @since 1.0.0
     * @param string $order_id Order ID
     * @param array $order Order data
     * @return array|WP_Error Response or error
     */
    public function update_order( $order_id, $order ) {
        $data = array(
            'idempotency_key' => uniqid( 'squarekit_' ),
            'order' => $order,
        );
        
        $response = $this->request( '/orders/' . $order_id, 'PUT', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['order'] ) ? $response['order'] : array();
    }

    /**
     * Create webhook
     *
     * @since 1.0.0
     * @param string $url Webhook URL
     * @param array $event_types Event types
     * @return array|WP_Error Response or error
     */
    public function create_webhook( $url, $event_types = array() ) {
        $data = array(
            'idempotency_key' => uniqid( 'squarekit_' ),
            'subscription' => array(
                'name' => 'Square Kit Webhook',
                'event_types' => $event_types,
                'notification_url' => $url,
                'api_version' => '2023-09-25',
            ),
        );
        
        $response = $this->request( '/webhooks/subscriptions', 'POST', $data );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        return isset( $response['subscription'] ) ? $response['subscription'] : array();
    }

    /**
     * Delete webhooks
     *
     * @since 1.0.0
     * @return array|WP_Error Response or error
     */
    public function delete_webhooks() {
        $response = $this->request( '/webhooks/subscriptions', 'GET' );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        $subscriptions = isset( $response['subscriptions'] ) ? $response['subscriptions'] : array();
        
        foreach ( $subscriptions as $subscription ) {
            $this->request( '/webhooks/subscriptions/' . $subscription['id'], 'DELETE' );
        }
        
        return array(
            'success' => true,
            'message' => __( 'Webhooks deleted successfully.', 'squarekit' ),
        );
    }

    /**
     * Get catalog items with filtering and pagination
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Catalog items or error
     */
    public function get_catalog_items( $args = array() ) {
        $defaults = array(
            'search' => '',
            'sync_status' => '',
            'type' => '',
            'sort' => 'name_asc',
            'page' => 1,
            'per_page' => 20,
            'cursor' => ''
        );

        $args = wp_parse_args( $args, $defaults );

        // Build query parameters
        $query_params = array();

        if ( ! empty( $args['search'] ) ) {
            $query_params['text_filter'] = $args['search'];
        }

        if ( ! empty( $args['type'] ) ) {
            $query_params['types'] = $args['type'];
        } else {
            // Default to ITEM only for products page - much faster
            $query_params['types'] = 'ITEM';
        }

        // Add pagination - only add cursor if it's provided and not empty
        // For page-based pagination, we'll need to implement cursor storage
        // For now, only add cursor if explicitly provided
        if ( ! empty( $args['cursor'] ) && $args['cursor'] !== '0' ) {
            $query_params['cursor'] = $args['cursor'];
        }
        $query_params['limit'] = $args['per_page'];

        $endpoint = '/catalog/list';
        if ( ! empty( $query_params ) ) {
            $endpoint = add_query_arg( $query_params, $endpoint );
        }

        $response = $this->request( $endpoint );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $objects = isset( $response['objects'] ) ? $response['objects'] : array();
        $cursor = isset( $response['cursor'] ) ? $response['cursor'] : '';

        // Process objects to add sync status
        $processed_objects = array();
        foreach ( $objects as $object ) {
            $processed_object = $this->process_catalog_object( $object, $args );
            if ( $processed_object ) {
                $processed_objects[] = $processed_object;
            }
        }

        // Sort results
        $processed_objects = $this->sort_catalog_objects( $processed_objects, $args['sort'] );

        // Filter by sync status if specified
        if ( ! empty( $args['sync_status'] ) ) {
            $processed_objects = $this->filter_by_sync_status( $processed_objects, $args['sync_status'] );
        }

        return array(
            'products' => $processed_objects,
            'pagination' => array(
                'per_page' => $args['per_page'],
                'total_items' => count( $processed_objects ),
                'has_more' => ! empty( $cursor ),
                'cursor' => $cursor
            )
        );
    }

    /**
     * Process catalog object to add sync status and formatting
     *
     * @since 1.0.0
     * @param array $object Catalog object
     * @param array $args Query arguments
     * @return array|false Processed object or false
     */
    private function process_catalog_object( $object, $args ) {
        $type = $object['type'] ?? '';
        
        switch ( $type ) {
            case 'ITEM':
                return $this->process_item_object( $object );
            case 'CATEGORY':
                return $this->process_category_object( $object );
            case 'MODIFIER_LIST':
                return $this->process_modifier_object( $object );
            default:
                return false;
        }
    }

    /**
     * Process item object
     *
     * @since 1.0.0
     * @param array $object Item object
     * @return array Processed item
     */
    private function process_item_object( $object ) {
        $item_data = $object['item_data'] ?? array();
        
        // Get primary image
        $image_url = '';
        if ( ! empty( $item_data['image_ids'] ) ) {
            $image_url = $this->get_image_url( $item_data['image_ids'][0] );
        }
        
        // Get price from first variation
        $price_money = null;
        if ( ! empty( $item_data['variations'] ) ) {
            $first_variation = $item_data['variations'][0];
            $price_money = $first_variation['item_variation_data']['price_money'] ?? null;
        }
        
        // Check sync status
        $sync_status = $this->get_item_sync_status( $object['id'] );
        
        return array(
            'id' => $object['id'],
            'name' => $item_data['name'] ?? '',
            'description' => $item_data['description'] ?? '',
            'type' => $object['type'],
            'image_url' => $image_url,
            'price_money' => $price_money,
            'category_id' => $item_data['category_id'] ?? '',
            'variations' => $item_data['variations'] ?? array(),
            'modifier_list_info' => $item_data['modifier_list_info'] ?? array(),
            'tax_ids' => $item_data['tax_ids'] ?? array(),
            'present_at_all_locations' => $item_data['present_at_all_locations'] ?? false,
            'imported' => $sync_status['imported'],
            'synced' => $sync_status['synced'],
            'out_of_sync' => $sync_status['out_of_sync'],
            'wc_product_id' => $sync_status['wc_product_id']
        );
    }

    /**
     * Process category object
     *
     * @since 1.0.0
     * @param array $object Category object
     * @return array Processed category
     */
    private function process_category_object( $object ) {
        $category_data = $object['category_data'] ?? array();
        
        return array(
            'id' => $object['id'],
            'name' => $category_data['name'] ?? '',
            'description' => $category_data['description'] ?? '',
            'type' => $object['type'],
            'image_url' => '',
            'price_money' => null,
            'imported' => false,
            'synced' => false,
            'out_of_sync' => false,
            'wc_product_id' => null
        );
    }

    /**
     * Process modifier object
     *
     * @since 1.0.0
     * @param array $object Modifier object
     * @return array Processed modifier
     */
    private function process_modifier_object( $object ) {
        $modifier_data = $object['modifier_list_data'] ?? array();
        
        return array(
            'id' => $object['id'],
            'name' => $modifier_data['name'] ?? '',
            'description' => '',
            'type' => $object['type'],
            'image_url' => '',
            'price_money' => null,
            'imported' => false,
            'synced' => false,
            'out_of_sync' => false,
            'wc_product_id' => null
        );
    }

    /**
     * Get item sync status
     *
     * @since 1.0.0
     * @param string $square_id Square item ID
     * @return array Sync status
     */
    private function get_item_sync_status( $square_id ) {
        global $wpdb;
        
        // Check if product exists in WooCommerce
        $wc_product_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = '_square_id' AND meta_value = %s",
            $square_id
        ) );
        
        if ( ! $wc_product_id ) {
            return array(
                'imported' => false,
                'synced' => false,
                'out_of_sync' => false,
                'wc_product_id' => null
            );
        }
        
        // Check if product is synced
        $last_sync = get_post_meta( $wc_product_id, '_square_last_sync', true );
        $square_updated = get_post_meta( $wc_product_id, '_square_updated_at', true );
        
        $synced = ! empty( $last_sync );
        $out_of_sync = $synced && ! empty( $square_updated ) && $last_sync < $square_updated;
        
        return array(
            'imported' => true,
            'synced' => $synced,
            'out_of_sync' => $out_of_sync,
            'wc_product_id' => $wc_product_id
        );
    }

    /**
     * Get image URL from Square
     *
     * @since 1.0.0
     * @param string $image_id Square image ID
     * @return string Image URL
     */
    private function get_image_url( $image_id ) {
        $image = $this->get_catalog_item( $image_id );
        
        if ( is_wp_error( $image ) || empty( $image ) ) {
            return '';
        }
        
        return isset( $image['image_data']['url'] ) ? $image['image_data']['url'] : '';
    }

    /**
     * Get catalog images in batch
     *
     * @since 1.0.0
     * @param array $image_ids Array of image IDs
     * @return array|WP_Error Array of image objects or error
     */
    public function batch_retrieve_catalog_images( $image_ids ) {
        if ( empty( $image_ids ) || ! is_array( $image_ids ) ) {
            return array();
        }

        // Remove duplicates and limit to 1000 (Square API limit)
        $image_ids = array_unique( array_slice( $image_ids, 0, 1000 ) );

        $data = array(
            'object_ids' => $image_ids,
            'include_related_objects' => false
        );

        $response = $this->request( '/catalog/batch-retrieve', 'POST', $data );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return isset( $response['objects'] ) ? $response['objects'] : array();
    }

    /**
     * Get image URLs by IDs with caching
     *
     * @since 1.0.0
     * @param array $image_ids Array of image IDs
     * @return array Array of image_id => image_url mappings
     */
    public function get_image_urls_by_ids( $image_ids ) {
        if ( empty( $image_ids ) || ! is_array( $image_ids ) ) {
            return array();
        }

        $image_urls = array();
        $uncached_ids = array();

        // Check cache first
        foreach ( $image_ids as $image_id ) {
            if ( isset( $this->image_cache[ $image_id ] ) ) {
                $image_urls[ $image_id ] = $this->image_cache[ $image_id ];
            } else {
                $uncached_ids[] = $image_id;
            }
        }

        // Fetch uncached images
        if ( ! empty( $uncached_ids ) ) {
            $images = $this->batch_retrieve_catalog_images( $uncached_ids );

            if ( ! is_wp_error( $images ) ) {
                foreach ( $images as $image ) {
                    if ( isset( $image['type'] ) && $image['type'] === 'IMAGE' &&
                         isset( $image['id'] ) && isset( $image['image_data']['url'] ) ) {

                        $image_id = $image['id'];
                        $image_url = $image['image_data']['url'];

                        // Cache the result
                        $this->image_cache[ $image_id ] = $image_url;
                        $image_urls[ $image_id ] = $image_url;
                    }
                }
            }
        }

        return $image_urls;
    }

    /**
     * Get all catalog images
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|WP_Error Array of image objects or error
     */
    public function get_catalog_images( $args = array() ) {
        $defaults = array(
            'types' => 'IMAGE',
            'cursor' => '',
            'limit' => 1000
        );

        $args = wp_parse_args( $args, $defaults );

        $endpoint = '/catalog/list';

        if ( ! empty( $args ) ) {
            $endpoint = add_query_arg( $args, $endpoint );
        }

        $response = $this->request( $endpoint );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return isset( $response['objects'] ) ? $response['objects'] : array();
    }

    /**
     * Create comprehensive image map from catalog
     *
     * @since 1.0.0
     * @param array $catalog_objects Array of catalog objects (items and images)
     * @return array Image ID to URL mapping
     */
    public function create_comprehensive_image_map( $catalog_objects ) {
        $image_map = array();

        if ( empty( $catalog_objects ) || ! is_array( $catalog_objects ) ) {
            return $image_map;
        }

        foreach ( $catalog_objects as $object ) {
            if ( isset( $object['type'] ) && $object['type'] === 'IMAGE' &&
                 isset( $object['id'] ) && isset( $object['image_data']['url'] ) ) {

                $image_id = $object['id'];
                $image_url = $object['image_data']['url'];

                $image_map[ $image_id ] = $image_url;

                // Also cache it
                $this->image_cache[ $image_id ] = $image_url;
            }
        }

        return $image_map;
    }

    /**
     * Clear image cache
     *
     * @since 1.0.0
     */
    public function clear_image_cache() {
        $this->image_cache = array();
    }

    /**
     * Log image retrieval error
     *
     * @since 1.0.0
     * @param string $image_id Image ID that failed
     * @param string $error_message Error message
     */
    protected function log_image_error( $image_id, $error_message ) {
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( sprintf(
                'SquareKit Image Error - ID: %s, Error: %s',
                $image_id,
                $error_message
            ) );
        }
    }

    /**
     * Validate image URL
     *
     * @since 1.0.0
     * @param string $url Image URL to validate
     * @return bool True if valid, false otherwise
     */
    protected function validate_image_url( $url ) {
        if ( empty( $url ) || ! is_string( $url ) ) {
            return false;
        }

        // Check if it's a valid URL
        if ( ! filter_var( $url, FILTER_VALIDATE_URL ) ) {
            return false;
        }

        // Check if it's an image URL (basic check)
        $parsed_url = parse_url( $url );
        if ( ! isset( $parsed_url['path'] ) ) {
            return false;
        }

        $path = $parsed_url['path'];
        $extension = strtolower( pathinfo( $path, PATHINFO_EXTENSION ) );

        $valid_extensions = array( 'jpg', 'jpeg', 'png', 'gif', 'webp' );

        // If no extension, assume it's valid (Square URLs might not have extensions)
        return empty( $extension ) || in_array( $extension, $valid_extensions );
    }

    /**
     * Get image statistics
     *
     * @since 1.0.0
     * @return array Image cache statistics
     */
    public function get_image_cache_stats() {
        return array(
            'cached_images' => count( $this->image_cache ),
            'cache_size_kb' => round( strlen( serialize( $this->image_cache ) ) / 1024, 2 ),
            'cached_ids' => array_keys( $this->image_cache )
        );
    }

    /**
     * Sort catalog objects
     *
     * @since 1.0.0
     * @param array $objects Catalog objects
     * @param string $sort Sort order
     * @return array Sorted objects
     */
    private function sort_catalog_objects( $objects, $sort ) {
        switch ( $sort ) {
            case 'name_desc':
                usort( $objects, function( $a, $b ) {
                    return strcasecmp( $b['name'], $a['name'] );
                } );
                break;
            case 'price_asc':
                usort( $objects, function( $a, $b ) {
                    $price_a = $a['price_money']['amount'] ?? 0;
                    $price_b = $b['price_money']['amount'] ?? 0;
                    return $price_a - $price_b;
                } );
                break;
            case 'price_desc':
                usort( $objects, function( $a, $b ) {
                    $price_a = $a['price_money']['amount'] ?? 0;
                    $price_b = $b['price_money']['amount'] ?? 0;
                    return $price_b - $price_a;
                } );
                break;
            case 'updated_desc':
                usort( $objects, function( $a, $b ) {
                    $updated_a = $a['updated_at'] ?? '';
                    $updated_b = $b['updated_at'] ?? '';
                    return strcasecmp( $updated_b, $updated_a );
                } );
                break;
            default: // name_asc
                usort( $objects, function( $a, $b ) {
                    return strcasecmp( $a['name'], $b['name'] );
                } );
                break;
        }
        
        return $objects;
    }

    /**
     * Filter objects by sync status
     *
     * @since 1.0.0
     * @param array $objects Catalog objects
     * @param string $sync_status Sync status filter
     * @return array Filtered objects
     */
    private function filter_by_sync_status( $objects, $sync_status ) {
        return array_filter( $objects, function( $object ) use ( $sync_status ) {
            switch ( $sync_status ) {
                case 'imported':
                    return $object['imported'];
                case 'not_imported':
                    return ! $object['imported'];
                case 'synced':
                    return $object['synced'];
                case 'out_of_sync':
                    return $object['out_of_sync'];
                default:
                    return true;
            }
        } );
    }

    /**
     * Test connection to Square API
     *
     * @since 1.0.0
     * @return bool|WP_Error True on success, error on failure
     */
    public function test_connection() {
        $response = $this->request( '/locations' );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return true;
    }

    /**
     * Get merchant information
     *
     * @since 1.0.0
     * @return array|WP_Error Merchant info or error
     */
    public function get_merchant_info() {
        $response = $this->request( '/merchants/me' );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        return isset( $response['merchant'] ) ? $response['merchant'] : array();
    }

    /**
     * Refresh access token
     *
     * @since 1.0.0
     * @param string $refresh_token Refresh token
     * @return bool|WP_Error True on success, error on failure
     */
    public function refresh_access_token( $refresh_token ) {
        $environment = $this->settings->get_environment();
        $client_id = $this->settings->get( $environment . '_application_id', '' );
        $client_secret = $this->settings->get( $environment . '_client_secret', '' );
        
        $token_url = ( $environment === 'sandbox' ? 'https://connect.squareupsandbox.com/oauth2/token' : 'https://connect.squareup.com/oauth2/token' );
        
        $response = wp_remote_post( $token_url, array(
            'headers' => array( 'Content-Type' => 'application/json' ),
            'body' => wp_json_encode( array(
                'client_id' => $client_id,
                'client_secret' => $client_secret,
                'refresh_token' => $refresh_token,
                'grant_type' => 'refresh_token',
            ) ),
            'timeout' => 30,
        ) );
        
        if ( is_wp_error( $response ) ) {
            return $response;
        }
        
        $body = json_decode( wp_remote_retrieve_body( $response ), true );
        
        if ( ! empty( $body['access_token'] ) ) {
            $this->settings->set( $environment . '_access_token', $body['access_token'] );
            
            if ( ! empty( $body['refresh_token'] ) ) {
                $this->settings->set( $environment . '_refresh_token', $body['refresh_token'] );
            }
            
            return true;
        }
        
        return new WP_Error(
            'squarekit_token_refresh_failed',
            __( 'Failed to refresh access token.', 'squarekit' )
        );
    }
} 