<?php
/**
 * The REST API functionality of the plugin.
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/api
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * The REST API functionality of the plugin.
 *
 * @since 1.0.0
 */
class SquareKit_API {

    /**
     * Settings instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_Settings
     */
    protected $settings;

    /**
     * DB instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_DB
     */
    protected $db;

    /**
     * Namespace
     *
     * @since 1.0.0
     * @access protected
     * @var string
     */
    protected $namespace = 'sws/v1';

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->settings = new SquareKit_Settings();
        $this->db = new SquareKit_DB();
    }

    /**
     * Register REST API routes
     *
     * @since 1.0.0
     */
    public function register_routes() {
        // Settings endpoints
        register_rest_route( 'squarekit/v1', '/settings', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'update_settings' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Inventory endpoints
        register_rest_route( 'squarekit/v1', '/inventory', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_inventory' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'update_inventory' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Logs endpoints
        register_rest_route( 'squarekit/v1', '/logs', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_logs' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Orders endpoints
        register_rest_route( 'squarekit/v1', '/orders', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_orders' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'sync_orders' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Customers endpoints
        register_rest_route( 'squarekit/v1', '/customers', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_customers' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'sync_customers' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // OAuth endpoints
        register_rest_route( 'squarekit/v1', '/oauth', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_oauth_status' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'handle_oauth' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Bulk operations endpoints
        register_rest_route( 'squarekit/v1', '/bulk-operations', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_bulk_operations' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'start_bulk_operation' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Individual bulk operation endpoints
        register_rest_route( 'squarekit/v1', '/bulk-operations/(?P<id>\d+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_bulk_operation' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array( $this, 'update_bulk_operation' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
            array(
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => array( $this, 'cancel_bulk_operation' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );

        // Bulk operation progress endpoint
        register_rest_route( 'squarekit/v1', '/bulk-operations/(?P<id>\d+)/progress', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array( $this, 'get_bulk_operation_progress' ),
                'permission_callback' => array( $this, 'check_permissions' ),
            ),
        ) );
    }

    /**
     * Check if user has permissions
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return bool True if user has permission, false otherwise
     */
    public function check_permissions( $request ) {
        return current_user_can( 'manage_options' );
    }

    /**
     * Get settings
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_settings( $request ) {
        $settings = $this->settings->get_all();
        
        // Remove sensitive data
        if ( isset( $settings['sandbox_access_token'] ) ) {
            $settings['sandbox_access_token'] = ! empty( $settings['sandbox_access_token'] );
        }
        
        if ( isset( $settings['production_access_token'] ) ) {
            $settings['production_access_token'] = ! empty( $settings['production_access_token'] );
        }
        
        if ( isset( $settings['webhook_signature_key'] ) ) {
            $settings['webhook_signature_key'] = ! empty( $settings['webhook_signature_key'] );
        }
        
        return rest_ensure_response( $settings );
    }

    /**
     * Update settings
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function update_settings( $request ) {
        $params = $request->get_params();
        
        // Update settings
        $result = $this->settings->update( $params );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Settings updated successfully.', 'squarekit' ),
            ) );
        } else {
            return new WP_Error(
                'squarekit_settings_update_failed',
                __( 'Failed to update settings.', 'squarekit' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Handle OAuth flow
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function handle_oauth( $request ) {
        $params = $request->get_params();
        
        if ( isset( $params['code'] ) ) {
            // Handle OAuth callback
            $result = $this->settings->handle_oauth_callback( $params['code'] );
            
            if ( is_wp_error( $result ) ) {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => $result->get_error_message(),
                ), 400 );
            }
            
            return new WP_REST_Response( array(
                'success' => true,
                'message' => __( 'OAuth connection successful.', 'squarekit' ),
            ) );
        }
        
        return new WP_REST_Response( array(
            'success' => false,
            'message' => __( 'Invalid OAuth parameters.', 'squarekit' ),
        ), 400 );
    }

    /**
     * Get OAuth status
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_oauth_status( $request ) {
        $access_token = $this->settings->get_access_token();
        $is_connected = ! empty( $access_token );
        
        return new WP_REST_Response( array(
            'success' => true,
            'connected' => $is_connected,
            'access_token' => $is_connected ? '***' : null,
        ) );
    }

    /**
     * Get inventory
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_inventory( $request ) {
        $params = $request->get_params();

        // Check import status
        if ( isset($params['action']) && $params['action'] === 'import_status' ) {
            // Return import status - this would need to be implemented in the WooCommerce integration
            return rest_ensure_response( array(
                'status' => 'complete',
                'message' => __( 'Import complete', 'squarekit' ),
                'percent_complete' => 100
            ) );
        }

        // Get inventory
        $inventory = $this->db->get_inventory( $params );

        return rest_ensure_response( $inventory );
    }

    /**
     * Update inventory
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function update_inventory( $request ) {
        $params = $request->get_params();
        // Start import from Square
        if ( isset($params['action']) && $params['action'] === 'start_import' ) {
            if ( ! class_exists('SquareKit_WooCommerce') ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
            }
            $wc = new SquareKit_WooCommerce();
            $result = $wc->import_products_from_square();
            return rest_ensure_response($result);
        }
        
        // Import categories from Square
        if ( isset($params['action']) && $params['action'] === 'import_categories' ) {
            if ( ! class_exists('SquareKit_WooCommerce') ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
            }
            $wc = new SquareKit_WooCommerce();
            $result = $wc->import_categories_from_square();
            return rest_ensure_response($result);
        }
        
        // Import inventory from Square
        if ( isset($params['action']) && $params['action'] === 'import_inventory' ) {
            if ( ! class_exists('SquareKit_WooCommerce') ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
            }
            $wc = new SquareKit_WooCommerce();
            $result = $wc->import_inventory_from_square();
            return rest_ensure_response($result);
        }
        
        // Update inventory
        $result = $this->db->update_inventory( $params );
        
        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Inventory updated successfully.', 'squarekit' ),
            ) );
        } else {
            return new WP_Error(
                'squarekit_inventory_update_failed',
                __( 'Failed to update inventory.', 'squarekit' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Get logs
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_logs( $request ) {
        $params = $request->get_params();
        
        // Get logs
        $logs = $this->db->get_logs( $params );
        $total = $this->db->count_logs( $params );
        
        return rest_ensure_response( array(
            'logs' => $logs,
            'total' => $total,
        ) );
    }

    /**
     * Add log
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function add_log( $request ) {
        $params = $request->get_params();
        
        // Required parameters
        if ( ! isset( $params['type'] ) || ! isset( $params['message'] ) ) {
            return new WP_Error(
                'squarekit_missing_params',
                __( 'Missing required parameters.', 'squarekit' ),
                array( 'status' => 400 )
            );
        }
        
        // Add log
        $log_id = $this->db->add_log(
            sanitize_text_field( $params['type'] ),
            sanitize_text_field( $params['message'] ),
            isset( $params['data'] ) ? $params['data'] : array()
        );
        
        if ( $log_id ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Log added successfully.', 'squarekit' ),
                'log_id' => $log_id,
            ) );
        } else {
            return new WP_Error(
                'squarekit_log_add_failed',
                __( 'Failed to add log.', 'squarekit' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Get orders
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_orders( $request ) {
        // This will be implemented in the WooCommerce integration class
        return rest_ensure_response( array() );
    }

    /**
     * Create order
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function create_order( $request ) {
        $params = $request->get_params();
        // Start import from Square
        if ( isset($params['action']) && $params['action'] === 'start_import' ) {
            if ( ! class_exists('SquareKit_WooCommerce') ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
            }
            $wc = new SquareKit_WooCommerce();
            $result = $wc->import_orders_from_square();
            return rest_ensure_response($result);
        }
        // This will be implemented in the WooCommerce integration class
        return rest_ensure_response( array(
            'success' => false,
            'message' => __( 'Not implemented yet.', 'squarekit' ),
        ) );
    }

    /**
     * Get customers
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_customers( $request ) {
        $params = $request->get_params();
        
        // Get customers
        $customers = $this->db->get_customer( array_merge( $params, array( 'single' => false ) ) );
        
        return rest_ensure_response( $customers );
    }

    /**
     * Sync customers
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function sync_customers( $request ) {
        $params = $request->get_params();
        // Start import from Square
        if ( isset($params['action']) && $params['action'] === 'start_import' ) {
            if ( ! class_exists('SquareKit_WooCommerce') ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
            }
            $wc = new SquareKit_WooCommerce();
            $result = $wc->import_customers_from_square();
            return rest_ensure_response($result);
        }

        // Check import status
        if ( isset($params['action']) && $params['action'] === 'import_status' ) {
            // Return import status - this would need to be implemented in the WooCommerce integration
            return rest_ensure_response( array(
                'status' => 'complete',
                'message' => __( 'Import complete', 'squarekit' ),
                'percent_complete' => 100
            ) );
        }

        // Default sync action
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        $wc = new SquareKit_WooCommerce();
        $result = $wc->sync_customers();
        return rest_ensure_response($result);
    }

    /**
     * Update customer
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function update_customer( $request ) {
        $params = $request->get_params();
        // Start import from Square
        if ( isset($params['action']) && $params['action'] === 'start_import' ) {
            if ( ! class_exists('SquareKit_WooCommerce') ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
            }
            $wc = new SquareKit_WooCommerce();
            $result = $wc->import_customers_from_square();
            return rest_ensure_response($result);
        }

        // Update customer
        $result = $this->db->update_customer( $params );

        if ( $result ) {
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Customer updated successfully.', 'squarekit' ),
            ) );
        } else {
            return new WP_Error(
                'squarekit_customer_update_failed',
                __( 'Failed to update customer.', 'squarekit' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Handle webhook
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function handle_webhook( $request ) {
        $signature = $request->get_header( 'X-Square-Signature' );
        $body = $request->get_body();
        
        // Verify webhook signature
        $webhook_signature_key = $this->settings->get_webhook_signature_key();
        
        if ( ! empty( $webhook_signature_key ) && ! empty( $signature ) ) {
            $computed_signature = base64_encode( hash_hmac( 'sha1', $body, $webhook_signature_key, true ) );
            
            if ( $computed_signature !== $signature ) {
                return new WP_Error(
                    'squarekit_invalid_signature',
                    __( 'Invalid webhook signature.', 'squarekit' ),
                    array( 'status' => 401 )
                );
            }
        }
        
        // Parse webhook data
        $data = json_decode( $body, true );
        
        if ( ! $data ) {
            return new WP_Error(
                'squarekit_invalid_data',
                __( 'Invalid webhook data.', 'squarekit' ),
                array( 'status' => 400 )
            );
        }
        
        // Log webhook
        $this->db->add_log( 'webhook', 'Received webhook', $data );
        
        // Process webhook
        do_action( 'squarekit_process_webhook', $data );
        
        return rest_ensure_response( array(
            'success' => true,
            'message' => __( 'Webhook processed successfully.', 'squarekit' ),
        ) );
    }

    /**
     * Get bulk operations
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_bulk_operations( $request ) {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $bulk_ops = new SquareKit_Bulk_Operations();
        $operations = $bulk_ops->get_recent_operations( 20 );
        
        return new WP_REST_Response( array(
            'success' => true,
            'operations' => $operations,
        ) );
    }

    /**
     * Start bulk operation
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function start_bulk_operation( $request ) {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $params = $request->get_params();
        $operation_type = isset( $params['operation_type'] ) ? sanitize_text_field( $params['operation_type'] ) : '';
        $operation_data = isset( $params['operation_data'] ) ? $params['operation_data'] : array();
        
        if ( empty( $operation_type ) ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Operation type is required.', 'squarekit' ),
            ), 400 );
        }
        
        $bulk_ops = new SquareKit_Bulk_Operations();
        $operation_id = $bulk_ops->start_operation( $operation_type, $operation_data );
        
        if ( is_wp_error( $operation_id ) ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => $operation_id->get_error_message(),
            ), 400 );
        }
        
        // Start processing in background
        wp_schedule_single_event( time(), 'squarekit_process_bulk_operation', array( $operation_id ) );
        
        return new WP_REST_Response( array(
            'success' => true,
            'operation_id' => $operation_id,
            'message' => __( 'Bulk operation started successfully.', 'squarekit' ),
        ) );
    }

    /**
     * Get bulk operation details
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_bulk_operation( $request ) {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $operation_id = (int) $request['id'];
        $bulk_ops = new SquareKit_Bulk_Operations();
        $operation = $bulk_ops->get_operation( $operation_id );
        
        if ( ! $operation ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Operation not found.', 'squarekit' ),
            ), 404 );
        }
        
        return new WP_REST_Response( array(
            'success' => true,
            'operation' => $operation,
        ) );
    }

    /**
     * Update bulk operation
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function update_bulk_operation( $request ) {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $operation_id = (int) $request['id'];
        $params = $request->get_params();
        
        $bulk_ops = new SquareKit_Bulk_Operations();
        
        // Handle different update types
        if ( isset( $params['action'] ) ) {
            switch ( $params['action'] ) {
                case 'cancel':
                    $result = $bulk_ops->cancel_operation( $operation_id );
                    break;
                    
                case 'retry':
                    // Restart processing
                    wp_schedule_single_event( time(), 'squarekit_process_bulk_operation', array( $operation_id ) );
                    $result = true;
                    break;
                    
                default:
                    return new WP_REST_Response( array(
                        'success' => false,
                        'message' => __( 'Invalid action.', 'squarekit' ),
                    ), 400 );
            }
            
            if ( $result ) {
                return new WP_REST_Response( array(
                    'success' => true,
                    'message' => __( 'Operation updated successfully.', 'squarekit' ),
                ) );
            } else {
                return new WP_REST_Response( array(
                    'success' => false,
                    'message' => __( 'Failed to update operation.', 'squarekit' ),
                ), 500 );
            }
        }
        
        return new WP_REST_Response( array(
            'success' => false,
            'message' => __( 'No action specified.', 'squarekit' ),
        ), 400 );
    }

    /**
     * Cancel bulk operation
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function cancel_bulk_operation( $request ) {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $operation_id = (int) $request['id'];
        $bulk_ops = new SquareKit_Bulk_Operations();
        $result = $bulk_ops->cancel_operation( $operation_id );
        
        if ( $result ) {
            return new WP_REST_Response( array(
                'success' => true,
                'message' => __( 'Operation cancelled successfully.', 'squarekit' ),
            ) );
        } else {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Failed to cancel operation.', 'squarekit' ),
            ), 500 );
        }
    }

    /**
     * Get bulk operation progress
     *
     * @since 1.0.0
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function get_bulk_operation_progress( $request ) {
        if ( ! class_exists( 'SquareKit_Bulk_Operations' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-bulk-operations.php';
        }
        
        $operation_id = (int) $request['id'];
        $bulk_ops = new SquareKit_Bulk_Operations();
        $operation = $bulk_ops->get_operation( $operation_id );
        
        if ( ! $operation ) {
            return new WP_REST_Response( array(
                'success' => false,
                'message' => __( 'Operation not found.', 'squarekit' ),
            ), 404 );
        }
        
        return new WP_REST_Response( array(
            'success' => true,
            'progress' => array(
                'status' => $operation->operation_status,
                'total_items' => $operation->total_items,
                'processed_items' => $operation->processed_items,
                'successful_items' => $operation->successful_items,
                'failed_items' => $operation->failed_items,
                'progress_percentage' => $operation->progress_percentage,
                'current_item_id' => $operation->current_item_id,
                'current_item_name' => $operation->current_item_name,
                'started_at' => $operation->started_at,
                'completed_at' => $operation->completed_at,
            ),
        ) );
    }
} 