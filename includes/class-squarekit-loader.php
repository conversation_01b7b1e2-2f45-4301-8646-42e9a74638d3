<?php
/**
 * The file that defines the core plugin loader class
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * The core plugin loader class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since 1.0.0
 */
class SquareKit_Loader {

    /**
     * The array of actions registered with WordPress.
     *
     * @since 1.0.0
     * @access protected
     * @var array $actions The actions registered with WordPress to fire when the plugin loads.
     */
    protected $actions = array();

    /**
     * The array of filters registered with WordPress.
     *
     * @since 1.0.0
     * @access protected
     * @var array $filters The filters registered with WordPress to fire when the plugin loads.
     */
    protected $filters = array();

    /**
     * The admin-specific functionality of the plugin.
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_Admin $plugin_admin Maintains and registers all hooks for the admin area.
     */
    protected $plugin_admin;

    /**
     * Initialize the loader.
     *
     * @since 1.0.0
     */
    public function init() {
        $this->load_dependencies();
        $this->define_admin_hooks();
        $this->define_public_hooks();
        $this->define_api_hooks();
        
        // Register all actions and filters
        $this->run();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * @since 1.0.0
     * @access private
     */
    private function load_dependencies() {
        // Core functionality
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';

        // API classes (load first as dependencies)
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-api.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';

        // Utility classes (load before importers)
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/utils/class-squarekit-price-calculator.php';

        // Import handler classes (load after dependencies)
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-attribute-importer.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-variation-importer.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-modifier-importer.php';

        // SWEVER-based import architecture (load after basic importers)
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-option-resolver.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-import-validator.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-create-product.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-square-import.php';
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';

        // Integration classes
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since 1.0.0
     * @access private
     */
    private function define_admin_hooks() {
        $this->plugin_admin = new SquareKit_Admin();

        $this->add_action( 'admin_enqueue_scripts', $this->plugin_admin, 'enqueue_styles' );
        $this->add_action( 'admin_enqueue_scripts', $this->plugin_admin, 'enqueue_scripts' );
        $this->add_action( 'admin_menu', $this->plugin_admin, 'register_admin_menu' );
        $this->add_action( 'admin_init', $this->plugin_admin, 'maybe_redirect_to_wizard' );

        // Bulk operations background processing
        $this->add_action( 'squarekit_process_bulk_operation', $this->plugin_admin, 'process_bulk_operation_background' );

        // Cleanup old operations
        $this->add_action( 'squarekit_cleanup_old_operations', $this->plugin_admin, 'cleanup_old_operations' );
        
        // Schedule cleanup if not already scheduled
        if ( ! wp_next_scheduled( 'squarekit_cleanup_old_operations' ) ) {
            wp_schedule_event( time(), 'daily', 'squarekit_cleanup_old_operations' );
        }
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since 1.0.0
     * @access private
     */
    private function define_public_hooks() {
        // WooCommerce integration hooks will be registered here
        if ( class_exists( 'SquareKit_WooCommerce' ) ) {
            $woocommerce = new SquareKit_WooCommerce();
            
            // Initialize WooCommerce integration
            $this->add_action( 'woocommerce_init', $woocommerce, 'init' );
        }
        
        // Register cron hooks for scheduled sync
        $this->add_action( 'squarekit_scheduled_sync', $this, 'handle_scheduled_sync' );
        $this->add_action( 'squarekit_sync_products', $this, 'handle_selective_product_sync' );
        $this->add_action( 'squarekit_sync_orders', $this, 'handle_selective_order_sync' );
        $this->add_action( 'squarekit_sync_customers', $this, 'handle_selective_customer_sync' );
        $this->add_action( 'squarekit_sync_inventory', $this, 'handle_selective_inventory_sync' );
        
        // Register webhook endpoint
        $this->add_action( 'rest_api_init', $this, 'register_webhook_endpoint' );
    }

    /**
     * Register all of the hooks related to the API functionality
     * of the plugin.
     *
     * @since 1.0.0
     * @access private
     */
    private function define_api_hooks() {
        if ( class_exists( 'SquareKit_API' ) ) {
            $api = new SquareKit_API();
            
            // Register REST API endpoints
            $this->add_action( 'rest_api_init', $api, 'register_routes' );
        }
    }

    /**
     * Add a new action to the collection to be registered with WordPress.
     *
     * @since 1.0.0
     * @param string $hook          The name of the WordPress action that is being registered.
     * @param object $component     A reference to the instance of the object on which the action is defined.
     * @param string $callback      The name of the function definition on the $component.
     * @param int    $priority      Optional. The priority at which the function should be fired. Default is 10.
     * @param int    $accepted_args Optional. The number of arguments that should be passed to the $callback. Default is 1.
     */
    public function add_action( $hook, $component, $callback, $priority = 10, $accepted_args = 1 ) {
        $this->actions = $this->add( $this->actions, $hook, $component, $callback, $priority, $accepted_args );
    }

    /**
     * Add a new filter to the collection to be registered with WordPress.
     *
     * @since 1.0.0
     * @param string $hook          The name of the WordPress filter that is being registered.
     * @param object $component     A reference to the instance of the object on which the filter is defined.
     * @param string $callback      The name of the function definition on the $component.
     * @param int    $priority      Optional. The priority at which the function should be fired. Default is 10.
     * @param int    $accepted_args Optional. The number of arguments that should be passed to the $callback. Default is 1.
     */
    public function add_filter( $hook, $component, $callback, $priority = 10, $accepted_args = 1 ) {
        $this->filters = $this->add( $this->filters, $hook, $component, $callback, $priority, $accepted_args );
    }

    /**
     * A utility function that is used to register the actions and hooks into a single
     * collection.
     *
     * @since 1.0.0
     * @access private
     * @param array  $hooks         The collection of hooks that is being registered (that is, actions or filters).
     * @param string $hook          The name of the WordPress filter that is being registered.
     * @param object $component     A reference to the instance of the object on which the filter is defined.
     * @param string $callback      The name of the function definition on the $component.
     * @param int    $priority      The priority at which the function should be fired.
     * @param int    $accepted_args The number of arguments that should be passed to the $callback.
     * @return array                The collection of actions and filters registered with WordPress.
     */
    private function add( $hooks, $hook, $component, $callback, $priority, $accepted_args ) {
        $hooks[] = array(
            'hook'          => $hook,
            'component'     => $component,
            'callback'      => $callback,
            'priority'      => $priority,
            'accepted_args' => $accepted_args,
        );

        return $hooks;
    }

    /**
     * Register the filters and actions with WordPress.
     *
     * @since 1.0.0
     */
    public function run() {
        foreach ( $this->filters as $hook ) {
            add_filter( $hook['hook'], array( $hook['component'], $hook['callback'] ), $hook['priority'], $hook['accepted_args'] );
        }

        foreach ( $this->actions as $hook ) {
            add_action( $hook['hook'], array( $hook['component'], $hook['callback'] ), $hook['priority'], $hook['accepted_args'] );
        }
    }

    /**
     * Register cron event on activation
     */
    public function activate_cron() {
        $settings = new SquareKit_Settings();
        $schedule = $settings->get( 'cron_schedule', 'hourly' );
        
        // Clear any existing cron events
        $this->deactivate_cron();
        
        // Schedule the main sync event
        if ( ! wp_next_scheduled( 'squarekit_scheduled_sync' ) ) {
            wp_schedule_event( time(), $schedule, 'squarekit_scheduled_sync' );
        }
        
        // Schedule individual sync events for selective sync
        $this->schedule_selective_sync_events();
        
        $this->log_cron_event( 'info', 'Cron events activated', array(
            'schedule' => $schedule,
            'next_run' => wp_next_scheduled( 'squarekit_scheduled_sync' )
        ) );
    }

    /**
     * Clear cron event on deactivation
     */
    public function deactivate_cron() {
        wp_clear_scheduled_hook( 'squarekit_scheduled_sync' );
        wp_clear_scheduled_hook( 'squarekit_sync_products' );
        wp_clear_scheduled_hook( 'squarekit_sync_orders' );
        wp_clear_scheduled_hook( 'squarekit_sync_customers' );
        wp_clear_scheduled_hook( 'squarekit_sync_inventory' );
        
        $this->log_cron_event( 'info', 'Cron events deactivated' );
    }

    /**
     * Schedule selective sync events based on settings
     */
    private function schedule_selective_sync_events() {
        $settings = new SquareKit_Settings();
        $sync_preferences = $settings->get_sync_preferences();
        
        // Schedule individual sync events if selective sync is enabled
        if ( $settings->get( 'enable_selective_sync', false ) ) {
            $schedule = $settings->get( 'cron_schedule', 'hourly' );
            
            if ( $sync_preferences['products'] ) {
                wp_schedule_event( time(), $schedule, 'squarekit_sync_products' );
            }
            
            if ( $sync_preferences['orders'] ) {
                wp_schedule_event( time(), $schedule, 'squarekit_sync_orders' );
            }
            
            if ( $sync_preferences['customers'] ) {
                wp_schedule_event( time(), $schedule, 'squarekit_sync_customers' );
            }
            
            if ( $settings->get( 'sync_inventory', true ) ) {
                wp_schedule_event( time(), $schedule, 'squarekit_sync_inventory' );
            }
        }
    }

    /**
     * Handle scheduled sync (products, orders, customers)
     */
    public function handle_scheduled_sync() {
        $start_time = microtime( true );
        $settings = new SquareKit_Settings();
        
        // Check if sync is enabled
        if ( ! $settings->get( 'enable_automatic_sync', true ) ) {
            $this->log_cron_event( 'info', 'Automatic sync disabled, skipping scheduled sync' );
            return;
        }
        
        // Check if Square is connected
        if ( ! $settings->is_connected() ) {
            $this->log_cron_event( 'error', 'Square not connected, skipping scheduled sync' );
            return;
        }
        
        $sync_preferences = $settings->get_sync_preferences();
        $sync_results = array();
        
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        $wc = new SquareKit_WooCommerce();
        
        // Import categories first (if products sync is enabled)
        if ( $sync_preferences['products'] ) {
            $sync_results['categories'] = $wc->import_categories_from_square();
        }
        
        // Import products
        if ( $sync_preferences['products'] ) {
            $sync_results['products'] = $wc->import_products_from_square();
        }
        
        // Import inventory
        if ( $settings->get( 'sync_inventory', true ) ) {
            $sync_results['inventory'] = $wc->import_inventory_from_square();
        }
        
        // Import orders
        if ( $sync_preferences['orders'] ) {
            $sync_results['orders'] = $wc->import_orders_from_square();
        }
        
        // Import customers
        if ( $sync_preferences['customers'] ) {
            $sync_results['customers'] = $wc->import_customers_from_square();
        }
        
        $processing_time = microtime( true ) - $start_time;
        
        $this->log_cron_event( 'info', 'Scheduled sync completed', array(
            'processing_time' => round( $processing_time, 3 ),
            'results' => $sync_results
        ) );
    }

    /**
     * Handle selective product sync
     */
    public function handle_selective_product_sync() {
        $settings = new SquareKit_Settings();
        
        if ( ! $settings->get( 'enable_automatic_sync', true ) || ! $settings->is_connected() ) {
            return;
        }
        
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        $wc->import_categories_from_square();
        $wc->import_products_from_square();
        
        $this->log_cron_event( 'info', 'Selective product sync completed' );
    }

    /**
     * Handle selective order sync
     */
    public function handle_selective_order_sync() {
        $settings = new SquareKit_Settings();
        
        if ( ! $settings->get( 'enable_automatic_sync', true ) || ! $settings->is_connected() ) {
            return;
        }
        
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        $wc->import_orders_from_square();
        
        $this->log_cron_event( 'info', 'Selective order sync completed' );
    }

    /**
     * Handle selective customer sync
     */
    public function handle_selective_customer_sync() {
        $settings = new SquareKit_Settings();
        
        if ( ! $settings->get( 'enable_automatic_sync', true ) || ! $settings->is_connected() ) {
            return;
        }
        
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        $wc->import_customers_from_square();
        
        $this->log_cron_event( 'info', 'Selective customer sync completed' );
    }

    /**
     * Handle selective inventory sync
     */
    public function handle_selective_inventory_sync() {
        $settings = new SquareKit_Settings();
        
        if ( ! $settings->get( 'enable_automatic_sync', true ) || ! $settings->is_connected() ) {
            return;
        }
        
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        $wc->import_inventory_from_square();
        
        $this->log_cron_event( 'info', 'Selective inventory sync completed' );
    }

    /**
     * Update cron schedule when settings change
     */
    public function update_cron_schedule( $new_schedule ) {
        $current_schedule = wp_get_schedule( 'squarekit_scheduled_sync' );
        
        if ( $current_schedule !== $new_schedule ) {
            // Clear existing schedule
            wp_clear_scheduled_hook( 'squarekit_scheduled_sync' );
            
            // Set new schedule
            if ( ! wp_next_scheduled( 'squarekit_scheduled_sync' ) ) {
                wp_schedule_event( time(), $new_schedule, 'squarekit_scheduled_sync' );
            }
            
            $this->log_cron_event( 'info', 'Cron schedule updated', array(
                'old_schedule' => $current_schedule,
                'new_schedule' => $new_schedule
            ) );
        }
    }

    /**
     * Get cron status information
     */
    public function get_cron_status() {
        $settings = new SquareKit_Settings();
        $schedule = $settings->get( 'cron_schedule', 'hourly' );
        $next_run = wp_next_scheduled( 'squarekit_scheduled_sync' );
        $last_run = get_option( 'squarekit_last_cron_run', 0 );
        
        return array(
            'enabled' => $settings->get( 'enable_automatic_sync', true ),
            'schedule' => $schedule,
            'next_run' => $next_run,
            'last_run' => $last_run,
            'selective_sync' => $settings->get( 'enable_selective_sync', false ),
            'sync_preferences' => $settings->get_sync_preferences()
        );
    }

    /**
     * Log cron events with detailed information
     *
     * @param string $level Log level (info, warning, error)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    private function log_cron_event( $level, $message, $context = array() ) {
        // Use the SquareKit Logger instead of direct DB access
        if ( ! class_exists('SquareKit_Logger') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $logger = new SquareKit_Logger();
        $logger->log( 'cron', $level, $message, $context );

        // Update last run time for main sync
        if ( $message === 'Scheduled sync completed' ) {
            update_option( 'squarekit_last_cron_run', time() );
        }

        // Also log to WordPress error log for critical errors
        if ( $level === 'error' ) {
            error_log( "Square Kit Cron Error: {$message}" );
        }
    }

    /**
     * Register Square webhooks on activation
     */
    public function register_square_webhooks() {
        if ( ! class_exists('SquareKit_Square_API') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $settings = new SquareKit_Settings();
        $square_api = new SquareKit_Square_API();
        
        // Check if already connected
        if ( ! $settings->is_connected() ) {
            $this->log_webhook_event( 'error', 'Cannot register webhooks: Square not connected' );
            return false;
        }
        
        $webhook_url = get_rest_url( null, 'sws/v1/webhook' );
        $event_types = array(
            'catalog.version.updated',
            'order.created',
            'order.updated',
            'customer.created',
            'customer.updated',
            'inventory.count.updated',
            'payment.created',
            'payment.updated',
            'refund.created',
            'refund.updated',
        );
        
        try {
            $result = $square_api->create_webhook( $webhook_url, $event_types );
            
            if ( is_wp_error( $result ) ) {
                $this->log_webhook_event( 'error', 'Failed to register webhooks: ' . $result->get_error_message() );
                return false;
            }
            
            // Store webhook info
            $settings->set( 'webhook_status', true );
            $settings->set( 'webhook_url', $webhook_url );
            $settings->set( 'webhook_events', $event_types );
            
            if ( isset( $result['id'] ) ) {
                $settings->set( 'webhook_id', $result['id'] );
            }
            
            $this->log_webhook_event( 'info', 'Webhooks registered successfully', array(
                'url' => $webhook_url,
                'events' => $event_types,
                'webhook_id' => isset( $result['id'] ) ? $result['id'] : 'unknown'
            ) );
            
            return true;
            
        } catch ( Exception $e ) {
            $this->log_webhook_event( 'error', 'Exception registering webhooks: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Unregister Square webhooks on deactivation
     */
    public function unregister_square_webhooks() {
        if ( ! class_exists('SquareKit_Square_API') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $settings = new SquareKit_Settings();
        $square_api = new SquareKit_Square_API();
        
        try {
            $result = $square_api->delete_webhooks();
            
            if ( is_wp_error( $result ) ) {
                $this->log_webhook_event( 'error', 'Failed to unregister webhooks: ' . $result->get_error_message() );
                return false;
            }
            
            // Clear webhook settings
            $settings->set( 'webhook_status', false );
            $settings->delete( 'webhook_url' );
            $settings->delete( 'webhook_events' );
            $settings->delete( 'webhook_id' );
            
            $this->log_webhook_event( 'info', 'Webhooks unregistered successfully' );
            
            return true;
            
        } catch ( Exception $e ) {
            $this->log_webhook_event( 'error', 'Exception unregistering webhooks: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Register REST API endpoint for incoming Square webhooks
     */
    public function register_webhook_endpoint() {
        register_rest_route( 'sws/v1', '/webhook', array(
            'methods' => 'POST',
            'callback' => array( $this, 'handle_square_webhook' ),
            'permission_callback' => array( $this, 'verify_webhook_signature' ),
        ) );
    }

    /**
     * Verify webhook signature for security
     *
     * @param WP_REST_Request $request The request object
     * @return bool|WP_Error True if signature is valid, WP_Error otherwise
     */
    public function verify_webhook_signature( $request ) {
        $settings = new SquareKit_Settings();
        $signature_key = $settings->get_webhook_signature_key();
        
        // If no signature key is set, allow the request (for development)
        if ( empty( $signature_key ) ) {
            $this->log_webhook_event( 'warning', 'No webhook signature key configured, allowing request' );
            return true;
        }
        
        $signature = $request->get_header( 'x-square-signature' );
        if ( empty( $signature ) ) {
            $this->log_webhook_event( 'error', 'Missing webhook signature header' );
            return new WP_Error( 'missing_signature', 'Missing webhook signature', array( 'status' => 401 ) );
        }
        
        $body = $request->get_body();
        $expected_signature = hash_hmac( 'sha256', $body, $signature_key );
        
        if ( ! hash_equals( $signature, $expected_signature ) ) {
            $this->log_webhook_event( 'error', 'Invalid webhook signature' );
            return new WP_Error( 'invalid_signature', 'Invalid webhook signature', array( 'status' => 401 ) );
        }
        
        return true;
    }

    /**
     * Handle incoming Square webhook events with enhanced error handling
     */
    public function handle_square_webhook( $request ) {
        $start_time = microtime( true );
        $body = $request->get_body();
        $data = json_decode( $body, true );
        
        // Log incoming webhook
        $this->log_webhook_event( 'info', 'Webhook received', array(
            'type' => isset( $data['type'] ) ? $data['type'] : 'unknown',
            'id' => isset( $data['id'] ) ? $data['id'] : 'unknown',
            'body_size' => strlen( $body )
        ) );
        
        // Validate webhook payload
        if ( empty( $data['type'] ) ) {
            $this->log_webhook_event( 'error', 'Invalid webhook payload: missing type' );
            return new WP_Error( 'invalid_webhook', __( 'Invalid webhook payload.', 'squarekit' ), array( 'status' => 400 ) );
        }
        
        if ( empty( $data['data'] ) || empty( $data['data']['object'] ) ) {
            $this->log_webhook_event( 'error', 'Invalid webhook payload: missing data object' );
            return new WP_Error( 'invalid_webhook', __( 'Invalid webhook payload.', 'squarekit' ), array( 'status' => 400 ) );
        }
        
        // Check if webhook processing is enabled
        $settings = new SquareKit_Settings();
        if ( ! $settings->get_webhook_status() ) {
            $this->log_webhook_event( 'warning', 'Webhook processing disabled, ignoring event' );
            return rest_ensure_response( array( 'success' => true, 'message' => 'Webhook processing disabled' ) );
        }
        
        // Process webhook with retry mechanism
        $max_retries = 3;
        $retry_count = 0;
        $success = false;
        
        while ( $retry_count < $max_retries && ! $success ) {
            try {
                $success = $this->process_webhook_event( $data );
                
                if ( ! $success ) {
                    $retry_count++;
                    if ( $retry_count < $max_retries ) {
                        $this->log_webhook_event( 'warning', "Webhook processing failed, retry {$retry_count}/{$max_retries}" );
                        sleep( 2 ); // Wait 2 seconds before retry
                    }
                }
                
            } catch ( Exception $e ) {
                $retry_count++;
                $this->log_webhook_event( 'error', "Webhook processing exception: {$e->getMessage()}, retry {$retry_count}/{$max_retries}" );
                
                if ( $retry_count < $max_retries ) {
                    sleep( 2 );
                }
            }
        }
        
        $processing_time = microtime( true ) - $start_time;
        
        if ( $success ) {
            $this->log_webhook_event( 'info', 'Webhook processed successfully', array(
                'type' => $data['type'],
                'processing_time' => round( $processing_time, 3 ),
                'retries' => $retry_count
            ) );
            
            return rest_ensure_response( array( 
                'success' => true,
                'processing_time' => round( $processing_time, 3 )
            ) );
        } else {
            $this->log_webhook_event( 'error', 'Webhook processing failed after all retries', array(
                'type' => $data['type'],
                'retries' => $retry_count,
                'processing_time' => round( $processing_time, 3 )
            ) );
            
            return new WP_Error( 'webhook_processing_failed', 'Webhook processing failed', array( 'status' => 500 ) );
        }
    }

    /**
     * Process individual webhook event
     *
     * @param array $data Webhook data
     * @return bool True if successful, false otherwise
     */
    private function process_webhook_event( $data ) {
        if ( ! class_exists('SquareKit_WooCommerce') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc = new SquareKit_WooCommerce();
        $event_type = $data['type'];
        $object_data = $data['data']['object'];
        
        switch ( $event_type ) {
            case 'catalog.version.updated':
                $wc->import_categories_from_square();
                $wc->import_products_from_square();
                break;
                
            case 'order.created':
                $wc->import_orders_from_square();
                break;
                
            case 'order.updated':
                $wc->import_orders_from_square();
                // Update WooCommerce order status if present
                if ( ! empty( $object_data['order']['id'] ) && ! empty( $object_data['order']['state'] ) ) {
                    $wc->update_wc_order_status_from_square(
                        $object_data['order']['id'],
                        $object_data['order']['state']
                    );
                }
                break;
                
            case 'customer.created':
            case 'customer.updated':
                $wc->import_customers_from_square();
                break;
                
            case 'inventory.count.updated':
                $wc->import_inventory_from_square();
                break;
                
            case 'payment.created':
            case 'payment.updated':
                // Handle payment events for subscription renewals
                if ( ! empty( $object_data['payment']['id'] ) ) {
                    $this->handle_payment_webhook( $object_data['payment'] );
                }
                break;
                
            case 'refund.created':
            case 'refund.updated':
                // Handle refund events
                if ( ! empty( $object_data['refund']['id'] ) ) {
                    $this->handle_refund_webhook( $object_data['refund'] );
                }
                break;
                
            default:
                $this->log_webhook_event( 'warning', "Unhandled webhook event type: {$event_type}" );
                return true; // Don't fail for unhandled events
        }
        
        return true;
    }

    /**
     * Handle payment webhook events for subscription renewals
     *
     * @param array $payment_data Payment data from Square
     */
    private function handle_payment_webhook( $payment_data ) {
        if ( ! class_exists( 'WC_Subscriptions' ) ) {
            return;
        }
        
        $payment_id = $payment_data['id'];
        $order_id = $payment_data['order_id'] ?? null;
        
        if ( $order_id ) {
            // Find WooCommerce order by Square order ID
            $wc_order = $this->find_wc_order_by_square_id( $order_id );
            
            if ( $wc_order ) {
                $this->log_webhook_event( 'info', 'Payment webhook processed', array(
                    'payment_id' => $payment_id,
                    'order_id' => $order_id,
                    'wc_order_id' => $wc_order->get_id()
                ) );
            }
        }
    }

    /**
     * Handle refund webhook events
     *
     * @param array $refund_data Refund data from Square
     */
    private function handle_refund_webhook( $refund_data ) {
        $refund_id = $refund_data['id'];
        $payment_id = $refund_data['payment_id'] ?? null;
        
        if ( $payment_id ) {
            // Find WooCommerce order by Square payment ID
            $wc_order = $this->find_wc_order_by_square_payment_id( $payment_id );
            
            if ( $wc_order ) {
                $this->log_webhook_event( 'info', 'Refund webhook processed', array(
                    'refund_id' => $refund_id,
                    'payment_id' => $payment_id,
                    'wc_order_id' => $wc_order->get_id()
                ) );
            }
        }
    }

    /**
     * Find WooCommerce order by Square order ID
     *
     * @param string $square_order_id Square order ID
     * @return WC_Order|null WooCommerce order or null if not found
     */
    private function find_wc_order_by_square_id( $square_order_id ) {
        global $wpdb;
        
        $order_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} 
             WHERE meta_key = '_square_order_id' AND meta_value = %s",
            $square_order_id
        ) );
        
        return $order_id ? wc_get_order( $order_id ) : null;
    }

    /**
     * Find WooCommerce order by Square payment ID
     *
     * @param string $square_payment_id Square payment ID
     * @return WC_Order|null WooCommerce order or null if not found
     */
    private function find_wc_order_by_square_payment_id( $square_payment_id ) {
        global $wpdb;
        
        $order_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} 
             WHERE meta_key = '_square_payment_id' AND meta_value = %s",
            $square_payment_id
        ) );
        
        return $order_id ? wc_get_order( $order_id ) : null;
    }

    /**
     * Log webhook events with detailed information
     *
     * @param string $level Log level (info, warning, error)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    private function log_webhook_event( $level, $message, $context = array() ) {
        // Use the SquareKit Logger instead of direct DB access
        if ( ! class_exists('SquareKit_Logger') ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $logger = new SquareKit_Logger();
        $logger->log( 'webhook', $level, $message, $context );

        // Also log to WordPress error log for critical errors
        if ( $level === 'error' ) {
            error_log( "Square Kit Webhook Error: {$message}" );
        }
    }
} 