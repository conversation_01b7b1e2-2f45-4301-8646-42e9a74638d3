<?php
/**
 * SquareKit Order Sync Module
 *
 * Handles order synchronization between Square and WooCommerce.
 * Includes order import/export, payment handling, and status management.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Order Sync Class
 *
 * Manages order operations between Square and WooCommerce.
 * Handles order synchronization, payment processing, and status updates.
 */
class SquareKit_Order_Sync {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Order processing statistics
     *
     * @var array
     */
    private $order_stats = array(
        'processed' => 0,
        'imported' => 0,
        'exported' => 0,
        'updated' => 0,
        'failed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
        $this->init_hooks();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $this->square_api = new SquareKit_Square_API();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Order sync hooks
        add_action( 'woocommerce_order_status_changed', array( $this, 'sync_order_to_square' ), 10, 4 );
        
        // Order completion hooks for inventory
        add_action( 'woocommerce_order_status_completed', array( $this, 'handle_order_completion' ), 10, 1 );
        add_action( 'woocommerce_order_status_processing', array( $this, 'handle_order_processing' ), 10, 1 );
        add_action( 'woocommerce_order_status_cancelled', array( $this, 'handle_order_cancellation' ), 10, 1 );
        add_action( 'woocommerce_order_status_refunded', array( $this, 'handle_order_refund' ), 10, 1 );
    }

    /**
     * Sync WooCommerce order to Square
     *
     * @param int $order_id Order ID
     * @param string $old_status Old order status
     * @param string $new_status New order status
     * @param WC_Order $order Order object
     * @return bool Success status
     */
    public function sync_order_to_square( $order_id, $old_status, $new_status, $order ) {
        // Only sync if WooCommerce to Square sync is enabled
        if ( ! $this->settings->is_woo_to_square_sync_enabled() ) {
            return false;
        }

        $this->logger->log( 'order_sync', 'info', "Syncing order {$order_id} to Square: {$old_status} → {$new_status}" );

        if ( ! $order instanceof WC_Order ) {
            $order = wc_get_order( $order_id );
        }

        if ( ! $order ) {
            $this->logger->log( 'order_sync', 'error', "Order {$order_id} not found" );
            return false;
        }

        try {
            // Get status mapping
            $status_mapping = $this->settings->get_status_mapping();
            $mapped_status = isset( $status_mapping[ $new_status ] ) ? $status_mapping[ $new_status ] : false;
            $square_order_id = get_post_meta( $order_id, '_square_order_id', true );

            if ( $square_order_id && $mapped_status ) {
                // Update existing order in Square
                $result = $this->update_square_order( $square_order_id, $order, $mapped_status );
            } elseif ( ! $square_order_id ) {
                // Create new order in Square
                $result = $this->create_square_order( $order, $mapped_status );
                if ( $result && ! empty( $result['id'] ) ) {
                    update_post_meta( $order_id, '_square_order_id', $result['id'] );
                }
            } else {
                // No mapping, use default behavior
                $result = $this->update_square_order( $square_order_id, $order );
            }

            if ( is_wp_error( $result ) ) {
                $this->logger->log( 'order_sync', 'error', "Order sync failed: " . $result->get_error_message() );
                $this->update_order_stats( 'failed' );
                return false;
            }

            $this->update_order_stats( 'exported' );
            $this->logger->log( 'order_sync', 'info', "Order {$order_id} successfully synced to Square" );
            return true;

        } catch ( Exception $e ) {
            $this->logger->log( 'order_sync', 'error', "Exception syncing order {$order_id}: " . $e->getMessage() );
            $this->update_order_stats( 'failed' );
            return false;
        }
    }

    /**
     * Create order in Square
     *
     * @param WC_Order $order WooCommerce order
     * @param string $status Square order status
     * @return array|WP_Error Square order data or error
     */
    protected function create_square_order( $order, $status = null ) {
        $this->logger->log( 'order_sync', 'info', "Creating order {$order->get_id()} in Square" );

        $square_order = $this->prepare_square_order_data( $order );
        
        if ( $status ) {
            $square_order['state'] = $status;
        }

        $response = $this->square_api->create_order( $square_order );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $this->logger->log( 'order_sync', 'info', "Order {$order->get_id()} created in Square with ID: " . $response['id'] );
        return $response;
    }

    /**
     * Update order in Square
     *
     * @param string $square_order_id Square order ID
     * @param WC_Order $order WooCommerce order
     * @param string $status Square order status
     * @return array|WP_Error Square order data or error
     */
    protected function update_square_order( $square_order_id, $order, $status = null ) {
        $this->logger->log( 'order_sync', 'info', "Updating Square order {$square_order_id}" );

        $square_order = $this->prepare_square_order_data( $order );
        
        if ( $status ) {
            $square_order['state'] = $status;
        }

        $response = $this->square_api->update_order( $square_order_id, $square_order );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        $this->logger->log( 'order_sync', 'info', "Square order {$square_order_id} updated successfully" );
        return $response;
    }

    /**
     * Prepare Square order data from WooCommerce order
     *
     * @param WC_Order $order WooCommerce order
     * @return array Square order data
     */
    protected function prepare_square_order_data( $order ) {
        $line_items = array();

        foreach ( $order->get_items() as $item ) {
            $product = $item->get_product();
            $square_id = null;

            if ( $product ) {
                $square_id = get_post_meta( $product->get_id(), '_square_item_id', true );
                if ( $product->is_type( 'variation' ) ) {
                    $variation_square_id = get_post_meta( $product->get_id(), '_square_variation_id', true );
                    if ( $variation_square_id ) {
                        $square_id = $variation_square_id;
                    }
                }
            }

            $line_item = array(
                'name' => $item->get_name(),
                'quantity' => (string) $item->get_quantity(),
                'base_price_money' => array(
                    'amount' => intval( $order->get_item_total( $item, false ) * 100 ), // Convert to cents
                    'currency' => $order->get_currency(),
                ),
                'note' => $item->get_description(),
            );

            if ( $square_id ) {
                $line_item['catalog_object_id'] = $square_id;
            }

            if ( $product && $product->is_type( 'variation' ) ) {
                $line_item['variation_name'] = $product->get_name();
            }

            $line_items[] = $line_item;
        }

        // Add shipping as line item if present
        $shipping_total = $order->get_shipping_total();
        if ( $shipping_total > 0 ) {
            $line_items[] = array(
                'name' => __( 'Shipping', 'squarekit' ),
                'quantity' => '1',
                'base_price_money' => array(
                    'amount' => intval( $shipping_total * 100 ),
                    'currency' => $order->get_currency(),
                ),
            );
        }

        // Add taxes as line item if present
        $tax_total = $order->get_total_tax();
        if ( $tax_total > 0 ) {
            $line_items[] = array(
                'name' => __( 'Tax', 'squarekit' ),
                'quantity' => '1',
                'base_price_money' => array(
                    'amount' => intval( $tax_total * 100 ),
                    'currency' => $order->get_currency(),
                ),
            );
        }

        $data = array(
            'location_id' => $this->settings->get( 'location_id' ),
            'line_items' => $line_items,
            'reference_id' => (string) $order->get_id(),
            'state' => 'OPEN',
        );

        // Add customer ID if available
        $square_customer_id = get_post_meta( $order->get_id(), '_square_customer_id', true );
        if ( $square_customer_id ) {
            $data['customer_id'] = $square_customer_id;
        }

        return $data;
    }

    /**
     * Import orders from Square to WooCommerce
     *
     * @param array $options Import options
     * @return array Import results
     */
    public function import_orders_from_square( $options = array() ) {
        // Only import if Square to WooCommerce sync is enabled
        if ( ! $this->settings->is_square_to_woo_sync_enabled() ) {
            return array(
                'success' => false,
                'message' => __( 'Square to WooCommerce sync is disabled.', 'squarekit' )
            );
        }

        $this->logger->log( 'order_sync', 'info', 'Starting order import from Square' );

        $location_id = $this->settings->get( 'location_id' );
        if ( ! $location_id ) {
            return array(
                'success' => false,
                'message' => __( 'No Square location configured.', 'squarekit' )
            );
        }

        try {
            // Prepare search query
            $query = array();

            // Add date filter if specified
            if ( ! empty( $options['start_date'] ) ) {
                $query['filter'] = array(
                    'date_time_filter' => array(
                        'created_at' => array(
                            'start_at' => $options['start_date']
                        )
                    )
                );
            }

            $response = $this->square_api->request( '/orders/search', 'POST', array(
                'location_ids' => array( $location_id ),
                'query' => $query,
            ) );

            if ( is_wp_error( $response ) ) {
                $this->logger->log( 'order_sync', 'error', 'Square API error: ' . $response->get_error_message() );
                return array(
                    'success' => false,
                    'message' => $response->get_error_message()
                );
            }

            $orders = isset( $response['orders'] ) ? $response['orders'] : array();
            $imported = 0;
            $updated = 0;
            $failed = 0;

            foreach ( $orders as $sq_order ) {
                $result = $this->import_single_order_from_square( $sq_order );

                if ( $result === 'imported' ) {
                    $imported++;
                } elseif ( $result === 'updated' ) {
                    $updated++;
                } else {
                    $failed++;
                }

                $this->update_order_stats( 'processed' );
            }

            $this->logger->log( 'order_sync', 'info', "Order import completed: {$imported} imported, {$updated} updated, {$failed} failed" );

            return array(
                'success' => true,
                'imported' => $imported,
                'updated' => $updated,
                'failed' => $failed,
                'message' => sprintf(
                    __( 'Imported %d orders, updated %d orders from Square.', 'squarekit' ),
                    $imported,
                    $updated
                )
            );

        } catch ( Exception $e ) {
            $this->logger->log( 'order_sync', 'error', 'Exception during order import: ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => 'Order import failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Import single order from Square
     *
     * @param array $sq_order Square order data
     * @return string Import result ('imported', 'updated', or 'failed')
     */
    protected function import_single_order_from_square( $sq_order ) {
        $square_order_id = $sq_order['id'];

        try {
            $this->logger->log( 'order_sync', 'info', "Processing Square order: {$square_order_id}" );

            // Check if order already exists
            $existing_order_id = $this->find_wc_order_by_square_id( $square_order_id );

            if ( $existing_order_id ) {
                // Update existing order
                $order = wc_get_order( $existing_order_id );
                if ( ! $order ) {
                    return 'failed';
                }

                $this->update_wc_order_from_square( $order, $sq_order );
                $this->update_order_stats( 'updated' );
                return 'updated';
            } else {
                // Create new order
                $order = $this->create_wc_order_from_square( $sq_order );
                if ( ! $order ) {
                    return 'failed';
                }

                $this->update_order_stats( 'imported' );
                return 'imported';
            }

        } catch ( Exception $e ) {
            $this->logger->log( 'order_sync', 'error', "Failed to import order {$square_order_id}: " . $e->getMessage() );
            $this->update_order_stats( 'failed' );
            return 'failed';
        }
    }

    /**
     * Create WooCommerce order from Square order data
     *
     * @param array $sq_order Square order data
     * @return WC_Order|false WooCommerce order or false on failure
     */
    protected function create_wc_order_from_square( $sq_order ) {
        $square_order_id = $sq_order['id'];

        try {
            $order = wc_create_order();

            if ( ! $order ) {
                return false;
            }

            // Add line items
            $this->add_line_items_to_wc_order( $order, $sq_order );

            // Set customer if available
            if ( ! empty( $sq_order['customer_id'] ) ) {
                $wc_customer_id = $this->find_wc_customer_by_square_id( $sq_order['customer_id'] );
                if ( $wc_customer_id ) {
                    $order->set_customer_id( $wc_customer_id );
                }
                update_post_meta( $order->get_id(), '_square_customer_id', $sq_order['customer_id'] );
            }

            // Store Square order ID
            update_post_meta( $order->get_id(), '_square_order_id', $square_order_id );

            // Set order status based on Square state
            $wc_status = $this->map_square_status_to_wc( $sq_order['state'] ?? 'OPEN' );
            $order->set_status( $wc_status );

            // Apply fulfillment mapping if enabled
            $this->apply_fulfillment_mapping_to_order( $order->get_id(), $sq_order );

            // Update fulfillment status from Square
            $this->update_order_fulfillment_from_square( $order->get_id(), $sq_order );

            // Calculate totals and save
            $order->calculate_totals();
            $order->save();

            $this->logger->log( 'order_sync', 'info', "Created WooCommerce order {$order->get_id()} from Square order {$square_order_id}" );

            return $order;

        } catch ( Exception $e ) {
            $this->logger->log( 'order_sync', 'error', "Failed to create WC order from Square order {$square_order_id}: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Update WooCommerce order from Square order data
     *
     * @param WC_Order $order WooCommerce order
     * @param array $sq_order Square order data
     * @return bool Success status
     */
    protected function update_wc_order_from_square( $order, $sq_order ) {
        try {
            // Update order status
            $wc_status = $this->map_square_status_to_wc( $sq_order['state'] ?? 'OPEN' );
            $order->set_status( $wc_status );

            // Update fulfillment status
            $this->update_order_fulfillment_from_square( $order->get_id(), $sq_order );

            // Save changes
            $order->save();

            $this->logger->log( 'order_sync', 'info', "Updated WooCommerce order {$order->get_id()} from Square" );

            return true;

        } catch ( Exception $e ) {
            $this->logger->log( 'order_sync', 'error', "Failed to update WC order {$order->get_id()}: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Add line items to WooCommerce order from Square order data
     *
     * @param WC_Order $order WooCommerce order
     * @param array $sq_order Square order data
     */
    protected function add_line_items_to_wc_order( $order, $sq_order ) {
        if ( empty( $sq_order['line_items'] ) ) {
            return;
        }

        foreach ( $sq_order['line_items'] as $item ) {
            $product_id = null;

            // Try to find WooCommerce product by Square catalog object ID
            if ( ! empty( $item['catalog_object_id'] ) ) {
                $product_id = $this->find_wc_product_by_square_id( $item['catalog_object_id'] );
            }

            $quantity = intval( $item['quantity'] ?? 1 );

            if ( $product_id ) {
                // Add existing product
                $product = wc_get_product( $product_id );
                $order->add_product( $product, $quantity );
            } else {
                // Add as custom line item
                $item_args = array(
                    'name' => $item['name'] ?? __( 'Unknown Item', 'squarekit' ),
                    'total' => isset( $item['base_price_money']['amount'] ) ? $item['base_price_money']['amount'] / 100 : 0,
                );

                $order->add_product( null, $quantity, $item_args );
            }
        }
    }

    /**
     * Handle order completion
     *
     * @param int $order_id Order ID
     */
    public function handle_order_completion( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'completed' );
    }

    /**
     * Handle order processing
     *
     * @param int $order_id Order ID
     */
    public function handle_order_processing( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'processing' );
    }

    /**
     * Handle order cancellation
     *
     * @param int $order_id Order ID
     */
    public function handle_order_cancellation( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'cancelled' );
    }

    /**
     * Handle order refund
     *
     * @param int $order_id Order ID
     */
    public function handle_order_refund( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'refunded' );
    }

    /**
     * Handle order inventory changes based on status
     *
     * @param int $order_id Order ID
     * @param string $status Order status
     */
    protected function handle_order_inventory_change( $order_id, $status ) {
        if ( ! $this->settings->get( 'order_inventory_sync_enabled', false ) ) {
            return;
        }

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            return;
        }

        $this->logger->log( 'order_sync', 'info', "Handling inventory change for order {$order_id}, status: {$status}" );

        foreach ( $order->get_items() as $item ) {
            $product_id = $item->get_product_id();
            $variation_id = $item->get_variation_id();
            $quantity = $item->get_quantity();

            $target_product_id = $variation_id ? $variation_id : $product_id;

            switch ( $status ) {
                case 'completed':
                case 'processing':
                    // Sync inventory to Square (inventory already reduced by WooCommerce)
                    $this->sync_inventory_to_square( $target_product_id );
                    break;

                case 'cancelled':
                    // Restore inventory
                    $this->restore_inventory_from_cancelled_order( $target_product_id, $quantity );
                    break;

                case 'refunded':
                    // Restore inventory
                    $this->restore_inventory_from_refund( $target_product_id, $quantity );
                    break;
            }
        }
    }

    /**
     * Sync inventory to Square (delegates to inventory sync module)
     *
     * @param int $product_id Product ID
     */
    protected function sync_inventory_to_square( $product_id ) {
        if ( class_exists( 'SquareKit_Inventory_Sync' ) ) {
            $inventory_sync = new SquareKit_Inventory_Sync();
            $inventory_sync->sync_inventory_to_square( $product_id );
        }
    }

    /**
     * Restore inventory from cancelled order
     *
     * @param int $product_id Product ID
     * @param int $quantity Quantity to restore
     */
    protected function restore_inventory_from_cancelled_order( $product_id, $quantity ) {
        $product = wc_get_product( $product_id );
        if ( ! $product || ! $product->managing_stock() ) {
            return;
        }

        $current_stock = $product->get_stock_quantity();
        $new_stock = $current_stock + $quantity;

        $product->set_stock_quantity( $new_stock );
        $product->set_stock_status( 'instock' );
        $product->save();

        // Sync to Square if enabled
        $this->sync_inventory_to_square( $product_id );

        $this->logger->log( 'order_sync', 'info', "Restored inventory for product {$product_id}: +{$quantity} (new stock: {$new_stock})" );
    }

    /**
     * Restore inventory from refund
     *
     * @param int $product_id Product ID
     * @param int $quantity Quantity to restore
     */
    protected function restore_inventory_from_refund( $product_id, $quantity ) {
        $this->restore_inventory_from_cancelled_order( $product_id, $quantity );
    }

    /**
     * Apply fulfillment mapping to order
     *
     * @param int $order_id Order ID
     * @param array $sq_order Square order data
     */
    protected function apply_fulfillment_mapping_to_order( $order_id, $sq_order ) {
        // Check if fulfillment mapping is enabled
        if ( ! $this->settings->get( 'fulfillment_mapping_enabled', false ) ) {
            return;
        }

        // Apply fulfillment logic based on Square order data
        if ( ! empty( $sq_order['fulfillments'] ) ) {
            foreach ( $sq_order['fulfillments'] as $fulfillment ) {
                $this->process_fulfillment_data( $order_id, $fulfillment );
            }
        }
    }

    /**
     * Update order fulfillment from Square
     *
     * @param int $order_id Order ID
     * @param array $sq_order Square order data
     */
    protected function update_order_fulfillment_from_square( $order_id, $sq_order ) {
        // Update fulfillment status based on Square data
        if ( ! empty( $sq_order['fulfillments'] ) ) {
            $fulfillment_status = $this->determine_fulfillment_status( $sq_order['fulfillments'] );
            update_post_meta( $order_id, '_square_fulfillment_status', $fulfillment_status );
        }
    }

    /**
     * Process fulfillment data
     *
     * @param int $order_id Order ID
     * @param array $fulfillment Fulfillment data
     */
    protected function process_fulfillment_data( $order_id, $fulfillment ) {
        $fulfillment_type = $fulfillment['type'] ?? '';
        $fulfillment_state = $fulfillment['state'] ?? '';

        // Store fulfillment information
        $fulfillment_meta = get_post_meta( $order_id, '_square_fulfillments', true );
        if ( ! is_array( $fulfillment_meta ) ) {
            $fulfillment_meta = array();
        }

        $fulfillment_meta[] = array(
            'type' => $fulfillment_type,
            'state' => $fulfillment_state,
            'uid' => $fulfillment['uid'] ?? '',
            'created_at' => $fulfillment['created_at'] ?? '',
        );

        update_post_meta( $order_id, '_square_fulfillments', $fulfillment_meta );
    }

    /**
     * Determine fulfillment status from fulfillments array
     *
     * @param array $fulfillments Fulfillments data
     * @return string Fulfillment status
     */
    protected function determine_fulfillment_status( $fulfillments ) {
        $statuses = array();

        foreach ( $fulfillments as $fulfillment ) {
            $statuses[] = $fulfillment['state'] ?? 'UNKNOWN';
        }

        // Determine overall status
        if ( in_array( 'COMPLETED', $statuses, true ) ) {
            return 'completed';
        } elseif ( in_array( 'PREPARED', $statuses, true ) ) {
            return 'prepared';
        } elseif ( in_array( 'PROPOSED', $statuses, true ) ) {
            return 'proposed';
        } else {
            return 'pending';
        }
    }

    /**
     * Find WooCommerce order by Square ID
     *
     * @param string $square_order_id Square order ID
     * @return int|false Order ID or false if not found
     */
    protected function find_wc_order_by_square_id( $square_order_id ) {
        $orders = get_posts( array(
            'post_type' => 'shop_order',
            'meta_query' => array(
                array(
                    'key' => '_square_order_id',
                    'value' => $square_order_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $orders ) ? $orders[0] : false;
    }

    /**
     * Find WooCommerce product by Square ID
     *
     * @param string $square_id Square catalog object ID
     * @return int|false Product ID or false if not found
     */
    protected function find_wc_product_by_square_id( $square_id ) {
        // Check for regular products
        $products = get_posts( array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_square_item_id',
                    'value' => $square_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        if ( ! empty( $products ) ) {
            return $products[0];
        }

        // Check for variations
        $variations = get_posts( array(
            'post_type' => 'product_variation',
            'meta_query' => array(
                array(
                    'key' => '_square_variation_id',
                    'value' => $square_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $variations ) ? $variations[0] : false;
    }

    /**
     * Find WooCommerce customer by Square ID
     *
     * @param string $square_customer_id Square customer ID
     * @return int|false Customer ID or false if not found
     */
    protected function find_wc_customer_by_square_id( $square_customer_id ) {
        $users = get_users( array(
            'meta_key' => '_square_customer_id',
            'meta_value' => $square_customer_id,
            'number' => 1,
            'fields' => 'ID'
        ) );

        return ! empty( $users ) ? $users[0] : false;
    }

    /**
     * Map Square order status to WooCommerce status
     *
     * @param string $square_status Square order status
     * @return string WooCommerce order status
     */
    protected function map_square_status_to_wc( $square_status ) {
        $status_mapping = array(
            'OPEN' => 'pending',
            'COMPLETED' => 'completed',
            'CANCELED' => 'cancelled',
        );

        return isset( $status_mapping[ $square_status ] ) ? $status_mapping[ $square_status ] : 'pending';
    }

    /**
     * Update order processing statistics
     *
     * @param string $type Statistics type
     */
    protected function update_order_stats( $type ) {
        if ( isset( $this->order_stats[ $type ] ) ) {
            $this->order_stats[ $type ]++;
        }
    }

    /**
     * Get order processing statistics
     *
     * @return array Order statistics
     */
    public function get_order_stats() {
        return $this->order_stats;
    }

    /**
     * Reset order processing statistics
     */
    public function reset_order_stats() {
        $this->order_stats = array(
            'processed' => 0,
            'imported' => 0,
            'exported' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Export order to Square format
     *
     * @param int $order_id WooCommerce order ID
     * @return array|false Square order data or false
     */
    public function export_order_to_square( $order_id ) {
        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            return false;
        }

        $this->logger->log( 'order_sync', 'info', "Exporting order {$order_id} to Square format" );

        return $this->prepare_square_order_data( $order );
    }

    /**
     * Sync order payments to Square
     *
     * @param int $order_id Order ID
     * @return bool Success status
     */
    public function sync_order_payments( $order_id ) {
        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            return false;
        }

        $square_order_id = get_post_meta( $order_id, '_square_order_id', true );
        if ( ! $square_order_id ) {
            return false;
        }

        $this->logger->log( 'order_sync', 'info', "Syncing payments for order {$order_id}" );

        // Get payment information
        $payment_method = $order->get_payment_method();
        $payment_total = $order->get_total();

        if ( $payment_total <= 0 ) {
            return true; // No payment to sync
        }

        // Prepare payment data for Square
        $payment_data = array(
            'amount_money' => array(
                'amount' => intval( $payment_total * 100 ), // Convert to cents
                'currency' => $order->get_currency()
            ),
            'source_id' => $payment_method,
            'order_id' => $square_order_id,
            'reference_id' => (string) $order_id,
        );

        // Send payment to Square
        $response = $this->square_api->create_payment( $payment_data );

        if ( is_wp_error( $response ) ) {
            $this->logger->log( 'order_sync', 'error', "Payment sync failed for order {$order_id}: " . $response->get_error_message() );
            return false;
        }

        // Store payment ID
        if ( ! empty( $response['id'] ) ) {
            update_post_meta( $order_id, '_square_payment_id', $response['id'] );
        }

        $this->logger->log( 'order_sync', 'info', "Payment synced successfully for order {$order_id}" );
        return true;
    }

    /**
     * Handle order status changes
     *
     * @param int $order_id Order ID
     * @param string $old_status Old status
     * @param string $new_status New status
     * @return bool Success status
     */
    public function handle_order_status_changes( $order_id, $old_status, $new_status ) {
        $this->logger->log( 'order_sync', 'info', "Handling status change for order {$order_id}: {$old_status} → {$new_status}" );

        // Handle inventory changes
        $this->handle_order_inventory_change( $order_id, $new_status );

        // Sync payment if order is completed
        if ( $new_status === 'completed' ) {
            $this->sync_order_payments( $order_id );
        }

        return true;
    }

    /**
     * Bulk import orders from Square
     *
     * @param array $square_orders Array of Square order data
     * @return array Import results
     */
    public function bulk_import_orders( $square_orders ) {
        $this->logger->log( 'order_sync', 'info', 'Starting bulk order import for ' . count( $square_orders ) . ' orders' );

        $results = array(
            'total' => count( $square_orders ),
            'imported' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => array()
        );

        foreach ( $square_orders as $sq_order ) {
            try {
                $result = $this->import_single_order_from_square( $sq_order );

                if ( $result === 'imported' ) {
                    $results['imported']++;
                } elseif ( $result === 'updated' ) {
                    $results['updated']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Failed to import order: " . ( $sq_order['id'] ?? 'unknown' );
                }

            } catch ( Exception $e ) {
                $results['failed']++;
                $results['errors'][] = "Exception importing order: " . $e->getMessage();
                $this->logger->log( 'order_sync', 'error', "Exception in bulk import: " . $e->getMessage() );
            }
        }

        $this->logger->log( 'order_sync', 'info', 'Bulk order import completed', $results );

        return $results;
    }

    /**
     * Get order sync status for dashboard
     *
     * @return array Sync status information
     */
    public function get_sync_status() {
        $total_orders = wp_count_posts( 'shop_order' )->publish;

        $synced_orders = get_posts( array(
            'post_type' => 'shop_order',
            'meta_query' => array(
                array(
                    'key' => '_square_order_id',
                    'compare' => 'EXISTS'
                )
            ),
            'posts_per_page' => -1,
            'fields' => 'ids'
        ) );

        return array(
            'total_orders' => $total_orders,
            'synced_orders' => count( $synced_orders ),
            'sync_percentage' => $total_orders > 0 ? round( ( count( $synced_orders ) / $total_orders ) * 100, 1 ) : 0,
            'last_sync' => get_option( 'squarekit_last_order_sync', __( 'Never', 'squarekit' ) )
        );
    }
}
