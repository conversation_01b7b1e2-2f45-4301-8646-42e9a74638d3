<?php
/**
 * SquareKit Category Sync Module
 *
 * Handles category synchronization between Square and WooCommerce.
 * Includes category import/export, hierarchy handling, and category data management.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Category Sync Class
 *
 * Manages category operations between Square and WooCommerce.
 * Handles category synchronization, hierarchy management, and data mapping.
 */
class SquareKit_Category_Sync {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Category processing statistics
     *
     * @var array
     */
    private $category_stats = array(
        'processed' => 0,
        'imported' => 0,
        'exported' => 0,
        'updated' => 0,
        'skipped' => 0,
        'failed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $this->square_api = new SquareKit_Square_API();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Import categories from Square to WooCommerce
     *
     * @param array $options Import options
     * @return array Import results
     */
    public function import_categories_from_square( $options = array() ) {
        // Only import if Square to WooCommerce sync is enabled
        if ( ! $this->settings->is_square_to_woo_sync_enabled() ) {
            return array(
                'success' => false,
                'message' => __( 'Square to WooCommerce sync is disabled.', 'squarekit' )
            );
        }

        $this->logger->log( 'category_sync', 'info', 'Starting category import from Square' );

        $results = array(
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'errors' => array()
        );

        try {
            // Search for all categories in Square
            $categories = $this->square_api->search_catalog_objects( array(
                'object_types' => array( 'CATEGORY' ),
                'query' => array(
                    'exact_query' => array(
                        'attribute_name' => 'category_type',
                        'attribute_value' => 'REGULAR_CATEGORY'
                    )
                )
            ) );

            if ( is_wp_error( $categories ) ) {
                $results['errors'][] = 'Failed to fetch categories from Square: ' . $categories->get_error_message();
                return $results;
            }

            if ( empty( $categories['objects'] ) ) {
                $results['errors'][] = 'No categories found in Square catalog';
                return $results;
            }

            // Sort categories by hierarchy (top-level first)
            $sorted_categories = $this->sort_categories_by_hierarchy( $categories['objects'] );

            // Import each category
            foreach ( $sorted_categories as $category ) {
                $square_id = $category['id'];
                $category_data = $category['category_data'];
                $name = $category_data['name'] ?? '';
                $description = $category_data['description'] ?? '';
                $parent_id = isset( $category_data['parent_category'] ) ? $category_data['parent_category']['id'] : null;

                if ( empty( $name ) ) {
                    $results['skipped']++;
                    $this->update_category_stats( 'skipped' );
                    continue;
                }

                // Check if already exists
                if ( $this->find_wc_category_by_square_id( $square_id ) ) {
                    $results['skipped']++;
                    $this->update_category_stats( 'skipped' );
                    continue;
                }

                $wc_category_id = $this->import_single_category_with_parent(
                    $square_id,
                    $name,
                    $description,
                    $parent_id
                );

                if ( $wc_category_id ) {
                    $results['success']++;
                    $this->update_category_stats( 'imported' );
                } else {
                    $results['failed']++;
                    $results['errors'][] = 'Failed to import category: ' . $name;
                    $this->update_category_stats( 'failed' );
                }

                $this->update_category_stats( 'processed' );
            }

            $this->logger->log( 'category_sync', 'info', "Category import completed: {$results['success']} imported, {$results['failed']} failed, {$results['skipped']} skipped" );

            return array(
                'success' => true,
                'imported' => $results['success'],
                'failed' => $results['failed'],
                'skipped' => $results['skipped'],
                'message' => sprintf(
                    __( 'Imported %d categories, %d failed, %d skipped.', 'squarekit' ),
                    $results['success'],
                    $results['failed'],
                    $results['skipped']
                )
            );

        } catch ( Exception $e ) {
            $this->logger->log( 'category_sync', 'error', 'Exception during category import: ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => 'Category import failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Sort categories by hierarchy (top-level first)
     *
     * @param array $categories Array of Square categories
     * @return array Sorted categories
     */
    protected function sort_categories_by_hierarchy( $categories ) {
        $top_level = array();
        $child_categories = array();

        foreach ( $categories as $category ) {
            $category_data = $category['category_data'];
            
            if ( isset( $category_data['is_top_level'] ) && $category_data['is_top_level'] ) {
                $top_level[] = $category;
            } else {
                $child_categories[] = $category;
            }
        }

        // Return top-level first, then children
        return array_merge( $top_level, $child_categories );
    }

    /**
     * Import single category with parent relationship
     *
     * @param string $square_category_id Square category ID
     * @param string $category_name Category name
     * @param string $category_description Category description
     * @param string|null $parent_square_id Parent Square category ID
     * @return int|false WooCommerce category ID or false on failure
     */
    public function import_single_category_with_parent( $square_category_id, $category_name, $category_description, $parent_square_id = null ) {
        // Check if category already exists
        $existing_wc_id = $this->find_wc_category_by_square_id( $square_category_id );
        if ( $existing_wc_id ) {
            return $existing_wc_id;
        }

        $parent_wc_id = 0;

        // If this category has a parent, make sure the parent exists first
        if ( $parent_square_id ) {
            $parent_wc_id = $this->find_wc_category_by_square_id( $parent_square_id );

            if ( ! $parent_wc_id ) {
                // Parent doesn't exist, create it first
                $parent_hierarchy = $this->get_square_category_hierarchy( $parent_square_id );
                if ( ! empty( $parent_hierarchy ) ) {
                    $parent_data = $parent_hierarchy[0]; // Get the root parent
                    $parent_wc_id = $this->import_single_category_with_parent(
                        $parent_data['id'],
                        $parent_data['name'],
                        $parent_data['description'],
                        $parent_data['parent_id']
                    );
                }
            }
        }

        // Create the category with proper parent relationship
        $term_args = array(
            'description' => $category_description,
        );

        if ( $parent_wc_id ) {
            $term_args['parent'] = $parent_wc_id;
        }

        // Check if category already exists by name first
        $existing_category = get_term_by( 'name', $category_name, 'product_cat' );

        if ( $existing_category ) {
            // Category already exists, use it and link to Square ID
            $wc_category_id = $existing_category->term_id;

            // Update parent if needed and different
            if ( $parent_wc_id && $existing_category->parent != $parent_wc_id ) {
                wp_update_term( $wc_category_id, 'product_cat', array( 'parent' => $parent_wc_id ) );
            }

            $this->logger->log( 'category_sync', 'info', 'Using existing category "' . $category_name . '" (WC ID: ' . $wc_category_id . ') for Square ID: ' . $square_category_id );
        } else {
            // Create new category
            $term_result = wp_insert_term( $category_name, 'product_cat', $term_args );

            if ( is_wp_error( $term_result ) ) {
                // Check if error is due to term already existing (race condition)
                if ( $term_result->get_error_code() === 'term_exists' ) {
                    // Try to get the existing term
                    $existing_category = get_term_by( 'name', $category_name, 'product_cat' );
                    if ( $existing_category ) {
                        $wc_category_id = $existing_category->term_id;
                        $this->logger->log( 'category_sync', 'info', 'Category "' . $category_name . '" already exists, using existing (WC ID: ' . $wc_category_id . ') for Square ID: ' . $square_category_id );
                    } else {
                        $this->logger->log( 'category_sync', 'error', 'Failed to create or find existing WooCommerce category "' . $category_name . '": ' . $term_result->get_error_message() );
                        return false;
                    }
                } else {
                    $this->logger->log( 'category_sync', 'error', 'Failed to create WooCommerce category "' . $category_name . '": ' . $term_result->get_error_message() );
                    return false;
                }
            } else {
                $wc_category_id = $term_result['term_id'];
                $this->logger->log( 'category_sync', 'info', 'Created new category "' . $category_name . '" (WC ID: ' . $wc_category_id . ', Parent: ' . $parent_wc_id . ') for Square ID: ' . $square_category_id );
            }
        }

        // Store Square ID mapping (for both new and existing categories)
        update_term_meta( $wc_category_id, '_square_category_id', $square_category_id );

        return $wc_category_id;
    }

    /**
     * Get Square category hierarchy for a given category ID
     *
     * @param string $square_category_id Square category ID
     * @return array Category hierarchy (parent to child)
     */
    protected function get_square_category_hierarchy( $square_category_id ) {
        $hierarchy = array();
        $current_id = $square_category_id;

        while ( $current_id ) {
            $category_data = $this->square_api->get_catalog_item( $current_id );

            if ( is_wp_error( $category_data ) || empty( $category_data['category_data'] ) ) {
                break;
            }

            $category_info = $category_data['category_data'];
            $hierarchy[] = array(
                'id' => $current_id,
                'name' => $category_info['name'] ?? '',
                'description' => $category_info['description'] ?? '',
                'parent_id' => isset( $category_info['parent_category'] ) ? $category_info['parent_category']['id'] : null
            );

            // Move to parent
            $current_id = isset( $category_info['parent_category'] ) ? $category_info['parent_category']['id'] : null;
        }

        // Reverse to get parent-to-child order
        return array_reverse( $hierarchy );
    }

    /**
     * Import category from Square and assign to product
     *
     * @param string $square_category_id Square category ID
     * @param int $product_id WooCommerce product ID
     * @return int|false WooCommerce category ID or false on failure
     */
    public function import_and_assign_category( $square_category_id, $product_id ) {
        // First check if category already exists
        $wc_category_id = $this->find_wc_category_by_square_id( $square_category_id );

        if ( $wc_category_id ) {
            // Category exists, assign it to product
            wp_set_object_terms( $product_id, $wc_category_id, 'product_cat', false );
            return $wc_category_id;
        }

        // Category doesn't exist, import it from Square
        $category_data = $this->square_api->get_catalog_item( $square_category_id );

        if ( is_wp_error( $category_data ) || empty( $category_data['category_data'] ) ) {
            $this->logger->log( 'category_sync', 'error', 'Failed to fetch category data from Square for ID: ' . $square_category_id );
            return false;
        }

        $category_info = $category_data['category_data'];
        $category_name = $category_info['name'] ?? '';
        $category_description = $category_info['description'] ?? '';

        if ( empty( $category_name ) ) {
            $this->logger->log( 'category_sync', 'error', 'Category name is empty for Square ID: ' . $square_category_id );
            return false;
        }

        // Create new WooCommerce category
        $term_result = wp_insert_term( $category_name, 'product_cat', array(
            'description' => $category_description,
        ) );

        if ( is_wp_error( $term_result ) ) {
            $this->logger->log( 'category_sync', 'error', 'Failed to create WooCommerce category: ' . $term_result->get_error_message() );
            return false;
        }

        $wc_category_id = $term_result['term_id'];

        // Store Square ID mapping
        update_term_meta( $wc_category_id, '_square_category_id', $square_category_id );

        // Assign category to product
        wp_set_object_terms( $product_id, $wc_category_id, 'product_cat', false );

        $this->logger->log( 'category_sync', 'info', 'Created and assigned new category "' . $category_name . '" (ID: ' . $wc_category_id . ') for Square ID: ' . $square_category_id );

        return $wc_category_id;
    }

    /**
     * Handle category hierarchy for product import
     *
     * @param array $category_ids Array of Square category IDs
     * @param int $product_id WooCommerce product ID
     * @return array Array of assigned WooCommerce category IDs
     */
    public function handle_category_hierarchy( $category_ids, $product_id ) {
        if ( empty( $category_ids ) || ! is_array( $category_ids ) ) {
            return array();
        }

        $this->logger->log( 'category_sync', 'info', 'Handling category hierarchy for product ' . $product_id . ': ' . implode( ', ', $category_ids ) );

        // Build complete hierarchy for all categories
        $category_hierarchy = array();
        foreach ( $category_ids as $category_id ) {
            $hierarchy = $this->get_square_category_hierarchy( $category_id );
            foreach ( $hierarchy as $category_data ) {
                $category_hierarchy[ $category_data['id'] ] = $category_data;
            }
        }

        $assigned_category_ids = array();

        // Import and create each category in the hierarchy
        foreach ( $category_hierarchy as $category_data ) {
            $wc_category_id = $this->import_single_category_with_parent(
                $category_data['id'],
                $category_data['name'],
                $category_data['description'],
                $category_data['parent_id']
            );

            if ( $wc_category_id ) {
                $assigned_category_ids[] = $wc_category_id;
            }
        }

        // Assign all categories in the hierarchy to the product
        if ( ! empty( $assigned_category_ids ) ) {
            wp_set_object_terms( $product_id, $assigned_category_ids, 'product_cat', false );
            $this->logger->log( 'category_sync', 'info', 'Assigned category hierarchy to product ' . $product_id . ': ' . implode( ', ', $assigned_category_ids ) );
        }

        return $assigned_category_ids;
    }

    /**
     * Sync WooCommerce category to Square
     *
     * @param int $term_id WooCommerce category term ID
     * @return bool Success status
     */
    public function sync_category_to_square( $term_id ) {
        // Only sync if WooCommerce to Square sync is enabled
        if ( ! $this->settings->is_woo_to_square_sync_enabled() ) {
            return false;
        }

        $term = get_term( $term_id, 'product_cat' );
        if ( ! $term || is_wp_error( $term ) ) {
            $this->logger->log( 'category_sync', 'error', "Category {$term_id} not found" );
            return false;
        }

        $this->logger->log( 'category_sync', 'info', "Syncing category {$term_id} to Square" );

        try {
            // Prepare Square category data
            $category_data = $this->prepare_square_category_data( $term );
            $square_id = get_term_meta( $term_id, '_square_category_id', true );

            if ( $square_id ) {
                // Update existing category in Square
                $response = $this->square_api->update_catalog_object( $square_id, $category_data );
                if ( ! is_wp_error( $response ) ) {
                    $this->update_category_stats( 'updated' );
                    $this->logger->log( 'category_sync', 'info', "Updated Square category {$square_id} for term {$term_id}" );
                }
            } else {
                // Create new category in Square
                $response = $this->square_api->create_catalog_object( $category_data );
                if ( is_array( $response ) && ! empty( $response['id'] ) ) {
                    update_term_meta( $term_id, '_square_category_id', $response['id'] );
                    $this->update_category_stats( 'exported' );
                    $this->logger->log( 'category_sync', 'info', "Created Square category {$response['id']} for term {$term_id}" );
                }
            }

            if ( is_wp_error( $response ) ) {
                $this->logger->log( 'category_sync', 'error', "Category sync failed for term {$term_id}: " . $response->get_error_message() );
                $this->update_category_stats( 'failed' );
                return false;
            }

            $this->update_category_stats( 'processed' );
            return true;

        } catch ( Exception $e ) {
            $this->logger->log( 'category_sync', 'error', "Exception syncing category {$term_id}: " . $e->getMessage() );
            $this->update_category_stats( 'failed' );
            return false;
        }
    }

    /**
     * Prepare Square category data from WP_Term
     *
     * @param WP_Term $term WordPress term
     * @return array Square category data
     */
    protected function prepare_square_category_data( $term ) {
        $data = array(
            'type' => 'CATEGORY',
            'category_data' => array(
                'name' => $term->name,
                'description' => $term->description,
                'category_type' => 'REGULAR_CATEGORY',
                'is_top_level' => $term->parent === 0,
                'online_visibility' => true,
            )
        );

        // Add parent category if exists
        if ( $term->parent > 0 ) {
            $parent_square_id = get_term_meta( $term->parent, '_square_category_id', true );
            if ( $parent_square_id ) {
                $data['category_data']['parent_category'] = array(
                    'id' => $parent_square_id
                );
            }
        }

        return $data;
    }

    /**
     * Find WooCommerce category by Square ID
     *
     * @param string $square_category_id Square category ID
     * @return int|false Term ID or false if not found
     */
    protected function find_wc_category_by_square_id( $square_category_id ) {
        $terms = get_terms( array(
            'taxonomy' => 'product_cat',
            'meta_query' => array(
                array(
                    'key' => '_square_category_id',
                    'value' => $square_category_id,
                    'compare' => '='
                )
            ),
            'hide_empty' => false,
            'number' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $terms ) ? $terms[0] : false;
    }

    /**
     * Create WooCommerce category from Square category data
     *
     * @param array $sq_category Square category data
     * @return int|false Term ID or false on failure
     */
    protected function create_wc_category_from_square( $sq_category ) {
        $square_id = $sq_category['id'];
        $category_data = $sq_category['category_data'];
        $name = $category_data['name'] ?? '';
        $description = $category_data['description'] ?? '';

        if ( empty( $name ) ) {
            return false;
        }

        try {
            // Check if category already exists by name
            $existing_term = get_term_by( 'name', $name, 'product_cat' );
            if ( $existing_term ) {
                // Link existing category to Square
                update_term_meta( $existing_term->term_id, '_square_category_id', $square_id );
                return $existing_term->term_id;
            }

            // Create new category
            $term_result = wp_insert_term( $name, 'product_cat', array(
                'description' => $description,
            ) );

            if ( is_wp_error( $term_result ) ) {
                $this->logger->log( 'category_sync', 'error', 'Failed to create category "' . $name . '": ' . $term_result->get_error_message() );
                return false;
            }

            $term_id = $term_result['term_id'];

            // Store Square ID mapping
            update_term_meta( $term_id, '_square_category_id', $square_id );

            $this->logger->log( 'category_sync', 'info', 'Created category "' . $name . '" (ID: ' . $term_id . ') from Square category ' . $square_id );

            return $term_id;

        } catch ( Exception $e ) {
            $this->logger->log( 'category_sync', 'error', 'Exception creating category from Square: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Export category to Square format
     *
     * @param int $term_id WooCommerce category term ID
     * @return array|false Square category data or false
     */
    public function export_category_to_square( $term_id ) {
        $term = get_term( $term_id, 'product_cat' );
        if ( ! $term || is_wp_error( $term ) ) {
            return false;
        }

        $this->logger->log( 'category_sync', 'info', "Exporting category {$term_id} to Square format" );

        return $this->prepare_square_category_data( $term );
    }

    /**
     * Bulk import categories from Square
     *
     * @param array $square_categories Array of Square category data
     * @return array Import results
     */
    public function bulk_import_categories( $square_categories ) {
        $this->logger->log( 'category_sync', 'info', 'Starting bulk category import for ' . count( $square_categories ) . ' categories' );

        $results = array(
            'total' => count( $square_categories ),
            'imported' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => array()
        );

        // Sort categories by hierarchy first
        $sorted_categories = $this->sort_categories_by_hierarchy( $square_categories );

        foreach ( $sorted_categories as $sq_category ) {
            try {
                $square_id = $sq_category['id'];
                $category_data = $sq_category['category_data'];
                $name = $category_data['name'] ?? '';
                $description = $category_data['description'] ?? '';
                $parent_id = isset( $category_data['parent_category'] ) ? $category_data['parent_category']['id'] : null;

                if ( empty( $name ) ) {
                    $results['failed']++;
                    $results['errors'][] = "Category name is empty for Square ID: {$square_id}";
                    continue;
                }

                $existing_term_id = $this->find_wc_category_by_square_id( $square_id );

                if ( $existing_term_id ) {
                    // Update existing category
                    wp_update_term( $existing_term_id, 'product_cat', array(
                        'name' => $name,
                        'description' => $description,
                    ) );
                    $results['updated']++;
                } else {
                    // Create new category
                    $term_id = $this->import_single_category_with_parent( $square_id, $name, $description, $parent_id );
                    if ( $term_id ) {
                        $results['imported']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][] = "Failed to import category: {$name}";
                    }
                }

            } catch ( Exception $e ) {
                $results['failed']++;
                $results['errors'][] = "Exception importing category: " . $e->getMessage();
                $this->logger->log( 'category_sync', 'error', "Exception in bulk import: " . $e->getMessage() );
            }
        }

        $this->logger->log( 'category_sync', 'info', 'Bulk category import completed', $results );

        return $results;
    }

    /**
     * Update category processing statistics
     *
     * @param string $type Statistics type
     */
    protected function update_category_stats( $type ) {
        if ( isset( $this->category_stats[ $type ] ) ) {
            $this->category_stats[ $type ]++;
        }
    }

    /**
     * Get category processing statistics
     *
     * @return array Category statistics
     */
    public function get_category_stats() {
        return $this->category_stats;
    }

    /**
     * Reset category processing statistics
     */
    public function reset_category_stats() {
        $this->category_stats = array(
            'processed' => 0,
            'imported' => 0,
            'exported' => 0,
            'updated' => 0,
            'skipped' => 0,
            'failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Get category sync status for dashboard
     *
     * @return array Sync status information
     */
    public function get_sync_status() {
        $total_categories = wp_count_terms( 'product_cat', array( 'hide_empty' => false ) );

        $synced_categories = get_terms( array(
            'taxonomy' => 'product_cat',
            'meta_query' => array(
                array(
                    'key' => '_square_category_id',
                    'compare' => 'EXISTS'
                )
            ),
            'hide_empty' => false,
            'fields' => 'ids'
        ) );

        return array(
            'total_categories' => $total_categories,
            'synced_categories' => count( $synced_categories ),
            'sync_percentage' => $total_categories > 0 ? round( ( count( $synced_categories ) / $total_categories ) * 100, 1 ) : 0,
            'last_sync' => get_option( 'squarekit_last_category_sync', __( 'Never', 'squarekit' ) )
        );
    }

    /**
     * Sync categories (general method for API calls)
     *
     * @param array $options Sync options
     * @return array Sync results
     */
    public function sync_categories( $options = array() ) {
        $direction = $options['direction'] ?? 'square_to_wc';

        if ( $direction === 'square_to_wc' ) {
            return $this->import_categories_from_square( $options );
        } else {
            // For WC to Square sync, we would need to get all WC categories and sync them
            $categories = get_terms( array(
                'taxonomy' => 'product_cat',
                'hide_empty' => false
            ) );

            $synced = 0;
            $failed = 0;

            foreach ( $categories as $category ) {
                $result = $this->sync_category_to_square( $category->term_id );
                if ( $result ) {
                    $synced++;
                } else {
                    $failed++;
                }
            }

            return array(
                'success' => true,
                'synced' => $synced,
                'failed' => $failed,
                'message' => sprintf( __( 'Synced %d categories to Square, %d failed.', 'squarekit' ), $synced, $failed )
            );
        }
    }
}
