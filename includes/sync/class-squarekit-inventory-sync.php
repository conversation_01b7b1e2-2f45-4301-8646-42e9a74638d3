<?php
/**
 * SquareKit Inventory Sync Module
 *
 * Handles inventory synchronization between Square and WooCommerce.
 * Includes conflict detection, resolution, and order-based inventory updates.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Inventory Sync Class
 *
 * Manages inventory synchronization between Square and WooCommerce.
 * Handles conflicts, order-based updates, and real-time sync operations.
 */
class SquareKit_Inventory_Sync {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Database instance
     *
     * @var SquareKit_DB
     */
    private $db;

    /**
     * Sync statistics
     *
     * @var array
     */
    private $sync_stats = array(
        'processed' => 0,
        'updated' => 0,
        'conflicts' => 0,
        'failed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
        $this->init_hooks();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }

        $this->square_api = new SquareKit_Square_API();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
        $this->db = new SquareKit_DB();
        $this->conflict_manager = new SquareKit_Conflict_Manager();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Real-time inventory sync hooks
        add_action( 'woocommerce_order_status_completed', array( $this, 'handle_order_completion_inventory' ), 10, 1 );
        add_action( 'woocommerce_order_status_processing', array( $this, 'handle_order_processing_inventory' ), 10, 1 );
        add_action( 'woocommerce_order_status_cancelled', array( $this, 'handle_order_cancellation_inventory' ), 10, 1 );
        add_action( 'woocommerce_order_status_refunded', array( $this, 'handle_order_refund_inventory' ), 10, 1 );
        
        // AJAX handlers
        add_action( 'wp_ajax_squarekit_resolve_inventory_conflict', array( $this, 'ajax_resolve_inventory_conflict' ) );
        add_action( 'wp_ajax_squarekit_sync_inventory_manual', array( $this, 'ajax_sync_inventory_manual' ) );
    }

    /**
     * Import inventory from Square to WooCommerce
     *
     * @param array $options Import options
     * @return array Import results
     */
    public function import_inventory_from_square( $options = array() ) {
        $this->logger->log( 'inventory_sync', 'info', 'Starting inventory import from Square' );

        // Reset sync statistics
        $this->reset_sync_stats();

        $location_id = $this->settings->get( 'location_id' );
        if ( ! $location_id ) {
            $error_message = __( 'No Square location configured.', 'squarekit' );
            $this->logger->log( 'inventory_sync', 'error', $error_message );
            return array( 'success' => false, 'message' => $error_message );
        }

        try {
            // Get products with Square IDs
            $products = $this->get_products_with_square_ids();
            
            if ( empty( $products ) ) {
                $message = __( 'No products with Square IDs found.', 'squarekit' );
                $this->logger->log( 'inventory_sync', 'info', $message );
                return array( 'success' => true, 'updated' => 0, 'message' => $message );
            }

            // Get Square catalog object IDs
            $catalog_object_ids = array();
            foreach ( $products as $product ) {
                $square_id = get_post_meta( $product->ID, '_square_item_id', true );
                if ( $square_id ) {
                    $catalog_object_ids[] = $square_id;
                }
            }

            if ( empty( $catalog_object_ids ) ) {
                $message = __( 'No Square catalog objects found.', 'squarekit' );
                return array( 'success' => false, 'message' => $message );
            }

            // Get inventory from Square
            $response = $this->square_api->get_inventory( $catalog_object_ids, array( $location_id ) );
            if ( is_wp_error( $response ) ) {
                $this->logger->log( 'inventory_sync', 'error', 'Square API error: ' . $response->get_error_message() );
                return array( 'success' => false, 'message' => $response->get_error_message() );
            }

            $inventory_counts = is_array( $response ) ? $response : array();
            
            // Process inventory updates
            foreach ( $inventory_counts as $count ) {
                $result = $this->process_inventory_count( $count, $location_id );
                $this->update_sync_stats( $result );
            }

            $final_stats = $this->get_sync_stats();
            $this->logger->log( 'inventory_sync', 'info', 'Inventory import completed', $final_stats );

            return array(
                'success' => true,
                'updated' => $final_stats['updated'],
                'conflicts' => $final_stats['conflicts'],
                'message' => sprintf( __( 'Updated inventory for %d products from Square.', 'squarekit' ), $final_stats['updated'] )
            );

        } catch ( Exception $e ) {
            $error_message = 'Inventory import failed: ' . $e->getMessage();
            $this->logger->log( 'inventory_sync', 'error', $error_message );
            return array( 'success' => false, 'message' => $error_message );
        }
    }

    /**
     * Sync inventory from WooCommerce to Square
     *
     * @param int $product_id Product ID
     * @param int $quantity_change Quantity change (optional)
     * @return bool Success status
     */
    public function sync_inventory_to_square( $product_id, $quantity_change = 0 ) {
        $this->logger->log( 'inventory_sync', 'info', "Syncing inventory for product {$product_id} to Square" );

        // Only sync if WooCommerce to Square sync is enabled
        if ( ! $this->settings->is_woo_to_square_sync_enabled() ) {
            $this->logger->log( 'inventory_sync', 'info', 'WooCommerce to Square sync is disabled' );
            return false;
        }

        $location_id = $this->settings->get( 'location_id' );
        if ( ! $location_id ) {
            $this->logger->log( 'inventory_sync', 'error', 'No Square location configured' );
            return false;
        }

        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            $this->logger->log( 'inventory_sync', 'error', "Product {$product_id} not found" );
            return false;
        }

        $square_id = get_post_meta( $product_id, '_square_item_id', true );
        if ( ! $square_id ) {
            $this->logger->log( 'inventory_sync', 'info', "Product {$product_id} has no Square ID" );
            return false; // No Square ID, can't sync
        }

        try {
            $current_quantity = $product->get_stock_quantity();
            if ( $current_quantity === null ) {
                $current_quantity = 0;
            }

            // Check for inventory conflicts
            $conflict = $this->check_inventory_conflict( $product_id, $current_quantity );
            if ( $conflict ) {
                $this->logger->log( 'inventory_sync', 'warning', "Inventory conflict detected for product {$product_id}", $conflict );
                
                // Handle conflict based on settings
                $conflict_resolution = $this->settings->get( 'inventory_conflict_resolution', 'manual' );
                if ( $conflict_resolution === 'manual' ) {
                    // Use new conflict manager
                    $this->conflict_manager->detect_conflict(
                        'inventory',
                        $product_id,
                        'product',
                        $conflict['square_quantity'],
                        $conflict['wc_quantity'],
                        $conflict['square_last_modified'] ?? null,
                        $conflict['wc_last_modified'] ?? null
                    );
                    return false;
                } elseif ( $conflict_resolution === 'wc_wins' ) {
                    // Continue with WooCommerce quantity
                } elseif ( $conflict_resolution === 'square_wins' ) {
                    // Update WooCommerce with Square quantity
                    $product->set_stock_quantity( $conflict['square_quantity'] );
                    $product->set_stock_status( $conflict['square_quantity'] > 0 ? 'instock' : 'outofstock' );
                    $product->save();
                    $current_quantity = $conflict['square_quantity'];
                }
            }

            // Prepare inventory change for Square
            $inventory_change = array(
                'type' => 'ADJUSTMENT',
                'adjustment' => array(
                    'catalog_object_id' => $square_id,
                    'location_id' => $location_id,
                    'quantity' => strval( $current_quantity ),
                    'occurred_at' => gmdate( 'c' )
                )
            );

            // Send to Square
            $response = $this->square_api->update_inventory( array( $inventory_change ) );

            if ( ! is_wp_error( $response ) ) {
                // Update local inventory tracking
                $this->update_local_inventory_tracking( $product_id, $current_quantity, $square_id, $location_id );

                $this->log_inventory_event( 'info', 'Inventory synced to Square', array(
                    'product_id' => $product_id,
                    'quantity' => $current_quantity,
                    'square_id' => $square_id
                ) );

                return true;
            } else {
                $this->log_inventory_event( 'error', 'Failed to sync inventory to Square', array(
                    'product_id' => $product_id,
                    'error' => $response->get_error_message()
                ) );

                return false;
            }

        } catch ( Exception $e ) {
            $this->logger->log( 'inventory_sync', 'error', "Exception syncing product {$product_id}: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Check for inventory conflicts between WooCommerce and Square
     *
     * @param int $product_id Product ID
     * @param int $wc_quantity WooCommerce quantity
     * @return array|false Conflict data or false if no conflict
     */
    protected function check_inventory_conflict( $product_id, $wc_quantity ) {
        $square_id = get_post_meta( $product_id, '_square_item_id', true );
        if ( ! $square_id ) {
            return false;
        }

        // Get Square inventory
        $location_id = $this->settings->get( 'location_id' );
        $response = $this->square_api->get_inventory( array( $square_id ), array( $location_id ) );

        if ( is_wp_error( $response ) || empty( $response ) ) {
            return false;
        }

        $square_quantity = 0;
        foreach ( $response as $count ) {
            if ( $count['catalog_object_id'] === $square_id && $count['location_id'] === $location_id ) {
                $square_quantity = isset( $count['state'] ) && $count['state'] === 'IN_STOCK' ? intval( $count['quantity'] ) : 0;
                break;
            }
        }

        // Check if quantities differ significantly
        $difference = abs( $wc_quantity - $square_quantity );
        $threshold = $this->settings->get( 'inventory_conflict_threshold', 1 );

        if ( $difference > $threshold ) {
            return array(
                'wc_quantity' => $wc_quantity,
                'square_quantity' => $square_quantity,
                'difference' => $difference,
                'type' => $wc_quantity > $square_quantity ? 'wc_higher' : 'square_higher'
            );
        }

        return false;
    }

    /**
     * Update local inventory tracking
     *
     * @param int $product_id Product ID
     * @param int $quantity Quantity
     * @param string $square_id Square ID
     * @param string $location_id Location ID
     */
    protected function update_local_inventory_tracking( $product_id, $quantity, $square_id, $location_id ) {
        $this->db->update_inventory( array(
            'square_id' => $square_id,
            'wc_product_id' => $product_id,
            'variation_id' => 0,
            'location_id' => $location_id,
            'quantity' => $quantity,
            'last_sync' => current_time( 'mysql' ),
            'sync_direction' => 'wc_to_square'
        ) );
    }

    /**
     * Create inventory conflict notification
     *
     * @param int $product_id Product ID
     * @param array $conflict Conflict data
     */
    protected function create_inventory_conflict_notification( $product_id, $conflict ) {
        $conflicts = get_option( 'squarekit_inventory_conflicts', array() );

        $conflict_data = array(
            'product_id' => $product_id,
            'wc_quantity' => $conflict['wc_quantity'],
            'square_quantity' => $conflict['square_quantity'],
            'difference' => $conflict['difference'],
            'type' => $conflict['type'],
            'created_at' => current_time( 'mysql' ),
            'status' => 'pending'
        );

        $conflicts[] = $conflict_data;
        update_option( 'squarekit_inventory_conflicts', $conflicts );

        // Send notification if enabled
        if ( $this->settings->get( 'inventory_conflict_notifications', false ) ) {
            $this->send_inventory_conflict_notification( $conflict_data );
        }

        $this->logger->log( 'inventory_sync', 'warning', "Inventory conflict created for product {$product_id}", $conflict_data );
    }

    /**
     * Send inventory conflict notification
     *
     * @param array $conflict Conflict data
     */
    protected function send_inventory_conflict_notification( $conflict ) {
        $product = wc_get_product( $conflict['product_id'] );
        if ( ! $product ) {
            return;
        }

        $admin_email = get_option( 'admin_email' );
        $subject = sprintf( __( 'Inventory Conflict Detected - %s', 'squarekit' ), $product->get_name() );

        $message = sprintf(
            __( "An inventory conflict has been detected for product: %s\n\nWooCommerce Quantity: %d\nSquare Quantity: %d\nDifference: %d\n\nPlease resolve this conflict in your SquareKit dashboard.", 'squarekit' ),
            $product->get_name(),
            $conflict['wc_quantity'],
            $conflict['square_quantity'],
            $conflict['difference']
        );

        wp_mail( $admin_email, $subject, $message );
    }

    /**
     * Handle order completion inventory updates
     *
     * @param int $order_id Order ID
     */
    public function handle_order_completion_inventory( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'completed' );
    }

    /**
     * Handle order processing inventory updates
     *
     * @param int $order_id Order ID
     */
    public function handle_order_processing_inventory( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'processing' );
    }

    /**
     * Handle order cancellation inventory updates
     *
     * @param int $order_id Order ID
     */
    public function handle_order_cancellation_inventory( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'cancelled' );
    }

    /**
     * Handle order refund inventory updates
     *
     * @param int $order_id Order ID
     */
    public function handle_order_refund_inventory( $order_id ) {
        $this->handle_order_inventory_change( $order_id, 'refunded' );
    }

    /**
     * Handle order inventory changes based on status
     *
     * @param int $order_id Order ID
     * @param string $status Order status
     */
    protected function handle_order_inventory_change( $order_id, $status ) {
        if ( ! $this->settings->get( 'order_inventory_sync_enabled', false ) ) {
            return;
        }

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            return;
        }

        $this->logger->log( 'inventory_sync', 'info', "Handling inventory change for order {$order_id}, status: {$status}" );

        foreach ( $order->get_items() as $item ) {
            $product_id = $item->get_product_id();
            $variation_id = $item->get_variation_id();
            $quantity = $item->get_quantity();

            // Use variation ID if available, otherwise use product ID
            $target_product_id = $variation_id ? $variation_id : $product_id;

            switch ( $status ) {
                case 'completed':
                case 'processing':
                    // Reduce inventory (if not already done)
                    if ( ! get_post_meta( $order_id, '_squarekit_inventory_reduced', true ) ) {
                        $this->reduce_inventory_for_order_item( $target_product_id, $quantity );
                        update_post_meta( $order_id, '_squarekit_inventory_reduced', true );
                    }
                    break;

                case 'cancelled':
                case 'refunded':
                    // Restore inventory
                    $this->restore_inventory_from_cancelled_order( $target_product_id, $quantity );
                    delete_post_meta( $order_id, '_squarekit_inventory_reduced' );
                    break;
            }
        }
    }

    /**
     * Reduce inventory for order item
     *
     * @param int $product_id Product ID
     * @param int $quantity Quantity to reduce
     */
    protected function reduce_inventory_for_order_item( $product_id, $quantity ) {
        $product = wc_get_product( $product_id );
        if ( ! $product || ! $product->managing_stock() ) {
            return;
        }

        $current_stock = $product->get_stock_quantity();
        $new_stock = max( 0, $current_stock - $quantity );

        $product->set_stock_quantity( $new_stock );
        $product->set_stock_status( $new_stock > 0 ? 'instock' : 'outofstock' );
        $product->save();

        // Sync to Square if enabled
        if ( $this->settings->is_woo_to_square_sync_enabled() ) {
            $this->sync_inventory_to_square( $product_id );
        }

        $this->log_inventory_event( 'info', 'Inventory reduced for order', array(
            'product_id' => $product_id,
            'quantity_reduced' => $quantity,
            'new_stock' => $new_stock
        ) );
    }

    /**
     * Restore inventory from cancelled order
     *
     * @param int $product_id Product ID
     * @param int $quantity Quantity to restore
     */
    protected function restore_inventory_from_cancelled_order( $product_id, $quantity ) {
        $product = wc_get_product( $product_id );
        if ( ! $product || ! $product->managing_stock() ) {
            return;
        }

        $current_stock = $product->get_stock_quantity();
        $new_stock = $current_stock + $quantity;

        $product->set_stock_quantity( $new_stock );
        $product->set_stock_status( 'instock' );
        $product->save();

        // Sync to Square if enabled
        if ( $this->settings->is_woo_to_square_sync_enabled() ) {
            $this->sync_inventory_to_square( $product_id );
        }

        $this->log_inventory_event( 'info', 'Inventory restored from cancelled order', array(
            'product_id' => $product_id,
            'quantity_restored' => $quantity,
            'new_stock' => $new_stock
        ) );
    }

    /**
     * Restore inventory from refund
     *
     * @param int $product_id Product ID
     * @param int $quantity Quantity to restore
     */
    protected function restore_inventory_from_refund( $product_id, $quantity ) {
        $this->restore_inventory_from_cancelled_order( $product_id, $quantity );
    }

    /**
     * Log inventory event
     *
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Log context
     */
    protected function log_inventory_event( $level, $message, $context = array() ) {
        $this->logger->log( 'inventory_sync', $level, $message, $context );
    }

    /**
     * AJAX handler for resolving inventory conflicts
     */
    public function ajax_resolve_inventory_conflict() {
        check_ajax_referer( 'squarekit_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Insufficient permissions.', 'squarekit' ) ) );
        }

        $conflict_index = intval( $_POST['conflict_index'] ?? 0 );
        $resolution = sanitize_text_field( $_POST['resolution'] ?? '' );

        if ( ! in_array( $resolution, array( 'wc_wins', 'square_wins', 'ignore' ), true ) ) {
            wp_send_json_error( array( 'message' => __( 'Invalid resolution type.', 'squarekit' ) ) );
        }

        $conflicts = get_option( 'squarekit_inventory_conflicts', array() );

        if ( ! isset( $conflicts[ $conflict_index ] ) ) {
            wp_send_json_error( array( 'message' => __( 'Conflict not found.', 'squarekit' ) ) );
        }

        $conflict = $conflicts[ $conflict_index ];
        $product_id = $conflict['product_id'];

        switch ( $resolution ) {
            case 'wc_wins':
                // Use WooCommerce quantity
                $result = $this->sync_inventory_to_square( $product_id );
                if ( $result ) {
                    $message = __( 'Inventory synced to Square using WooCommerce quantity.', 'squarekit' );
                } else {
                    wp_send_json_error( array( 'message' => __( 'Failed to sync inventory to Square.', 'squarekit' ) ) );
                }
                break;

            case 'square_wins':
                // Update WooCommerce with Square quantity
                $product = wc_get_product( $product_id );
                if ( $product ) {
                    $product->set_stock_quantity( $conflict['square_quantity'] );
                    $product->set_stock_status( $conflict['square_quantity'] > 0 ? 'instock' : 'outofstock' );
                    $product->save();
                    $message = __( 'WooCommerce inventory updated with Square quantity.', 'squarekit' );
                } else {
                    wp_send_json_error( array( 'message' => __( 'Product not found.', 'squarekit' ) ) );
                }
                break;

            case 'ignore':
                $message = __( 'Conflict ignored.', 'squarekit' );
                break;
        }

        // Remove resolved conflict
        unset( $conflicts[ $conflict_index ] );
        $conflicts = array_values( $conflicts ); // Re-index array
        update_option( 'squarekit_inventory_conflicts', $conflicts );

        $this->log_inventory_event( 'info', "Inventory conflict resolved: {$resolution}", array(
            'product_id' => $product_id,
            'resolution' => $resolution
        ) );

        wp_send_json_success( array( 'message' => $message ) );
    }

    /**
     * AJAX handler for manual inventory sync
     */
    public function ajax_sync_inventory_manual() {
        check_ajax_referer( 'squarekit_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Insufficient permissions.', 'squarekit' ) ) );
        }

        $product_id = intval( $_POST['product_id'] ?? 0 );
        $direction = sanitize_text_field( $_POST['direction'] ?? '' );

        if ( ! $product_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid product ID.', 'squarekit' ) ) );
        }

        if ( ! in_array( $direction, array( 'wc_to_square', 'square_to_wc' ), true ) ) {
            wp_send_json_error( array( 'message' => __( 'Invalid sync direction.', 'squarekit' ) ) );
        }

        switch ( $direction ) {
            case 'wc_to_square':
                $result = $this->sync_inventory_to_square( $product_id );
                if ( $result ) {
                    wp_send_json_success( array( 'message' => __( 'Inventory synced from WooCommerce to Square.', 'squarekit' ) ) );
                } else {
                    wp_send_json_error( array( 'message' => __( 'Failed to sync inventory to Square.', 'squarekit' ) ) );
                }
                break;

            case 'square_to_wc':
                $result = $this->import_inventory_from_square();
                if ( $result['success'] ) {
                    wp_send_json_success( array( 'message' => __( 'Inventory synced from Square to WooCommerce.', 'squarekit' ) ) );
                } else {
                    wp_send_json_error( array( 'message' => $result['message'] ) );
                }
                break;
        }
    }

    /**
     * Process individual inventory count from Square
     *
     * @param array $count Inventory count data
     * @param string $location_id Location ID
     * @return array|WP_Error Process result
     */
    protected function process_inventory_count( $count, $location_id ) {
        $square_id = $count['catalog_object_id'];
        $quantity = isset( $count['state'] ) && $count['state'] === 'IN_STOCK' ? intval( $count['quantity'] ) : 0;

        try {
            // Find WooCommerce product by Square ID
            $wc_product_id = $this->find_wc_product_by_square_id( $square_id );
            if ( ! $wc_product_id ) {
                return array( 'action' => 'skipped', 'reason' => 'no_matching_product' );
            }

            $product = wc_get_product( $wc_product_id );
            if ( ! $product ) {
                return array( 'action' => 'skipped', 'reason' => 'product_not_found' );
            }

            // Handle variable products
            if ( $product->is_type( 'variable' ) ) {
                return $this->process_variable_product_inventory( $product, $square_id, $quantity, $location_id );
            } else {
                return $this->process_simple_product_inventory( $product, $square_id, $quantity, $location_id );
            }

        } catch ( Exception $e ) {
            return new WP_Error( 'inventory_process_failed', 'Failed to process inventory: ' . $e->getMessage() );
        }
    }

    /**
     * Process inventory for variable product
     *
     * @param WC_Product $product Product object
     * @param string $square_id Square ID
     * @param int $quantity Quantity
     * @param string $location_id Location ID
     * @return array Process result
     */
    protected function process_variable_product_inventory( $product, $square_id, $quantity, $location_id ) {
        // For variable products, check if this is a variation
        $variation_id = $this->find_wc_variation_by_square_id( $square_id );

        if ( $variation_id ) {
            $variation = wc_get_product( $variation_id );
            if ( $variation ) {
                $variation->set_manage_stock( true );
                $variation->set_stock_quantity( $quantity );
                $variation->set_stock_status( $quantity > 0 ? 'instock' : 'outofstock' );
                $variation->save();

                // Update inventory in database
                $this->db->update_inventory( array(
                    'square_id' => $square_id,
                    'wc_product_id' => $product->get_id(),
                    'variation_id' => $variation_id,
                    'location_id' => $location_id,
                    'quantity' => $quantity,
                ) );

                return array( 'action' => 'updated', 'type' => 'variation', 'product_id' => $variation_id );
            }
        } else {
            // This might be the main product, update overall stock
            $product->set_manage_stock( true );
            $product->set_stock_quantity( $quantity );
            $product->set_stock_status( $quantity > 0 ? 'instock' : 'outofstock' );
            $product->save();

            // Update inventory in database
            $this->db->update_inventory( array(
                'square_id' => $square_id,
                'wc_product_id' => $product->get_id(),
                'variation_id' => 0,
                'location_id' => $location_id,
                'quantity' => $quantity,
            ) );

            return array( 'action' => 'updated', 'type' => 'variable_parent', 'product_id' => $product->get_id() );
        }

        return array( 'action' => 'skipped', 'reason' => 'variation_not_found' );
    }

    /**
     * Process inventory for simple product
     *
     * @param WC_Product $product Product object
     * @param string $square_id Square ID
     * @param int $quantity Quantity
     * @param string $location_id Location ID
     * @return array Process result
     */
    protected function process_simple_product_inventory( $product, $square_id, $quantity, $location_id ) {
        $product->set_manage_stock( true );
        $product->set_stock_quantity( $quantity );
        $product->set_stock_status( $quantity > 0 ? 'instock' : 'outofstock' );
        $product->save();

        // Update inventory in database
        $this->db->update_inventory( array(
            'square_id' => $square_id,
            'wc_product_id' => $product->get_id(),
            'variation_id' => 0,
            'location_id' => $location_id,
            'quantity' => $quantity,
        ) );

        return array( 'action' => 'updated', 'type' => 'simple', 'product_id' => $product->get_id() );
    }

    /**
     * Get products with Square IDs
     *
     * @return array Products with Square IDs
     */
    protected function get_products_with_square_ids() {
        return get_posts( array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_square_item_id',
                    'compare' => 'EXISTS'
                )
            ),
            'posts_per_page' => -1
        ) );
    }

    /**
     * Find WooCommerce product by Square ID
     *
     * @param string $square_id Square ID
     * @return int|null Product ID or null if not found
     */
    protected function find_wc_product_by_square_id( $square_id ) {
        $products = get_posts( array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_square_item_id',
                    'value' => $square_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $products ) ? $products[0] : null;
    }

    /**
     * Find WooCommerce variation by Square ID
     *
     * @param string $square_id Square ID
     * @return int|null Variation ID or null if not found
     */
    protected function find_wc_variation_by_square_id( $square_id ) {
        $variations = get_posts( array(
            'post_type' => 'product_variation',
            'meta_query' => array(
                array(
                    'key' => '_square_item_variation_id',
                    'value' => $square_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $variations ) ? $variations[0] : null;
    }

    /**
     * Reset sync statistics
     */
    protected function reset_sync_stats() {
        $this->sync_stats = array(
            'processed' => 0,
            'updated' => 0,
            'conflicts' => 0,
            'failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Update sync statistics
     *
     * @param array|WP_Error $result Sync result
     */
    protected function update_sync_stats( $result ) {
        $this->sync_stats['processed']++;

        if ( is_wp_error( $result ) ) {
            $this->sync_stats['failed']++;
            $this->sync_stats['errors'][] = $result->get_error_message();
        } else {
            if ( $result['action'] === 'updated' ) {
                $this->sync_stats['updated']++;
            }
        }
    }

    /**
     * Get sync statistics
     *
     * @return array Sync statistics
     */
    public function get_sync_stats() {
        return $this->sync_stats;
    }

    /**
     * Get inventory conflicts
     *
     * @return array Inventory conflicts
     */
    public function get_inventory_conflicts() {
        return get_option( 'squarekit_inventory_conflicts', array() );
    }

    /**
     * Clear all inventory conflicts
     *
     * @return bool Success status
     */
    public function clear_inventory_conflicts() {
        return delete_option( 'squarekit_inventory_conflicts' );
    }

    /**
     * Bulk sync inventory for multiple products
     *
     * @param array $product_ids Product IDs
     * @param string $direction Sync direction ('wc_to_square' or 'square_to_wc')
     * @return array Sync results
     */
    public function bulk_sync_inventory( $product_ids, $direction = 'square_to_wc' ) {
        $this->logger->log( 'inventory_sync', 'info', 'Starting bulk inventory sync for ' . count( $product_ids ) . ' products' );

        $results = array(
            'total' => count( $product_ids ),
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'results' => array(),
            'errors' => array()
        );

        if ( $direction === 'square_to_wc' ) {
            // Use the main import method for Square to WC sync
            $import_result = $this->import_inventory_from_square();
            return $import_result;
        }

        // For WC to Square sync, process each product individually
        foreach ( $product_ids as $product_id ) {
            $result = $this->sync_inventory_to_square( $product_id );

            $results['processed']++;

            if ( $result ) {
                $results['successful']++;
            } else {
                $results['failed']++;
                $results['errors'][] = array(
                    'product_id' => $product_id,
                    'error' => 'Sync failed'
                );
            }

            $results['results'][] = array(
                'product_id' => $product_id,
                'result' => $result
            );
        }

        $this->logger->log( 'inventory_sync', 'info', "Bulk inventory sync completed: {$results['successful']} successful, {$results['failed']} failed" );

        return $results;
    }
}
