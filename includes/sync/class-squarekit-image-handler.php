<?php
/**
 * SquareKit Image Handler Module
 *
 * Handles image import, export, optimization, and gallery management.
 * Includes filename generation, image processing, and WordPress media integration.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Image Handler Class
 *
 * Manages image operations between Square and WooCommerce.
 * Handles import, export, optimization, and gallery management.
 */
class SquareKit_Image_Handler {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Image processing statistics
     *
     * @var array
     */
    private $image_stats = array(
        'processed' => 0,
        'imported' => 0,
        'optimized' => 0,
        'failed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $this->square_api = new SquareKit_Square_API();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Import image from URL with enhanced filename handling
     *
     * @param string $url Image URL
     * @param string $product_name Optional product name for filename generation
     * @param int $position Optional image position for multiple images
     * @return int|false Attachment ID or false
     */
    public function import_image_from_url( $url, $product_name = '', $position = 0 ) {
        if ( empty( $url ) ) {
            return false;
        }

        $this->logger->log( 'image_handler', 'info', "Importing image from URL: {$url}" );

        // Handle data URLs differently
        if ( strpos( $url, 'data:' ) === 0 ) {
            return $this->import_image_from_data_url( $url, $product_name, $position );
        }

        // Check if image already exists by URL
        $existing_attachment = $this->get_attachment_by_source_url( $url );
        if ( $existing_attachment ) {
            $this->logger->log( 'image_handler', 'info', "Image already exists: {$existing_attachment}" );
            return $existing_attachment;
        }

        try {
            // Download image with timeout and user agent
            $response = wp_remote_get( $url, array(
                'timeout' => 30,
                'user-agent' => 'SquareKit/1.0 WordPress Image Importer',
                'headers' => array(
                    'Accept' => 'image/*'
                )
            ) );

            if ( is_wp_error( $response ) ) {
                $this->log_image_import_error( $url, 'HTTP request failed: ' . $response->get_error_message() );
                return false;
            }

            $response_code = wp_remote_retrieve_response_code( $response );
            if ( $response_code !== 200 ) {
                $this->log_image_import_error( $url, "HTTP {$response_code} response" );
                return false;
            }

            $image_data = wp_remote_retrieve_body( $response );
            $image_type = wp_remote_retrieve_header( $response, 'content-type' );

            if ( empty( $image_data ) || ! preg_match( '!^image/!', $image_type ) ) {
                $this->log_image_import_error( $url, 'Invalid image data or type' );
                return false;
            }

            // Generate enhanced filename based on product name
            $filename = $this->generate_product_image_filename( $url, $product_name, $position, $image_type );

            // Optimize image if enabled
            $optimized_data = $this->optimize_image( $image_data, $image_type );

            // Upload to WordPress media library
            $upload = wp_upload_bits( $filename, null, $optimized_data );

            if ( $upload['error'] ) {
                $this->log_image_import_error( $url, 'Upload failed: ' . $upload['error'] );
                return false;
            }

            // Create attachment
            $attachment_data = array(
                'post_title' => ! empty( $product_name ) ? $product_name : 'Square Product Image',
                'post_content' => '',
                'post_status' => 'inherit',
                'post_mime_type' => $image_type
            );

            $attachment_id = wp_insert_attachment( $attachment_data, $upload['file'] );

            if ( is_wp_error( $attachment_id ) ) {
                $this->log_image_import_error( $url, 'Failed to create attachment: ' . $attachment_id->get_error_message() );
                return false;
            }

            // Generate attachment metadata
            require_once ABSPATH . 'wp-admin/includes/image.php';
            $attachment_metadata = wp_generate_attachment_metadata( $attachment_id, $upload['file'] );
            wp_update_attachment_metadata( $attachment_id, $attachment_metadata );

            // Store source URL for future reference
            update_post_meta( $attachment_id, '_square_source_url', $url );
            update_post_meta( $attachment_id, '_square_import_date', current_time( 'mysql' ) );

            $this->log_image_import_success( $url, $attachment_id, 0, array(
                'filename' => $filename,
                'product_name' => $product_name,
                'position' => $position
            ) );

            $this->update_image_stats( 'imported' );

            return $attachment_id;

        } catch ( Exception $e ) {
            $this->log_image_import_error( $url, 'Exception: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Import image from data URL (for testing purposes)
     *
     * @param string $data_url Data URL (data:image/...)
     * @param string $product_name Optional product name for filename generation
     * @param int $position Optional image position for multiple images
     * @return int|false Attachment ID or false
     */
    public function import_image_from_data_url( $data_url, $product_name = '', $position = 0 ) {
        if ( empty( $data_url ) || strpos( $data_url, 'data:' ) !== 0 ) {
            return false;
        }

        $this->logger->log( 'image_handler', 'info', 'Importing image from data URL' );

        // Parse data URL
        if ( ! preg_match( '/^data:image\/([a-zA-Z]+);base64,(.+)$/', $data_url, $matches ) ) {
            $this->log_image_import_error( $data_url, 'Invalid data URL format' );
            return false;
        }

        $image_type = $matches[1];
        $image_data = base64_decode( $matches[2] );

        if ( $image_data === false ) {
            $this->log_image_import_error( $data_url, 'Failed to decode base64 data' );
            return false;
        }

        try {
            // Generate filename with product context if available
            if ( ! empty( $product_name ) ) {
                $filename = $this->generate_product_image_filename( 'data:image/' . $image_type, $product_name, $position, 'image/' . $image_type );
            } else {
                $filename = 'test-image-' . uniqid() . '.' . $image_type;
            }

            // Upload to WordPress
            $upload = wp_upload_bits( $filename, null, $image_data );

            if ( $upload['error'] ) {
                $this->log_image_import_error( $data_url, 'Upload failed: ' . $upload['error'] );
                return false;
            }

            // Create attachment
            $attachment_data = array(
                'post_title' => ! empty( $product_name ) ? $product_name : 'Test Image',
                'post_content' => '',
                'post_status' => 'inherit',
                'post_mime_type' => 'image/' . $image_type
            );

            $attachment_id = wp_insert_attachment( $attachment_data, $upload['file'] );

            if ( is_wp_error( $attachment_id ) ) {
                $this->log_image_import_error( $data_url, 'Failed to create attachment: ' . $attachment_id->get_error_message() );
                return false;
            }

            // Generate attachment metadata
            require_once ABSPATH . 'wp-admin/includes/image.php';
            $attachment_metadata = wp_generate_attachment_metadata( $attachment_id, $upload['file'] );
            wp_update_attachment_metadata( $attachment_id, $attachment_metadata );

            // Store metadata
            update_post_meta( $attachment_id, '_square_source_type', 'data_url' );
            update_post_meta( $attachment_id, '_square_import_date', current_time( 'mysql' ) );

            $this->log_image_import_success( 'data_url', $attachment_id, 0, array(
                'filename' => $filename,
                'product_name' => $product_name,
                'type' => 'data_url'
            ) );

            $this->update_image_stats( 'imported' );

            return $attachment_id;

        } catch ( Exception $e ) {
            $this->log_image_import_error( $data_url, 'Exception: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Optimize image before import
     *
     * @param string $image_data Raw image data
     * @param string $image_type MIME type
     * @return string Optimized image data
     */
    public function optimize_image( $image_data, $image_type ) {
        // Check if image optimization is enabled
        $optimize_images = $this->settings->get( 'optimize_images', true );
        if ( ! $optimize_images ) {
            return $image_data;
        }

        $this->logger->log( 'image_handler', 'info', "Optimizing image of type: {$image_type}" );

        try {
            // Get max dimensions from settings
            $max_width = $this->settings->get( 'max_image_width', 1200 );
            $max_height = $this->settings->get( 'max_image_height', 1200 );
            $quality = $this->settings->get( 'image_quality', 85 );

            // Create image resource
            $image = imagecreatefromstring( $image_data );
            if ( ! $image ) {
                $this->logger->log( 'image_handler', 'warning', 'Failed to create image resource from data' );
                return $image_data;
            }

            $width = imagesx( $image );
            $height = imagesy( $image );

            // Check if resizing is needed
            if ( $width <= $max_width && $height <= $max_height ) {
                imagedestroy( $image );
                return $image_data;
            }

            // Calculate new dimensions
            $ratio = min( $max_width / $width, $max_height / $height );
            $new_width = round( $width * $ratio );
            $new_height = round( $height * $ratio );

            // Create new image
            $new_image = imagecreatetruecolor( $new_width, $new_height );

            // Preserve transparency for PNG images
            if ( $image_type === 'image/png' ) {
                imagealphablending( $new_image, false );
                imagesavealpha( $new_image, true );
                $transparent = imagecolorallocatealpha( $new_image, 255, 255, 255, 127 );
                imagefilledrectangle( $new_image, 0, 0, $new_width, $new_height, $transparent );
            }

            // Resize image
            imagecopyresampled( $new_image, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height );

            // Output optimized image
            ob_start();
            if ( $image_type === 'image/jpeg' || $image_type === 'image/jpg' ) {
                imagejpeg( $new_image, null, $quality );
            } elseif ( $image_type === 'image/png' ) {
                imagepng( $new_image, null, round( $quality / 10 ) );
            } elseif ( $image_type === 'image/gif' ) {
                imagegif( $new_image );
            } else {
                // Fallback for unsupported types
                imagejpeg( $new_image, null, $quality );
            }
            $optimized_data = ob_get_clean();

            // Clean up
            imagedestroy( $image );
            imagedestroy( $new_image );

            $this->update_image_stats( 'optimized' );

            $this->logger->log( 'image_handler', 'info', "Image optimized: {$width}x{$height} → {$new_width}x{$new_height}" );

            return $optimized_data;

        } catch ( Exception $e ) {
            $this->logger->log( 'image_handler', 'error', 'Image optimization failed: ' . $e->getMessage() );
            return $image_data;
        }
    }

    /**
     * Generate product image filename based on product name and position
     *
     * @param string $url Original image URL
     * @param string $product_name Product name
     * @param int $position Image position (0 for main image)
     * @param string $image_type MIME type
     * @return string Generated filename
     */
    public function generate_product_image_filename( $url, $product_name = '', $position = 0, $image_type = '' ) {
        // Determine file extension from MIME type or URL
        $extension = $this->get_file_extension_from_type( $image_type );
        if ( empty( $extension ) ) {
            $extension = $this->get_file_extension_from_url( $url );
        }
        if ( empty( $extension ) ) {
            $extension = 'jpg'; // Default fallback
        }

        // If no product name provided, fall back to URL-based naming
        if ( empty( $product_name ) ) {
            $url_filename = basename( parse_url( $url, PHP_URL_PATH ) );
            if ( ! empty( $url_filename ) && preg_match( '/\.(jpg|jpeg|png|gif|webp)$/i', $url_filename ) ) {
                return $url_filename;
            }
            return 'square-image-' . uniqid() . '.' . $extension;
        }

        // Sanitize product name for filename
        $base_name = $this->sanitize_filename( $product_name );

        // Handle multiple images with position suffix
        if ( $position > 0 ) {
            $filename = $base_name . '-' . $position . '.' . $extension;
        } else {
            $filename = $base_name . '.' . $extension;
        }

        // Ensure filename is unique to prevent conflicts
        $filename = $this->ensure_unique_filename( $filename );

        return $filename;
    }

    /**
     * Import product gallery from array of image URLs
     *
     * @param array $image_urls Array of image URLs
     * @param int $product_id Product ID
     * @return array Import results
     */
    public function import_product_gallery( $image_urls, $product_id ) {
        if ( empty( $image_urls ) || ! is_array( $image_urls ) ) {
            return array(
                'success' => false,
                'message' => __( 'No image URLs provided.', 'squarekit' ),
                'imported_count' => 0
            );
        }

        $this->logger->log( 'image_handler', 'info', "Importing gallery for product {$product_id}: " . count( $image_urls ) . ' images' );

        $attachment_ids = array();

        // Get product name for filename generation
        $product = wc_get_product( $product_id );
        $product_name = $product ? $product->get_name() : '';

        foreach ( $image_urls as $index => $url ) {
            // Import with product name and position for proper naming
            $attachment_id = $this->import_image_from_url( $url, $product_name, $index );
            if ( $attachment_id ) {
                $attachment_ids[] = $attachment_id;

                // Set first image as main product image
                if ( $index === 0 ) {
                    if ( $product ) {
                        $product->set_image_id( $attachment_id );
                        $product->save();
                    }
                }
            }
        }

        // Set gallery images (excluding the first one which is the main image)
        if ( count( $attachment_ids ) > 1 ) {
            $gallery_ids = array_slice( $attachment_ids, 1 );
            update_post_meta( $product_id, '_product_image_gallery', implode( ',', $gallery_ids ) );
        }

        $this->logger->log( 'image_handler', 'info', "Gallery import completed for product {$product_id}: " . count( $attachment_ids ) . ' images imported' );

        return array(
            'success' => true,
            'imported_count' => count( $attachment_ids ),
            'attachment_ids' => $attachment_ids,
            'message' => sprintf( __( 'Imported %d images for product gallery.', 'squarekit' ), count( $attachment_ids ) )
        );
    }

    /**
     * Import product gallery with enhanced error handling and performance
     *
     * @param array $image_urls Array of image URLs
     * @param int $product_id Product ID
     * @return array Enhanced import results
     */
    public function import_product_gallery_enhanced( $image_urls, $product_id ) {
        if ( empty( $image_urls ) || ! is_array( $image_urls ) ) {
            return array(
                'success' => false,
                'message' => __( 'No image URLs provided.', 'squarekit' ),
                'imported_count' => 0,
                'failed_count' => 0,
                'skipped_count' => 0
            );
        }

        $this->logger->log( 'image_handler', 'info', "Starting enhanced gallery import for product {$product_id}" );

        // Remove duplicates and invalid URLs
        $unique_urls = array_unique( array_filter( $image_urls, array( $this, 'is_valid_image_url' ) ) );
        $skipped_urls = array_diff( $image_urls, $unique_urls );
        $invalid_urls = array_filter( $image_urls, function( $url ) {
            return ! $this->is_valid_image_url( $url );
        } );

        $attachment_ids = array();
        $failed_urls = array();

        // Get product name for filename generation
        $product = wc_get_product( $product_id );
        $product_name = $product ? $product->get_name() : '';

        foreach ( $unique_urls as $index => $url ) {
            // Import with product name and position for proper naming
            $attachment_id = $this->import_image_from_url( $url, $product_name, $index );
            if ( $attachment_id ) {
                $attachment_ids[] = $attachment_id;

                // Log success with product context
                $this->log_image_import_success( $url, $attachment_id, $product_id, array(
                    'import_method' => 'gallery_enhanced',
                    'position' => $index,
                    'total_images' => count( $unique_urls )
                ) );

                // Set first image as main product image
                if ( $index === 0 && count( $attachment_ids ) === 1 ) {
                    if ( $product ) {
                        $product->set_image_id( $attachment_id );
                        $product->save();
                        $this->logger->log( 'image_handler', 'info', "Set main product image for product {$product_id}" );
                    }
                }
            } else {
                $failed_urls[] = $url;
                // Error already logged in import_image_from_url method
            }
        }

        // Set gallery images
        if ( ! empty( $attachment_ids ) ) {
            $gallery_ids = array_slice( $attachment_ids, 1 ); // Skip first image as it's the main image
            if ( ! empty( $gallery_ids ) ) {
                update_post_meta( $product_id, '_product_image_gallery', implode( ',', $gallery_ids ) );
                $this->logger->log( 'image_handler', 'info', "Set gallery images for product {$product_id}: " . implode( ',', $gallery_ids ) );
            }
        }

        $total_requested = count( $image_urls );
        $total_processed = count( $unique_urls );
        $total_imported = count( $attachment_ids );
        $total_failed = count( $failed_urls );
        $total_skipped = count( $skipped_urls );
        $total_invalid = count( $invalid_urls );

        $this->logger->log( 'image_handler', 'info', "Enhanced gallery import completed for product {$product_id}", array(
            'requested' => $total_requested,
            'processed' => $total_processed,
            'imported' => $total_imported,
            'failed' => $total_failed,
            'skipped' => $total_skipped,
            'invalid' => $total_invalid
        ) );

        return array(
            'success' => $total_imported > 0,
            'imported_count' => $total_imported,
            'failed_count' => $total_failed,
            'skipped_count' => $total_skipped,
            'invalid_count' => $total_invalid,
            'attachment_ids' => $attachment_ids,
            'failed_urls' => $failed_urls,
            'message' => sprintf(
                __( 'Gallery import completed: %d imported, %d failed, %d skipped.', 'squarekit' ),
                $total_imported,
                $total_failed,
                $total_skipped
            )
        );
    }

    /**
     * Export product images to Square
     *
     * @param int $product_id Product ID
     * @return array Array of image URLs
     */
    public function export_product_images( $product_id ) {
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return array();
        }

        $this->logger->log( 'image_handler', 'info', "Exporting images for product {$product_id}" );

        $image_urls = array();

        // Get main product image
        $image_id = $product->get_image_id();
        if ( $image_id ) {
            $image_url = wp_get_attachment_url( $image_id );
            if ( $image_url ) {
                $image_urls[] = $image_url;
            }
        }

        // Get gallery images
        $gallery_ids = $product->get_gallery_image_ids();
        foreach ( $gallery_ids as $gallery_id ) {
            $gallery_url = wp_get_attachment_url( $gallery_id );
            if ( $gallery_url ) {
                $image_urls[] = $gallery_url;
            }
        }

        $this->logger->log( 'image_handler', 'info', "Exported " . count( $image_urls ) . " images for product {$product_id}" );

        return $image_urls;
    }

    /**
     * Import product images from Square with enhanced error handling
     *
     * @param array $image_ids Square image IDs
     * @param int $product_id WooCommerce product ID
     * @param array $image_map Pre-built image ID to URL mapping
     * @return array Import results with success/failure details
     */
    public function import_product_images_enhanced( $image_ids, $product_id, $image_map = array() ) {
        if ( empty( $image_ids ) || ! is_array( $image_ids ) ) {
            return array(
                'success' => false,
                'message' => __( 'No image IDs provided.', 'squarekit' ),
                'imported_count' => 0,
                'failed_count' => 0
            );
        }

        $this->logger->log( 'image_handler', 'info', "Starting enhanced image import for product {$product_id}" );

        $image_urls = array();
        $failed_images = array();

        // Use pre-built image map if available, otherwise fetch from API
        if ( ! empty( $image_map ) ) {
            foreach ( $image_ids as $image_id ) {
                if ( isset( $image_map[ $image_id ] ) ) {
                    $image_urls[] = $image_map[ $image_id ];
                } else {
                    $failed_images[] = $image_id;
                    $this->logger->log( 'image_handler', 'error', "Image ID {$image_id} not found in image map for product {$product_id}" );
                }
            }
        } else {
            // Fallback to API calls for missing images
            $image_url_map = $this->square_api->get_image_urls_by_ids( $image_ids );
            foreach ( $image_ids as $image_id ) {
                if ( isset( $image_url_map[ $image_id ] ) ) {
                    $image_urls[] = $image_url_map[ $image_id ];
                } else {
                    $failed_images[] = $image_id;
                    $this->logger->log( 'image_handler', 'error', "Failed to retrieve image URL for ID {$image_id} for product {$product_id}" );
                }
            }
        }

        if ( empty( $image_urls ) ) {
            return array(
                'success' => false,
                'message' => __( 'No valid image URLs found.', 'squarekit' ),
                'imported_count' => 0,
                'failed_count' => count( $failed_images )
            );
        }

        // Import the gallery
        $gallery_result = $this->import_product_gallery_enhanced( $image_urls, $product_id );

        return array(
            'success' => $gallery_result['success'],
            'message' => $gallery_result['message'],
            'imported_count' => $gallery_result['imported_count'],
            'failed_count' => $gallery_result['failed_count'] + count( $failed_images ),
            'attachment_ids' => $gallery_result['attachment_ids'] ?? array(),
            'failed_image_ids' => $failed_images
        );
    }

    /**
     * Get attachment by source URL
     *
     * @param string $url Source URL
     * @return int|false Attachment ID or false if not found
     */
    protected function get_attachment_by_source_url( $url ) {
        $attachments = get_posts( array(
            'post_type' => 'attachment',
            'meta_query' => array(
                array(
                    'key' => '_square_source_url',
                    'value' => $url,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $attachments ) ? $attachments[0] : false;
    }

    /**
     * Get file extension from MIME type
     *
     * @param string $mime_type MIME type
     * @return string File extension
     */
    protected function get_file_extension_from_type( $mime_type ) {
        $mime_to_ext = array(
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/bmp' => 'bmp',
            'image/tiff' => 'tiff'
        );

        return isset( $mime_to_ext[ $mime_type ] ) ? $mime_to_ext[ $mime_type ] : '';
    }

    /**
     * Get file extension from URL
     *
     * @param string $url Image URL
     * @return string File extension
     */
    protected function get_file_extension_from_url( $url ) {
        $path = parse_url( $url, PHP_URL_PATH );
        $extension = pathinfo( $path, PATHINFO_EXTENSION );
        return strtolower( $extension );
    }

    /**
     * Sanitize filename for WordPress
     *
     * @param string $filename Original filename
     * @return string Sanitized filename
     */
    protected function sanitize_filename( $filename ) {
        // Remove special characters and spaces
        $filename = preg_replace( '/[^a-zA-Z0-9\-_]/', '-', $filename );

        // Remove multiple consecutive dashes
        $filename = preg_replace( '/-+/', '-', $filename );

        // Remove leading/trailing dashes
        $filename = trim( $filename, '-' );

        // Ensure it's not empty
        if ( empty( $filename ) ) {
            $filename = 'square-product';
        }

        // Limit length
        if ( strlen( $filename ) > 50 ) {
            $filename = substr( $filename, 0, 50 );
            $filename = rtrim( $filename, '-' );
        }

        return $filename;
    }

    /**
     * Ensure filename is unique
     *
     * @param string $filename Desired filename
     * @return string Unique filename
     */
    protected function ensure_unique_filename( $filename ) {
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['path'] . '/' . $filename;

        if ( ! file_exists( $file_path ) ) {
            return $filename;
        }

        // File exists, add number suffix
        $pathinfo = pathinfo( $filename );
        $basename = $pathinfo['filename'];
        $extension = isset( $pathinfo['extension'] ) ? '.' . $pathinfo['extension'] : '';

        $counter = 1;
        do {
            $new_filename = $basename . '-' . $counter . $extension;
            $new_file_path = $upload_dir['path'] . '/' . $new_filename;
            $counter++;
        } while ( file_exists( $new_file_path ) && $counter < 100 );

        return $new_filename;
    }

    /**
     * Validate image URL
     *
     * @param string $url Image URL
     * @return bool True if valid
     */
    protected function is_valid_image_url( $url ) {
        if ( empty( $url ) ) {
            return false;
        }

        // Check for data URLs
        if ( strpos( $url, 'data:image/' ) === 0 ) {
            return true;
        }

        // Check for valid HTTP/HTTPS URLs
        if ( ! filter_var( $url, FILTER_VALIDATE_URL ) ) {
            return false;
        }

        // Check for image file extensions
        $extension = $this->get_file_extension_from_url( $url );
        $valid_extensions = array( 'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp' );

        return in_array( $extension, $valid_extensions, true );
    }

    /**
     * Log image import error
     *
     * @param string $url Image URL
     * @param string $error Error message
     */
    protected function log_image_import_error( $url, $error ) {
        $this->logger->log( 'image_handler', 'error', "Image import failed for {$url}: {$error}" );
        $this->update_image_stats( 'failed' );
    }

    /**
     * Log image import success
     *
     * @param string $url Image URL
     * @param int $attachment_id Attachment ID
     * @param int $product_id Product ID
     * @param array $context Additional context
     */
    protected function log_image_import_success( $url, $attachment_id, $product_id = 0, $context = array() ) {
        $message = "Image imported successfully: {$url} → attachment {$attachment_id}";
        if ( $product_id ) {
            $message .= " for product {$product_id}";
        }

        $this->logger->log( 'image_handler', 'info', $message, $context );
    }

    /**
     * Update image processing statistics
     *
     * @param string $type Statistics type
     */
    protected function update_image_stats( $type ) {
        $this->image_stats['processed']++;

        if ( isset( $this->image_stats[ $type ] ) ) {
            $this->image_stats[ $type ]++;
        }
    }

    /**
     * Get image processing statistics
     *
     * @return array Image statistics
     */
    public function get_image_stats() {
        return $this->image_stats;
    }

    /**
     * Reset image processing statistics
     */
    public function reset_image_stats() {
        $this->image_stats = array(
            'processed' => 0,
            'imported' => 0,
            'optimized' => 0,
            'failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Debug image import for troubleshooting
     *
     * @param string $url Image URL to debug
     * @return array Debug information
     */
    public function debug_image_import( $url ) {
        $debug_info = array(
            'url' => $url,
            'checks' => array(),
            'response_info' => array(),
            'recommendations' => array()
        );

        // Check URL validity
        $debug_info['checks']['url_valid'] = filter_var( $url, FILTER_VALIDATE_URL ) !== false;
        if ( ! $debug_info['checks']['url_valid'] ) {
            $debug_info['recommendations'][] = 'URL format is invalid';
            return $debug_info;
        }

        // Check if it's a data URL
        $debug_info['checks']['is_data_url'] = strpos( $url, 'data:' ) === 0;

        if ( ! $debug_info['checks']['is_data_url'] ) {
            // Test HTTP accessibility
            $response = wp_remote_head( $url, array( 'timeout' => 10 ) );

            if ( is_wp_error( $response ) ) {
                $debug_info['checks']['http_accessible'] = false;
                $debug_info['http_error'] = $response->get_error_message();
                $debug_info['recommendations'][] = 'URL is not accessible: ' . $response->get_error_message();
            } else {
                $debug_info['checks']['http_accessible'] = true;
                $debug_info['response_info']['status_code'] = wp_remote_retrieve_response_code( $response );
                $debug_info['response_info']['content_type'] = wp_remote_retrieve_header( $response, 'content-type' );
                $debug_info['response_info']['content_length'] = wp_remote_retrieve_header( $response, 'content-length' );

                // Check content type
                $content_type = $debug_info['response_info']['content_type'];
                $debug_info['checks']['is_image'] = strpos( $content_type, 'image/' ) === 0;

                if ( ! $debug_info['checks']['is_image'] ) {
                    $debug_info['recommendations'][] = "Content type '{$content_type}' is not an image";
                }
            }
        }

        // Check file extension
        $extension = $this->get_file_extension_from_url( $url );
        $debug_info['checks']['valid_extension'] = in_array( $extension, array( 'jpg', 'jpeg', 'png', 'gif', 'webp' ), true );

        if ( ! $debug_info['checks']['valid_extension'] ) {
            $debug_info['recommendations'][] = "File extension '{$extension}' may not be supported";
        }

        return $debug_info;
    }

    /**
     * Get image import statistics for a time period
     *
     * @param int $days Number of days to look back
     * @return array Import statistics
     */
    public function get_image_import_stats( $days = 7 ) {
        $since_date = date( 'Y-m-d H:i:s', strtotime( "-{$days} days" ) );

        $total_imports = get_posts( array(
            'post_type' => 'attachment',
            'meta_query' => array(
                array(
                    'key' => '_square_import_date',
                    'value' => $since_date,
                    'compare' => '>='
                )
            ),
            'posts_per_page' => -1,
            'fields' => 'ids'
        ) );

        return array(
            'period_days' => $days,
            'total_imports' => count( $total_imports ),
            'average_per_day' => round( count( $total_imports ) / max( 1, $days ), 2 ),
            'since_date' => $since_date
        );
    }
}
