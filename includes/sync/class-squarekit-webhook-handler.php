<?php
/**
 * SquareKit Webhook Handler Module
 *
 * Handles webhook processing and event management from Square.
 * Includes webhook validation, signature verification, and event routing.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Webhook Handler Class
 *
 * Manages webhook operations and event processing from Square.
 * Handles webhook validation, signature verification, and event routing.
 */
class SquareKit_Webhook_Handler {

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Database instance
     *
     * @var SquareKit_DB
     */
    private $db;

    /**
     * Webhook processing statistics
     *
     * @var array
     */
    private $webhook_stats = array(
        'processed' => 0,
        'successful' => 0,
        'failed' => 0,
        'retries' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
        $this->init_hooks();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }

        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
        $this->db = new SquareKit_DB();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Register webhook endpoint
        add_action( 'rest_api_init', array( $this, 'register_webhook_endpoint' ) );
        
        // Process webhook events
        add_action( 'squarekit_process_webhook', array( $this, 'process_webhook_event' ), 10, 1 );
    }

    /**
     * Register REST API endpoint for incoming Square webhooks
     */
    public function register_webhook_endpoint() {
        register_rest_route( 'squarekit/v1', '/webhook', array(
            'methods' => 'POST',
            'callback' => array( $this, 'handle_square_webhook' ),
            'permission_callback' => array( $this, 'verify_webhook_signature' ),
        ) );
    }

    /**
     * Verify webhook signature
     *
     * @param WP_REST_Request $request Request object
     * @return bool True if signature is valid
     */
    public function verify_webhook_signature( $request ) {
        $signature = $request->get_header( 'X-Square-Signature' );
        $body = $request->get_body();
        
        // Get webhook signature key from settings
        $webhook_signature_key = $this->settings->get( 'webhook_signature_key' );
        
        if ( empty( $webhook_signature_key ) ) {
            // If no signature key is configured, allow the request
            $this->log_webhook_event( 'warning', 'No webhook signature key configured' );
            return true;
        }
        
        if ( empty( $signature ) ) {
            $this->log_webhook_event( 'error', 'Missing webhook signature' );
            return false;
        }
        
        // Compute expected signature
        $computed_signature = base64_encode( hash_hmac( 'sha1', $body, $webhook_signature_key, true ) );
        
        if ( $computed_signature !== $signature ) {
            $this->log_webhook_event( 'error', 'Invalid webhook signature', array(
                'expected' => $computed_signature,
                'received' => $signature
            ) );
            return false;
        }
        
        return true;
    }

    /**
     * Handle incoming Square webhook events with enhanced error handling
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     */
    public function handle_square_webhook( $request ) {
        $start_time = microtime( true );
        $body = $request->get_body();
        $data = json_decode( $body, true );
        
        // Log incoming webhook
        $this->log_webhook_event( 'info', 'Webhook received', array(
            'type' => isset( $data['type'] ) ? $data['type'] : 'unknown',
            'id' => isset( $data['id'] ) ? $data['id'] : 'unknown',
            'body_size' => strlen( $body )
        ) );
        
        // Validate webhook payload
        $validation_result = $this->validate_webhook_payload( $data );
        if ( is_wp_error( $validation_result ) ) {
            return $validation_result;
        }
        
        // Check if webhook processing is enabled
        if ( ! $this->settings->get( 'webhook_enabled', true ) ) {
            $this->log_webhook_event( 'warning', 'Webhook processing disabled, ignoring event' );
            return rest_ensure_response( array( 
                'success' => true, 
                'message' => 'Webhook processing disabled' 
            ) );
        }
        
        // Process webhook with retry mechanism
        $result = $this->process_webhook_with_retry( $data );
        
        // Calculate processing time
        $processing_time = microtime( true ) - $start_time;
        
        if ( $result ) {
            $this->update_webhook_stats( 'successful' );
            $this->log_webhook_event( 'info', 'Webhook processed successfully', array(
                'processing_time' => round( $processing_time, 3 )
            ) );
            
            return rest_ensure_response( array(
                'success' => true,
                'message' => __( 'Webhook processed successfully.', 'squarekit' ),
                'processing_time' => round( $processing_time, 3 )
            ) );
        } else {
            $this->update_webhook_stats( 'failed' );
            $this->log_webhook_event( 'error', 'Webhook processing failed after retries', array(
                'processing_time' => round( $processing_time, 3 )
            ) );
            
            return new WP_Error(
                'webhook_processing_failed',
                __( 'Webhook processing failed.', 'squarekit' ),
                array( 'status' => 500 )
            );
        }
    }

    /**
     * Validate webhook payload
     *
     * @param array $data Webhook data
     * @return bool|WP_Error True if valid, WP_Error if invalid
     */
    protected function validate_webhook_payload( $data ) {
        if ( empty( $data['type'] ) ) {
            $this->log_webhook_event( 'error', 'Invalid webhook payload: missing type' );
            return new WP_Error( 
                'invalid_webhook', 
                __( 'Invalid webhook payload: missing type.', 'squarekit' ), 
                array( 'status' => 400 ) 
            );
        }
        
        if ( empty( $data['data'] ) || empty( $data['data']['object'] ) ) {
            $this->log_webhook_event( 'error', 'Invalid webhook payload: missing data object' );
            return new WP_Error( 
                'invalid_webhook', 
                __( 'Invalid webhook payload: missing data object.', 'squarekit' ), 
                array( 'status' => 400 ) 
            );
        }
        
        return true;
    }

    /**
     * Process webhook with retry mechanism
     *
     * @param array $data Webhook data
     * @return bool Success status
     */
    protected function process_webhook_with_retry( $data ) {
        $max_retries = 3;
        $retry_count = 0;
        $success = false;
        
        while ( $retry_count < $max_retries && ! $success ) {
            try {
                $success = $this->process_webhook_event( $data );
                
                if ( ! $success ) {
                    $retry_count++;
                    $this->update_webhook_stats( 'retries' );
                    
                    if ( $retry_count < $max_retries ) {
                        $this->log_webhook_event( 'warning', "Webhook processing failed, retry {$retry_count}/{$max_retries}" );
                        sleep( 2 ); // Wait 2 seconds before retry
                    }
                }
                
            } catch ( Exception $e ) {
                $retry_count++;
                $this->update_webhook_stats( 'retries' );
                $this->log_webhook_event( 'error', "Webhook processing exception: {$e->getMessage()}, retry {$retry_count}/{$max_retries}" );
                
                if ( $retry_count < $max_retries ) {
                    sleep( 2 );
                }
            }
        }
        
        return $success;
    }

    /**
     * Process webhook event based on type
     *
     * @param array $data Webhook data
     * @return bool Success status
     */
    public function process_webhook_event( $data ) {
        $this->update_webhook_stats( 'processed' );
        
        $event_type = $data['type'];
        $object_data = $data['data']['object'];
        
        $this->log_webhook_event( 'info', "Processing webhook event: {$event_type}" );
        
        switch ( $event_type ) {
            case 'catalog.version.updated':
                return $this->handle_catalog_webhook( $object_data );
                
            case 'inventory.count.updated':
                return $this->handle_inventory_webhook( $object_data );
                
            case 'order.created':
            case 'order.updated':
                return $this->handle_order_webhook( $object_data );
                
            case 'customer.created':
            case 'customer.updated':
                return $this->handle_customer_webhook( $object_data );
                
            case 'payment.created':
            case 'payment.updated':
                return $this->handle_payment_webhook( $object_data );
                
            case 'refund.created':
            case 'refund.updated':
                return $this->handle_refund_webhook( $object_data );
                
            default:
                $this->log_webhook_event( 'warning', "Unhandled webhook event type: {$event_type}" );
                return true; // Return true for unknown events to avoid retries
        }
    }

    /**
     * Handle catalog webhook events
     *
     * @param array $catalog_data Catalog data from Square
     * @return bool Success status
     */
    protected function handle_catalog_webhook( $catalog_data ) {
        $this->log_webhook_event( 'info', 'Processing catalog webhook' );

        // Trigger catalog sync if enabled
        if ( $this->settings->get( 'auto_sync_on_catalog_update', false ) ) {
            // Use product sync module if available
            if ( class_exists( 'SquareKit_Product_Sync' ) ) {
                $product_sync = new SquareKit_Product_Sync();
                $result = $product_sync->import_products_from_square();

                $this->log_webhook_event( 'info', 'Catalog sync triggered by webhook', $result );
                return $result['success'] ?? false;
            }
        }

        return true;
    }

    /**
     * Handle inventory webhook events
     *
     * @param array $inventory_data Inventory data from Square
     * @return bool Success status
     */
    protected function handle_inventory_webhook( $inventory_data ) {
        $catalog_object_id = $inventory_data['catalog_object_id'] ?? '';
        $quantity = $inventory_data['quantity'] ?? 0;

        if ( empty( $catalog_object_id ) ) {
            $this->log_webhook_event( 'error', 'Inventory webhook missing catalog_object_id' );
            return false;
        }

        $this->log_webhook_event( 'info', "Processing inventory webhook for {$catalog_object_id}, quantity: {$quantity}" );

        // Use inventory sync module if available
        if ( class_exists( 'SquareKit_Inventory_Sync' ) ) {
            $inventory_sync = new SquareKit_Inventory_Sync();
            return $inventory_sync->update_inventory_from_square( $catalog_object_id, $quantity );
        }

        return true;
    }

    /**
     * Handle order webhook events
     *
     * @param array $order_data Order data from Square
     * @return bool Success status
     */
    protected function handle_order_webhook( $order_data ) {
        $square_order_id = $order_data['id'] ?? '';

        if ( empty( $square_order_id ) ) {
            $this->log_webhook_event( 'error', 'Order webhook missing order ID' );
            return false;
        }

        $this->log_webhook_event( 'info', "Processing order webhook for {$square_order_id}" );

        // Use order sync module if available
        if ( class_exists( 'SquareKit_Order_Sync' ) ) {
            $order_sync = new SquareKit_Order_Sync();

            // Check if this is a fulfillment update
            if ( isset( $order_data['fulfillments'] ) ) {
                return $this->handle_fulfillment_webhook( $order_data );
            }

            // Import or update the order
            $result = $order_sync->import_single_order_from_square( $order_data );
            return $result !== 'failed';
        }

        return true;
    }

    /**
     * Handle customer webhook events
     *
     * @param array $customer_data Customer data from Square
     * @return bool Success status
     */
    protected function handle_customer_webhook( $customer_data ) {
        $square_customer_id = $customer_data['id'] ?? '';

        if ( empty( $square_customer_id ) ) {
            $this->log_webhook_event( 'error', 'Customer webhook missing customer ID' );
            return false;
        }

        $this->log_webhook_event( 'info', "Processing customer webhook for {$square_customer_id}" );

        // Use customer sync module if available
        if ( class_exists( 'SquareKit_Customer_Sync' ) ) {
            $customer_sync = new SquareKit_Customer_Sync();
            $result = $customer_sync->import_single_customer_from_square( $customer_data );
            return $result !== 'failed';
        }

        return true;
    }

    /**
     * Handle payment webhook events
     *
     * @param array $payment_data Payment data from Square
     * @return bool Success status
     */
    protected function handle_payment_webhook( $payment_data ) {
        $payment_id = $payment_data['id'] ?? '';
        $order_id = $payment_data['order_id'] ?? '';

        $this->log_webhook_event( 'info', "Processing payment webhook for payment {$payment_id}, order {$order_id}" );

        // Handle subscription renewals if WooCommerce Subscriptions is active
        if ( class_exists( 'WC_Subscriptions' ) && $order_id ) {
            return $this->handle_subscription_payment( $payment_data );
        }

        // Log payment information
        $this->db->add_log(
            'webhook_payment',
            'Payment webhook processed',
            array(
                'payment_id' => $payment_id,
                'order_id' => $order_id,
                'amount' => $payment_data['amount_money']['amount'] ?? 0,
                'status' => $payment_data['status'] ?? 'unknown'
            )
        );

        return true;
    }

    /**
     * Handle refund webhook events
     *
     * @param array $refund_data Refund data from Square
     * @return bool Success status
     */
    protected function handle_refund_webhook( $refund_data ) {
        $refund_id = $refund_data['id'] ?? '';
        $payment_id = $refund_data['payment_id'] ?? '';

        $this->log_webhook_event( 'info', "Processing refund webhook for refund {$refund_id}, payment {$payment_id}" );

        // Log refund information
        $this->db->add_log(
            'webhook_refund',
            'Refund webhook processed',
            array(
                'refund_id' => $refund_id,
                'payment_id' => $payment_id,
                'amount' => $refund_data['amount_money']['amount'] ?? 0,
                'status' => $refund_data['status'] ?? 'unknown'
            )
        );

        return true;
    }

    /**
     * Handle fulfillment webhook events from Square
     *
     * @param array $order_data Order data from Square
     * @return bool Success status
     */
    protected function handle_fulfillment_webhook( $order_data ) {
        $square_order_id = $order_data['id'] ?? '';

        if ( empty( $square_order_id ) ) {
            return false;
        }

        // Find WooCommerce order by Square ID
        $wc_order_id = $this->find_wc_order_by_square_id( $square_order_id );
        if ( ! $wc_order_id ) {
            $this->log_webhook_event( 'warning', "Fulfillment webhook received for unknown order: {$square_order_id}" );
            return false;
        }

        // Use order sync module for fulfillment processing
        if ( class_exists( 'SquareKit_Order_Sync' ) ) {
            $order_sync = new SquareKit_Order_Sync();

            // Apply fulfillment mapping
            $mapping_applied = $order_sync->apply_fulfillment_mapping_to_order( $wc_order_id, $order_data );

            // Update fulfillment status
            $status_updated = $order_sync->update_order_fulfillment_from_square( $wc_order_id, $order_data );

            if ( $mapping_applied || $status_updated ) {
                $this->log_webhook_event( 'info', "Processed fulfillment webhook for order {$wc_order_id} (Square: {$square_order_id})" );
                return true;
            }
        }

        return false;
    }

    /**
     * Handle subscription payment webhook
     *
     * @param array $payment_data Payment data from Square
     * @return bool Success status
     */
    protected function handle_subscription_payment( $payment_data ) {
        $payment_id = $payment_data['id'] ?? '';
        $order_id = $payment_data['order_id'] ?? '';

        if ( ! $order_id ) {
            return false;
        }

        // Find WooCommerce order by Square order ID
        $wc_order = $this->find_wc_order_by_square_id( $order_id );

        if ( $wc_order ) {
            $this->log_webhook_event( 'info', 'Subscription payment webhook processed', array(
                'payment_id' => $payment_id,
                'square_order_id' => $order_id,
                'wc_order_id' => $wc_order->get_id()
            ) );

            // Additional subscription processing could be added here
            return true;
        }

        return false;
    }

    /**
     * Find WooCommerce order by Square ID
     *
     * @param string $square_order_id Square order ID
     * @return WC_Order|false WooCommerce order or false if not found
     */
    protected function find_wc_order_by_square_id( $square_order_id ) {
        $orders = wc_get_orders( array(
            'meta_key' => '_square_order_id',
            'meta_value' => $square_order_id,
            'limit' => 1,
        ) );

        return ! empty( $orders ) ? $orders[0] : false;
    }

    /**
     * Log webhook event
     *
     * @param string $level Log level (info, warning, error)
     * @param string $message Log message
     * @param array $context Additional context data
     */
    protected function log_webhook_event( $level, $message, $context = array() ) {
        $this->logger->log( 'webhook', $level, $message, $context );

        // Also log to database for webhook-specific tracking
        $this->db->add_log(
            'webhook_event',
            $message,
            array_merge( $context, array( 'level' => $level ) )
        );
    }

    /**
     * Update webhook processing statistics
     *
     * @param string $type Statistics type
     */
    protected function update_webhook_stats( $type ) {
        if ( isset( $this->webhook_stats[ $type ] ) ) {
            $this->webhook_stats[ $type ]++;
        }
    }

    /**
     * Get webhook processing statistics
     *
     * @return array Webhook statistics
     */
    public function get_webhook_stats() {
        return $this->webhook_stats;
    }

    /**
     * Reset webhook processing statistics
     */
    public function reset_webhook_stats() {
        $this->webhook_stats = array(
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'retries' => 0,
            'errors' => array()
        );
    }

    /**
     * Get webhook status for dashboard
     *
     * @return array Webhook status information
     */
    public function get_webhook_status() {
        $webhook_enabled = $this->settings->get( 'webhook_enabled', true );
        $signature_key_configured = ! empty( $this->settings->get( 'webhook_signature_key' ) );

        return array(
            'enabled' => $webhook_enabled,
            'signature_configured' => $signature_key_configured,
            'endpoint_url' => get_rest_url( null, 'squarekit/v1/webhook' ),
            'last_webhook' => get_option( 'squarekit_last_webhook_received', __( 'Never', 'squarekit' ) ),
            'stats' => $this->webhook_stats
        );
    }

    /**
     * Test webhook endpoint
     *
     * @return array Test results
     */
    public function test_webhook_endpoint() {
        $endpoint_url = get_rest_url( null, 'squarekit/v1/webhook' );

        // Test if endpoint is accessible
        $response = wp_remote_post( $endpoint_url, array(
            'body' => json_encode( array(
                'type' => 'test.webhook',
                'data' => array(
                    'object' => array(
                        'id' => 'test-webhook-' . time()
                    )
                )
            ) ),
            'headers' => array(
                'Content-Type' => 'application/json',
                'X-Square-Signature' => 'test-signature'
            ),
            'timeout' => 10
        ) );

        if ( is_wp_error( $response ) ) {
            return array(
                'success' => false,
                'message' => 'Webhook endpoint test failed: ' . $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code( $response );
        $response_body = wp_remote_retrieve_body( $response );

        return array(
            'success' => $status_code === 200,
            'status_code' => $status_code,
            'response' => $response_body,
            'endpoint_url' => $endpoint_url,
            'message' => $status_code === 200 ? 'Webhook endpoint is accessible' : 'Webhook endpoint returned status ' . $status_code
        );
    }

    /**
     * Process webhook payload (for manual testing)
     *
     * @param array $payload Webhook payload
     * @return array Processing results
     */
    public function process_webhook_payload( $payload ) {
        if ( ! is_array( $payload ) ) {
            return array(
                'success' => false,
                'message' => 'Invalid payload format'
            );
        }

        $validation_result = $this->validate_webhook_payload( $payload );
        if ( is_wp_error( $validation_result ) ) {
            return array(
                'success' => false,
                'message' => $validation_result->get_error_message()
            );
        }

        $result = $this->process_webhook_event( $payload );

        return array(
            'success' => $result,
            'message' => $result ? 'Webhook processed successfully' : 'Webhook processing failed',
            'stats' => $this->webhook_stats
        );
    }

    /**
     * Get supported webhook event types
     *
     * @return array Supported event types
     */
    public function get_supported_event_types() {
        return array(
            'catalog.version.updated' => __( 'Catalog Updated', 'squarekit' ),
            'inventory.count.updated' => __( 'Inventory Updated', 'squarekit' ),
            'order.created' => __( 'Order Created', 'squarekit' ),
            'order.updated' => __( 'Order Updated', 'squarekit' ),
            'customer.created' => __( 'Customer Created', 'squarekit' ),
            'customer.updated' => __( 'Customer Updated', 'squarekit' ),
            'payment.created' => __( 'Payment Created', 'squarekit' ),
            'payment.updated' => __( 'Payment Updated', 'squarekit' ),
            'refund.created' => __( 'Refund Created', 'squarekit' ),
            'refund.updated' => __( 'Refund Updated', 'squarekit' )
        );
    }

    /**
     * Enable webhook processing
     */
    public function enable_webhook_processing() {
        $this->settings->update( 'webhook_enabled', true );
        $this->log_webhook_event( 'info', 'Webhook processing enabled' );
    }

    /**
     * Disable webhook processing
     */
    public function disable_webhook_processing() {
        $this->settings->update( 'webhook_enabled', false );
        $this->log_webhook_event( 'info', 'Webhook processing disabled' );
    }

    /**
     * Update webhook signature key
     *
     * @param string $signature_key New signature key
     */
    public function update_webhook_signature_key( $signature_key ) {
        $this->settings->update( 'webhook_signature_key', $signature_key );
        $this->log_webhook_event( 'info', 'Webhook signature key updated' );
    }
}
