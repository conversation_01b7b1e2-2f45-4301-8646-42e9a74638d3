<?php
/**
 * SquareKit Customer Sync Module
 *
 * Handles customer synchronization between Square and WooCommerce.
 * Includes customer import/export, role management, and customer data handling.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Customer Sync Class
 *
 * Manages customer operations between Square and WooCommerce.
 * Handles customer synchronization, role management, and data mapping.
 */
class SquareKit_Customer_Sync {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Database instance
     *
     * @var SquareKit_DB
     */
    private $db;

    /**
     * Customer processing statistics
     *
     * @var array
     */
    private $customer_stats = array(
        'processed' => 0,
        'imported' => 0,
        'exported' => 0,
        'updated' => 0,
        'roles_applied' => 0,
        'failed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
        $this->init_hooks();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }

        $this->square_api = new SquareKit_Square_API();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
        $this->db = new SquareKit_DB();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Customer sync hooks
        add_action( 'user_register', array( $this, 'sync_customer_to_square' ), 10, 1 );
        add_action( 'profile_update', array( $this, 'sync_customer_to_square' ), 10, 1 );
        
        // AJAX handlers
        add_action( 'wp_ajax_squarekit_apply_role_mapping_to_all', array( $this, 'ajax_apply_role_mapping_to_all' ) );
    }

    /**
     * Sync WooCommerce customer to Square
     *
     * @param int $user_id User ID
     * @return bool Success status
     */
    public function sync_customer_to_square( $user_id ) {
        // Only sync if WooCommerce to Square sync is enabled
        if ( ! $this->settings->is_woo_to_square_sync_enabled() ) {
            return false;
        }

        $this->logger->log( 'customer_sync', 'info', "Syncing customer {$user_id} to Square" );

        $user = get_userdata( $user_id );
        if ( ! $user ) {
            $this->logger->log( 'customer_sync', 'error', "User {$user_id} not found" );
            return false;
        }

        try {
            // Prepare Square customer data
            $customer_data = $this->prepare_square_customer_data( $user );
            $square_id = get_user_meta( $user_id, '_square_customer_id', true );

            if ( $square_id ) {
                // Update existing customer in Square
                $response = $this->square_api->update_customer( $square_id, $customer_data );
                if ( ! is_wp_error( $response ) ) {
                    $this->update_customer_stats( 'updated' );
                    $this->logger->log( 'customer_sync', 'info', "Updated Square customer {$square_id} for user {$user_id}" );
                }
            } else {
                // Create new customer in Square
                $response = $this->square_api->create_customer( $customer_data );
                if ( is_array( $response ) && ! empty( $response['id'] ) ) {
                    update_user_meta( $user_id, '_square_customer_id', $response['id'] );
                    $this->update_customer_stats( 'exported' );
                    $this->logger->log( 'customer_sync', 'info', "Created Square customer {$response['id']} for user {$user_id}" );
                }
            }

            if ( is_wp_error( $response ) ) {
                $this->logger->log( 'customer_sync', 'error', "Customer sync failed for user {$user_id}: " . $response->get_error_message() );
                $this->update_customer_stats( 'failed' );
                return false;
            }

            $this->update_customer_stats( 'processed' );
            return true;

        } catch ( Exception $e ) {
            $this->logger->log( 'customer_sync', 'error', "Exception syncing customer {$user_id}: " . $e->getMessage() );
            $this->update_customer_stats( 'failed' );
            return false;
        }
    }

    /**
     * Prepare Square customer data from WP_User
     *
     * @param WP_User $user WordPress user
     * @return array Square customer data
     */
    protected function prepare_square_customer_data( $user ) {
        $first_name = get_user_meta( $user->ID, 'first_name', true );
        $last_name = get_user_meta( $user->ID, 'last_name', true );
        
        $data = array(
            'given_name' => $first_name ? $first_name : $user->user_login,
            'family_name' => $last_name,
            'email_address' => $user->user_email,
            'reference_id' => (string) $user->ID,
        );

        // Add optional fields
        if ( $user->user_url ) {
            $data['note'] = $user->user_url;
        }

        // Add phone number if available
        $phone = get_user_meta( $user->ID, 'billing_phone', true );
        if ( $phone ) {
            $data['phone_number'] = $phone;
        }

        // Add address if available
        $address = $this->get_customer_address( $user->ID );
        if ( $address ) {
            $data['address'] = $address;
        }

        return $data;
    }

    /**
     * Get customer address from user meta
     *
     * @param int $user_id User ID
     * @return array|null Address data or null
     */
    protected function get_customer_address( $user_id ) {
        $address_line_1 = get_user_meta( $user_id, 'billing_address_1', true );
        $address_line_2 = get_user_meta( $user_id, 'billing_address_2', true );
        $city = get_user_meta( $user_id, 'billing_city', true );
        $state = get_user_meta( $user_id, 'billing_state', true );
        $postcode = get_user_meta( $user_id, 'billing_postcode', true );
        $country = get_user_meta( $user_id, 'billing_country', true );

        if ( ! $address_line_1 && ! $city ) {
            return null;
        }

        $address = array();
        
        if ( $address_line_1 ) {
            $address['address_line_1'] = $address_line_1;
        }
        
        if ( $address_line_2 ) {
            $address['address_line_2'] = $address_line_2;
        }
        
        if ( $city ) {
            $address['locality'] = $city;
        }
        
        if ( $state ) {
            $address['administrative_district_level_1'] = $state;
        }
        
        if ( $postcode ) {
            $address['postal_code'] = $postcode;
        }
        
        if ( $country ) {
            $address['country'] = $country;
        }

        return ! empty( $address ) ? $address : null;
    }

    /**
     * Import customers from Square to WooCommerce
     *
     * @param array $options Import options
     * @return array Import results
     */
    public function import_customers_from_square( $options = array() ) {
        // Only import if Square to WooCommerce sync is enabled
        if ( ! $this->settings->is_square_to_woo_sync_enabled() ) {
            return array(
                'success' => false,
                'message' => __( 'Square to WooCommerce sync is disabled.', 'squarekit' )
            );
        }

        $this->logger->log( 'customer_sync', 'info', 'Starting customer import from Square' );

        try {
            $response = $this->square_api->get_customers();

            if ( is_wp_error( $response ) ) {
                $this->logger->log( 'customer_sync', 'error', 'Square API error: ' . $response->get_error_message() );
                return array(
                    'success' => false,
                    'message' => $response->get_error_message()
                );
            }

            $customers = is_array( $response ) ? $response : array();
            $imported = 0;
            $updated = 0;
            $failed = 0;

            foreach ( $customers as $sq_customer ) {
                $result = $this->import_single_customer_from_square( $sq_customer );

                if ( $result === 'imported' ) {
                    $imported++;
                } elseif ( $result === 'updated' ) {
                    $updated++;
                } else {
                    $failed++;
                }

                $this->update_customer_stats( 'processed' );
            }

            $this->logger->log( 'customer_sync', 'info', "Customer import completed: {$imported} imported, {$updated} updated, {$failed} failed" );

            return array(
                'success' => true,
                'imported' => $imported,
                'updated' => $updated,
                'failed' => $failed,
                'message' => sprintf(
                    __( 'Imported %d customers, updated %d customers from Square.', 'squarekit' ),
                    $imported,
                    $updated
                )
            );

        } catch ( Exception $e ) {
            $this->logger->log( 'customer_sync', 'error', 'Exception during customer import: ' . $e->getMessage() );
            return array(
                'success' => false,
                'message' => 'Customer import failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Import single customer from Square
     *
     * @param array $sq_customer Square customer data
     * @return string Import result ('imported', 'updated', or 'failed')
     */
    protected function import_single_customer_from_square( $sq_customer ) {
        $square_id = $sq_customer['id'];

        try {
            $this->logger->log( 'customer_sync', 'info', "Processing Square customer: {$square_id}" );

            // Check if customer already exists
            $existing_user_id = $this->find_wc_user_by_square_id( $square_id );

            if ( $existing_user_id ) {
                // Update existing customer
                $this->update_wc_customer_from_square( $existing_user_id, $sq_customer );
                $this->update_customer_stats( 'updated' );
                return 'updated';
            } else {
                // Create new customer
                $user_id = $this->create_wc_customer_from_square( $sq_customer );
                if ( $user_id ) {
                    $this->update_customer_stats( 'imported' );
                    return 'imported';
                } else {
                    $this->update_customer_stats( 'failed' );
                    return 'failed';
                }
            }

        } catch ( Exception $e ) {
            $this->logger->log( 'customer_sync', 'error', "Failed to import customer {$square_id}: " . $e->getMessage() );
            $this->update_customer_stats( 'failed' );
            return 'failed';
        }
    }

    /**
     * Create WooCommerce customer from Square customer data
     *
     * @param array $sq_customer Square customer data
     * @return int|false User ID or false on failure
     */
    protected function create_wc_customer_from_square( $sq_customer ) {
        $square_id = $sq_customer['id'];
        $email = isset( $sq_customer['email_address'] ) ? $sq_customer['email_address'] : '';

        if ( ! is_email( $email ) ) {
            $this->logger->log( 'customer_sync', 'warning', "Skipping Square customer {$square_id}: no valid email" );
            return false;
        }

        // Check if user with this email already exists
        $existing_user = get_user_by( 'email', $email );
        if ( $existing_user ) {
            // Link existing user to Square customer
            update_user_meta( $existing_user->ID, '_square_customer_id', $square_id );
            $this->update_wc_customer_from_square( $existing_user->ID, $sq_customer );
            return $existing_user->ID;
        }

        try {
            // Create new user
            $user_id = wp_create_user(
                $email,
                wp_generate_password(),
                $email
            );

            if ( is_wp_error( $user_id ) ) {
                $this->logger->log( 'customer_sync', 'error', "Failed to create user for Square customer {$square_id}: " . $user_id->get_error_message() );
                return false;
            }

            // Store Square customer ID
            update_user_meta( $user_id, '_square_customer_id', $square_id );

            // Update customer data
            $this->update_wc_customer_from_square( $user_id, $sq_customer );

            // Apply customer role mapping if enabled
            $role_applied = $this->apply_customer_role( $user_id, $sq_customer );
            if ( $role_applied ) {
                $this->update_customer_stats( 'roles_applied' );
                $this->db->add_log(
                    'customer_import',
                    sprintf( 'Applied role mapping for customer %d during import', $user_id ),
                    array(
                        'user_id' => $user_id,
                        'square_customer_id' => $square_id,
                        'customer_data' => $sq_customer
                    )
                );
            }

            $this->logger->log( 'customer_sync', 'info', "Created WooCommerce customer {$user_id} from Square customer {$square_id}" );

            return $user_id;

        } catch ( Exception $e ) {
            $this->logger->log( 'customer_sync', 'error', "Exception creating customer from Square customer {$square_id}: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Update WooCommerce customer from Square customer data
     *
     * @param int $user_id User ID
     * @param array $sq_customer Square customer data
     * @return bool Success status
     */
    protected function update_wc_customer_from_square( $user_id, $sq_customer ) {
        try {
            // Update first name
            if ( ! empty( $sq_customer['given_name'] ) ) {
                update_user_meta( $user_id, 'first_name', $sq_customer['given_name'] );
            }

            // Update last name
            if ( ! empty( $sq_customer['family_name'] ) ) {
                update_user_meta( $user_id, 'last_name', $sq_customer['family_name'] );
            }

            // Update phone number
            if ( ! empty( $sq_customer['phone_number'] ) ) {
                update_user_meta( $user_id, 'billing_phone', $sq_customer['phone_number'] );
            }

            // Update address
            if ( ! empty( $sq_customer['address'] ) ) {
                $this->update_customer_address( $user_id, $sq_customer['address'] );
            }

            // Update note as user URL
            if ( ! empty( $sq_customer['note'] ) ) {
                wp_update_user( array(
                    'ID' => $user_id,
                    'user_url' => $sq_customer['note']
                ) );
            }

            $this->logger->log( 'customer_sync', 'info', "Updated WooCommerce customer {$user_id} from Square" );

            return true;

        } catch ( Exception $e ) {
            $this->logger->log( 'customer_sync', 'error', "Failed to update WC customer {$user_id}: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Update customer address from Square address data
     *
     * @param int $user_id User ID
     * @param array $address Square address data
     */
    protected function update_customer_address( $user_id, $address ) {
        if ( ! empty( $address['address_line_1'] ) ) {
            update_user_meta( $user_id, 'billing_address_1', $address['address_line_1'] );
        }

        if ( ! empty( $address['address_line_2'] ) ) {
            update_user_meta( $user_id, 'billing_address_2', $address['address_line_2'] );
        }

        if ( ! empty( $address['locality'] ) ) {
            update_user_meta( $user_id, 'billing_city', $address['locality'] );
        }

        if ( ! empty( $address['administrative_district_level_1'] ) ) {
            update_user_meta( $user_id, 'billing_state', $address['administrative_district_level_1'] );
        }

        if ( ! empty( $address['postal_code'] ) ) {
            update_user_meta( $user_id, 'billing_postcode', $address['postal_code'] );
        }

        if ( ! empty( $address['country'] ) ) {
            update_user_meta( $user_id, 'billing_country', $address['country'] );
        }
    }

    /**
     * Apply customer role based on mapping rules
     *
     * @param int $user_id WooCommerce user ID
     * @param array $customer_data Square customer data
     * @return bool True if role was applied
     */
    public function apply_customer_role( $user_id, $customer_data ) {
        if ( ! $this->settings->is_customer_role_mapping_enabled() ) {
            return false;
        }

        $role = $this->determine_customer_role( $customer_data );
        if ( ! $role ) {
            return false;
        }

        $user = get_userdata( $user_id );
        if ( ! $user ) {
            return false;
        }

        // Check if user already has this role
        if ( in_array( $role, $user->roles, true ) ) {
            return false;
        }

        // Remove existing customer roles (except administrator, editor, etc.)
        $excluded_roles = array( 'administrator', 'editor', 'author', 'contributor' );
        foreach ( $user->roles as $existing_role ) {
            if ( ! in_array( $existing_role, $excluded_roles, true ) ) {
                $user->remove_role( $existing_role );
            }
        }

        // Add new role
        $user->add_role( $role );

        // Log role assignment
        $this->db->add_log(
            'customer_role_mapping',
            sprintf( 'Assigned role "%s" to user %d based on Square customer data', $role, $user_id ),
            array(
                'user_id' => $user_id,
                'assigned_role' => $role,
                'customer_data' => $customer_data
            )
        );

        $this->logger->log( 'customer_sync', 'info', "Applied role '{$role}' to user {$user_id}" );

        return true;
    }

    /**
     * Determine customer role based on Square customer data
     *
     * @param array $customer_data Square customer data
     * @return string|false Role name or false
     */
    protected function determine_customer_role( $customer_data ) {
        $mapping_rules = $this->settings->get_customer_role_mapping();
        if ( empty( $mapping_rules ) ) {
            return false;
        }

        foreach ( $mapping_rules as $rule ) {
            if ( $this->evaluate_role_mapping_rule( $customer_data, $rule ) ) {
                return $rule['role'];
            }
        }

        return false;
    }

    /**
     * Evaluate role mapping rule against customer data
     *
     * @param array $customer_data Square customer data
     * @param array $rule Role mapping rule
     * @return bool True if rule matches
     */
    protected function evaluate_role_mapping_rule( $customer_data, $rule ) {
        $criteria = $rule['criteria'] ?? '';
        $operator = $rule['operator'] ?? '>=';
        $value = $rule['value'] ?? 0;

        $customer_value = $this->get_customer_value_for_criteria( $customer_data, $criteria );

        switch ( $operator ) {
            case '>=':
                return $customer_value >= $value;
            case '>':
                return $customer_value > $value;
            case '<=':
                return $customer_value <= $value;
            case '<':
                return $customer_value < $value;
            case '=':
            case '==':
                return $customer_value == $value;
            case '!=':
                return $customer_value != $value;
            default:
                return false;
        }
    }

    /**
     * Get customer value for specific criteria
     *
     * @param array $customer_data Square customer data
     * @param string $criteria Criteria to evaluate
     * @return mixed Customer value for criteria
     */
    protected function get_customer_value_for_criteria( $customer_data, $criteria ) {
        switch ( $criteria ) {
            case 'loyalty_points':
                return isset( $customer_data['loyalty_points'] ) ? intval( $customer_data['loyalty_points'] ) : 0;

            case 'total_spent':
                // Get total spent from WooCommerce orders
                $wc_user_id = $this->find_wc_user_by_square_id( $customer_data['id'] );
                if ( $wc_user_id ) {
                    $customer = new WC_Customer( $wc_user_id );
                    return $customer->get_total_spent();
                }
                return 0;

            case 'order_count':
                // Get order count from WooCommerce
                $wc_user_id = $this->find_wc_user_by_square_id( $customer_data['id'] );
                if ( $wc_user_id ) {
                    $customer = new WC_Customer( $wc_user_id );
                    return $customer->get_order_count();
                }
                return 0;

            case 'registration_date':
                $wc_user_id = $this->find_wc_user_by_square_id( $customer_data['id'] );
                if ( $wc_user_id ) {
                    $user = get_userdata( $wc_user_id );
                    return $user ? strtotime( $user->user_registered ) : 0;
                }
                return 0;

            case 'last_order_date':
                $wc_user_id = $this->find_wc_user_by_square_id( $customer_data['id'] );
                if ( $wc_user_id ) {
                    $customer = new WC_Customer( $wc_user_id );
                    $last_order = $customer->get_last_order();
                    return $last_order ? strtotime( $last_order->get_date_created() ) : 0;
                }
                return 0;

            default:
                return 0;
        }
    }

    /**
     * Apply role mapping to all existing customers
     *
     * @return array Result summary
     */
    public function apply_role_mapping_to_all_customers() {
        if ( ! $this->settings->is_customer_role_mapping_enabled() ) {
            return array( 'success' => false, 'message' => __( 'Customer role mapping is disabled.', 'squarekit' ) );
        }

        $mapping_rules = $this->settings->get_customer_role_mapping();
        if ( empty( $mapping_rules ) ) {
            return array( 'success' => false, 'message' => __( 'No role mapping rules configured.', 'squarekit' ) );
        }

        $this->logger->log( 'customer_sync', 'info', 'Starting bulk role mapping for all customers' );

        $customers = get_users( array(
            'role__in' => array( 'customer', 'subscriber' ),
            'number' => -1,
        ) );

        $processed = 0;
        $roles_applied = 0;

        foreach ( $customers as $customer ) {
            $square_id = get_user_meta( $customer->ID, '_square_customer_id', true );
            if ( ! $square_id ) {
                continue; // Skip customers without Square ID
            }

            // Get Square customer data
            $square_customer = $this->square_api->get_customer( $square_id );
            if ( is_wp_error( $square_customer ) ) {
                continue; // Skip if can't get Square data
            }

            $role_applied = $this->apply_customer_role( $customer->ID, $square_customer );
            if ( $role_applied ) {
                $roles_applied++;
                $this->update_customer_stats( 'roles_applied' );
            }
            $processed++;
            $this->update_customer_stats( 'processed' );
        }

        $this->logger->log( 'customer_sync', 'info', "Bulk role mapping completed: {$processed} processed, {$roles_applied} roles applied" );

        return array(
            'success' => true,
            'processed' => $processed,
            'roles_applied' => $roles_applied,
            'message' => sprintf( __( 'Processed %d customers, applied roles to %d customers.', 'squarekit' ), $processed, $roles_applied )
        );
    }

    /**
     * AJAX handler: Apply role mapping to all customers
     */
    public function ajax_apply_role_mapping_to_all() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ), 403 );
        }

        check_ajax_referer( 'squarekit_admin', 'nonce' );

        $result = $this->apply_role_mapping_to_all_customers();

        if ( $result['success'] ) {
            wp_send_json_success( $result );
        } else {
            wp_send_json_error( $result );
        }
    }

    /**
     * Find WooCommerce user by Square ID
     *
     * @param string $square_customer_id Square customer ID
     * @return int|false User ID or false if not found
     */
    protected function find_wc_user_by_square_id( $square_customer_id ) {
        $users = get_users( array(
            'meta_key' => '_square_customer_id',
            'meta_value' => $square_customer_id,
            'number' => 1,
            'fields' => 'ID'
        ) );

        return ! empty( $users ) ? $users[0] : false;
    }

    /**
     * Export customer to Square format
     *
     * @param int $user_id WooCommerce user ID
     * @return array|false Square customer data or false
     */
    public function export_customer_to_square( $user_id ) {
        $user = get_userdata( $user_id );
        if ( ! $user ) {
            return false;
        }

        $this->logger->log( 'customer_sync', 'info', "Exporting customer {$user_id} to Square format" );

        return $this->prepare_square_customer_data( $user );
    }

    /**
     * Bulk import customers from Square
     *
     * @param array $square_customers Array of Square customer data
     * @return array Import results
     */
    public function bulk_import_customers( $square_customers ) {
        $this->logger->log( 'customer_sync', 'info', 'Starting bulk customer import for ' . count( $square_customers ) . ' customers' );

        $results = array(
            'total' => count( $square_customers ),
            'imported' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => array()
        );

        foreach ( $square_customers as $sq_customer ) {
            try {
                $result = $this->import_single_customer_from_square( $sq_customer );

                if ( $result === 'imported' ) {
                    $results['imported']++;
                } elseif ( $result === 'updated' ) {
                    $results['updated']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Failed to import customer: " . ( $sq_customer['id'] ?? 'unknown' );
                }

            } catch ( Exception $e ) {
                $results['failed']++;
                $results['errors'][] = "Exception importing customer: " . $e->getMessage();
                $this->logger->log( 'customer_sync', 'error', "Exception in bulk import: " . $e->getMessage() );
            }
        }

        $this->logger->log( 'customer_sync', 'info', 'Bulk customer import completed', $results );

        return $results;
    }

    /**
     * Update customer processing statistics
     *
     * @param string $type Statistics type
     */
    protected function update_customer_stats( $type ) {
        if ( isset( $this->customer_stats[ $type ] ) ) {
            $this->customer_stats[ $type ]++;
        }
    }

    /**
     * Get customer processing statistics
     *
     * @return array Customer statistics
     */
    public function get_customer_stats() {
        return $this->customer_stats;
    }

    /**
     * Reset customer processing statistics
     */
    public function reset_customer_stats() {
        $this->customer_stats = array(
            'processed' => 0,
            'imported' => 0,
            'exported' => 0,
            'updated' => 0,
            'roles_applied' => 0,
            'failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Get customer sync status for dashboard
     *
     * @return array Sync status information
     */
    public function get_sync_status() {
        $total_customers = count( get_users( array( 'role__in' => array( 'customer', 'subscriber' ) ) ) );

        $synced_customers = get_users( array(
            'meta_key' => '_square_customer_id',
            'meta_compare' => 'EXISTS',
            'number' => -1,
            'fields' => 'ID'
        ) );

        return array(
            'total_customers' => $total_customers,
            'synced_customers' => count( $synced_customers ),
            'sync_percentage' => $total_customers > 0 ? round( ( count( $synced_customers ) / $total_customers ) * 100, 1 ) : 0,
            'last_sync' => get_option( 'squarekit_last_customer_sync', __( 'Never', 'squarekit' ) )
        );
    }

    /**
     * Sync customers (general method for API calls)
     *
     * @param array $options Sync options
     * @return array Sync results
     */
    public function sync_customers( $options = array() ) {
        $direction = $options['direction'] ?? 'square_to_wc';

        if ( $direction === 'square_to_wc' ) {
            return $this->import_customers_from_square( $options );
        } else {
            // For WC to Square sync, we would need to get all WC customers and sync them
            $customers = get_users( array( 'role__in' => array( 'customer', 'subscriber' ) ) );
            $synced = 0;
            $failed = 0;

            foreach ( $customers as $customer ) {
                $result = $this->sync_customer_to_square( $customer->ID );
                if ( $result ) {
                    $synced++;
                } else {
                    $failed++;
                }
            }

            return array(
                'success' => true,
                'synced' => $synced,
                'failed' => $failed,
                'message' => sprintf( __( 'Synced %d customers to Square, %d failed.', 'squarekit' ), $synced, $failed )
            );
        }
    }
}
