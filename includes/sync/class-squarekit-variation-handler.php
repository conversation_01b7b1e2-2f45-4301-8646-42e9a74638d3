<?php
/**
 * SquareKit Variation Handler Module
 *
 * Handles product variations, attributes, and option sets between Square and WooCommerce.
 * Includes variation import/export, attribute creation, and option set management.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Variation Handler Class
 *
 * Manages product variations and attributes between Square and WooCommerce.
 * Handles complex variation logic, option sets, and attribute mapping.
 */
class SquareKit_Variation_Handler {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Variation processing statistics
     *
     * @var array
     */
    private $variation_stats = array(
        'processed' => 0,
        'created' => 0,
        'updated' => 0,
        'attributes_created' => 0,
        'failed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $this->square_api = new SquareKit_Square_API();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Import item option sets from Square
     *
     * @param array $item Square catalog item
     * @param int $product_id WooCommerce product ID
     * @return bool Success status
     */
    public function import_item_option_sets_from_square( $item, $product_id ) {
        $this->logger->log( 'variation_handler', 'info', "Importing option sets for product {$product_id}" );

        // Get item option sets from Square
        $option_sets = array();
        
        // Check for option sets in item data
        if ( ! empty( $item['item_data']['item_option_set_ids'] ) && is_array( $item['item_data']['item_option_set_ids'] ) ) {
            foreach ( $item['item_data']['item_option_set_ids'] as $option_set_id ) {
                $option_set = $this->square_api->get_item_option_set( $option_set_id );
                if ( ! is_wp_error( $option_set ) && ! empty( $option_set ) ) {
                    $option_sets[] = $option_set;
                }
            }
        }
        
        // Import each option set
        $imported_option_sets = array();
        foreach ( $option_sets as $option_set ) {
            $imported_set = $this->import_single_option_set( $option_set, $product_id );
            if ( $imported_set ) {
                $imported_option_sets[] = $imported_set;
            }
        }
        
        // Store imported option sets in product meta
        if ( ! empty( $imported_option_sets ) ) {
            update_post_meta( $product_id, '_squarekit_option_sets', $imported_option_sets );
            $this->logger->log( 'variation_handler', 'info', "Imported " . count( $imported_option_sets ) . " option sets for product {$product_id}" );
        }
        
        return true;
    }

    /**
     * Import a single option set from Square
     *
     * @param array $option_set Square option set data
     * @param int $product_id WooCommerce product ID
     * @return array|false Imported option set data or false on failure
     */
    public function import_single_option_set( $option_set, $product_id ) {
        if ( empty( $option_set['item_option_set_data'] ) ) {
            return false;
        }
        
        $set_data = $option_set['item_option_set_data'];
        $square_set_id = $option_set['id'];
        
        $this->logger->log( 'variation_handler', 'info', "Importing option set: {$set_data['name']} (ID: {$square_set_id})" );
        
        // Create WooCommerce attribute from option set
        $attribute_name = sanitize_title( $set_data['name'] );
        $attribute_label = $set_data['name'];
        
        // Check if attribute already exists
        $existing_attribute = $this->find_wc_attribute_by_square_id( $square_set_id );
        if ( ! $existing_attribute ) {
            // Create new attribute
            $attribute_id = $this->create_wc_attribute_from_option_set( $set_data, $square_set_id );
            if ( ! $attribute_id ) {
                $this->logger->log( 'variation_handler', 'error', "Failed to create attribute for option set {$square_set_id}" );
                return false;
            }
            $this->update_variation_stats( 'attributes_created' );
        } else {
            $attribute_id = $existing_attribute;
        }
        
        // Get option values
        $option_values = array();
        if ( ! empty( $set_data['options'] ) && is_array( $set_data['options'] ) ) {
            foreach ( $set_data['options'] as $option ) {
                if ( ! empty( $option['item_option_data']['name'] ) ) {
                    $option_values[] = $option['item_option_data']['name'];
                }
            }
        }
        
        // Add attribute to product
        $this->add_attribute_to_product( $product_id, $attribute_id, $option_values );
        
        return array(
            'square_set_id' => $square_set_id,
            'wc_attribute_id' => $attribute_id,
            'name' => $attribute_label,
            'options' => $option_values,
            'source' => 'square'
        );
    }

    /**
     * Create WooCommerce attribute from Square option set
     *
     * @param array $set_data Square option set data
     * @param string $square_set_id Square option set ID
     * @return int|false Attribute ID or false on failure
     */
    public function create_wc_attribute_from_option_set( $set_data, $square_set_id ) {
        $attribute_name = sanitize_title( $set_data['name'] );
        $attribute_label = $set_data['name'];
        
        $this->logger->log( 'variation_handler', 'info', "Creating WooCommerce attribute: {$attribute_label}" );
        
        // Create WooCommerce attribute
        $attribute_id = wc_create_attribute( array(
            'name' => $attribute_label,
            'slug' => $attribute_name,
            'type' => 'select',
            'order_by' => 'menu_order',
            'has_archives' => false
        ) );
        
        if ( is_wp_error( $attribute_id ) ) {
            $this->logger->log( 'variation_handler', 'error', "Failed to create attribute: " . $attribute_id->get_error_message() );
            return false;
        }
        
        // Store Square ID mapping
        update_option( "squarekit_attribute_mapping_{$square_set_id}", $attribute_id );
        
        $this->logger->log( 'variation_handler', 'info', "Created attribute {$attribute_label} with ID {$attribute_id}" );
        
        return $attribute_id;
    }

    /**
     * Import variations with proper attribute creation
     *
     * @param array $variations Square variations
     * @param int $product_id WooCommerce product ID
     * @return bool Success status
     */
    public function import_variations_with_option_sets( $variations, $product_id ) {
        $product = wc_get_product( $product_id );
        if ( ! $product || ! $product->is_type( 'variable' ) ) {
            $this->logger->log( 'variation_handler', 'error', "Product {$product_id} is not a variable product" );
            return false;
        }

        $this->logger->log( 'variation_handler', 'info', "Importing " . count( $variations ) . " variations for product {$product_id}" );

        // First, create attributes from all variations
        $this->create_attributes_from_variations( $variations, $product_id );

        // Then create the actual variations
        return $this->create_wc_variations_from_square( $variations, $product_id );
    }

    /**
     * Create attributes from variations
     *
     * @param array $variations Square variations
     * @param int $product_id WooCommerce product ID
     * @return bool Success status
     */
    public function create_attributes_from_variations( $variations, $product_id ) {
        $this->logger->log( 'variation_handler', 'info', "Creating attributes from variations for product {$product_id}" );

        $attributes = array();
        
        foreach ( $variations as $variation ) {
            if ( empty( $variation['item_variation_data']['item_option_values'] ) ) {
                continue;
            }
            
            foreach ( $variation['item_variation_data']['item_option_values'] as $option_value ) {
                $option_id = $option_value['item_option_id'];
                $value_id = $option_value['item_option_value_id'];
                
                // Get option set name
                $option_set_name = $this->get_option_set_name_by_option_id( $option_id );
                if ( ! $option_set_name ) {
                    continue;
                }
                
                // Get option value name
                $value_name = $this->get_option_value_name_by_id( $value_id );
                if ( ! $value_name ) {
                    continue;
                }
                
                $attribute_slug = sanitize_title( $option_set_name );
                
                if ( ! isset( $attributes[ $attribute_slug ] ) ) {
                    $attributes[ $attribute_slug ] = array(
                        'name' => $option_set_name,
                        'values' => array(),
                        'square_option_id' => $option_id
                    );
                }
                
                if ( ! in_array( $value_name, $attributes[ $attribute_slug ]['values'] ) ) {
                    $attributes[ $attribute_slug ]['values'][] = $value_name;
                }
            }
        }
        
        // Create WooCommerce attributes
        foreach ( $attributes as $attribute_slug => $attribute_data ) {
            $this->create_or_update_product_attribute( $product_id, $attribute_data );
        }
        
        return true;
    }

    /**
     * Create WooCommerce variations from Square variations
     *
     * @param array $variations Square variations
     * @param int $product_id WooCommerce product ID
     * @return bool Success status
     */
    public function create_wc_variations_from_square( $variations, $product_id ) {
        $this->logger->log( 'variation_handler', 'info', "Creating WooCommerce variations for product {$product_id}" );

        $created_count = 0;
        $updated_count = 0;

        foreach ( $variations as $variation ) {
            $square_variation_id = $variation['id'];

            // Check if variation already exists
            $existing_variation_id = $this->find_wc_variation_by_square_id( $square_variation_id );

            if ( $existing_variation_id ) {
                // Update existing variation
                $result = $this->update_wc_variation( $existing_variation_id, $variation );
                if ( $result ) {
                    $updated_count++;
                    $this->update_variation_stats( 'updated' );
                }
            } else {
                // Create new variation
                $result = $this->create_single_wc_variation( $product_id, $variation );
                if ( $result ) {
                    $created_count++;
                    $this->update_variation_stats( 'created' );
                }
            }

            if ( ! $result ) {
                $this->update_variation_stats( 'failed' );
                $this->logger->log( 'variation_handler', 'error', "Failed to process variation {$square_variation_id}" );
            }

            $this->update_variation_stats( 'processed' );
        }

        $this->logger->log( 'variation_handler', 'info', "Variation processing completed: {$created_count} created, {$updated_count} updated" );

        return true;
    }

    /**
     * Create a single WooCommerce variation from Square data
     *
     * @param int $product_id WooCommerce product ID
     * @param array $variation Square variation data
     * @return int|false Variation ID or false on failure
     */
    protected function create_single_wc_variation( $product_id, $variation ) {
        $variation_data = $variation['item_variation_data'];
        $square_variation_id = $variation['id'];

        try {
            // Create new variation
            $wc_variation = new WC_Product_Variation();
            $wc_variation->set_parent_id( $product_id );

            // Set variation attributes
            $attributes = $this->build_variation_attributes( $variation );
            if ( empty( $attributes ) ) {
                $this->logger->log( 'variation_handler', 'warning', "No attributes found for variation {$square_variation_id}" );
                return false;
            }

            $wc_variation->set_attributes( $attributes );

            // Set pricing
            $this->set_variation_pricing( $wc_variation, $variation_data );

            // Set other variation data
            $this->set_variation_data( $wc_variation, $variation );

            // Save variation
            $variation_id = $wc_variation->save();

            if ( ! $variation_id ) {
                $this->logger->log( 'variation_handler', 'error', "Failed to save variation for {$square_variation_id}" );
                return false;
            }

            // Store Square variation ID mapping
            update_post_meta( $variation_id, '_square_variation_id', $square_variation_id );

            $this->logger->log( 'variation_handler', 'info', "Created variation {$variation_id} for Square variation {$square_variation_id}" );

            return $variation_id;

        } catch ( Exception $e ) {
            $this->logger->log( 'variation_handler', 'error', "Exception creating variation: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Update existing WooCommerce variation
     *
     * @param int $variation_id WooCommerce variation ID
     * @param array $variation Square variation data
     * @return bool Success status
     */
    protected function update_wc_variation( $variation_id, $variation ) {
        $variation_data = $variation['item_variation_data'];
        $square_variation_id = $variation['id'];

        try {
            $wc_variation = wc_get_product( $variation_id );
            if ( ! $wc_variation || ! $wc_variation instanceof WC_Product_Variation ) {
                return false;
            }

            // Update attributes
            $attributes = $this->build_variation_attributes( $variation );
            if ( ! empty( $attributes ) ) {
                $wc_variation->set_attributes( $attributes );
            }

            // Update pricing
            $this->set_variation_pricing( $wc_variation, $variation_data );

            // Update other variation data
            $this->set_variation_data( $wc_variation, $variation );

            // Save variation
            $wc_variation->save();

            $this->logger->log( 'variation_handler', 'info', "Updated variation {$variation_id} for Square variation {$square_variation_id}" );

            return true;

        } catch ( Exception $e ) {
            $this->logger->log( 'variation_handler', 'error', "Exception updating variation: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Build variation attributes from Square variation data
     *
     * @param array $variation Square variation data
     * @return array Variation attributes
     */
    protected function build_variation_attributes( $variation ) {
        $attributes = array();

        if ( empty( $variation['item_variation_data']['item_option_values'] ) ) {
            return $attributes;
        }

        foreach ( $variation['item_variation_data']['item_option_values'] as $option_value ) {
            $option_id = $option_value['item_option_id'];
            $value_id = $option_value['item_option_value_id'];

            // Get option set name (attribute name)
            $option_set_name = $this->get_option_set_name_by_option_id( $option_id );
            if ( ! $option_set_name ) {
                continue;
            }

            // Get option value name
            $value_name = $this->get_option_value_name_by_id( $value_id );
            if ( ! $value_name ) {
                continue;
            }

            $attribute_slug = 'pa_' . sanitize_title( $option_set_name );
            $attributes[ $attribute_slug ] = sanitize_title( $value_name );
        }

        return $attributes;
    }

    /**
     * Set variation pricing from Square data
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $variation_data Square variation data
     */
    protected function set_variation_pricing( $variation, $variation_data ) {
        // Regular price
        if ( ! empty( $variation_data['price_money']['amount'] ) ) {
            $price = $variation_data['price_money']['amount'] / 100; // Convert from cents
            $variation->set_regular_price( $price );
            $variation->set_price( $price );
        }

        // Sale price (if available)
        if ( ! empty( $variation_data['sale_price_money']['amount'] ) ) {
            $sale_price = $variation_data['sale_price_money']['amount'] / 100;
            $variation->set_sale_price( $sale_price );
            $variation->set_price( $sale_price );
        }
    }

    /**
     * Set other variation data from Square
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $square_variation Square variation data
     */
    protected function set_variation_data( $variation, $square_variation ) {
        $variation_data = $square_variation['item_variation_data'];

        // SKU
        if ( ! empty( $variation_data['sku'] ) ) {
            $variation->set_sku( $variation_data['sku'] );
        }

        // Stock management
        if ( isset( $variation_data['track_inventory'] ) && $variation_data['track_inventory'] ) {
            $variation->set_manage_stock( true );

            if ( isset( $square_variation['inventory_count'] ) ) {
                $variation->set_stock_quantity( intval( $square_variation['inventory_count'] ) );
            }
        }

        // Weight
        if ( ! empty( $variation_data['measurement_unit_id'] ) ) {
            // Handle weight/measurement units if needed
        }

        // Status
        $variation->set_status( 'publish' );
    }

    /**
     * Import enhanced variations with improved error handling
     *
     * @param array $variations Square variations
     * @param int $product_id WooCommerce product ID
     * @param array $related_objects Related Square objects
     * @return array Import results
     */
    public function import_enhanced_variations( $variations, $product_id, $related_objects = array() ) {
        $this->logger->log( 'variation_handler', 'info', "Starting enhanced variation import for product {$product_id}" );

        $results = array(
            'total' => count( $variations ),
            'created' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => array()
        );

        if ( empty( $variations ) ) {
            return $results;
        }

        // Ensure product is variable
        $product = wc_get_product( $product_id );
        if ( ! $product || ! $product->is_type( 'variable' ) ) {
            $product->set_type( 'variable' );
            $product->save();
        }

        // Process each variation
        foreach ( $variations as $variation ) {
            $square_variation_id = $variation['id'];

            try {
                $existing_variation_id = $this->find_wc_variation_by_square_id( $square_variation_id );

                if ( $existing_variation_id ) {
                    $result = $this->create_enhanced_variation( $product_id, $variation, 'update', $related_objects );
                    if ( $result ) {
                        $results['updated']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][] = "Failed to update variation {$square_variation_id}";
                    }
                } else {
                    $result = $this->create_enhanced_variation( $product_id, $variation, 'create', $related_objects );
                    if ( $result ) {
                        $results['created']++;
                    } else {
                        $results['failed']++;
                        $results['errors'][] = "Failed to create variation {$square_variation_id}";
                    }
                }

            } catch ( Exception $e ) {
                $results['failed']++;
                $results['errors'][] = "Exception processing variation {$square_variation_id}: " . $e->getMessage();
                $this->logger->log( 'variation_handler', 'error', "Exception processing variation: " . $e->getMessage() );
            }
        }

        $this->logger->log( 'variation_handler', 'info', "Enhanced variation import completed", $results );

        return $results;
    }

    /**
     * Create enhanced variation with improved attribute handling
     *
     * @param int $product_id WooCommerce product ID
     * @param array $variation Square variation data
     * @param string $mode 'create' or 'update'
     * @param array $related_objects Related Square objects
     * @return int|false Variation ID or false on failure
     */
    protected function create_enhanced_variation( $product_id, $variation, $mode = 'create', $related_objects = array() ) {
        $variation_data = $variation['item_variation_data'];
        $square_variation_id = $variation['id'];

        try {
            if ( $mode === 'update' ) {
                $variation_id = $this->find_wc_variation_by_square_id( $square_variation_id );
                $wc_variation = wc_get_product( $variation_id );

                if ( ! $wc_variation || ! $wc_variation instanceof WC_Product_Variation ) {
                    return false;
                }
            } else {
                $wc_variation = new WC_Product_Variation();
                $wc_variation->set_parent_id( $product_id );
            }

            // Enhanced attribute handling
            $attributes = $this->build_enhanced_variation_attributes( $variation, $related_objects );
            if ( ! empty( $attributes ) ) {
                $wc_variation->set_attributes( $attributes );
            }

            // Enhanced pricing with currency handling
            $this->set_enhanced_variation_pricing( $wc_variation, $variation_data );

            // Enhanced data setting
            $this->set_enhanced_variation_data( $wc_variation, $variation, $related_objects );

            // Save variation
            $variation_id = $wc_variation->save();

            if ( ! $variation_id ) {
                return false;
            }

            // Store enhanced metadata
            update_post_meta( $variation_id, '_square_variation_id', $square_variation_id );
            update_post_meta( $variation_id, '_square_import_date', current_time( 'mysql' ) );
            update_post_meta( $variation_id, '_square_import_method', 'enhanced' );

            return $variation_id;

        } catch ( Exception $e ) {
            $this->logger->log( 'variation_handler', 'error', "Exception in create_enhanced_variation: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Build enhanced variation attributes with better mapping
     *
     * @param array $variation Square variation data
     * @param array $related_objects Related Square objects
     * @return array Variation attributes
     */
    protected function build_enhanced_variation_attributes( $variation, $related_objects = array() ) {
        $attributes = array();

        if ( empty( $variation['item_variation_data']['item_option_values'] ) ) {
            return $attributes;
        }

        foreach ( $variation['item_variation_data']['item_option_values'] as $option_value ) {
            $option_id = $option_value['item_option_id'];
            $value_id = $option_value['item_option_value_id'];

            // Try to get from related objects first for better performance
            $option_set_name = $this->get_option_set_name_from_related_objects( $option_id, $related_objects );
            if ( ! $option_set_name ) {
                $option_set_name = $this->get_option_set_name_by_option_id( $option_id );
            }

            if ( ! $option_set_name ) {
                continue;
            }

            $value_name = $this->get_option_value_name_from_related_objects( $value_id, $related_objects );
            if ( ! $value_name ) {
                $value_name = $this->get_option_value_name_by_id( $value_id );
            }

            if ( ! $value_name ) {
                continue;
            }

            $attribute_slug = 'pa_' . sanitize_title( $option_set_name );
            $attributes[ $attribute_slug ] = sanitize_title( $value_name );
        }

        return $attributes;
    }

    /**
     * Set enhanced variation pricing with currency support
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $variation_data Square variation data
     */
    protected function set_enhanced_variation_pricing( $variation, $variation_data ) {
        // Regular price with currency handling
        if ( ! empty( $variation_data['price_money']['amount'] ) ) {
            $price = $this->convert_square_price_to_wc( $variation_data['price_money'] );
            $variation->set_regular_price( $price );
            $variation->set_price( $price );
        }

        // Sale price with currency handling
        if ( ! empty( $variation_data['sale_price_money']['amount'] ) ) {
            $sale_price = $this->convert_square_price_to_wc( $variation_data['sale_price_money'] );
            $variation->set_sale_price( $sale_price );
            $variation->set_price( $sale_price );
        }
    }

    /**
     * Set enhanced variation data with additional fields
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $square_variation Square variation data
     * @param array $related_objects Related Square objects
     */
    protected function set_enhanced_variation_data( $variation, $square_variation, $related_objects = array() ) {
        $variation_data = $square_variation['item_variation_data'];

        // Basic data
        $this->set_variation_data( $variation, $square_variation );

        // Enhanced fields
        if ( ! empty( $variation_data['name'] ) ) {
            $variation->set_name( $variation_data['name'] );
        }

        // Description
        if ( ! empty( $variation_data['description'] ) ) {
            $variation->set_description( $variation_data['description'] );
        }

        // Additional metadata
        if ( ! empty( $variation_data['ordinal'] ) ) {
            update_post_meta( $variation->get_id(), '_square_ordinal', $variation_data['ordinal'] );
        }
    }

    /**
     * Find WooCommerce attribute by Square ID
     *
     * @param string $square_set_id Square option set ID
     * @return int|false Attribute ID or false if not found
     */
    protected function find_wc_attribute_by_square_id( $square_set_id ) {
        $attribute_id = get_option( "squarekit_attribute_mapping_{$square_set_id}" );
        return $attribute_id ? intval( $attribute_id ) : false;
    }

    /**
     * Find WooCommerce variation by Square ID
     *
     * @param string $square_variation_id Square variation ID
     * @return int|false Variation ID or false if not found
     */
    protected function find_wc_variation_by_square_id( $square_variation_id ) {
        $variations = get_posts( array(
            'post_type' => 'product_variation',
            'meta_query' => array(
                array(
                    'key' => '_square_variation_id',
                    'value' => $square_variation_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $variations ) ? $variations[0] : false;
    }

    /**
     * Get option set name by option ID
     *
     * @param string $option_id Square option ID
     * @return string|false Option set name or false
     */
    protected function get_option_set_name_by_option_id( $option_id ) {
        $option = $this->square_api->get_catalog_item( $option_id );

        if ( is_wp_error( $option ) || empty( $option['item_option_data']['name'] ) ) {
            return false;
        }

        return $option['item_option_data']['name'];
    }

    /**
     * Get option value name by ID
     *
     * @param string $value_id Square option value ID
     * @return string|false Option value name or false
     */
    protected function get_option_value_name_by_id( $value_id ) {
        $value = $this->square_api->get_catalog_item( $value_id );

        if ( is_wp_error( $value ) || empty( $value['item_option_value_data']['name'] ) ) {
            return false;
        }

        return $value['item_option_value_data']['name'];
    }

    /**
     * Get option set name from related objects (performance optimization)
     *
     * @param string $option_id Square option ID
     * @param array $related_objects Related Square objects
     * @return string|false Option set name or false
     */
    protected function get_option_set_name_from_related_objects( $option_id, $related_objects ) {
        foreach ( $related_objects as $object ) {
            if ( $object['id'] === $option_id && $object['type'] === 'ITEM_OPTION' ) {
                return $object['item_option_data']['name'] ?? false;
            }
        }
        return false;
    }

    /**
     * Get option value name from related objects (performance optimization)
     *
     * @param string $value_id Square option value ID
     * @param array $related_objects Related Square objects
     * @return string|false Option value name or false
     */
    protected function get_option_value_name_from_related_objects( $value_id, $related_objects ) {
        foreach ( $related_objects as $object ) {
            if ( $object['id'] === $value_id && $object['type'] === 'ITEM_OPTION_VAL' ) {
                return $object['item_option_value_data']['name'] ?? false;
            }
        }
        return false;
    }

    /**
     * Add attribute to product
     *
     * @param int $product_id WooCommerce product ID
     * @param int $attribute_id Attribute ID
     * @param array $option_values Option values
     */
    protected function add_attribute_to_product( $product_id, $attribute_id, $option_values ) {
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return;
        }

        $attributes = $product->get_attributes();
        $attribute_name = 'pa_' . wc_attribute_taxonomy_name_by_id( $attribute_id );

        // Create terms for the attribute values
        foreach ( $option_values as $value ) {
            $term = get_term_by( 'name', $value, $attribute_name );
            if ( ! $term ) {
                wp_insert_term( $value, $attribute_name );
            }
        }

        // Add attribute to product
        $attribute = new WC_Product_Attribute();
        $attribute->set_id( $attribute_id );
        $attribute->set_name( $attribute_name );
        $attribute->set_options( $option_values );
        $attribute->set_visible( true );
        $attribute->set_variation( true );

        $attributes[ $attribute_name ] = $attribute;
        $product->set_attributes( $attributes );
        $product->save();
    }

    /**
     * Create or update product attribute
     *
     * @param int $product_id WooCommerce product ID
     * @param array $attribute_data Attribute data
     * @return bool Success status
     */
    protected function create_or_update_product_attribute( $product_id, $attribute_data ) {
        $attribute_name = sanitize_title( $attribute_data['name'] );
        $attribute_label = $attribute_data['name'];

        // Check if global attribute exists
        $attribute_id = wc_attribute_taxonomy_id_by_name( $attribute_name );

        if ( ! $attribute_id ) {
            // Create global attribute
            $attribute_id = wc_create_attribute( array(
                'name' => $attribute_label,
                'slug' => $attribute_name,
                'type' => 'select',
                'order_by' => 'menu_order',
                'has_archives' => false
            ) );

            if ( is_wp_error( $attribute_id ) ) {
                return false;
            }

            $this->update_variation_stats( 'attributes_created' );
        }

        // Add attribute to product
        $this->add_attribute_to_product( $product_id, $attribute_id, $attribute_data['values'] );

        return true;
    }

    /**
     * Convert Square price to WooCommerce price
     *
     * @param array $price_money Square price money object
     * @return float Converted price
     */
    protected function convert_square_price_to_wc( $price_money ) {
        $amount = $price_money['amount'] ?? 0;
        $currency = $price_money['currency'] ?? 'USD';

        // Convert from cents to dollars (or equivalent)
        $price = $amount / 100;

        // Handle currency conversion if needed
        // For now, we'll assume the same currency
        return $price;
    }

    /**
     * Update variation processing statistics
     *
     * @param string $type Statistics type
     */
    protected function update_variation_stats( $type ) {
        if ( isset( $this->variation_stats[ $type ] ) ) {
            $this->variation_stats[ $type ]++;
        }
    }

    /**
     * Get variation processing statistics
     *
     * @return array Variation statistics
     */
    public function get_variation_stats() {
        return $this->variation_stats;
    }

    /**
     * Reset variation processing statistics
     */
    public function reset_variation_stats() {
        $this->variation_stats = array(
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'attributes_created' => 0,
            'failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Export product variations to Square
     *
     * @param int $product_id WooCommerce product ID
     * @return array Export results
     */
    public function export_product_variations( $product_id ) {
        $product = wc_get_product( $product_id );
        if ( ! $product || ! $product->is_type( 'variable' ) ) {
            return array(
                'success' => false,
                'message' => __( 'Product is not a variable product.', 'squarekit' )
            );
        }

        $this->logger->log( 'variation_handler', 'info', "Exporting variations for product {$product_id}" );

        $variations = $product->get_children();
        $exported_variations = array();

        foreach ( $variations as $variation_id ) {
            $variation = wc_get_product( $variation_id );
            if ( ! $variation ) {
                continue;
            }

            $square_data = $this->convert_wc_variation_to_square( $variation );
            if ( $square_data ) {
                $exported_variations[] = $square_data;
            }
        }

        $this->logger->log( 'variation_handler', 'info', "Exported " . count( $exported_variations ) . " variations for product {$product_id}" );

        return array(
            'success' => true,
            'variations' => $exported_variations,
            'count' => count( $exported_variations )
        );
    }

    /**
     * Convert WooCommerce variation to Square format
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @return array|false Square variation data or false
     */
    protected function convert_wc_variation_to_square( $variation ) {
        $square_data = array(
            'type' => 'ITEM_VARIATION',
            'item_variation_data' => array(
                'name' => $variation->get_name(),
                'sku' => $variation->get_sku(),
                'price_money' => array(
                    'amount' => intval( $variation->get_price() * 100 ), // Convert to cents
                    'currency' => get_woocommerce_currency()
                )
            )
        );

        // Add variation attributes as option values
        $attributes = $variation->get_attributes();
        $option_values = array();

        foreach ( $attributes as $attribute_name => $attribute_value ) {
            // Convert WC attribute to Square option value format
            $option_values[] = array(
                'item_option_id' => $this->get_square_option_id_by_attribute( $attribute_name ),
                'item_option_value_id' => $this->get_square_option_value_id( $attribute_name, $attribute_value )
            );
        }

        if ( ! empty( $option_values ) ) {
            $square_data['item_variation_data']['item_option_values'] = $option_values;
        }

        return $square_data;
    }

    /**
     * Get Square option ID by WooCommerce attribute
     *
     * @param string $attribute_name WooCommerce attribute name
     * @return string|false Square option ID or false
     */
    protected function get_square_option_id_by_attribute( $attribute_name ) {
        // This would need to be implemented based on your mapping strategy
        return get_option( "squarekit_wc_to_square_option_{$attribute_name}" );
    }

    /**
     * Get Square option value ID
     *
     * @param string $attribute_name WooCommerce attribute name
     * @param string $attribute_value WooCommerce attribute value
     * @return string|false Square option value ID or false
     */
    protected function get_square_option_value_id( $attribute_name, $attribute_value ) {
        // This would need to be implemented based on your mapping strategy
        return get_option( "squarekit_wc_to_square_value_{$attribute_name}_{$attribute_value}" );
    }

    /**
     * Bulk import variations for multiple products
     *
     * @param array $product_variations Array of product ID => variations data
     * @return array Bulk import results
     */
    public function bulk_import_variations( $product_variations ) {
        $this->logger->log( 'variation_handler', 'info', 'Starting bulk variation import for ' . count( $product_variations ) . ' products' );

        $results = array(
            'total_products' => count( $product_variations ),
            'processed_products' => 0,
            'total_variations' => 0,
            'created_variations' => 0,
            'updated_variations' => 0,
            'failed_variations' => 0,
            'errors' => array()
        );

        foreach ( $product_variations as $product_id => $variations ) {
            try {
                $import_result = $this->import_enhanced_variations( $variations, $product_id );

                $results['processed_products']++;
                $results['total_variations'] += $import_result['total'];
                $results['created_variations'] += $import_result['created'];
                $results['updated_variations'] += $import_result['updated'];
                $results['failed_variations'] += $import_result['failed'];

                if ( ! empty( $import_result['errors'] ) ) {
                    $results['errors'] = array_merge( $results['errors'], $import_result['errors'] );
                }

            } catch ( Exception $e ) {
                $results['errors'][] = "Exception processing product {$product_id}: " . $e->getMessage();
                $this->logger->log( 'variation_handler', 'error', "Exception in bulk import: " . $e->getMessage() );
            }
        }

        $this->logger->log( 'variation_handler', 'info', 'Bulk variation import completed', $results );

        return $results;
    }
}
