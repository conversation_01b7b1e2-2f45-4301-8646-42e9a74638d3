<?php
/**
 * SquareKit Product Sync Module
 *
 * Handles core product import/export operations between Square and WooCommerce.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Product Sync Class
 *
 * Manages product synchronization between Square and WooCommerce.
 * Handles import, export, and bidirectional sync operations.
 */
class SquareKit_Product_Sync {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Image handler instance
     *
     * @var SquareKit_Image_Handler
     */
    private $image_handler;

    /**
     * Variation handler instance
     *
     * @var SquareKit_Variation_Handler
     */
    private $variation_handler;

    /**
     * Import statistics
     *
     * @var array
     */
    private $import_stats = array(
        'processed' => 0,
        'created' => 0,
        'updated' => 0,
        'failed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $this->square_api = new SquareKit_Square_API();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Initialize supporting handlers (lazy loading)
     */
    private function init_handlers() {
        if ( is_null( $this->image_handler ) ) {
            if ( ! class_exists( 'SquareKit_Image_Handler' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-image-handler.php';
            }
            $this->image_handler = new SquareKit_Image_Handler();
        }

        if ( is_null( $this->variation_handler ) ) {
            if ( ! class_exists( 'SquareKit_Variation_Handler' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-variation-handler.php';
            }
            $this->variation_handler = new SquareKit_Variation_Handler();
        }
    }

    /**
     * Import products from Square catalog
     *
     * @param array $options Import options
     * @return array|WP_Error Import results or error
     */
    public function import_products_from_square( $options = array() ) {
        $this->logger->log( 'product_sync', 'info', 'Starting bulk product import from Square' );

        // Reset import statistics
        $this->reset_import_stats();

        // Get default options
        $default_options = array(
            'batch_size' => defined( 'SQUAREKIT_SYNC_BATCH_SIZE' ) ? SQUAREKIT_SYNC_BATCH_SIZE : 100, // Use config or default to 100
            'cursor' => null,
            'include_deleted' => false,
            'include_images' => true,
            'include_variations' => true,
            'include_modifiers' => true,
            'update_existing' => true,
            'max_products' => 0 // 0 = unlimited
        );

        $options = array_merge( $default_options, $options );

        try {
            $all_items = array();
            $cursor = $options['cursor'];
            $total_fetched = 0;
            $batch_count = 0;

            $this->logger->log( 'product_sync', 'info', 'Starting paginated fetch from Square catalog' );

            // Fetch all products using pagination
            do {
                $batch_count++;
                $this->logger->log( 'product_sync', 'info', "Fetching batch {$batch_count} from Square (cursor: " . ($cursor ?: 'initial') . ")" );

                // Build query parameters for this batch
                $query_params = array(
                    'types' => 'ITEM',
                    'limit' => $options['batch_size']
                );

                if ( $cursor ) {
                    $query_params['cursor'] = $cursor;
                }

                if ( $options['include_deleted'] ) {
                    $query_params['include_deleted_objects'] = 'true';
                }

                // Use the new Square API method that returns cursor
                $catalog_response = $this->square_api->get_catalog_with_cursor( $query_params );

                if ( is_wp_error( $catalog_response ) ) {
                    $this->logger->log( 'product_sync', 'error', 'Failed to fetch catalog batch from Square: ' . $catalog_response->get_error_message() );
                    return $catalog_response;
                }

                // Extract items and cursor from response
                $batch_items = isset( $catalog_response['objects'] ) ? $catalog_response['objects'] : array();
                $cursor = isset( $catalog_response['cursor'] ) ? $catalog_response['cursor'] : null;
                $items_in_batch = count( $batch_items );
                $total_fetched += $items_in_batch;

                $this->logger->log( 'product_sync', 'info', "Batch {$batch_count}: Retrieved {$items_in_batch} items (total: {$total_fetched})" . ($cursor ? ", next cursor: " . substr($cursor, 0, 20) . "..." : ", no more pages") );

                // Add items to our collection
                $all_items = array_merge( $all_items, $batch_items );

                // Check if we've hit the max products limit (if set)
                if ( $options['max_products'] > 0 && $total_fetched >= $options['max_products'] ) {
                    $this->logger->log( 'product_sync', 'info', "Reached max products limit of {$options['max_products']}, stopping fetch" );
                    break;
                }

                // Continue if we have a cursor (more pages available)
                if ( ! $cursor ) {
                    $this->logger->log( 'product_sync', 'info', "No more pages available, stopping fetch" );
                    break;
                }

            } while ( $cursor && $items_in_batch > 0 );

            $this->logger->log( 'product_sync', 'info', "Completed fetching {$total_fetched} total items from Square in {$batch_count} batches" );

            // Process each item
            $processed_count = 0;
            foreach ( $all_items as $item ) {
                if ( isset( $item['type'] ) && $item['type'] === 'ITEM' ) {
                    $processed_count++;
                    $this->logger->log( 'product_sync', 'info', "Processing item {$processed_count}/{$total_fetched}: " . ($item['item_data']['name'] ?? 'Unknown') );

                    $result = $this->import_single_product( $item, $options );
                    $this->update_import_stats( $result );
                }
            }

            // Create image map for batch processing
            $image_map = array();
            if ( $options['include_images'] ) {
                $image_map = $this->create_image_map( $all_items );
            }

            $final_stats = $this->get_import_stats();
            $this->logger->log( 'product_sync', 'info', 'Bulk import completed', $final_stats );

            return array(
                'success' => true,
                'stats' => $final_stats,
                'total_fetched' => $total_fetched,
                'total_processed' => $processed_count,
                'cursor' => $cursor,
                'image_map' => $image_map
            );

        } catch ( Exception $e ) {
            $error = new WP_Error( 'import_exception', 'Product import failed: ' . $e->getMessage() );
            $this->logger->log( 'product_sync', 'error', $error->get_error_message() );
            return $error;
        }
    }

    /**
     * Import a single product from Square
     *
     * @param array $item Square catalog item data
     * @param array $options Import options
     * @return array|WP_Error Import result or error
     */
    public function import_single_product( $item, $options = array() ) {
        $square_id = $item['id'] ?? '';
        $this->logger->log( 'product_sync', 'info', "Importing product: {$square_id}" );

        try {
            // Check if product already exists
            $existing_product_id = $this->find_wc_product_by_square_id( $square_id );

            if ( $existing_product_id && ! ( $options['update_existing'] ?? true ) ) {
                $this->logger->log( 'product_sync', 'info', "Product {$square_id} already exists, skipping" );
                return array(
                    'success' => true,
                    'action' => 'skipped',
                    'product_id' => $existing_product_id,
                    'square_id' => $square_id
                );
            }

            // Extract item data
            $item_data = $item['item_data'] ?? array();
            $product_name = $item_data['name'] ?? 'Untitled Product';
            $product_description = $item_data['description'] ?? '';

            // Create or update WooCommerce product
            if ( $existing_product_id ) {
                $product = wc_get_product( $existing_product_id );
                $action = 'updated';
            } else {
                $product = new WC_Product_Simple();
                $action = 'created';
            }

            // Set basic product data
            $product->set_name( $product_name );
            $product->set_description( $product_description );
            $product->set_status( 'publish' );

            // Set Square metadata
            $product->update_meta_data( '_square_item_id', $square_id );
            $product->update_meta_data( '_square_sync_enabled', 'yes' );
            $product->update_meta_data( '_square_last_sync', current_time( 'mysql' ) );

            // Handle variations if present
            $variations = $item_data['variations'] ?? array();
            if ( ! empty( $variations ) ) {
                $this->init_handlers();
                $variation_result = $this->variation_handler->process_variations( $product, $variations, $item );
                
                if ( is_wp_error( $variation_result ) ) {
                    $this->logger->log( 'product_sync', 'warning', "Variation processing failed for {$square_id}: " . $variation_result->get_error_message() );
                }
            } else {
                // Handle simple product pricing
                $this->set_simple_product_pricing( $product, $variations[0] ?? array() );
            }

            // Save the product
            $product_id = $product->save();

            if ( ! $product_id ) {
                throw new Exception( 'Failed to save WooCommerce product' );
            }

            // Handle images if enabled
            if ( $options['include_images'] ?? true ) {
                $this->process_product_images( $product_id, $item_data, $options );
            }

            // Handle categories
            $this->process_product_categories( $product_id, $item_data );

            $this->logger->log( 'product_sync', 'info', "Successfully {$action} product {$square_id} as WC product {$product_id}" );

            return array(
                'success' => true,
                'action' => $action,
                'product_id' => $product_id,
                'square_id' => $square_id,
                'created' => $action === 'created'
            );

        } catch ( Exception $e ) {
            $error = new WP_Error( 'product_import_failed', "Failed to import product {$square_id}: " . $e->getMessage() );
            $this->logger->log( 'product_sync', 'error', $error->get_error_message() );
            return $error;
        }
    }

    /**
     * Sync a WooCommerce product to Square
     *
     * @param int $product_id WooCommerce product ID
     * @param array $options Sync options
     * @return array|WP_Error Sync result or error
     */
    public function sync_product_to_square( $product_id, $options = array() ) {
        $this->logger->log( 'product_sync', 'info', "Syncing WC product {$product_id} to Square" );

        try {
            // Check if WooCommerce to Square sync is enabled globally
            if ( ! $this->settings->is_woo_to_square_sync_enabled() ) {
                $this->logger->log( 'product_sync', 'info', "WooCommerce to Square sync is disabled globally, skipping product {$product_id}" );
                return array( 'success' => true, 'action' => 'skipped', 'reason' => 'global_sync_disabled' );
            }

            $product = wc_get_product( $product_id );

            if ( ! $product ) {
                throw new Exception( 'Product not found' );
            }

            // Check if sync is enabled for this product
            if ( $product->get_meta( '_square_sync_enabled' ) !== 'yes' ) {
                $this->logger->log( 'product_sync', 'info', "Sync disabled for product {$product_id}" );
                return array( 'success' => true, 'action' => 'skipped', 'reason' => 'sync_disabled' );
            }

            // Prepare Square catalog item data
            $catalog_item = $this->prepare_square_catalog_item( $product );

            if ( is_wp_error( $catalog_item ) ) {
                return $catalog_item;
            }

            // Check if product exists in Square
            $square_id = $product->get_meta( '_square_item_id' );

            if ( $square_id ) {
                // Update existing item
                $result = $this->square_api->update_catalog_item( $square_id, $catalog_item );
                $action = 'updated';
            } else {
                // Create new item
                $result = $this->square_api->create_catalog_item( $catalog_item );
                $action = 'created';

                if ( ! is_wp_error( $result ) && isset( $result['catalog_object']['id'] ) ) {
                    $square_id = $result['catalog_object']['id'];
                    $product->update_meta_data( '_square_item_id', $square_id );
                    $product->save_meta_data();
                }
            }

            if ( is_wp_error( $result ) ) {
                $this->logger->log( 'product_sync', 'error', "Failed to sync product {$product_id} to Square: " . $result->get_error_message() );
                return $result;
            }

            // Update sync timestamp
            $product->update_meta_data( '_square_last_sync', current_time( 'mysql' ) );
            $product->save_meta_data();

            $this->logger->log( 'product_sync', 'info', "Successfully {$action} product {$product_id} in Square as {$square_id}" );

            return array(
                'success' => true,
                'action' => $action,
                'product_id' => $product_id,
                'square_id' => $square_id
            );

        } catch ( Exception $e ) {
            $error = new WP_Error( 'product_sync_failed', "Failed to sync product {$product_id}: " . $e->getMessage() );
            $this->logger->log( 'product_sync', 'error', $error->get_error_message() );
            return $error;
        }
    }

    /**
     * Delete a product from Square
     *
     * @param int $product_id WooCommerce product ID
     * @return array|WP_Error Delete result or error
     */
    public function delete_product_from_square( $product_id ) {
        $this->logger->log( 'product_sync', 'info', "Deleting WC product {$product_id} from Square" );

        try {
            // Check if WooCommerce to Square sync is enabled globally
            if ( ! $this->settings->is_woo_to_square_sync_enabled() ) {
                $this->logger->log( 'product_sync', 'info', "WooCommerce to Square sync is disabled globally, skipping deletion of product {$product_id}" );
                return array( 'success' => true, 'action' => 'skipped', 'reason' => 'global_sync_disabled' );
            }

            $product = wc_get_product( $product_id );

            if ( ! $product ) {
                throw new Exception( 'Product not found' );
            }

            $square_id = $product->get_meta( '_square_item_id' );

            if ( ! $square_id ) {
                $this->logger->log( 'product_sync', 'info', "Product {$product_id} has no Square ID, skipping deletion" );
                return array( 'success' => true, 'action' => 'skipped', 'reason' => 'no_square_id' );
            }

            // Delete from Square
            $result = $this->square_api->delete_catalog_item( $square_id );

            if ( is_wp_error( $result ) ) {
                $this->logger->log( 'product_sync', 'error', "Failed to delete product {$square_id} from Square: " . $result->get_error_message() );
                return $result;
            }

            // Clear Square metadata
            $product->delete_meta_data( '_square_item_id' );
            $product->delete_meta_data( '_square_sync_enabled' );
            $product->delete_meta_data( '_square_last_sync' );
            $product->save_meta_data();

            $this->logger->log( 'product_sync', 'info', "Successfully deleted product {$square_id} from Square" );

            return array(
                'success' => true,
                'action' => 'deleted',
                'product_id' => $product_id,
                'square_id' => $square_id
            );

        } catch ( Exception $e ) {
            $error = new WP_Error( 'product_delete_failed', "Failed to delete product {$product_id}: " . $e->getMessage() );
            $this->logger->log( 'product_sync', 'error', $error->get_error_message() );
            return $error;
        }
    }

    /**
     * Prepare Square catalog item data from WooCommerce product
     *
     * @param WC_Product $product WooCommerce product
     * @return array|WP_Error Square catalog item data or error
     */
    protected function prepare_square_catalog_item( $product ) {
        try {
            $catalog_item = array(
                'type' => 'ITEM',
                'id' => '#' . uniqid(),
                'item_data' => array(
                    'name' => $product->get_name(),
                    'description' => $product->get_description(),
                    'category_id' => $this->get_square_category_id( $product ),
                    'variations' => array()
                )
            );

            // Handle product variations
            if ( $product->is_type( 'variable' ) ) {
                $variations = $product->get_children();
                foreach ( $variations as $variation_id ) {
                    $variation = wc_get_product( $variation_id );
                    if ( $variation ) {
                        $catalog_item['item_data']['variations'][] = $this->prepare_square_variation( $variation );
                    }
                }
            } else {
                // Simple product - create single variation
                $catalog_item['item_data']['variations'][] = $this->prepare_square_variation( $product );
            }

            return $catalog_item;

        } catch ( Exception $e ) {
            return new WP_Error( 'catalog_item_preparation_failed', 'Failed to prepare catalog item: ' . $e->getMessage() );
        }
    }

    /**
     * Prepare Square variation data from WooCommerce product
     *
     * @param WC_Product $product WooCommerce product or variation
     * @return array Square variation data
     */
    protected function prepare_square_variation( $product ) {
        $variation_data = array(
            'type' => 'ITEM_VARIATION',
            'id' => '#' . uniqid(),
            'item_variation_data' => array(
                'name' => $product->get_name(),
                'sku' => $product->get_sku(),
                'pricing_type' => 'FIXED_PRICING',
                'price_money' => array(
                    'amount' => intval( $product->get_price() * 100 ), // Convert to cents
                    'currency' => get_woocommerce_currency()
                )
            )
        );

        // Add variation attributes if this is a variation
        if ( $product->is_type( 'variation' ) ) {
            $attributes = $product->get_variation_attributes();
            $variation_data['item_variation_data']['item_option_values'] = array();

            foreach ( $attributes as $attribute => $value ) {
                $variation_data['item_variation_data']['item_option_values'][] = array(
                    'item_option_id' => $this->get_or_create_square_option_id( $attribute ),
                    'item_option_value_id' => $this->get_or_create_square_option_value_id( $attribute, $value )
                );
            }
        }

        return $variation_data;
    }

    /**
     * Find WooCommerce product by Square ID
     *
     * @param string $square_id Square item ID
     * @return int|null WooCommerce product ID or null if not found
     */
    public function find_wc_product_by_square_id( $square_id ) {
        $products = get_posts( array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_square_item_id',
                    'value' => $square_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return ! empty( $products ) ? $products[0] : null;
    }

    /**
     * Set simple product pricing from Square variation data
     *
     * @param WC_Product $product WooCommerce product
     * @param array $variation_data Square variation data
     */
    protected function set_simple_product_pricing( $product, $variation_data ) {
        $variation_data = $variation_data['item_variation_data'] ?? array();
        $price_money = $variation_data['price_money'] ?? array();

        if ( isset( $price_money['amount'] ) ) {
            $price = $price_money['amount'] / 100; // Convert from cents
            $product->set_regular_price( $price );
            $product->set_price( $price );
        }

        // Set SKU if available
        if ( ! empty( $variation_data['sku'] ) ) {
            $product->set_sku( $variation_data['sku'] );
        }
    }

    /**
     * Process product images
     *
     * @param int $product_id WooCommerce product ID
     * @param array $item_data Square item data
     * @param array $options Import options
     */
    protected function process_product_images( $product_id, $item_data, $options ) {
        $this->init_handlers();

        $image_ids = array();
        if ( isset( $item_data['image_ids'] ) && is_array( $item_data['image_ids'] ) ) {
            $image_ids = $item_data['image_ids'];
        }

        if ( ! empty( $image_ids ) ) {
            $result = $this->image_handler->import_product_images( $image_ids, $product_id );

            if ( is_wp_error( $result ) ) {
                $this->logger->log( 'product_sync', 'warning', "Image import failed for product {$product_id}: " . $result->get_error_message() );
            }
        }
    }

    /**
     * Process product categories
     *
     * @param int $product_id WooCommerce product ID
     * @param array $item_data Square item data
     */
    protected function process_product_categories( $product_id, $item_data ) {
        if ( ! empty( $item_data['category_id'] ) ) {
            // This would typically delegate to a Category Manager
            // For now, we'll implement basic category assignment
            $category_id = $this->find_wc_category_by_square_id( $item_data['category_id'] );

            if ( $category_id ) {
                wp_set_object_terms( $product_id, array( $category_id ), 'product_cat' );
            }
        }
    }

    /**
     * Find WooCommerce category by Square ID
     *
     * @param string $square_category_id Square category ID
     * @return int|null WooCommerce category ID or null if not found
     */
    protected function find_wc_category_by_square_id( $square_category_id ) {
        $terms = get_terms( array(
            'taxonomy' => 'product_cat',
            'meta_query' => array(
                array(
                    'key' => '_square_category_id',
                    'value' => $square_category_id,
                    'compare' => '='
                )
            ),
            'hide_empty' => false,
            'number' => 1
        ) );

        return ! empty( $terms ) ? $terms[0]->term_id : null;
    }

    /**
     * Get Square category ID for WooCommerce product
     *
     * @param WC_Product $product WooCommerce product
     * @return string|null Square category ID or null
     */
    protected function get_square_category_id( $product ) {
        $categories = wp_get_post_terms( $product->get_id(), 'product_cat' );

        if ( ! empty( $categories ) ) {
            $category = $categories[0];
            return get_term_meta( $category->term_id, '_square_category_id', true );
        }

        return null;
    }

    /**
     * Get or create Square option ID for attribute
     *
     * @param string $attribute_name Attribute name
     * @return string Square option ID
     */
    protected function get_or_create_square_option_id( $attribute_name ) {
        // This would typically interact with Square API to get/create option sets
        // For now, return a placeholder
        return 'OPTION_' . strtoupper( str_replace( ' ', '_', $attribute_name ) );
    }

    /**
     * Get or create Square option value ID
     *
     * @param string $attribute_name Attribute name
     * @param string $value Attribute value
     * @return string Square option value ID
     */
    protected function get_or_create_square_option_value_id( $attribute_name, $value ) {
        // This would typically interact with Square API to get/create option values
        // For now, return a placeholder
        return 'VALUE_' . strtoupper( str_replace( ' ', '_', $value ) );
    }

    /**
     * Create image map from catalog objects
     *
     * @param array $catalog_objects Square catalog objects
     * @return array Image map
     */
    protected function create_image_map( $catalog_objects ) {
        $image_map = array();

        foreach ( $catalog_objects as $object ) {
            if ( $object['type'] === 'IMAGE' ) {
                $image_map[ $object['id'] ] = $object['image_data']['url'] ?? '';
            }
        }

        return $image_map;
    }

    /**
     * Reset import statistics
     */
    protected function reset_import_stats() {
        $this->import_stats = array(
            'processed' => 0,
            'created' => 0,
            'updated' => 0,
            'failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Update import statistics
     *
     * @param array|WP_Error $result Import result
     */
    protected function update_import_stats( $result ) {
        $this->import_stats['processed']++;

        if ( is_wp_error( $result ) ) {
            $this->import_stats['failed']++;
            $this->import_stats['errors'][] = $result->get_error_message();
        } else {
            if ( $result['action'] === 'created' ) {
                $this->import_stats['created']++;
            } elseif ( $result['action'] === 'updated' ) {
                $this->import_stats['updated']++;
            }
        }
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_stats() {
        return $this->import_stats;
    }

    /**
     * Get products with Square IDs
     *
     * @param array $args Query arguments
     * @return array Product IDs with Square IDs
     */
    public function get_products_with_square_ids( $args = array() ) {
        $default_args = array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_square_item_id',
                    'compare' => 'EXISTS'
                )
            ),
            'posts_per_page' => -1,
            'fields' => 'ids'
        );

        $args = array_merge( $default_args, $args );
        return get_posts( $args );
    }

    /**
     * Bulk sync products to Square
     *
     * @param array $product_ids Product IDs to sync
     * @param array $options Sync options
     * @return array Sync results
     */
    public function bulk_sync_products_to_square( $product_ids, $options = array() ) {
        $this->logger->log( 'product_sync', 'info', 'Starting bulk sync of ' . count( $product_ids ) . ' products to Square' );

        $results = array(
            'total' => count( $product_ids ),
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'results' => array(),
            'errors' => array()
        );

        foreach ( $product_ids as $product_id ) {
            $result = $this->sync_product_to_square( $product_id, $options );

            $results['processed']++;

            if ( is_wp_error( $result ) ) {
                $results['failed']++;
                $results['errors'][] = array(
                    'product_id' => $product_id,
                    'error' => $result->get_error_message()
                );
            } else {
                $results['successful']++;
            }

            $results['results'][] = array(
                'product_id' => $product_id,
                'result' => $result
            );
        }

        $this->logger->log( 'product_sync', 'info', "Bulk sync completed: {$results['successful']} successful, {$results['failed']} failed" );

        return $results;
    }
}
