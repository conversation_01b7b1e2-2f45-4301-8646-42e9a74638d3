<?php
/**
 * SquareKit Sync Coordinator Module
 *
 * Handles coordination and orchestration of sync operations between Square and WooCommerce.
 * Includes workflow management, dependency handling, and conflict resolution.
 * Extracted from the monolithic WooCommerce integration class for better maintainability.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Sync Coordinator Class
 *
 * Manages coordination and orchestration of sync operations.
 * Handles workflow management, dependency resolution, and conflict management.
 */
class SquareKit_Sync_Coordinator {

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Database instance
     *
     * @var SquareKit_DB
     */
    private $db;

    /**
     * Sync modules instances
     *
     * @var array
     */
    private $sync_modules = array();

    /**
     * Coordination statistics
     *
     * @var array
     */
    private $coordination_stats = array(
        'operations_coordinated' => 0,
        'conflicts_resolved' => 0,
        'dependencies_managed' => 0,
        'workflows_completed' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
        $this->init_sync_modules();
        $this->init_hooks();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        if ( ! class_exists( 'SquareKit_DB' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-db.php';
        }

        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
        $this->db = new SquareKit_DB();
    }

    /**
     * Initialize sync modules
     */
    private function init_sync_modules() {
        // Initialize all sync modules for coordination
        $module_classes = array(
            'product' => 'SquareKit_Product_Sync',
            'inventory' => 'SquareKit_Inventory_Sync',
            'order' => 'SquareKit_Order_Sync',
            'customer' => 'SquareKit_Customer_Sync',
            'category' => 'SquareKit_Category_Sync',
            'webhook' => 'SquareKit_Webhook_Handler'
        );

        foreach ( $module_classes as $key => $class_name ) {
            if ( class_exists( $class_name ) ) {
                try {
                    $this->sync_modules[ $key ] = new $class_name();
                } catch ( Exception $e ) {
                    $this->logger->log( 'sync_coordinator', 'error', "Failed to initialize {$class_name}: " . $e->getMessage() );
                }
            }
        }
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // AJAX handlers for conflict resolution
        add_action( 'wp_ajax_squarekit_resolve_inventory_conflict', array( $this, 'ajax_resolve_inventory_conflict' ) );
        add_action( 'wp_ajax_squarekit_sync_inventory_manual', array( $this, 'ajax_sync_inventory_manual' ) );
        add_action( 'wp_ajax_squarekit_coordinate_full_sync', array( $this, 'ajax_coordinate_full_sync' ) );
    }

    /**
     * Coordinate full synchronization workflow
     *
     * @param array $options Sync options
     * @return array Coordination results
     */
    public function coordinate_full_sync( $options = array() ) {
        $this->logger->log( 'sync_coordinator', 'info', 'Starting full sync coordination' );

        $results = array(
            'success' => true,
            'modules_synced' => array(),
            'total_processed' => 0,
            'total_errors' => 0,
            'workflow_time' => 0
        );

        $start_time = microtime( true );

        try {
            // Step 1: Sync categories first (dependencies)
            if ( isset( $this->sync_modules['category'] ) && $this->should_sync_module( 'categories', $options ) ) {
                $category_result = $this->sync_modules['category']->sync_categories( $options );
                $results['modules_synced']['categories'] = $category_result;
                $this->update_coordination_stats( 'dependencies_managed' );
            }

            // Step 2: Sync products (depends on categories)
            if ( isset( $this->sync_modules['product'] ) && $this->should_sync_module( 'products', $options ) ) {
                $product_result = $this->sync_modules['product']->sync_products( $options );
                $results['modules_synced']['products'] = $product_result;
            }

            // Step 3: Sync inventory (depends on products)
            if ( isset( $this->sync_modules['inventory'] ) && $this->should_sync_module( 'inventory', $options ) ) {
                $inventory_result = $this->sync_modules['inventory']->sync_inventory( $options );
                $results['modules_synced']['inventory'] = $inventory_result;
            }

            // Step 4: Sync customers
            if ( isset( $this->sync_modules['customer'] ) && $this->should_sync_module( 'customers', $options ) ) {
                $customer_result = $this->sync_modules['customer']->sync_customers( $options );
                $results['modules_synced']['customers'] = $customer_result;
            }

            // Step 5: Sync orders (depends on products and customers)
            if ( isset( $this->sync_modules['order'] ) && $this->should_sync_module( 'orders', $options ) ) {
                $order_result = $this->sync_modules['order']->sync_orders( $options );
                $results['modules_synced']['orders'] = $order_result;
            }

            // Calculate totals
            foreach ( $results['modules_synced'] as $module_result ) {
                if ( isset( $module_result['processed'] ) ) {
                    $results['total_processed'] += $module_result['processed'];
                }
                if ( isset( $module_result['failed'] ) ) {
                    $results['total_errors'] += $module_result['failed'];
                }
            }

            $results['workflow_time'] = microtime( true ) - $start_time;
            $this->update_coordination_stats( 'workflows_completed' );

            $this->logger->log( 'sync_coordinator', 'info', 'Full sync coordination completed', $results );

        } catch ( Exception $e ) {
            $results['success'] = false;
            $results['error'] = $e->getMessage();
            $this->logger->log( 'sync_coordinator', 'error', 'Full sync coordination failed: ' . $e->getMessage() );
        }

        $this->update_coordination_stats( 'operations_coordinated' );
        return $results;
    }

    /**
     * Coordinate partial synchronization workflow
     *
     * @param string $module_type Module type to sync
     * @param array $options Sync options
     * @return array Coordination results
     */
    public function coordinate_partial_sync( $module_type, $options = array() ) {
        $this->logger->log( 'sync_coordinator', 'info', "Starting partial sync coordination for {$module_type}" );

        if ( ! isset( $this->sync_modules[ $module_type ] ) ) {
            return array(
                'success' => false,
                'error' => "Sync module '{$module_type}' not available"
            );
        }

        try {
            // Handle dependencies
            $dependencies = $this->get_module_dependencies( $module_type );
            foreach ( $dependencies as $dependency ) {
                if ( isset( $this->sync_modules[ $dependency ] ) ) {
                    $this->logger->log( 'sync_coordinator', 'info', "Syncing dependency: {$dependency}" );
                    $this->sync_modules[ $dependency ]->sync_categories( $options );
                    $this->update_coordination_stats( 'dependencies_managed' );
                }
            }

            // Sync the requested module
            $method_name = "sync_{$module_type}";
            if ( method_exists( $this->sync_modules[ $module_type ], $method_name ) ) {
                $result = $this->sync_modules[ $module_type ]->$method_name( $options );
            } else {
                // Fallback to generic sync method
                $result = $this->sync_modules[ $module_type ]->sync_categories( $options );
            }

            $this->update_coordination_stats( 'operations_coordinated' );
            return $result;

        } catch ( Exception $e ) {
            $this->logger->log( 'sync_coordinator', 'error', "Partial sync coordination failed for {$module_type}: " . $e->getMessage() );
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Handle inventory conflict resolution
     *
     * @param int $product_id Product ID
     * @param array $conflict Conflict data
     * @param string $resolution Resolution strategy
     * @return bool Success status
     */
    public function resolve_inventory_conflict( $product_id, $conflict, $resolution ) {
        $this->logger->log( 'sync_coordinator', 'info', "Resolving inventory conflict for product {$product_id} with strategy: {$resolution}" );

        try {
            switch ( $resolution ) {
                case 'wc_wins':
                    // WooCommerce quantity takes precedence
                    if ( isset( $this->sync_modules['inventory'] ) ) {
                        $result = $this->sync_modules['inventory']->sync_inventory_to_square( $product_id );
                    }
                    break;

                case 'square_wins':
                    // Square quantity takes precedence
                    if ( isset( $this->sync_modules['inventory'] ) ) {
                        $result = $this->sync_modules['inventory']->update_inventory_from_square( 
                            $conflict['square_catalog_id'], 
                            $conflict['square_quantity'] 
                        );
                    }
                    break;

                case 'manual':
                    // Manual resolution - log for admin review
                    $this->create_inventory_conflict_notification( $product_id, $conflict );
                    $result = true;
                    break;

                default:
                    $result = false;
            }

            if ( $result ) {
                $this->update_coordination_stats( 'conflicts_resolved' );
                $this->logger->log( 'sync_coordinator', 'info', "Inventory conflict resolved for product {$product_id}" );
            }

            return $result;

        } catch ( Exception $e ) {
            $this->logger->log( 'sync_coordinator', 'error', "Failed to resolve inventory conflict: " . $e->getMessage() );
            return false;
        }
    }

    /**
     * Handle SKU conflict resolution
     *
     * @param string $original_sku Original SKU
     * @param int $product_id Product ID
     * @return string Resolved SKU
     */
    public function resolve_sku_conflict( $original_sku, $product_id = 0 ) {
        $conflict_resolution = $this->settings->get( 'sku_conflict_resolution', 'append_id' );
        
        switch ( $conflict_resolution ) {
            case 'skip':
                // Return original SKU but log the conflict
                $this->db->add_log(
                    'sku_conflict',
                    sprintf( 'SKU conflict detected for product %d: %s', $product_id, $original_sku ),
                    array( 'product_id' => $product_id, 'sku' => $original_sku )
                );
                return $original_sku;
                
            case 'append_id':
                // Append product ID to make it unique
                $new_sku = $original_sku . '-' . $product_id;
                $this->logger->log( 'sync_coordinator', 'info', "SKU conflict resolved by appending ID: {$original_sku} -> {$new_sku}" );
                return $new_sku;
                
            case 'append_timestamp':
                // Append timestamp to make it unique
                $new_sku = $original_sku . '-' . time();
                $this->logger->log( 'sync_coordinator', 'info', "SKU conflict resolved by appending timestamp: {$original_sku} -> {$new_sku}" );
                return $new_sku;
                
            case 'generate_new':
                // Generate completely new SKU
                $new_sku = 'SK-' . wp_generate_uuid4();
                $this->logger->log( 'sync_coordinator', 'info', "SKU conflict resolved by generating new SKU: {$original_sku} -> {$new_sku}" );
                return $new_sku;
                
            default:
                return $original_sku;
        }
    }

    /**
     * Get module dependencies
     *
     * @param string $module_type Module type
     * @return array Dependencies
     */
    protected function get_module_dependencies( $module_type ) {
        $dependencies = array(
            'product' => array( 'category' ),
            'inventory' => array( 'product' ),
            'order' => array( 'product', 'customer' ),
            'customer' => array(),
            'category' => array(),
            'webhook' => array()
        );

        return isset( $dependencies[ $module_type ] ) ? $dependencies[ $module_type ] : array();
    }

    /**
     * Check if module should be synced based on options
     *
     * @param string $module_type Module type
     * @param array $options Sync options
     * @return bool Should sync
     */
    protected function should_sync_module( $module_type, $options ) {
        // Check if specific modules are requested
        if ( isset( $options['modules'] ) && is_array( $options['modules'] ) ) {
            return in_array( $module_type, $options['modules'], true );
        }

        // Check settings for module enablement
        $setting_key = "sync_{$module_type}";
        return $this->settings->get( $setting_key, true );
    }

    /**
     * Create inventory conflict notification
     *
     * @param int $product_id Product ID
     * @param array $conflict Conflict data
     */
    protected function create_inventory_conflict_notification( $product_id, $conflict ) {
        $conflicts = get_option( 'squarekit_inventory_conflicts', array() );

        $conflicts[] = array(
            'product_id' => $product_id,
            'wc_quantity' => $conflict['wc_quantity'],
            'square_quantity' => $conflict['square_quantity'],
            'square_catalog_id' => $conflict['square_catalog_id'],
            'detected_at' => current_time( 'mysql' ),
            'resolved' => false
        );

        update_option( 'squarekit_inventory_conflicts', $conflicts );

        $this->logger->log( 'sync_coordinator', 'warning', "Inventory conflict created for product {$product_id}", $conflict );
    }

    /**
     * AJAX handler: Resolve inventory conflict
     */
    public function ajax_resolve_inventory_conflict() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ) );
        }

        check_ajax_referer( 'squarekit_admin', 'nonce' );

        $conflict_index = isset( $_POST['conflict_index'] ) ? intval( $_POST['conflict_index'] ) : -1;
        $resolution = isset( $_POST['resolution'] ) ? sanitize_text_field( $_POST['resolution'] ) : '';

        if ( $conflict_index < 0 || empty( $resolution ) ) {
            wp_send_json_error( array( 'message' => __( 'Invalid parameters.', 'squarekit' ) ) );
        }

        $conflicts = get_option( 'squarekit_inventory_conflicts', array() );
        if ( ! isset( $conflicts[ $conflict_index ] ) ) {
            wp_send_json_error( array( 'message' => __( 'Conflict not found.', 'squarekit' ) ) );
        }

        $conflict = $conflicts[ $conflict_index ];
        $product_id = $conflict['product_id'];

        $result = $this->resolve_inventory_conflict( $product_id, $conflict, $resolution );

        if ( $result ) {
            // Mark conflict as resolved
            $conflicts[ $conflict_index ]['resolved'] = true;
            $conflicts[ $conflict_index ]['resolved_at'] = current_time( 'mysql' );
            $conflicts[ $conflict_index ]['resolution'] = $resolution;
            update_option( 'squarekit_inventory_conflicts', $conflicts );

            wp_send_json_success( array( 'message' => __( 'Conflict resolved successfully.', 'squarekit' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to resolve conflict.', 'squarekit' ) ) );
        }
    }

    /**
     * AJAX handler: Manual inventory sync
     */
    public function ajax_sync_inventory_manual() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ) );
        }

        check_ajax_referer( 'squarekit_admin', 'nonce' );

        $product_id = isset( $_POST['product_id'] ) ? intval( $_POST['product_id'] ) : 0;
        $direction = isset( $_POST['direction'] ) ? sanitize_text_field( $_POST['direction'] ) : '';

        if ( ! $product_id || ! in_array( $direction, array( 'wc_to_square', 'square_to_wc' ), true ) ) {
            wp_send_json_error( array( 'message' => __( 'Invalid parameters.', 'squarekit' ) ) );
        }

        if ( ! isset( $this->sync_modules['inventory'] ) ) {
            wp_send_json_error( array( 'message' => __( 'Inventory sync module not available.', 'squarekit' ) ) );
        }

        switch ( $direction ) {
            case 'wc_to_square':
                $result = $this->sync_modules['inventory']->sync_inventory_to_square( $product_id );
                if ( $result ) {
                    wp_send_json_success( array( 'message' => __( 'Inventory synced from WooCommerce to Square.', 'squarekit' ) ) );
                } else {
                    wp_send_json_error( array( 'message' => __( 'Failed to sync inventory to Square.', 'squarekit' ) ) );
                }
                break;

            case 'square_to_wc':
                $result = $this->sync_modules['inventory']->import_inventory_from_square();
                if ( $result['success'] ) {
                    wp_send_json_success( array( 'message' => __( 'Inventory synced from Square to WooCommerce.', 'squarekit' ) ) );
                } else {
                    wp_send_json_error( array( 'message' => $result['message'] ) );
                }
                break;

            default:
                wp_send_json_error( array( 'message' => __( 'Invalid sync direction.', 'squarekit' ) ) );
        }
    }

    /**
     * AJAX handler: Coordinate full sync
     */
    public function ajax_coordinate_full_sync() {
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'squarekit' ) ) );
        }

        check_ajax_referer( 'squarekit_admin', 'nonce' );

        $options = isset( $_POST['options'] ) ? json_decode( stripslashes( $_POST['options'] ), true ) : array();

        $result = $this->coordinate_full_sync( $options );

        if ( $result['success'] ) {
            wp_send_json_success( $result );
        } else {
            wp_send_json_error( $result );
        }
    }

    /**
     * Monitor sync progress across modules
     *
     * @return array Progress information
     */
    public function monitor_sync_progress() {
        $progress = array(
            'modules' => array(),
            'overall_progress' => 0,
            'active_operations' => 0
        );

        foreach ( $this->sync_modules as $module_name => $module ) {
            if ( method_exists( $module, 'get_sync_status' ) ) {
                $progress['modules'][ $module_name ] = $module->get_sync_status();
            }
        }

        // Calculate overall progress
        $total_modules = count( $progress['modules'] );
        $completed_modules = 0;

        foreach ( $progress['modules'] as $module_progress ) {
            if ( isset( $module_progress['sync_percentage'] ) && $module_progress['sync_percentage'] >= 100 ) {
                $completed_modules++;
            }
        }

        $progress['overall_progress'] = $total_modules > 0 ? round( ( $completed_modules / $total_modules ) * 100, 1 ) : 0;

        return $progress;
    }

    /**
     * Update coordination statistics
     *
     * @param string $type Statistics type
     */
    protected function update_coordination_stats( $type ) {
        if ( isset( $this->coordination_stats[ $type ] ) ) {
            $this->coordination_stats[ $type ]++;
        }
    }

    /**
     * Get coordination statistics
     *
     * @return array Coordination statistics
     */
    public function get_coordination_stats() {
        return $this->coordination_stats;
    }

    /**
     * Reset coordination statistics
     */
    public function reset_coordination_stats() {
        $this->coordination_stats = array(
            'operations_coordinated' => 0,
            'conflicts_resolved' => 0,
            'dependencies_managed' => 0,
            'workflows_completed' => 0,
            'errors' => array()
        );
    }

    /**
     * Get sync coordinator status for dashboard
     *
     * @return array Coordinator status information
     */
    public function get_coordinator_status() {
        return array(
            'modules_available' => count( $this->sync_modules ),
            'modules_loaded' => array_keys( $this->sync_modules ),
            'coordination_stats' => $this->coordination_stats,
            'sync_progress' => $this->monitor_sync_progress(),
            'last_coordination' => get_option( 'squarekit_last_coordination', __( 'Never', 'squarekit' ) )
        );
    }

    /**
     * Validate sync dependencies
     *
     * @param array $modules Modules to validate
     * @return array Validation results
     */
    public function validate_sync_dependencies( $modules ) {
        $validation = array(
            'valid' => true,
            'missing_dependencies' => array(),
            'available_modules' => array_keys( $this->sync_modules )
        );

        foreach ( $modules as $module ) {
            $dependencies = $this->get_module_dependencies( $module );
            foreach ( $dependencies as $dependency ) {
                if ( ! isset( $this->sync_modules[ $dependency ] ) ) {
                    $validation['valid'] = false;
                    $validation['missing_dependencies'][] = array(
                        'module' => $module,
                        'missing_dependency' => $dependency
                    );
                }
            }
        }

        return $validation;
    }

    /**
     * Execute coordinated sync workflow
     *
     * @param array $workflow Workflow configuration
     * @return array Execution results
     */
    public function execute_sync_workflow( $workflow ) {
        $this->logger->log( 'sync_coordinator', 'info', 'Executing coordinated sync workflow' );

        $results = array(
            'success' => true,
            'steps_completed' => 0,
            'total_steps' => count( $workflow['steps'] ),
            'step_results' => array()
        );

        foreach ( $workflow['steps'] as $step ) {
            try {
                $step_result = $this->execute_workflow_step( $step );
                $results['step_results'][] = $step_result;

                if ( $step_result['success'] ) {
                    $results['steps_completed']++;
                } else {
                    $results['success'] = false;
                    break; // Stop on first failure
                }

            } catch ( Exception $e ) {
                $results['success'] = false;
                $results['step_results'][] = array(
                    'success' => false,
                    'error' => $e->getMessage()
                );
                break;
            }
        }

        $this->update_coordination_stats( 'workflows_completed' );
        return $results;
    }

    /**
     * Execute individual workflow step
     *
     * @param array $step Step configuration
     * @return array Step results
     */
    protected function execute_workflow_step( $step ) {
        $module_type = $step['module'];
        $action = $step['action'];
        $options = isset( $step['options'] ) ? $step['options'] : array();

        if ( ! isset( $this->sync_modules[ $module_type ] ) ) {
            return array(
                'success' => false,
                'error' => "Module '{$module_type}' not available"
            );
        }

        $module = $this->sync_modules[ $module_type ];

        if ( ! method_exists( $module, $action ) ) {
            return array(
                'success' => false,
                'error' => "Action '{$action}' not available in module '{$module_type}'"
            );
        }

        try {
            $result = $module->$action( $options );
            return array(
                'success' => true,
                'result' => $result
            );
        } catch ( Exception $e ) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
}
