# SquareKit Sync Modules

This directory contains the refactored sync modules extracted from the monolithic WooCommerce integration class.

## 📁 **Module Structure**

```
includes/sync/
├── class-squarekit-product-sync.php        # Product import/export/sync
├── class-squarekit-inventory-sync.php      # Inventory management
├── class-squarekit-image-handler.php       # Image processing
├── class-squarekit-variation-handler.php   # Variations & attributes
├── class-squarekit-order-sync.php          # Order synchronization
├── class-squarekit-customer-sync.php       # Customer management
├── class-squarekit-category-sku-manager.php # Categories & SKU handling
├── class-squarekit-modifier-handler.php    # Modifier management
├── class-squarekit-sync-coordinator.php    # Orchestration layer
└── README.md                               # This file
```

## 🎯 **Module Responsibilities**

### **Product Sync** (~1,200 lines)
- Core product import/export operations
- Product synchronization with Square
- Basic product CRUD operations
- Product data transformation

### **Inventory Sync** (~900 lines)
- Stock level synchronization
- Inventory conflict detection and resolution
- Order-based inventory updates
- Manual inventory sync operations

### **Image Handler** (~800 lines)
- Image import from URLs and data URLs
- Image optimization and processing
- Gallery management
- Filename generation and sanitization

### **Variation Handler** (~700 lines)
- Product variations import/export
- Attribute creation and management
- Option sets handling
- Enhanced variation processing

### **Order Sync** (~600 lines)
- Order synchronization between platforms
- Order status management
- Fulfillment handling
- Webhook processing

### **Customer Sync** (~500 lines)
- Customer data synchronization
- Customer role management
- Customer data mapping and transformation

### **Category & SKU Manager** (~400 lines)
- Category import and hierarchy management
- SKU validation and conflict resolution
- Category-product associations

### **Modifier Handler** (~400 lines)
- Product modifier display and management
- Cart and checkout integration
- Modifier import/export operations

### **Sync Coordinator** (~300 lines)
- Orchestrates all sync operations
- Manages module dependencies
- Provides unified interface
- Handles WordPress integration

## 🔧 **Usage Pattern**

### **Direct Module Usage**
```php
// Use specific module directly
$product_sync = new SquareKit_Product_Sync();
$result = $product_sync->import_product_from_square($item_data);
```

### **Through Coordinator**
```php
// Use coordinator for complex operations
$coordinator = new SquareKit_Sync_Coordinator();
$result = $coordinator->sync_product($product_id);
```

### **Through Main Integration Class**
```php
// Use main class (delegates to modules)
$wc_integration = new SquareKit_WooCommerce();
$result = $wc_integration->import_product_unified($input);
```

## 🏗️ **Architecture Benefits**

1. **Single Responsibility**: Each module has one clear purpose
2. **Loose Coupling**: Modules can work independently
3. **High Cohesion**: Related functionality grouped together
4. **Testability**: Each module can be unit tested
5. **Maintainability**: Easier to locate and fix issues
6. **Scalability**: New features can be added to specific modules

## 📋 **Dependencies**

### **Common Dependencies**
All modules depend on:
- `SquareKit_Settings`
- `SquareKit_Logger`
- `SquareKit_Square_API`

### **Module-Specific Dependencies**
- **Product Sync**: Image Handler, Variation Handler
- **Inventory Sync**: Product Sync (for product lookups)
- **Order Sync**: Customer Sync, Product Sync
- **Coordinator**: All other modules

## 🧪 **Testing Strategy**

### **Unit Testing**
Each module should have comprehensive unit tests:
```
tests/sync/
├── test-product-sync.php
├── test-inventory-sync.php
├── test-image-handler.php
└── ...
```

### **Integration Testing**
Test module interactions:
```
tests/integration/
├── test-product-inventory-sync.php
├── test-order-customer-sync.php
└── ...
```

## 🚀 **Migration Strategy**

### **Phase 1**: Extract Core Modules
1. Product Sync (highest priority)
2. Inventory Sync (complex logic)
3. Image Handler (independent)

### **Phase 2**: Extract Supporting Modules
4. Variation Handler
5. Order Sync
6. Customer Sync

### **Phase 3**: Extract Utility Modules
7. Category/SKU Manager
8. Modifier Handler
9. Sync Coordinator

### **Phase 4**: Refactor Main Class
- Update main class to use modules
- Maintain backward compatibility
- Add deprecation notices for old methods

## 📝 **Coding Standards**

### **Class Naming**
- Prefix: `SquareKit_`
- Descriptive names: `SquareKit_Product_Sync`
- Consistent with existing codebase

### **Method Naming**
- Public methods: Clear, descriptive names
- Private methods: Prefixed with underscore if needed
- Consistent parameter naming

### **Documentation**
- PHPDoc for all public methods
- Inline comments for complex logic
- Usage examples in class headers

## 🔄 **Backward Compatibility**

The refactoring maintains 100% backward compatibility:

### **Existing Code Continues to Work**
```php
// This still works exactly as before
$wc = new SquareKit_WooCommerce();
$result = $wc->import_product_from_square($item_data, $image_map);
```

### **New Modular Approach Available**
```php
// New modular approach also available
$product_sync = new SquareKit_Product_Sync();
$result = $product_sync->import_product($item_data);
```

### **Unified Bridge Interface**
```php
// Unified interface works with both approaches
$wc = new SquareKit_WooCommerce();
$result = $wc->import_product_unified($input); // Auto-detects best method
```

---

**This modular architecture transforms the 5,435-line monolithic file into 9 focused, maintainable modules while preserving all existing functionality!** 🎯
