<?php
/**
 * Bulk Operations Manager for Square Kit
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Bulk Operations Manager class
 *
 * @since 1.0.0
 */
class SquareKit_Bulk_Operations {

    /**
     * Settings instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_Settings
     */
    protected $settings;

    /**
     * DB instance
     *
     * @since 1.0.0
     * @access protected
     * @var SquareKit_DB
     */
    protected $db;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->settings = new SquareKit_Settings();
        $this->db = new SquareKit_DB();
    }

    /**
     * Start a new bulk operation
     *
     * @since 1.0.0
     * @param string $operation_type Type of operation
     * @param array $operation_data Operation-specific data
     * @return int|WP_Error Operation ID or error
     */
    public function start_operation( $operation_type, $operation_data = array() ) {
        global $wpdb;

        // Initialize logger for debugging
        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }
        $logger = SquareKit_Logger::get_instance();

        $logger->log( 'bulk_operation', 'info', 'Starting bulk operation', array(
            'operation_type' => $operation_type,
            'operation_data' => $operation_data
        ) );

        try {
            $user_id = get_current_user_id();
            if ( ! $user_id ) {
                $logger->log( 'bulk_operation', 'error', 'No user logged in' );
                return new WP_Error( 'no_user', __( 'User not logged in.', 'squarekit' ) );
            }

            $logger->log( 'bulk_operation', 'info', 'User ID: ' . $user_id );

            // Validate operation type
            $valid_types = array(
                'import_products',
                'export_products',
                'sync_inventory',
                'import_customers',
                'export_customers',
                'bulk_status_update',
                'bulk_category_update',
                'bulk_attribute_update'
            );

            if ( ! in_array( $operation_type, $valid_types ) ) {
                $logger->log( 'bulk_operation', 'error', 'Invalid operation type: ' . $operation_type );
                return new WP_Error( 'invalid_operation', __( 'Invalid operation type.', 'squarekit' ) );
            }

            $logger->log( 'bulk_operation', 'info', 'Operation type validated: ' . $operation_type );

            // Check if database table exists
            $table_name = $wpdb->prefix . 'squarekit_bulk_operations';
            $table_exists = $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) ) === $table_name;

            if ( ! $table_exists ) {
                $logger->log( 'bulk_operation', 'error', 'Database table does not exist: ' . $table_name );
                return new WP_Error( 'table_missing', __( 'Bulk operations table does not exist.', 'squarekit' ) );
            }

            $logger->log( 'bulk_operation', 'info', 'Database table exists: ' . $table_name );

            // Calculate total items
            $logger->log( 'bulk_operation', 'info', 'Calculating total items for operation type: ' . $operation_type );
            $total_items = $this->calculate_total_items( $operation_type, $operation_data );

            $logger->log( 'bulk_operation', 'info', 'Total items calculated: ' . $total_items );

            // Create operation record
            $insert_data = array(
                'operation_type' => $operation_type,
                'operation_status' => 'pending',
                'total_items' => $total_items,
                'processed_items' => 0,
                'successful_items' => 0,
                'failed_items' => 0,
                'progress_percentage' => 0.00,
                'operation_data' => wp_json_encode( $operation_data ),
                'created_by' => $user_id
            );

            $logger->log( 'bulk_operation', 'info', 'Inserting operation record', $insert_data );

            $result = $wpdb->insert(
                $table_name,
                $insert_data,
                array( '%s', '%s', '%d', '%d', '%d', '%d', '%f', '%s', '%d' )
            );

            if ( $result === false ) {
                $logger->log( 'bulk_operation', 'error', 'Database insert failed', array(
                    'wpdb_error' => $wpdb->last_error,
                    'wpdb_query' => $wpdb->last_query
                ) );
                return new WP_Error( 'db_error', __( 'Failed to create operation record.', 'squarekit' ) . ' Error: ' . $wpdb->last_error );
            }

            $operation_id = $wpdb->insert_id;

            // Log operation start (already logged above with detailed logger)

            $logger->log( 'bulk_operation', 'info', sprintf(
                'Started bulk operation: %s (ID: %d, Total items: %d)',
                $operation_type,
                $operation_id,
                $total_items
            ) );

            return $operation_id;

        } catch ( Exception $e ) {
            $logger->log( 'bulk_operation', 'error', 'Exception in start_operation: ' . $e->getMessage(), array(
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ) );
            return new WP_Error( 'exception', 'Exception: ' . $e->getMessage() );
        } catch ( Error $e ) {
            $logger->log( 'bulk_operation', 'error', 'Fatal error in start_operation: ' . $e->getMessage(), array(
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ) );
            return new WP_Error( 'fatal_error', 'Fatal error: ' . $e->getMessage() );
        }
    }

    /**
     * Calculate total items for operation
     *
     * @since 1.0.0
     * @param string $operation_type Operation type
     * @param array $operation_data Operation data
     * @return int Total items
     */
    protected function calculate_total_items( $operation_type, $operation_data ) {
        // Initialize logger for debugging
        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }
        $logger = SquareKit_Logger::get_instance();

        $logger->log( 'bulk_operation', 'info', 'Calculating total items for operation: ' . $operation_type );

        try {
            switch ( $operation_type ) {
                case 'import_products':
                    // Get total products from Square using unlimited pagination
                    $logger->log( 'bulk_operation', 'info', 'Loading SquareKit_Square_API for unlimited product count' );

                    if ( ! class_exists( 'SquareKit_Square_API' ) ) {
                        require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
                    }

                    if ( ! class_exists( 'SquareKit_Square_API' ) ) {
                        $logger->log( 'bulk_operation', 'error', 'Failed to load SquareKit_Square_API class' );
                        return 0;
                    }

                    $logger->log( 'bulk_operation', 'info', 'Creating SquareKit_Square_API instance' );
                    $square_api = new \SquareKit_Square_API();

                    // Count all products using pagination to get accurate total
                    $total_count = 0;
                    $cursor = null;
                    $batch_count = 0;
                    $batch_size = defined( 'SQUAREKIT_SYNC_BATCH_SIZE' ) ? SQUAREKIT_SYNC_BATCH_SIZE : 100;

                    $logger->log( 'bulk_operation', 'info', 'Starting unlimited pagination count with batch size: ' . $batch_size );

                    do {
                        $batch_count++;
                        $logger->log( 'bulk_operation', 'info', "Counting batch {$batch_count} for total calculation" );

                        // Build query parameters for this batch
                        $query_params = array(
                            'types' => 'ITEM',
                            'limit' => $batch_size
                        );

                        if ( $cursor ) {
                            $query_params['cursor'] = $cursor;
                        }

                        // Use the new Square API method that returns cursor
                        $catalog_response = $square_api->get_catalog_with_cursor( $query_params );

                        if ( is_wp_error( $catalog_response ) ) {
                            $logger->log( 'bulk_operation', 'error', 'get_catalog_with_cursor returned WP_Error: ' . $catalog_response->get_error_message() );
                            return $total_count; // Return what we have so far
                        }

                        // Extract items and cursor from response
                        $batch_items = isset( $catalog_response['objects'] ) ? $catalog_response['objects'] : array();
                        $cursor = isset( $catalog_response['cursor'] ) ? $catalog_response['cursor'] : null;
                        $items_in_batch = count( $batch_items );
                        $total_count += $items_in_batch;

                        $logger->log( 'bulk_operation', 'info', "Batch {$batch_count}: Found {$items_in_batch} items (total so far: {$total_count})" );

                    } while ( $cursor && $items_in_batch > 0 );

                    $logger->log( 'bulk_operation', 'info', "FINAL TOTAL: {$total_count} Square products found across {$batch_count} batches" );

                    return $total_count;

                case 'export_products':
                    // Get total WooCommerce products
                    $logger->log( 'bulk_operation', 'info', 'Counting WooCommerce products' );
                    $products = wc_get_products( array(
                        'limit' => -1,
                        'status' => 'publish',
                        'return' => 'ids'
                    ) );
                    $count = count( $products );
                    $logger->log( 'bulk_operation', 'info', 'Found ' . $count . ' WooCommerce products' );
                    return $count;

                case 'sync_inventory':
                    // Get products with Square IDs
                    $logger->log( 'bulk_operation', 'info', 'Counting products with Square IDs' );
                    $products = wc_get_products( array(
                        'limit' => -1,
                        'meta_key' => '_square_id',
                        'meta_compare' => 'EXISTS',
                        'return' => 'ids'
                    ) );
                    $count = count( $products );
                    $logger->log( 'bulk_operation', 'info', 'Found ' . $count . ' products with Square IDs' );
                    return $count;

                case 'import_customers':
                    // Get total customers from Square
                    $logger->log( 'bulk_operation', 'info', 'Counting Square customers' );
                    if ( ! class_exists( 'SquareKit_Square_API' ) ) {
                        require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
                    }
                    $square_api = new \SquareKit_Square_API();
                    $customers = $square_api->get_customers();
                    $count = is_array( $customers ) ? count( $customers ) : 0;
                    $logger->log( 'bulk_operation', 'info', 'Found ' . $count . ' Square customers' );
                    return $count;

                case 'export_customers':
                    // Get total WooCommerce customers
                    $logger->log( 'bulk_operation', 'info', 'Counting WooCommerce customers' );
                    $users = get_users( array(
                        'role' => 'customer',
                        'count_total' => true
                    ) );
                    $logger->log( 'bulk_operation', 'info', 'Found ' . $users . ' WooCommerce customers' );
                    return $users;

                case 'bulk_status_update':
                case 'bulk_category_update':
                case 'bulk_attribute_update':
                    // Use provided product IDs or get all products
                    if ( !empty( $operation_data['product_ids'] ) ) {
                        $count = count( $operation_data['product_ids'] );
                        $logger->log( 'bulk_operation', 'info', 'Using provided product IDs: ' . $count . ' products' );
                        return $count;
                    } else {
                        $logger->log( 'bulk_operation', 'info', 'Counting all published products for bulk operation' );
                        $products = wc_get_products( array(
                            'limit' => -1,
                            'status' => 'publish',
                            'return' => 'ids'
                        ) );
                        $count = count( $products );
                        $logger->log( 'bulk_operation', 'info', 'Found ' . $count . ' products for bulk operation' );
                        return $count;
                    }

                default:
                    $logger->log( 'bulk_operation', 'warning', 'Unknown operation type: ' . $operation_type );
                    return 0;
            }

        } catch ( Exception $e ) {
            $logger->log( 'bulk_operation', 'error', 'Exception in calculate_total_items: ' . $e->getMessage(), array(
                'operation_type' => $operation_type,
                'trace' => $e->getTraceAsString()
            ) );
            return 0;
        } catch ( Error $e ) {
            $logger->log( 'bulk_operation', 'error', 'Fatal error in calculate_total_items: ' . $e->getMessage(), array(
                'operation_type' => $operation_type,
                'trace' => $e->getTraceAsString()
            ) );
            return 0;
        }
    }

    /**
     * Update operation progress
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param int $processed Processed items count
     * @param int $successful Successful items count
     * @param int $failed Failed items count
     * @param int $current_item_id Current item ID
     * @param string $current_item_name Current item name
     * @return bool Success status
     */
    public function update_progress( $operation_id, $processed, $successful, $failed, $current_item_id = null, $current_item_name = null ) {
        global $wpdb;
        
        $total_items = $this->get_operation_total_items( $operation_id );
        $progress_percentage = $total_items > 0 ? ( $processed / $total_items ) * 100 : 0;
        
        $result = $wpdb->update(
            $wpdb->prefix . 'squarekit_bulk_operations',
            array(
                'processed_items' => $processed,
                'successful_items' => $successful,
                'failed_items' => $failed,
                'progress_percentage' => $progress_percentage,
                'current_item_id' => $current_item_id,
                'current_item_name' => $current_item_name
            ),
            array( 'id' => $operation_id ),
            array( '%d', '%d', '%d', '%f', '%d', '%s' ),
            array( '%d' )
        );
        
        return $result !== false;
    }

    /**
     * Complete operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param string $status Final status (completed, failed, cancelled)
     * @param string $error_log Error log if any
     * @return bool Success status
     */
    public function complete_operation( $operation_id, $status = 'completed', $error_log = '' ) {
        global $wpdb;
        
        $result = $wpdb->update(
            $wpdb->prefix . 'squarekit_bulk_operations',
            array(
                'operation_status' => $status,
                'completed_at' => current_time( 'mysql' ),
                'error_log' => $error_log
            ),
            array( 'id' => $operation_id ),
            array( '%s', '%s', '%s' ),
            array( '%d' )
        );
        
        if ( $result !== false ) {
            // Log operation completion
            $operation = $this->get_operation( $operation_id );
            if ( $operation ) {
                // Initialize logger for completion logging
                if ( ! class_exists( 'SquareKit_Logger' ) ) {
                    require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
                }
                $logger = SquareKit_Logger::get_instance();

                $logger->log( 'bulk_operation', 'info', sprintf(
                    'Completed bulk operation: %s (ID: %d, Status: %s, Processed: %d/%d)',
                    $operation->operation_type,
                    $operation_id,
                    $status,
                    $operation->processed_items,
                    $operation->total_items
                ) );
            }
        }
        
        return $result !== false;
    }

    /**
     * Get operation details
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @return object|false Operation object or false
     */
    public function get_operation( $operation_id ) {
        global $wpdb;
        
        return $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}squarekit_bulk_operations WHERE id = %d",
            $operation_id
        ) );
    }

    /**
     * Get operation total items
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @return int Total items
     */
    public function get_operation_total_items( $operation_id ) {
        global $wpdb;
        
        return (int) $wpdb->get_var( $wpdb->prepare(
            "SELECT total_items FROM {$wpdb->prefix}squarekit_bulk_operations WHERE id = %d",
            $operation_id
        ) );
    }

    /**
     * Get recent operations
     *
     * @since 1.0.0
     * @param int $limit Number of operations to retrieve
     * @return array Recent operations
     */
    public function get_recent_operations( $limit = 10 ) {
        global $wpdb;
        
        return $wpdb->get_results( $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}squarekit_bulk_operations 
             ORDER BY started_at DESC 
             LIMIT %d",
            $limit
        ) );
    }

    /**
     * Get active operations
     *
     * @since 1.0.0
     * @return array Active operations
     */
    public function get_active_operations() {
        global $wpdb;
        
        return $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}squarekit_bulk_operations 
             WHERE operation_status IN ('pending', 'in_progress') 
             ORDER BY started_at DESC"
        );
    }

    /**
     * Cancel operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @return bool Success status
     */
    public function cancel_operation( $operation_id ) {
        return $this->complete_operation( $operation_id, 'cancelled' );
    }

    /**
     * Clean up old operations
     *
     * @since 1.0.0
     * @param int $days_old Days old to clean up
     * @return int Number of operations cleaned up
     */
    public function cleanup_old_operations( $days_old = 30 ) {
        global $wpdb;
        
        $result = $wpdb->query( $wpdb->prepare(
            "DELETE FROM {$wpdb->prefix}squarekit_bulk_operations 
             WHERE started_at < DATE_SUB(NOW(), INTERVAL %d DAY)
             AND operation_status IN ('completed', 'failed', 'cancelled')",
            $days_old
        ) );
        
        return $result;
    }

    /**
     * Process bulk operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @return bool Success status
     */
    public function process_operation( $operation_id ) {
        $operation = $this->get_operation( $operation_id );
        if ( ! $operation ) {
            return false;
        }
        
        // Update status to in_progress
        global $wpdb;
        $wpdb->update(
            $wpdb->prefix . 'squarekit_bulk_operations',
            array( 'operation_status' => 'in_progress' ),
            array( 'id' => $operation_id ),
            array( '%s' ),
            array( '%d' )
        );
        
        $operation_data = json_decode( $operation->operation_data, true );
        $processed = 0;
        $successful = 0;
        $failed = 0;
        $error_log = array();
        
        switch ( $operation->operation_type ) {
            case 'import_products':
                $result = $this->process_import_products( $operation_id, $operation_data );
                break;
                
            case 'export_products':
                $result = $this->process_export_products( $operation_id, $operation_data );
                break;
                
            case 'sync_inventory':
                $result = $this->process_sync_inventory( $operation_id, $operation_data );
                break;
                
            case 'import_customers':
                $result = $this->process_import_customers( $operation_id, $operation_data );
                break;
                
            case 'export_customers':
                $result = $this->process_export_customers( $operation_id, $operation_data );
                break;
                
            case 'bulk_status_update':
                $result = $this->process_bulk_status_update( $operation_id, $operation_data );
                break;
                
            case 'bulk_category_update':
                $result = $this->process_bulk_category_update( $operation_id, $operation_data );
                break;
                
            case 'bulk_attribute_update':
                $result = $this->process_bulk_attribute_update( $operation_id, $operation_data );
                break;
                
            default:
                $result = false;
        }
        
        // Complete operation
        $status = $result ? 'completed' : 'failed';
        $this->complete_operation( $operation_id, $status, wp_json_encode( $error_log ) );
        
        return $result;
    }

    /**
     * Process import products operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_import_products( $operation_id, $operation_data ) {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        // Use SWEVER-style Product Importer (same as working test)
        if ( ! class_exists( 'SquareKit_Product_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
        }

        $importer = new \SquareKit_Product_Importer();
        $square_api = new \SquareKit_Square_API();

        // Fetch all items using pagination to avoid 100-item limit
        $all_items = array();
        $cursor = null;
        $batch_count = 0;
        $batch_size = defined( 'SQUAREKIT_SYNC_BATCH_SIZE' ) ? SQUAREKIT_SYNC_BATCH_SIZE : 100;

        do {
            $batch_count++;

            // Build query parameters for this batch
            $query_params = array(
                'types' => 'ITEM',
                'limit' => $batch_size
            );

            if ( $cursor ) {
                $query_params['cursor'] = $cursor;
            }

            // Use the new Square API method that returns cursor
            $catalog_response = $square_api->get_catalog_with_cursor( $query_params );

            if ( is_wp_error( $catalog_response ) ) {
                return false;
            }

            // Extract items and cursor from response
            $batch_items = isset( $catalog_response['objects'] ) ? $catalog_response['objects'] : array();
            $cursor = isset( $catalog_response['cursor'] ) ? $catalog_response['cursor'] : null;

            // Filter only ITEM objects and add to collection
            $item_objects = array_filter( $batch_items, function( $object ) {
                return isset( $object['type'] ) && $object['type'] === 'ITEM';
            } );

            $all_items = array_merge( $all_items, $item_objects );

        } while ( $cursor );

        $items = $all_items;

        // Configure import settings (same as working test)
        $import_config = array(
            'name' => true,
            'description' => true,
            'images' => true,
            'categories' => true,
            'variations' => true,
            'modifiers' => true,
            'attributesDisabled' => false
        );

        $processed = 0;
        $successful = 0;
        $failed = 0;

        foreach ( $items as $item ) {
            $processed++;

            // Get item name for progress tracking
            $item_name = isset( $item['item_data']['name'] ) ? $item['item_data']['name'] : 'Unknown Product';

            // Update progress with current item name
            $this->update_progress( $operation_id, $processed, $successful, $failed, $item['id'], $item_name );

            try {
                // Use SWEVER-style import with Square item ID
                $result = $importer->import_product_swever_style(
                    $item['id'],
                    $import_config,
                    false // Not update-only
                );

                if ( is_wp_error( $result ) ) {
                    $failed++;
                } else {
                    $successful++;
                }
            } catch ( Exception $e ) {
                $failed++;
            }
        }

        // Final progress update
        $this->update_progress( $operation_id, $processed, $successful, $failed, null, 'Import completed' );
        return $failed === 0;
    }

    /**
     * Process export products operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_export_products( $operation_id, $operation_data ) {
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc_integration = new \SquareKit_WooCommerce();
        $products = wc_get_products( array(
            'limit' => -1,
            'status' => 'publish',
            'return' => 'objects'
        ) );
        
        $processed = 0;
        $successful = 0;
        $failed = 0;
        
        foreach ( $products as $product ) {
            $processed++;
            
            try {
                $wc_integration->sync_product_to_square( $product->get_id(), get_post( $product->get_id() ), true );
                $successful++;
            } catch ( Exception $e ) {
                $failed++;
            }
            
            // Update progress every 10 items
            if ( $processed % 10 === 0 ) {
                $this->update_progress( $operation_id, $processed, $successful, $failed );
            }
        }
        
        $this->update_progress( $operation_id, $processed, $successful, $failed );
        return $failed === 0;
    }

    /**
     * Process sync inventory operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_sync_inventory( $operation_id, $operation_data ) {
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc_integration = new \SquareKit_WooCommerce();
        $result = $wc_integration->import_inventory_from_square();
        
        if ( $result['success'] ) {
            $this->update_progress( $operation_id, $result['updated'], $result['updated'], 0 );
            return true;
        } else {
            $this->update_progress( $operation_id, 0, 0, 1 );
            return false;
        }
    }

    /**
     * Process import customers operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_import_customers( $operation_id, $operation_data ) {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $square_api = new \SquareKit_Square_API();
        $wc_integration = new \SquareKit_WooCommerce();
        
        $customers = $square_api->get_customers();
        if ( is_wp_error( $customers ) ) {
            return false;
        }
        
        $processed = 0;
        $successful = 0;
        $failed = 0;
        
        foreach ( $customers as $customer ) {
            $processed++;
            
            try {
                $wc_integration->sync_customer_to_square( $customer['id'] );
                $successful++;
            } catch ( Exception $e ) {
                $failed++;
            }
            
            // Update progress every 10 items
            if ( $processed % 10 === 0 ) {
                $this->update_progress( $operation_id, $processed, $successful, $failed );
            }
        }
        
        $this->update_progress( $operation_id, $processed, $successful, $failed );
        return $failed === 0;
    }

    /**
     * Process export customers operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_export_customers( $operation_id, $operation_data ) {
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        
        $wc_integration = new \SquareKit_WooCommerce();
        $users = get_users( array( 'role' => 'customer' ) );
        
        $processed = 0;
        $successful = 0;
        $failed = 0;
        
        foreach ( $users as $user ) {
            $processed++;
            
            try {
                $wc_integration->sync_customer_to_square( $user->ID );
                $successful++;
            } catch ( Exception $e ) {
                $failed++;
            }
            
            // Update progress every 10 items
            if ( $processed % 10 === 0 ) {
                $this->update_progress( $operation_id, $processed, $successful, $failed );
            }
        }
        
        $this->update_progress( $operation_id, $processed, $successful, $failed );
        return $failed === 0;
    }

    /**
     * Process bulk status update operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_bulk_status_update( $operation_id, $operation_data ) {
        $product_ids = isset( $operation_data['product_ids'] ) ? $operation_data['product_ids'] : array();
        $new_status = isset( $operation_data['status'] ) ? $operation_data['status'] : 'publish';
        
        if ( empty( $product_ids ) ) {
            $products = wc_get_products( array(
                'limit' => -1,
                'status' => 'publish',
                'return' => 'ids'
            ) );
        } else {
            $products = $product_ids;
        }
        
        $processed = 0;
        $successful = 0;
        $failed = 0;
        
        foreach ( $products as $product_id ) {
            $processed++;
            
            try {
                $product = wc_get_product( $product_id );
                if ( $product ) {
                    $product->set_status( $new_status );
                    $product->save();
                    $successful++;
                } else {
                    $failed++;
                }
            } catch ( Exception $e ) {
                $failed++;
            }
            
            // Update progress every 10 items
            if ( $processed % 10 === 0 ) {
                $this->update_progress( $operation_id, $processed, $successful, $failed );
            }
        }
        
        $this->update_progress( $operation_id, $processed, $successful, $failed );
        return $failed === 0;
    }

    /**
     * Process bulk category update operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_bulk_category_update( $operation_id, $operation_data ) {
        $product_ids = isset( $operation_data['product_ids'] ) ? $operation_data['product_ids'] : array();
        $category_ids = isset( $operation_data['category_ids'] ) ? $operation_data['category_ids'] : array();
        $append = isset( $operation_data['append'] ) ? $operation_data['append'] : false;
        
        if ( empty( $product_ids ) ) {
            $products = wc_get_products( array(
                'limit' => -1,
                'status' => 'publish',
                'return' => 'ids'
            ) );
        } else {
            $products = $product_ids;
        }
        
        $processed = 0;
        $successful = 0;
        $failed = 0;
        
        foreach ( $products as $product_id ) {
            $processed++;
            
            try {
                if ( $append ) {
                    wp_set_object_terms( $product_id, $category_ids, 'product_cat', true );
                } else {
                    wp_set_object_terms( $product_id, $category_ids, 'product_cat', false );
                }
                $successful++;
            } catch ( Exception $e ) {
                $failed++;
            }
            
            // Update progress every 10 items
            if ( $processed % 10 === 0 ) {
                $this->update_progress( $operation_id, $processed, $successful, $failed );
            }
        }
        
        $this->update_progress( $operation_id, $processed, $successful, $failed );
        return $failed === 0;
    }

    /**
     * Process bulk attribute update operation
     *
     * @since 1.0.0
     * @param int $operation_id Operation ID
     * @param array $operation_data Operation data
     * @return bool Success status
     */
    protected function process_bulk_attribute_update( $operation_id, $operation_data ) {
        $product_ids = isset( $operation_data['product_ids'] ) ? $operation_data['product_ids'] : array();
        $attributes = isset( $operation_data['attributes'] ) ? $operation_data['attributes'] : array();
        
        if ( empty( $product_ids ) ) {
            $products = wc_get_products( array(
                'limit' => -1,
                'status' => 'publish',
                'return' => 'ids'
            ) );
        } else {
            $products = $product_ids;
        }
        
        $processed = 0;
        $successful = 0;
        $failed = 0;
        
        foreach ( $products as $product_id ) {
            $processed++;
            
            try {
                $product = wc_get_product( $product_id );
                if ( $product ) {
                    $product_attributes = $product->get_attributes();
                    
                    foreach ( $attributes as $attribute_name => $attribute_value ) {
                        $attribute_object = new WC_Product_Attribute();
                        $attribute_object->set_name( $attribute_name );
                        $attribute_object->set_options( array( $attribute_value ) );
                        $attribute_object->set_position( count( $product_attributes ) );
                        $attribute_object->set_visible( true );
                        $attribute_object->set_variation( false );
                        
                        $product_attributes[] = $attribute_object;
                    }
                    
                    $product->set_attributes( $product_attributes );
                    $product->save();
                    $successful++;
                } else {
                    $failed++;
                }
            } catch ( Exception $e ) {
                $failed++;
            }
            
            // Update progress every 10 items
            if ( $processed % 10 === 0 ) {
                $this->update_progress( $operation_id, $processed, $successful, $failed );
            }
        }
        
        $this->update_progress( $operation_id, $processed, $successful, $failed );
        return $failed === 0;
    }
} 