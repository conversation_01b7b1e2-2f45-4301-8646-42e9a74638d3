<?php
/**
 * SquareKit Modifier Handler Class
 *
 * Handles all modifier-related functionality including frontend display,
 * cart integration, and admin management.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Modifier Handler Class
 *
 * Lightweight, focused class for handling Square modifiers in WooCommerce.
 */
class SquareKit_Modifier_Handler {

    /**
     * Logger instance
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_logger();
        $this->init_hooks();
    }

    /**
     * Initialize logger
     */
    private function init_logger() {
        if ( class_exists( 'SquareKit_Logger' ) ) {
            $this->logger = SquareKit_Logger::get_instance();
        }
    }

    /**
     * Initialize WordPress hooks
     * Note: This class is not currently being used - SquareKit_WooCommerce handles frontend display
     */
    private function init_hooks() {
        // Frontend display hooks are handled by SquareKit_WooCommerce class
        // Keeping this class for potential future use or admin-only functionality

        // Cart integration (if needed separately)
        // add_filter( 'woocommerce_add_cart_item_data', array( $this, 'add_modifiers_to_cart_item' ), 10, 3 );
        // add_action( 'woocommerce_before_calculate_totals', array( $this, 'adjust_cart_item_price' ), 20 );
        // add_filter( 'woocommerce_get_item_data', array( $this, 'display_modifiers_in_cart' ), 10, 2 );

        // Order integration (if needed separately)
        // add_action( 'woocommerce_checkout_create_order_line_item', array( $this, 'add_modifiers_to_order_item' ), 10, 4 );
    }

    /**
     * Display product modifiers on frontend
     */
    public function display_product_modifiers() {
        global $product;
        if ( ! $product || ! is_a( $product, 'WC_Product' ) ) {
            return;
        }

        $modifier_sets = get_post_meta( $product->get_id(), '_squarekit_modifier_sets', true );
        if ( empty( $modifier_sets ) || ! is_array( $modifier_sets ) ) {
            return;
        }

        echo '<div class="squarekit-modifiers-wrapper">';
        echo '<h4>' . esc_html__( 'Customization Options', 'squarekit' ) . '</h4>';

        foreach ( $modifier_sets as $set_index => $set_data ) {
            $this->render_modifier_set( $set_data, $set_index );
        }

        $this->render_price_breakdown();
        echo '</div>';
    }

    /**
     * Render a single modifier set
     */
    private function render_modifier_set( $set_data, $set_index ) {
        $set_name = $set_data['set_name'] ?? '';
        $single_choice = ! empty( $set_data['single_choice'] );
        $options = $set_data['options'] ?? array();

        if ( empty( $options ) || empty( $set_name ) ) {
            return;
        }

        $input_name_base = "squarekit_modifier_sets[{$set_index}]";

        echo '<div class="squarekit-modifier-set-frontend">';
        echo '<h5>' . esc_html( $set_name ) . '</h5>';

        foreach ( $options as $opt_index => $option ) {
            $this->render_modifier_option( $option, $input_name_base, $set_index, $opt_index, $single_choice );
        }

        echo '</div>';
    }

    /**
     * Render a single modifier option
     */
    private function render_modifier_option( $option, $input_name_base, $set_index, $opt_index, $single_choice ) {
        $opt_name = $option['name'] ?? '';
        $opt_price = floatval( $option['price'] ?? 0 );
        $opt_stock = $option['stock'] ?? '';
        $sold_out = $option['sold_out'] ?? false;

        if ( empty( $opt_name ) ) {
            return;
        }

        // Check availability - sold out or numeric stock check
        $disabled = '';
        $availability_class = '';
        $availability_text = '';

        if ( $sold_out || $opt_stock === 'sold_out' ) {
            $disabled = 'disabled';
            $availability_class = 'squarekit-modifier-sold-out';
            $availability_text = ' (Sold Out)';
        } elseif ( is_numeric( $opt_stock ) && intval( $opt_stock ) <= 0 ) {
            $disabled = 'disabled';
            $availability_class = 'squarekit-modifier-out-of-stock';
            $availability_text = ' (Out of Stock)';
        }

        // Format price display
        $price_text = $opt_price != 0 ? wc_price( $opt_price ) : '';

        // Input type and name
        $input_type = $single_choice ? 'radio' : 'checkbox';
        $input_name = $single_choice ? $input_name_base : $input_name_base . '[]';
        $option_id = $input_name_base . '_' . $opt_index;

        echo '<label class="squarekit-modifier-option-label ' . esc_attr( $availability_class ) . '" for="' . esc_attr( $option_id ) . '">';
        echo '<input type="' . esc_attr( $input_type ) . '" ';
        echo 'id="' . esc_attr( $option_id ) . '" ';
        echo 'name="' . esc_attr( $input_name ) . '" ';
        echo 'value="' . esc_attr( $opt_name ) . '" ';
        echo 'data-price="' . esc_attr( $opt_price ) . '" ';
        echo 'data-set-index="' . esc_attr( $set_index ) . '" ';
        echo 'data-option-index="' . esc_attr( $opt_index ) . '" ';
        echo $disabled . ' />';

        echo '<span class="option-name">' . esc_html( $opt_name ) . esc_html( $availability_text ) . '</span>';
        if ( $price_text ) {
            echo ' <span class="option-price">(+' . $price_text . ')</span>';
        }

        // Show stock info if available
        if ( is_numeric( $opt_stock ) && intval( $opt_stock ) > 0 ) {
            echo ' <small class="stock-info">(' . 
                 sprintf( esc_html__( 'In stock: %d', 'squarekit' ), intval( $opt_stock ) ) . 
                 ')</small>';
        }

        echo '</label>';
    }

    /**
     * Render price breakdown display
     */
    private function render_price_breakdown() {
        echo '<div class="squarekit-price-breakdown" style="display: none;">';
        echo '<div class="squarekit-base-price-line">';
        echo '<span class="squarekit-price-label">' . esc_html__( 'Base Price:', 'squarekit' ) . '</span>';
        echo '<span class="squarekit-base-price-display"></span>';
        echo '</div>';
        echo '<div class="squarekit-selected-options-line">';
        echo '<span class="squarekit-price-label">' . esc_html__( 'Selected Options:', 'squarekit' ) . '</span>';
        echo '<span class="squarekit-selected-summary"></span>';
        echo '</div>';
        echo '<div class="squarekit-modifiers-price-list"></div>';
        echo '<div class="squarekit-total-price-line">';
        echo '<span class="squarekit-price-label">' . esc_html__( 'Total:', 'squarekit' ) . '</span>';
        echo '<span class="squarekit-total-price-display"></span>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        if ( ! is_product() ) {
            return;
        }

        global $product;
        if ( ! is_a( $product, 'WC_Product' ) ) {
            $product = wc_get_product();
        }

        // Only enqueue if product has modifiers
        if ( $product && is_a( $product, 'WC_Product' ) ) {
            $modifier_sets = get_post_meta( $product->get_id(), '_squarekit_modifier_sets', true );
            if ( empty( $modifier_sets ) ) {
                return;
            }
        }

        // Enqueue CSS
        wp_enqueue_style( 
            'squarekit-frontend', 
            SQUAREKIT_PLUGIN_URL . 'assets/css/squarekit-frontend.css', 
            array(), 
            '1.0.0' 
        );

        // Enqueue JavaScript
        wp_enqueue_script( 
            'squarekit-frontend', 
            SQUAREKIT_PLUGIN_URL . 'assets/js/squarekit-frontend.js', 
            array( 'jquery' ), 
            '1.0.0', 
            true 
        );

        // Localize script with product data
        if ( $product && is_a( $product, 'WC_Product' ) ) {
            wp_localize_script( 'squarekit-frontend', 'squarekit_frontend', array(
                'base_price' => $product->get_price(),
                'currency_symbol' => get_woocommerce_currency_symbol(),
                'currency_position' => get_option( 'woocommerce_currency_pos' ),
                'thousand_separator' => wc_get_price_thousand_separator(),
                'decimal_separator' => wc_get_price_decimal_separator(),
                'decimals' => wc_get_price_decimals()
            ) );
        }
    }

    /**
     * Add modifiers to cart item data
     */
    public function add_modifiers_to_cart_item( $cart_item_data, $product_id, $variation_id ) {
        if ( isset( $_POST['squarekit_modifier_sets'] ) && is_array( $_POST['squarekit_modifier_sets'] ) ) {
            $raw_modifiers = wc_clean( wp_unslash( $_POST['squarekit_modifier_sets'] ) );
            $cart_item_data['squarekit_chosen_sets'] = $raw_modifiers;
        }

        return $cart_item_data;
    }

    /**
     * Adjust cart item price based on selected modifiers
     */
    public function adjust_cart_item_price( $cart ) {
        if ( is_admin() && ! defined( 'DOING_AJAX' ) ) {
            return;
        }
        
        if ( did_action( 'woocommerce_before_calculate_totals' ) >= 2 ) {
            return;
        }
        
        if ( ! is_object( $cart ) ) {
            return;
        }

        foreach ( $cart->get_cart() as $cart_item ) {
            if ( ! empty( $cart_item['squarekit_chosen_sets'] ) ) {
                $this->calculate_cart_item_price( $cart_item );
            }
        }
    }

    /**
     * Calculate price for a single cart item with modifiers
     */
    private function calculate_cart_item_price( $cart_item ) {
        $chosen_sets = $cart_item['squarekit_chosen_sets'];
        $product_id = $cart_item['product_id'];
        $available_sets = get_post_meta( $product_id, '_squarekit_modifier_sets', true );
        
        if ( ! is_array( $available_sets ) ) {
            return;
        }

        $base_price = floatval( $cart_item['data']->get_price() );
        $price_extra = 0.0;

        // Calculate modifier price additions
        foreach ( $chosen_sets as $set_index => $selected ) {
            if ( ! isset( $available_sets[ $set_index ] ) ) {
                continue;
            }
            
            $set_data = $available_sets[ $set_index ];
            $options = $set_data['options'] ?? array();
            
            // Create option lookup
            $option_prices = array();
            foreach ( $options as $option ) {
                if ( isset( $option['name'], $option['price'] ) ) {
                    $option_prices[ $option['name'] ] = floatval( $option['price'] );
                }
            }

            if ( is_array( $selected ) ) {
                // Multiple choice (checkboxes)
                foreach ( $selected as $opt_name ) {
                    if ( isset( $option_prices[ $opt_name ] ) ) {
                        $price_extra += $option_prices[ $opt_name ];
                    }
                }
            } else {
                // Single choice (radio)
                if ( isset( $option_prices[ $selected ] ) ) {
                    $price_extra += $option_prices[ $selected ];
                }
            }
        }

        $new_price = $base_price + $price_extra;
        $cart_item['data']->set_price( $new_price );
    }

    /**
     * Display modifiers in cart
     */
    public function display_modifiers_in_cart( $item_data, $cart_item ) {
        if ( ! isset( $cart_item['squarekit_chosen_sets'] ) || empty( $cart_item['squarekit_chosen_sets'] ) ) {
            return $item_data;
        }

        $chosen_sets = $cart_item['squarekit_chosen_sets'];
        $available_sets = get_post_meta( $cart_item['product_id'], '_squarekit_modifier_sets', true );
        
        if ( ! is_array( $available_sets ) ) {
            return $item_data;
        }

        foreach ( $chosen_sets as $set_index => $selected ) {
            if ( ! isset( $available_sets[ $set_index ] ) ) {
                continue;
            }
            
            $set_data = $available_sets[ $set_index ];
            $set_name = $set_data['set_name'] ?? '';
            $options = $set_data['options'] ?? array();
            
            // Create option lookup
            $option_details = array();
            foreach ( $options as $option ) {
                if ( isset( $option['name'] ) ) {
                    $option_details[ $option['name'] ] = array(
                        'name' => $option['name'],
                        'price' => floatval( $option['price'] ?? 0 )
                    );
                }
            }

            $selected_options = array();
            
            if ( is_array( $selected ) ) {
                // Multiple choice
                foreach ( $selected as $opt_name ) {
                    if ( isset( $option_details[ $opt_name ] ) ) {
                        $option_data = $option_details[ $opt_name ];
                        $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';
                        $selected_options[] = $option_data['name'] . $price_text;
                    }
                }
            } else {
                // Single choice
                if ( isset( $option_details[ $selected ] ) ) {
                    $option_data = $option_details[ $selected ];
                    $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';
                    $selected_options[] = $option_data['name'] . $price_text;
                }
            }
            
            if ( ! empty( $selected_options ) ) {
                $item_data[] = array(
                    'key' => $set_name,
                    'value' => implode( ', ', $selected_options ),
                    'display' => ''
                );
            }
        }
        
        return $item_data;
    }

    /**
     * Add modifiers to order item
     */
    public function add_modifiers_to_order_item( $item, $cart_item_key, $values, $order ) {
        if ( ! isset( $values['squarekit_chosen_sets'] ) || empty( $values['squarekit_chosen_sets'] ) ) {
            return;
        }

        $chosen_sets = $values['squarekit_chosen_sets'];
        $available_sets = get_post_meta( $values['product_id'], '_squarekit_modifier_sets', true );
        
        if ( ! is_array( $available_sets ) ) {
            return;
        }

        // Store raw modifier data for future reference
        $item->add_meta_data( '_squarekit_chosen_sets', $chosen_sets );

        // Add human-readable modifier data
        foreach ( $chosen_sets as $set_index => $selected ) {
            if ( ! isset( $available_sets[ $set_index ] ) ) {
                continue;
            }
            
            $set_data = $available_sets[ $set_index ];
            $set_name = $set_data['set_name'] ?? '';
            $options = $set_data['options'] ?? array();
            
            // Create option lookup
            $option_details = array();
            foreach ( $options as $option ) {
                if ( isset( $option['name'] ) ) {
                    $option_details[ $option['name'] ] = array(
                        'name' => $option['name'],
                        'price' => floatval( $option['price'] ?? 0 )
                    );
                }
            }

            $selected_options = array();
            
            if ( is_array( $selected ) ) {
                // Multiple choice
                foreach ( $selected as $opt_name ) {
                    if ( isset( $option_details[ $opt_name ] ) ) {
                        $option_data = $option_details[ $opt_name ];
                        $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';
                        $selected_options[] = $option_data['name'] . $price_text;
                    }
                }
            } else {
                // Single choice
                if ( isset( $option_details[ $selected ] ) ) {
                    $option_data = $option_details[ $selected ];
                    $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';
                    $selected_options[] = $option_data['name'] . $price_text;
                }
            }
            
            if ( ! empty( $selected_options ) ) {
                $item->add_meta_data( $set_name, implode( ', ', $selected_options ) );
            }
        }
    }
}
