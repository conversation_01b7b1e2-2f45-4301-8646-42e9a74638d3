<?php
/**
 * SquareKit Modifier Admin Handler Class
 *
 * Handles admin-side modifier functionality including product edit panels
 * and AJAX operations.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Modifier Admin Handler Class
 *
 * Focused class for handling modifier admin functionality.
 */
class SquareKit_Modifier_Admin {

    /**
     * Logger instance
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_logger();
        $this->init_hooks();
    }

    /**
     * Initialize logger
     */
    private function init_logger() {
        if ( class_exists( 'SquareKit_Logger' ) ) {
            $this->logger = SquareKit_Logger::get_instance();
        }
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Admin hooks
        add_filter( 'woocommerce_product_data_tabs', array( $this, 'add_modifiers_tab' ) );
        add_action( 'woocommerce_product_data_panels', array( $this, 'add_modifiers_panel' ) );
        add_action( 'woocommerce_process_product_meta', array( $this, 'save_modifiers_meta' ), 20, 2 );

        // AJAX hooks
        add_action( 'wp_ajax_squarekit_reimport_modifiers', array( $this, 'ajax_reimport_modifiers' ) );
    }

    /**
     * Add modifiers tab to product data tabs
     */
    public function add_modifiers_tab( $tabs ) {
        $tabs['squarekit_modifiers'] = array(
            'label'    => __( 'Square Modifiers', 'squarekit' ),
            'target'   => 'squarekit_modifiers_panel',
            'class'    => array( 'show_if_simple', 'show_if_variable' ),
            'priority' => 80,
        );
        return $tabs;
    }

    /**
     * Add modifiers panel to product data panels
     */
    public function add_modifiers_panel() {
        global $post;
        
        $modifier_sets = get_post_meta( $post->ID, '_squarekit_modifier_sets', true );
        if ( ! is_array( $modifier_sets ) ) {
            $modifier_sets = array();
        }

        echo '<div id="squarekit_modifiers_panel" class="panel woocommerce_options_panel">';
        echo '<div class="options_group">';
        echo '<p><strong>' . __( 'Square Modifiers', 'squarekit' ) . '</strong></p>';
        echo '<p>' . __( 'Manage modifier sets imported from Square. Changes will be saved when you update the product.', 'squarekit' ) . '</p>';
        
        // Control buttons
        echo '<div class="squarekit-modifier-controls">';
        echo '<button type="button" id="squarekit_reimport_modifiers" class="button button-secondary">';
        echo __( 'Re-import from Square', 'squarekit' );
        echo '</button>';
        echo '<button type="button" id="squarekit_add_modifier_set" class="button button-primary">';
        echo __( 'Add Modifier Set', 'squarekit' );
        echo '</button>';
        echo '</div>';

        // Container for modifier sets
        echo '<div id="squarekit_modifier_sets_container"></div>';

        // Hidden input to store JSON data
        echo '<input type="hidden" id="squarekit_modifier_sets_data" name="squarekit_modifier_sets" value="' . esc_attr( wp_json_encode( $modifier_sets ) ) . '" />';

        echo '</div>';
        echo '</div>';

        // Add inline JavaScript for modifier management
        $this->add_modifier_admin_script( $modifier_sets );
    }







    }

    /**
     * Add modifier admin script
     */
    private function add_modifier_admin_script( $modifier_sets ) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var modifierSets = <?php echo wp_json_encode( $modifier_sets ); ?>;
            var $container = $('#squarekit_modifier_sets_container');
            var $dataInput = $('#squarekit_modifier_sets_data');

            // Render all modifier sets
            function renderModifierSets() {
                $container.empty();
                
                if (modifierSets.length === 0) {
                    $container.html('<p><?php echo esc_js( __( 'No modifier sets yet.', 'squarekit' ) ); ?></p>');
                    return;
                }

                $.each(modifierSets, function(index, setData) {
                    renderModifierSet(setData, index);
                });
            }

            // Render a single modifier set
            function renderModifierSet(setData, index) {
                var setName = setData.set_name || '';
                var squareListId = setData.square_mod_list_id || '';
                var singleChoice = setData.single_choice ? 'checked' : '';
                var options = setData.options || [];
                var isSquareManaged = setData.source === 'square';

                var setHTML = `
                    <div class="squarekit-modifier-set" data-set-index="${index}">
                        <div class="squarekit-modifier-set-header">
                            <div>
                                <label><?php echo esc_js( __( 'Set Name', 'squarekit' ) ); ?></label>
                                <input type="text" class="set-name" value="${setName}" placeholder="<?php echo esc_js( __( 'e.g. Extras', 'squarekit' ) ); ?>" ${isSquareManaged ? 'readonly' : ''}/>
                            </div>
                            <div>
                                <label><?php echo esc_js( __( 'Square Mod List ID', 'squarekit' ) ); ?></label>
                                <input type="text" class="square-list-id" value="${squareListId}" placeholder="<?php echo esc_js( __( 'WEYL7DNTWBB4MQOIXMYWPK2J', 'squarekit' ) ); ?>" readonly/>
                            </div>
                            <div>
                                <label><?php echo esc_js( __( 'Single Choice', 'squarekit' ) ); ?></label>
                                <input type="checkbox" class="single-choice" ${singleChoice} ${isSquareManaged ? 'disabled' : ''} />
                            </div>
                            <div>
                                <button type="button" class="remove-set-btn"><?php echo esc_js( __( 'Remove Set', 'squarekit' ) ); ?></button>
                            </div>
                        </div>

                        <table class="squarekit-options-table">
                            <thead>
                                <tr>
                                    <th><?php echo esc_js( __( 'Option Name', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Price', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Stock (optional)', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Square Modifier ID', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Actions', 'squarekit' ) ); ?></th>
                                </tr>
                            </thead>
                            <tbody class="options-tbody">
                            </tbody>
                        </table>
                        ${!isSquareManaged ? '<button type="button" class="add-option-btn">' + '<?php echo esc_js( __( 'Add Option', 'squarekit' ) ); ?>' + '</button>' : ''}
                    </div>
                `;
                
                $container.append(setHTML);
                
                // Render options for this set
                var $setElement = $container.find('.squarekit-modifier-set[data-set-index="' + index + '"]');
                var $tbody = $setElement.find('.options-tbody');
                
                $.each(options, function(optIndex, optData) {
                    renderOption($tbody, optData, isSquareManaged);
                });
            }

            // Render a single option row
            function renderOption($tbody, optData, isSquareManaged) {
                var optionName = optData.name || '';
                var optionPrice = optData.price || '';
                var optionStock = optData.stock || '';
                var squareModId = optData.square_mod_id || '';

                var optionHTML = `
                    <tr>
                        <td><input type="text" class="option-name" value="${optionName}" placeholder="<?php echo esc_js( __( 'e.g. Cheese', 'squarekit' ) ); ?>" ${isSquareManaged ? 'readonly' : ''} /></td>
                        <td><input type="number" step="0.01" class="option-price" value="${optionPrice}" placeholder="0.00" ${isSquareManaged ? 'readonly' : ''} /></td>
                        <td><input type="text" class="option-stock" value="${optionStock}" placeholder="<?php echo esc_js( __( 'optional', 'squarekit' ) ); ?>" ${isSquareManaged ? 'readonly' : ''} /></td>
                        <td><input type="text" class="option-square-id" value="${squareModId}" placeholder="<?php echo esc_js( __( 'Square Modifier ID', 'squarekit' ) ); ?>" readonly /></td>
                        <td><button type="button" class="remove-option-btn" ${isSquareManaged ? 'disabled' : ''}><?php echo esc_js( __( 'Remove', 'squarekit' ) ); ?></button></td>
                    </tr>
                `;
                
                $tbody.append(optionHTML);
            }

            // Save data to hidden input
            function saveData() {
                $dataInput.val(JSON.stringify(modifierSets));
            }

            // Update modifier sets from UI
            function updateModifierSetsFromUI() {
                modifierSets = [];
                
                $container.find('.squarekit-modifier-set').each(function() {
                    var $set = $(this);
                    var setData = {
                        set_name: $set.find('.set-name').val(),
                        square_mod_list_id: $set.find('.square-list-id').val(),
                        single_choice: $set.find('.single-choice').is(':checked'),
                        source: $set.find('.square-list-id').val() ? 'square' : 'wc',
                        options: []
                    };
                    
                    $set.find('.options-tbody tr').each(function() {
                        var $row = $(this);
                        var optionData = {
                            name: $row.find('.option-name').val(),
                            price: parseFloat($row.find('.option-price').val()) || 0,
                            stock: $row.find('.option-stock').val(),
                            square_mod_id: $row.find('.option-square-id').val()
                        };
                        
                        if (optionData.name) {
                            setData.options.push(optionData);
                        }
                    });
                    
                    if (setData.set_name) {
                        modifierSets.push(setData);
                    }
                });
                
                saveData();
            }

            // Event handlers
            $('#squarekit_add_modifier_set').on('click', function() {
                var newSet = {
                    set_name: '',
                    square_mod_list_id: '',
                    single_choice: false,
                    source: 'wc',
                    options: []
                };
                modifierSets.push(newSet);
                renderModifierSets();
                saveData();
            });

            $(document).on('click', '.remove-set-btn', function() {
                var index = $(this).closest('.squarekit-modifier-set').data('set-index');
                modifierSets.splice(index, 1);
                renderModifierSets();
                saveData();
            });

            $(document).on('click', '.add-option-btn', function() {
                var $tbody = $(this).siblings('.squarekit-options-table').find('.options-tbody');
                renderOption($tbody, {}, false);
            });

            $(document).on('click', '.remove-option-btn', function() {
                $(this).closest('tr').remove();
                updateModifierSetsFromUI();
            });

            $(document).on('change', '.set-name, .single-choice, .option-name, .option-price, .option-stock', function() {
                updateModifierSetsFromUI();
            });

            // Re-import from Square
            $('#squarekit_reimport_modifiers').on('click', function() {
                var $btn = $(this);
                var productId = $('#post_ID').val();
                
                $btn.prop('disabled', true).text('<?php echo esc_js( __( 'Re-importing...', 'squarekit' ) ); ?>');
                
                $.post(ajaxurl, {
                    action: 'squarekit_reimport_modifiers',
                    product_id: productId,
                    nonce: '<?php echo wp_create_nonce( 'squarekit_admin' ); ?>'
                }, function(response) {
                    if (response.success && response.data.modifiers) {
                        // Replace Square-managed sets with fresh data
                        modifierSets = modifierSets.filter(function(set) {
                            return set.source !== 'square';
                        });
                        
                        // Add fresh Square data
                        $.each(response.data.modifiers, function(i, set) {
                            set.source = 'square';
                            modifierSets.push(set);
                        });
                        
                        renderModifierSets();
                        saveData();
                        alert('<?php echo esc_js( __( 'Modifiers re-imported successfully!', 'squarekit' ) ); ?>');
                    } else {
                        alert('<?php echo esc_js( __( 'Failed to re-import modifiers from Square.', 'squarekit' ) ); ?>');
                    }
                }).fail(function() {
                    alert('<?php echo esc_js( __( 'Error communicating with server.', 'squarekit' ) ); ?>');
                }).always(function() {
                    $btn.prop('disabled', false).text('<?php echo esc_js( __( 'Re-import from Square', 'squarekit' ) ); ?>');
                });
            });

            // Initial render
            renderModifierSets();
        });
        </script>
        <?php
    }

    /**
     * Save modifiers meta
     */
    public function save_modifiers_meta( $post_id, $post ) {
        if ( ! isset( $_POST['squarekit_modifier_sets'] ) ) {
            return;
        }

        $modifier_sets = json_decode( wp_unslash( $_POST['squarekit_modifier_sets'] ), true );
        if ( is_array( $modifier_sets ) ) {
            // Save in primary format
            update_post_meta( $post_id, '_squarekit_modifier_sets', $modifier_sets );
            
            // Also save in legacy format for backward compatibility
            $legacy_format = $this->convert_to_legacy_format( $modifier_sets );
            update_post_meta( $post_id, '_squarekit_modifiers', $legacy_format );
        }
    }

    /**
     * Convert to legacy format for backward compatibility
     */
    private function convert_to_legacy_format( $modifier_sets ) {
        $legacy_format = array();
        
        foreach ( $modifier_sets as $set ) {
            $legacy_format[] = array(
                'name' => $set['set_name'] ?? '',
                'single' => $set['single_choice'] ?? false,
                'square_modifier_list_id' => $set['square_mod_list_id'] ?? '',
                'options' => $set['options'] ?? array()
            );
        }
        
        return $legacy_format;
    }

    /**
     * AJAX handler: Re-import modifiers from Square
     */
    public function ajax_reimport_modifiers() {
        check_ajax_referer( 'squarekit_admin', 'nonce' );
        
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Insufficient permissions.', 'squarekit' ) ), 403 );
        }
        
        $product_id = intval( $_POST['product_id'] );
        if ( ! $product_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid product ID.', 'squarekit' ) ), 400 );
        }
        
        // For now, return empty array - this would be implemented with actual Square API call
        $modifiers = array();
        
        wp_send_json_success( array( 'modifiers' => $modifiers ) );
    }
}
