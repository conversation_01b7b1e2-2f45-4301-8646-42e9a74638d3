<?php
/**
 * SquareKit Attribute Mapper
 *
 * Handles mapping between Square option sets/options and WooCommerce attributes/terms
 *
 * @package SquareKit
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Attribute Mapper Class
 */
class SquareKit_Attribute_Mapper {

    /**
     * Cache for mappings to avoid repeated database queries
     *
     * @var array
     */
    private $mapping_cache = array();

    /**
     * Cache for WooCommerce attributes
     *
     * @var array
     */
    private $wc_attributes_cache = array();

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_cache();
    }

    /**
     * Initialize mapping cache
     */
    private function init_cache() {
        // Load existing mappings into cache
        $this->load_mappings_cache();
        $this->load_wc_attributes_cache();
    }

    /**
     * Map Square item option to WooCommerce attribute
     *
     * @param array $item_option Square item option data
     * @param bool $create_if_missing Whether to create attribute if it doesn't exist
     * @return int|false WooCommerce attribute ID or false on failure
     */
    public function map_option_set_to_attribute( $item_option, $create_if_missing = true ) {
        $square_option_id = $item_option['id'] ?? '';
        $option_name = $item_option['item_option_data']['name'] ?? '';

        if ( empty( $square_option_id ) || empty( $option_name ) ) {
            return false;
        }

        // Check cache first
        if ( isset( $this->mapping_cache['option_sets'][$square_option_id] ) ) {
            return $this->mapping_cache['option_sets'][$square_option_id]['wc_attribute_id'];
        }

        // Check database
        $existing_mapping = $this->get_option_set_mapping( $square_option_id );
        if ( $existing_mapping ) {
            $this->mapping_cache['option_sets'][$square_option_id] = $existing_mapping;
            return $existing_mapping['wc_attribute_id'];
        }

        // Create new attribute if requested
        if ( $create_if_missing ) {
            $attribute_id = $this->create_wc_attribute_from_option_set( $item_option );
            if ( $attribute_id ) {
                $this->store_option_set_mapping( $square_option_id, $attribute_id );
                return $attribute_id;
            }
        }

        return false;
    }

    /**
     * Map Square option value to WooCommerce attribute term
     *
     * @param array $option_value Square option value data
     * @param int $attribute_id WooCommerce attribute ID
     * @param bool $create_if_missing Whether to create term if it doesn't exist
     * @return int|false WooCommerce term ID or false on failure
     */
    public function map_option_to_term( $option_value, $attribute_id, $create_if_missing = true ) {
        $square_option_value_id = $option_value['id'] ?? '';
        // Try both possible data structures
        $option_value_name = $option_value['item_option_value_data']['name'] ?? $option_value['name'] ?? '';

        if ( empty( $square_option_value_id ) || empty( $option_value_name ) || ! $attribute_id ) {
            return false;
        }

        // Check cache first
        if ( isset( $this->mapping_cache['options'][$square_option_value_id] ) ) {
            return $this->mapping_cache['options'][$square_option_value_id]['wc_term_id'];
        }

        // Check database
        $existing_mapping = $this->get_option_mapping( $square_option_value_id );
        if ( $existing_mapping ) {
            $this->mapping_cache['options'][$square_option_value_id] = $existing_mapping;
            return $existing_mapping['wc_term_id'];
        }

        // Create new term if requested
        if ( $create_if_missing ) {
            $term_id = $this->create_wc_term_from_option( $option_value, $attribute_id );
            if ( $term_id ) {
                $this->store_option_mapping( $square_option_value_id, $attribute_id, $term_id );
                return $term_id;
            }
        }

        return false;
    }

    /**
     * Create WooCommerce attribute from Square item option
     *
     * @param array $item_option Square item option data
     * @return int|false Attribute ID or false on failure
     */
    private function create_wc_attribute_from_option_set( $item_option ) {
        $option_name = $item_option['item_option_data']['name'] ?? '';

        if ( empty( $option_name ) ) {
            return false;
        }

        $attribute_slug = sanitize_title( $option_name );
        $attribute_label = $option_name;

        // Check if attribute already exists
        $existing_id = wc_attribute_taxonomy_id_by_name( $attribute_slug );
        if ( $existing_id ) {
            return $existing_id;
        }

        // Create new attribute
        $attribute_id = wc_create_attribute( array(
            'name' => $attribute_label,
            'slug' => $attribute_slug,
            'type' => 'select',
            'order_by' => 'menu_order',
            'has_archives' => false
        ) );

        if ( is_wp_error( $attribute_id ) ) {
            return false;
        }

        return $attribute_id;
    }

    /**
     * Create WooCommerce term from Square option value
     *
     * @param array $option_value Square option value data
     * @param int $attribute_id WooCommerce attribute ID
     * @return int|false Term ID or false on failure
     */
    private function create_wc_term_from_option( $option_value, $attribute_id ) {
        // Try both possible data structures
        $option_value_name = $option_value['item_option_value_data']['name'] ?? $option_value['name'] ?? '';

        if ( empty( $option_value_name ) || ! $attribute_id ) {
            return false;
        }

        // Normalize the option value name to proper title case
        $option_value_name = $this->normalize_attribute_value( $option_value_name );

        // Get attribute taxonomy name
        $attribute = wc_get_attribute( $attribute_id );
        if ( ! $attribute ) {
            return false;
        }

        $taxonomy = wc_attribute_taxonomy_name( $attribute->slug );
        
        // Check if term already exists
        $existing_term = get_term_by( 'name', $option_value_name, $taxonomy );
        if ( $existing_term ) {
            return $existing_term->term_id;
        }

        // Create new term
        $term_result = wp_insert_term( $option_value_name, $taxonomy );
        
        if ( is_wp_error( $term_result ) ) {
            return false;
        }

        return $term_result['term_id'];
    }

    /**
     * Get option set mapping from database
     *
     * @param string $square_option_set_id Square option set ID
     * @return array|false Mapping data or false if not found
     */
    private function get_option_set_mapping( $square_option_set_id ) {
        global $wpdb;

        $result = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}squarekit_attribute_mappings 
             WHERE square_option_set_id = %s AND square_option_id IS NULL",
            $square_option_set_id
        ), ARRAY_A );

        return $result ? $result : false;
    }

    /**
     * Get option mapping from database
     *
     * @param string $square_option_id Square option ID
     * @return array|false Mapping data or false if not found
     */
    private function get_option_mapping( $square_option_id ) {
        global $wpdb;

        $result = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}squarekit_attribute_mappings 
             WHERE square_option_id = %s",
            $square_option_id
        ), ARRAY_A );

        return $result ? $result : false;
    }

    /**
     * Store option set mapping in database
     *
     * @param string $square_option_set_id Square option set ID
     * @param int $wc_attribute_id WooCommerce attribute ID
     * @return bool Success status
     */
    private function store_option_set_mapping( $square_option_set_id, $wc_attribute_id ) {
        global $wpdb;

        $result = $wpdb->insert(
            $wpdb->prefix . 'squarekit_attribute_mappings',
            array(
                'square_option_set_id' => $square_option_set_id,
                'wc_attribute_id' => $wc_attribute_id,
                'created_at' => current_time( 'mysql' )
            ),
            array( '%s', '%d', '%s' )
        );

        if ( $result ) {
            // Update cache
            $this->mapping_cache['option_sets'][$square_option_set_id] = array(
                'wc_attribute_id' => $wc_attribute_id
            );
        }

        return $result !== false;
    }

    /**
     * Store option mapping in database
     *
     * @param string $square_option_id Square option ID
     * @param int $wc_attribute_id WooCommerce attribute ID
     * @param int $wc_term_id WooCommerce term ID
     * @return bool Success status
     */
    private function store_option_mapping( $square_option_id, $wc_attribute_id, $wc_term_id ) {
        global $wpdb;

        $result = $wpdb->insert(
            $wpdb->prefix . 'squarekit_attribute_mappings',
            array(
                'square_option_id' => $square_option_id,
                'wc_attribute_id' => $wc_attribute_id,
                'wc_term_id' => $wc_term_id,
                'created_at' => current_time( 'mysql' )
            ),
            array( '%s', '%d', '%d', '%s' )
        );

        if ( $result ) {
            // Update cache
            $this->mapping_cache['options'][$square_option_id] = array(
                'wc_attribute_id' => $wc_attribute_id,
                'wc_term_id' => $wc_term_id
            );
        }

        return $result !== false;
    }

    /**
     * Load mappings cache from database
     */
    private function load_mappings_cache() {
        global $wpdb;

        // Load option set mappings
        $option_set_mappings = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}squarekit_attribute_mappings 
             WHERE square_option_id IS NULL",
            ARRAY_A
        );

        foreach ( $option_set_mappings as $mapping ) {
            $this->mapping_cache['option_sets'][$mapping['square_option_set_id']] = $mapping;
        }

        // Load option mappings
        $option_mappings = $wpdb->get_results(
            "SELECT * FROM {$wpdb->prefix}squarekit_attribute_mappings 
             WHERE square_option_id IS NOT NULL",
            ARRAY_A
        );

        foreach ( $option_mappings as $mapping ) {
            $this->mapping_cache['options'][$mapping['square_option_id']] = $mapping;
        }
    }

    /**
     * Load WooCommerce attributes cache
     */
    private function load_wc_attributes_cache() {
        $attributes = wc_get_attribute_taxonomies();
        foreach ( $attributes as $attribute ) {
            $this->wc_attributes_cache[$attribute->attribute_id] = $attribute;
        }
    }

    /**
     * Normalize attribute value to proper title case
     *
     * @param string $value Raw attribute value from Square
     * @return string Normalized attribute value
     */
    private function normalize_attribute_value( $value ) {
        // Trim whitespace
        $value = trim( $value );

        // Convert to title case for consistency
        // Handle special cases for common size/measurement values
        $value = strtolower( $value );

        // Special handling for common attribute values
        $special_cases = array(
            'xs' => 'XS',
            'sm' => 'SM',
            's' => 'S',
            'm' => 'M',
            'l' => 'L',
            'xl' => 'XL',
            'xxl' => 'XXL',
            'xxxl' => 'XXXL',
            'small' => 'Small',
            'medium' => 'Medium',
            'large' => 'Large',
            'extra small' => 'Extra Small',
            'extra large' => 'Extra Large',
            'light' => 'Light',
            'medium roast' => 'Medium Roast',
            'dark' => 'Dark',
            'dark roast' => 'Dark Roast',
            'decaf' => 'Decaf',
            'regular' => 'Regular'
        );

        // Check for exact matches first
        if ( isset( $special_cases[ $value ] ) ) {
            return $special_cases[ $value ];
        }

        // Default to title case
        return ucwords( $value );
    }
}
