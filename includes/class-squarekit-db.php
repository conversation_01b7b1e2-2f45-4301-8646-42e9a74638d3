<?php
/**
 * Database operations for the plugin
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Database operations class.
 *
 * This class handles all database operations for the plugin.
 *
 * @since 1.0.0
 */
class SquareKit_DB {

    /**
     * Table names
     *
     * @since 1.0.0
     * @access protected
     * @var array
     */
    protected $tables = array();

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        global $wpdb;
        
        // Define table names
        $this->tables = array(
            'sync_logs'         => $wpdb->prefix . 'squarekit_sync_logs',
            'inventory'         => $wpdb->prefix . 'squarekit_inventory',
            'customers'         => $wpdb->prefix . 'squarekit_customers',
            'locations'         => $wpdb->prefix . 'squarekit_orderable_locations',
            'fetched_products'  => $wpdb->prefix . 'squarekit_fetched_products',
        );
    }

    /**
     * Get table name
     *
     * @since 1.0.0
     * @param string $table Table key
     * @return string Table name
     */
    public function get_table_name( $table ) {
        return isset( $this->tables[ $table ] ) ? $this->tables[ $table ] : '';
    }

    /**
     * Add a log entry
     *
     * @since 1.0.0
     * @param string $type Log type
     * @param string $message Log message
     * @param array $data Additional data
     * @return int|false Log ID on success, false on failure
     */
    public function add_log( $type, $message, $data = array() ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'sync_logs' );
        
        // Prepare data
        $log_data = array(
            'log_type'    => sanitize_text_field( $type ),
            'log_message' => sanitize_text_field( $message ),
            'log_data'    => ! empty( $data ) ? wp_json_encode( $data ) : null,
            'created_at'  => current_time( 'mysql' ),
        );
        
        // Insert log
        $result = $wpdb->insert( $table, $log_data );
        
        // Return log ID or false
        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Get logs
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array Logs
     */
    public function get_logs( $args = array() ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'sync_logs' );
        
        // Default arguments
        $defaults = array(
            'number'     => 20,
            'offset'     => 0,
            'orderby'    => 'created_at',
            'order'      => 'DESC',
            'log_type'   => '',
            'search'     => '',
            'date_query' => array(),
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        // Build query
        $query = "SELECT * FROM {$table}";
        $where = array();
        
        // Filter by log type
        if ( ! empty( $args['log_type'] ) ) {
            $where[] = $wpdb->prepare( "log_type = %s", $args['log_type'] );
        }
        
        // Search
        if ( ! empty( $args['search'] ) ) {
            $search = '%' . $wpdb->esc_like( $args['search'] ) . '%';
            $where[] = $wpdb->prepare( "(log_message LIKE %s OR log_data LIKE %s)", $search, $search );
        }
        
        // Date query
        if ( ! empty( $args['date_query'] ) ) {
            $date_query = $args['date_query'];
            
            if ( ! empty( $date_query['after'] ) ) {
                $where[] = $wpdb->prepare( "created_at >= %s", $date_query['after'] );
            }
            
            if ( ! empty( $date_query['before'] ) ) {
                $where[] = $wpdb->prepare( "created_at <= %s", $date_query['before'] );
            }
        }
        
        // Add where clause
        if ( ! empty( $where ) ) {
            $query .= ' WHERE ' . implode( ' AND ', $where );
        }
        
        // Order
        $query .= $wpdb->prepare( " ORDER BY {$args['orderby']} {$args['order']} LIMIT %d OFFSET %d", $args['number'], $args['offset'] );
        
        // Get results
        $results = $wpdb->get_results( $query );
        
        // Parse log data
        foreach ( $results as $key => $result ) {
            if ( ! empty( $result->log_data ) ) {
                $results[ $key ]->log_data = json_decode( $result->log_data, true );
            }
        }
        
        return $results;
    }

    /**
     * Count logs
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return int Count
     */
    public function count_logs( $args = array() ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'sync_logs' );
        
        // Default arguments
        $defaults = array(
            'log_type'   => '',
            'search'     => '',
            'date_query' => array(),
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        // Build query
        $query = "SELECT COUNT(*) FROM {$table}";
        $where = array();
        
        // Filter by log type
        if ( ! empty( $args['log_type'] ) ) {
            $where[] = $wpdb->prepare( "log_type = %s", $args['log_type'] );
        }
        
        // Search
        if ( ! empty( $args['search'] ) ) {
            $search = '%' . $wpdb->esc_like( $args['search'] ) . '%';
            $where[] = $wpdb->prepare( "(log_message LIKE %s OR log_data LIKE %s)", $search, $search );
        }
        
        // Date query
        if ( ! empty( $args['date_query'] ) ) {
            $date_query = $args['date_query'];
            
            if ( ! empty( $date_query['after'] ) ) {
                $where[] = $wpdb->prepare( "created_at >= %s", $date_query['after'] );
            }
            
            if ( ! empty( $date_query['before'] ) ) {
                $where[] = $wpdb->prepare( "created_at <= %s", $date_query['before'] );
            }
        }
        
        // Add where clause
        if ( ! empty( $where ) ) {
            $query .= ' WHERE ' . implode( ' AND ', $where );
        }
        
        // Get count
        return (int) $wpdb->get_var( $query );
    }

    /**
     * Update inventory
     *
     * @since 1.0.0
     * @param array $data Inventory data
     * @return int|false Number of rows affected on success, false on failure
     */
    public function update_inventory( $data ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'inventory' );
        
        // Required fields
        $required = array( 'square_id', 'wc_product_id', 'location_id' );
        
        foreach ( $required as $field ) {
            if ( empty( $data[ $field ] ) ) {
                return false;
            }
        }
        
        // Prepare data
        $inventory_data = array(
            'square_id'     => sanitize_text_field( $data['square_id'] ),
            'wc_product_id' => absint( $data['wc_product_id'] ),
            'variation_id'  => isset( $data['variation_id'] ) ? absint( $data['variation_id'] ) : 0,
            'quantity'      => isset( $data['quantity'] ) ? absint( $data['quantity'] ) : 0,
            'location_id'   => sanitize_text_field( $data['location_id'] ),
            'updated_at'    => current_time( 'mysql' ),
        );
        
        // Check if inventory exists
        $exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM {$table} WHERE square_id = %s AND wc_product_id = %d AND variation_id = %d AND location_id = %s",
                $inventory_data['square_id'],
                $inventory_data['wc_product_id'],
                $inventory_data['variation_id'],
                $inventory_data['location_id']
            )
        );
        
        // Update or insert
        if ( $exists ) {
            return $wpdb->update(
                $table,
                $inventory_data,
                array(
                    'square_id'     => $inventory_data['square_id'],
                    'wc_product_id' => $inventory_data['wc_product_id'],
                    'variation_id'  => $inventory_data['variation_id'],
                    'location_id'   => $inventory_data['location_id'],
                )
            );
        } else {
            $result = $wpdb->insert( $table, $inventory_data );
            return $result ? $wpdb->insert_id : false;
        }
    }

    /**
     * Get inventory
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array Inventory
     */
    public function get_inventory( $args = array() ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'inventory' );
        
        // Default arguments
        $defaults = array(
            'square_id'     => '',
            'wc_product_id' => 0,
            'variation_id'  => 0,
            'location_id'   => '',
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        // Build query
        $query = "SELECT * FROM {$table}";
        $where = array();
        
        // Filter by square ID
        if ( ! empty( $args['square_id'] ) ) {
            $where[] = $wpdb->prepare( "square_id = %s", $args['square_id'] );
        }
        
        // Filter by WC product ID
        if ( ! empty( $args['wc_product_id'] ) ) {
            $where[] = $wpdb->prepare( "wc_product_id = %d", $args['wc_product_id'] );
        }
        
        // Filter by variation ID
        if ( ! empty( $args['variation_id'] ) ) {
            $where[] = $wpdb->prepare( "variation_id = %d", $args['variation_id'] );
        }
        
        // Filter by location ID
        if ( ! empty( $args['location_id'] ) ) {
            $where[] = $wpdb->prepare( "location_id = %s", $args['location_id'] );
        }
        
        // Add where clause
        if ( ! empty( $where ) ) {
            $query .= ' WHERE ' . implode( ' AND ', $where );
        }
        
        // Get results
        return $wpdb->get_results( $query );
    }

    /**
     * Update customer
     *
     * @since 1.0.0
     * @param array $data Customer data
     * @return int|false Number of rows affected on success, false on failure
     */
    public function update_customer( $data ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'customers' );
        
        // Required fields
        $required = array( 'square_id', 'wc_customer_id' );
        
        foreach ( $required as $field ) {
            if ( empty( $data[ $field ] ) ) {
                return false;
            }
        }
        
        // Prepare data
        $customer_data = array(
            'square_id'      => sanitize_text_field( $data['square_id'] ),
            'wc_customer_id' => absint( $data['wc_customer_id'] ),
            'loyalty_id'     => isset( $data['loyalty_id'] ) ? sanitize_text_field( $data['loyalty_id'] ) : null,
            'loyalty_points' => isset( $data['loyalty_points'] ) ? absint( $data['loyalty_points'] ) : 0,
            'updated_at'     => current_time( 'mysql' ),
        );
        
        // Check if customer exists
        $exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM {$table} WHERE square_id = %s AND wc_customer_id = %d",
                $customer_data['square_id'],
                $customer_data['wc_customer_id']
            )
        );
        
        // Update or insert
        if ( $exists ) {
            return $wpdb->update(
                $table,
                $customer_data,
                array(
                    'square_id'      => $customer_data['square_id'],
                    'wc_customer_id' => $customer_data['wc_customer_id'],
                )
            );
        } else {
            $result = $wpdb->insert( $table, $customer_data );
            return $result ? $wpdb->insert_id : false;
        }
    }

    /**
     * Get customer
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array|object|null Customer
     */
    public function get_customer( $args = array() ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'customers' );
        
        // Default arguments
        $defaults = array(
            'square_id'      => '',
            'wc_customer_id' => 0,
            'loyalty_id'     => '',
            'single'         => true,
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        // Build query
        $query = "SELECT * FROM {$table}";
        $where = array();
        
        // Filter by square ID
        if ( ! empty( $args['square_id'] ) ) {
            $where[] = $wpdb->prepare( "square_id = %s", $args['square_id'] );
        }
        
        // Filter by WC customer ID
        if ( ! empty( $args['wc_customer_id'] ) ) {
            $where[] = $wpdb->prepare( "wc_customer_id = %d", $args['wc_customer_id'] );
        }
        
        // Filter by loyalty ID
        if ( ! empty( $args['loyalty_id'] ) ) {
            $where[] = $wpdb->prepare( "loyalty_id = %s", $args['loyalty_id'] );
        }
        
        // Add where clause
        if ( ! empty( $where ) ) {
            $query .= ' WHERE ' . implode( ' AND ', $where );
        }
        
        // Get results
        if ( $args['single'] ) {
            return $wpdb->get_row( $query );
        } else {
            return $wpdb->get_results( $query );
        }
    }

    /**
     * Add or update location
     *
     * @since 1.0.0
     * @param array $data Location data
     * @return int|false Number of rows affected on success, false on failure
     */
    public function update_location( $data ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'locations' );
        
        // Required fields
        $required = array( 'square_location_id', 'location_name' );
        
        foreach ( $required as $field ) {
            if ( empty( $data[ $field ] ) ) {
                return false;
            }
        }
        
        // Prepare data
        $location_data = array(
            'square_location_id'               => sanitize_text_field( $data['square_location_id'] ),
            'location_name'                    => sanitize_text_field( $data['location_name'] ),
            'address_line_1'                   => isset( $data['address_line_1'] ) ? sanitize_text_field( $data['address_line_1'] ) : null,
            'address_line_2'                   => isset( $data['address_line_2'] ) ? sanitize_text_field( $data['address_line_2'] ) : null,
            'locality'                         => isset( $data['locality'] ) ? sanitize_text_field( $data['locality'] ) : null,
            'administrative_district_level_1'  => isset( $data['administrative_district_level_1'] ) ? sanitize_text_field( $data['administrative_district_level_1'] ) : null,
            'postal_code'                      => isset( $data['postal_code'] ) ? sanitize_text_field( $data['postal_code'] ) : null,
            'country'                          => isset( $data['country'] ) ? sanitize_text_field( $data['country'] ) : null,
            'phone_number'                     => isset( $data['phone_number'] ) ? sanitize_text_field( $data['phone_number'] ) : null,
            'status'                           => isset( $data['status'] ) ? sanitize_text_field( $data['status'] ) : 'active',
            'updated_at'                       => current_time( 'mysql' ),
        );
        
        // Check if location exists
        $exists = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM {$table} WHERE square_location_id = %s",
                $location_data['square_location_id']
            )
        );
        
        // Update or insert
        if ( $exists ) {
            return $wpdb->update(
                $table,
                $location_data,
                array(
                    'square_location_id' => $location_data['square_location_id'],
                )
            );
        } else {
            $location_data['created_at'] = current_time( 'mysql' );
            $result = $wpdb->insert( $table, $location_data );
            return $result ? $wpdb->insert_id : false;
        }
    }

    /**
     * Get locations
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array Locations
     */
    public function get_locations( $args = array() ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'locations' );
        
        // Default arguments
        $defaults = array(
            'square_location_id' => '',
            'status'            => 'active',
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        // Build query
        $query = "SELECT * FROM {$table}";
        $where = array();
        
        // Filter by square location ID
        if ( ! empty( $args['square_location_id'] ) ) {
            $where[] = $wpdb->prepare( "square_location_id = %s", $args['square_location_id'] );
        }
        
        // Filter by status
        if ( ! empty( $args['status'] ) ) {
            $where[] = $wpdb->prepare( "status = %s", $args['status'] );
        }
        
        // Add where clause
        if ( ! empty( $where ) ) {
            $query .= ' WHERE ' . implode( ' AND ', $where );
        }
        
        // Get results
        return $wpdb->get_results( $query );
    }

    /**
     * Delete logs older than specified days
     *
     * @since 1.0.0
     * @param int $days Number of days
     * @return int|false Number of rows affected on success, false on failure
     */
    public function delete_old_logs( $days = 30 ) {
        global $wpdb;
        
        $table = $this->get_table_name( 'sync_logs' );
        
        // Calculate date
        $date = date( 'Y-m-d H:i:s', strtotime( "-{$days} days" ) );
        
        // Delete logs
        return $wpdb->query(
            $wpdb->prepare(
                "DELETE FROM {$table} WHERE created_at < %s",
                $date
            )
        );
    }

    /**
     * Save fetched products to cache
     *
     * @since 1.0.0
     * @param array $products Array of Square product objects
     * @param string $session_id Unique session identifier
     * @return bool Success status
     */
    public function save_fetched_products( $products, $session_id = null ) {
        global $wpdb;

        if ( empty( $products ) ) {
            return false;
        }

        $table = $this->get_table_name( 'fetched_products' );

        // Generate session ID if not provided
        if ( ! $session_id ) {
            $session_id = uniqid( 'fetch_' . time() . '_' );
        }

        // Clear existing products for this session
        $this->clear_fetched_products( $session_id );

        $success_count = 0;

        foreach ( $products as $product ) {
            if ( empty( $product['id'] ) ) {
                continue;
            }

            $product_data = array(
                'square_id'         => sanitize_text_field( $product['id'] ),
                'product_data'      => wp_json_encode( $product ),
                'fetch_session_id'  => sanitize_text_field( $session_id ),
                'fetched_at'        => current_time( 'mysql' ),
                'expires_at'        => date( 'Y-m-d H:i:s', strtotime( '+24 hours' ) ), // Cache for 24 hours
                'is_imported'       => 0,
                'import_status'     => 'not_imported',
            );

            $result = $wpdb->insert( $table, $product_data );
            if ( $result ) {
                $success_count++;
            }
        }

        return $success_count > 0;
    }

    /**
     * Get fetched products from cache
     *
     * @since 1.0.0
     * @param string $session_id Optional session ID to filter by
     * @param array $args Query arguments
     * @return array Cached products
     */
    public function get_fetched_products( $session_id = null, $args = array() ) {
        global $wpdb;

        $table = $this->get_table_name( 'fetched_products' );

        $defaults = array(
            'search'        => '',
            'import_status' => '',
            'limit'         => 20,
            'offset'        => 0,
            'order_by'      => 'fetched_at',
            'order'         => 'DESC'
        );

        $args = wp_parse_args( $args, $defaults );

        // Build WHERE clause
        $where_conditions = array();
        $where_values = array();

        // Filter by session ID
        if ( $session_id ) {
            $where_conditions[] = 'fetch_session_id = %s';
            $where_values[] = $session_id;
        } else {
            // Get latest session if no session specified
            $latest_session = $this->get_latest_fetch_session();
            if ( $latest_session ) {
                $where_conditions[] = 'fetch_session_id = %s';
                $where_values[] = $latest_session;
            }
        }

        // Filter by import status
        if ( ! empty( $args['import_status'] ) ) {
            $where_conditions[] = 'import_status = %s';
            $where_values[] = $args['import_status'];
        }

        // Search filter
        if ( ! empty( $args['search'] ) ) {
            $where_conditions[] = 'product_data LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like( $args['search'] ) . '%';
        }

        // Remove expired products
        $where_conditions[] = '(expires_at IS NULL OR expires_at > %s)';
        $where_values[] = current_time( 'mysql' );

        $where_clause = ! empty( $where_conditions ) ? 'WHERE ' . implode( ' AND ', $where_conditions ) : '';

        // Build ORDER BY clause
        $order_by = sanitize_sql_orderby( $args['order_by'] . ' ' . $args['order'] );

        // Build LIMIT clause
        $limit_clause = '';
        if ( $args['limit'] > 0 ) {
            $limit_clause = $wpdb->prepare( 'LIMIT %d OFFSET %d', $args['limit'], $args['offset'] );
        }

        // Execute query
        $query = "SELECT * FROM {$table} {$where_clause} ORDER BY {$order_by} {$limit_clause}";

        if ( ! empty( $where_values ) ) {
            $query = $wpdb->prepare( $query, $where_values );
        }

        $results = $wpdb->get_results( $query );

        // Decode product data
        $products = array();
        foreach ( $results as $row ) {
            $product_data = json_decode( $row->product_data, true );
            if ( $product_data ) {
                $product_data['cache_id'] = $row->id;
                $product_data['fetch_session_id'] = $row->fetch_session_id;
                $product_data['fetched_at'] = $row->fetched_at;
                $product_data['is_imported'] = (bool) $row->is_imported;
                $product_data['import_status'] = $row->import_status;
                $product_data['wc_product_id'] = $row->wc_product_id;
                $products[] = $product_data;
            }
        }

        return $products;
    }

    /**
     * Get count of fetched products
     *
     * @since 1.0.0
     * @param string $session_id Optional session ID to filter by
     * @param array $args Query arguments
     * @return int Count of cached products
     */
    public function get_fetched_products_count( $session_id = null, $args = array() ) {
        global $wpdb;

        $table = $this->get_table_name( 'fetched_products' );

        $defaults = array(
            'search'        => '',
            'import_status' => ''
        );

        $args = wp_parse_args( $args, $defaults );

        // Build WHERE clause
        $where_conditions = array();
        $where_values = array();

        // Filter by session ID
        if ( $session_id ) {
            $where_conditions[] = 'fetch_session_id = %s';
            $where_values[] = $session_id;
        } else {
            // Get latest session if no session specified
            $latest_session = $this->get_latest_fetch_session();
            if ( $latest_session ) {
                $where_conditions[] = 'fetch_session_id = %s';
                $where_values[] = $latest_session;
            }
        }

        // Filter by import status
        if ( ! empty( $args['import_status'] ) ) {
            $where_conditions[] = 'import_status = %s';
            $where_values[] = $args['import_status'];
        }

        // Search filter
        if ( ! empty( $args['search'] ) ) {
            $where_conditions[] = 'product_data LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like( $args['search'] ) . '%';
        }

        // Remove expired products
        $where_conditions[] = '(expires_at IS NULL OR expires_at > %s)';
        $where_values[] = current_time( 'mysql' );

        $where_clause = ! empty( $where_conditions ) ? 'WHERE ' . implode( ' AND ', $where_conditions ) : '';

        // Execute count query
        $query = "SELECT COUNT(*) FROM {$table} {$where_clause}";

        if ( ! empty( $where_values ) ) {
            $query = $wpdb->prepare( $query, $where_values );
        }

        return (int) $wpdb->get_var( $query );
    }

    /**
     * Get latest fetch session ID
     *
     * @since 1.0.0
     * @return string|null Latest session ID
     */
    public function get_latest_fetch_session() {
        global $wpdb;

        $table = $this->get_table_name( 'fetched_products' );

        return $wpdb->get_var(
            $wpdb->prepare(
                "SELECT fetch_session_id FROM {$table}
                WHERE expires_at IS NULL OR expires_at > %s
                ORDER BY fetched_at DESC
                LIMIT 1",
                current_time( 'mysql' )
            )
        );
    }

    /**
     * Clear fetched products cache
     *
     * @since 1.0.0
     * @param string $session_id Optional session ID to clear specific session
     * @return bool Success status
     */
    public function clear_fetched_products( $session_id = null ) {
        global $wpdb;

        $table = $this->get_table_name( 'fetched_products' );

        if ( $session_id ) {
            // Clear specific session
            return $wpdb->delete( $table, array( 'fetch_session_id' => $session_id ) ) !== false;
        } else {
            // Clear all expired products
            return $wpdb->query(
                $wpdb->prepare(
                    "DELETE FROM {$table} WHERE expires_at IS NOT NULL AND expires_at <= %s",
                    current_time( 'mysql' )
                )
            ) !== false;
        }
    }

    /**
     * Update product import status
     *
     * @since 1.0.0
     * @param string $square_id Square product ID
     * @param string $status Import status
     * @param int $wc_product_id WooCommerce product ID
     * @return bool Success status
     */
    public function update_product_import_status( $square_id, $status, $wc_product_id = null ) {
        global $wpdb;

        $table = $this->get_table_name( 'fetched_products' );

        $update_data = array(
            'import_status' => sanitize_text_field( $status ),
            'is_imported'   => in_array( $status, array( 'imported', 'synced' ) ) ? 1 : 0,
        );

        if ( $wc_product_id ) {
            $update_data['wc_product_id'] = intval( $wc_product_id );
        }

        return $wpdb->update(
            $table,
            $update_data,
            array( 'square_id' => $square_id )
        ) !== false;
    }

    /**
     * Get cache statistics
     *
     * @since 1.0.0
     * @param string $session_id Optional session ID
     * @return array Cache statistics
     */
    public function get_cache_stats( $session_id = null ) {
        global $wpdb;

        $table = $this->get_table_name( 'fetched_products' );

        $where_clause = '';
        $where_values = array();

        if ( $session_id ) {
            $where_clause = 'WHERE fetch_session_id = %s';
            $where_values[] = $session_id;
        } else {
            // Get stats for latest session
            $latest_session = $this->get_latest_fetch_session();
            if ( $latest_session ) {
                $where_clause = 'WHERE fetch_session_id = %s';
                $where_values[] = $latest_session;
            }
        }

        $query = "SELECT
            COUNT(*) as total_products,
            SUM(CASE WHEN is_imported = 1 THEN 1 ELSE 0 END) as imported_count,
            SUM(CASE WHEN import_status = 'not_imported' THEN 1 ELSE 0 END) as not_imported_count,
            SUM(CASE WHEN import_status = 'importing' THEN 1 ELSE 0 END) as importing_count,
            SUM(CASE WHEN import_status = 'failed' THEN 1 ELSE 0 END) as failed_count,
            MAX(fetched_at) as last_fetch_time
            FROM {$table} {$where_clause}";

        if ( ! empty( $where_values ) ) {
            $query = $wpdb->prepare( $query, $where_values );
        }

        $stats = $wpdb->get_row( $query, ARRAY_A );

        return $stats ? $stats : array(
            'total_products'     => 0,
            'imported_count'     => 0,
            'not_imported_count' => 0,
            'importing_count'    => 0,
            'failed_count'       => 0,
            'last_fetch_time'    => null
        );
    }
}