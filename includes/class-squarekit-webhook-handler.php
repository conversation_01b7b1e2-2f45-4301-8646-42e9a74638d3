<?php
/**
 * SquareKit Webhook Handler
 * Handles incoming webhooks from Square for real-time synchronization
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 * @since 1.1.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Webhook Handler Class
 * 
 * Processes Square webhooks for payment updates, inventory changes,
 * order synchronization, and other real-time events.
 */
class SquareKit_Webhook_Handler {

    /**
     * Webhook endpoint slug
     * @var string
     */
    private $endpoint_slug = 'squarekit-webhook';

    /**
     * Payment logger instance
     * @var SquareKit_Payment_Logger
     */
    private $payment_logger;

    /**
     * Logger instance
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Settings instance
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Supported webhook events
     * @var array
     */
    private $supported_events = array(
        'payment.created',
        'payment.updated',
        'refund.created',
        'refund.updated',
        'dispute.created',
        'dispute.updated',
        'order.created',
        'order.updated',
        'order.fulfilled',
        'inventory.count.updated',
        'catalog.version.updated'
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->payment_logger = new SquareKit_Payment_Logger();
        $this->logger = new SquareKit_Logger();
        $this->settings = new SquareKit_Settings();

        // Initialize webhook endpoint
        add_action( 'init', array( $this, 'add_webhook_endpoint' ) );
        add_action( 'template_redirect', array( $this, 'handle_webhook_request' ) );
        
        // Add webhook URL to admin
        add_action( 'admin_init', array( $this, 'register_webhook_settings' ) );
    }

    /**
     * Add webhook endpoint
     */
    public function add_webhook_endpoint() {
        add_rewrite_rule(
            '^' . $this->endpoint_slug . '/?$',
            'index.php?squarekit_webhook=1',
            'top'
        );
        
        add_rewrite_tag( '%squarekit_webhook%', '([^&]+)' );
    }

    /**
     * Handle incoming webhook requests
     */
    public function handle_webhook_request() {
        if ( ! get_query_var( 'squarekit_webhook' ) ) {
            return;
        }

        // Verify request method
        if ( $_SERVER['REQUEST_METHOD'] !== 'POST' ) {
            $this->send_webhook_response( 405, 'Method not allowed' );
            return;
        }

        try {
            // Get raw POST data
            $raw_body = file_get_contents( 'php://input' );
            
            if ( empty( $raw_body ) ) {
                throw new Exception( 'Empty webhook payload' );
            }

            // Verify webhook signature
            if ( ! $this->verify_webhook_signature( $raw_body ) ) {
                throw new Exception( 'Invalid webhook signature' );
            }

            // Parse webhook data
            $webhook_data = json_decode( $raw_body, true );
            
            if ( json_last_error() !== JSON_ERROR_NONE ) {
                throw new Exception( 'Invalid JSON payload: ' . json_last_error_msg() );
            }

            // Process webhook
            $this->process_webhook( $webhook_data );

            // Send success response
            $this->send_webhook_response( 200, 'Webhook processed successfully' );

        } catch ( Exception $e ) {
            // Log error
            $this->logger->log( 'error', 'webhook_error', 'Webhook processing failed: ' . $e->getMessage(), array(
                'error_code' => $e->getCode(),
                'request_headers' => getallheaders(),
                'request_body' => substr( $raw_body ?? '', 0, 1000 ) // Truncate for security
            ) );

            // Send error response
            $this->send_webhook_response( 400, 'Webhook processing failed: ' . $e->getMessage() );
        }
    }

    /**
     * Verify webhook signature
     *
     * @param string $payload Raw webhook payload
     * @return bool
     */
    private function verify_webhook_signature( $payload ) {
        $signature_header = $_SERVER['HTTP_X_SQUARE_SIGNATURE'] ?? '';
        
        if ( empty( $signature_header ) ) {
            return false;
        }

        // Get webhook signature key from settings
        $signature_key = $this->settings->get( 'webhook_signature_key' );
        
        if ( empty( $signature_key ) ) {
            $this->logger->log( 'warning', 'webhook_signature_missing', 'Webhook signature key not configured' );
            return false;
        }

        // Calculate expected signature
        $expected_signature = base64_encode( hash_hmac( 'sha1', $payload, $signature_key, true ) );
        
        // Compare signatures
        return hash_equals( $expected_signature, $signature_header );
    }

    /**
     * Process webhook event
     *
     * @param array $webhook_data
     */
    private function process_webhook( $webhook_data ) {
        $event_type = $webhook_data['type'] ?? '';
        $event_data = $webhook_data['data'] ?? array();
        $event_id = $webhook_data['event_id'] ?? '';

        // Log webhook received
        $this->logger->log( 'info', 'webhook_received', 'Webhook received: ' . $event_type, array(
            'event_id' => $event_id,
            'event_type' => $event_type,
            'merchant_id' => $webhook_data['merchant_id'] ?? '',
            'location_id' => $webhook_data['location_id'] ?? ''
        ) );

        // Check if event is supported
        if ( ! in_array( $event_type, $this->supported_events, true ) ) {
            $this->logger->log( 'warning', 'webhook_unsupported', 'Unsupported webhook event: ' . $event_type );
            return;
        }

        // Check for duplicate events
        if ( $this->is_duplicate_event( $event_id ) ) {
            $this->logger->log( 'info', 'webhook_duplicate', 'Duplicate webhook event ignored: ' . $event_id );
            return;
        }

        // Mark event as processed
        $this->mark_event_processed( $event_id );

        // Route to appropriate handler
        switch ( $event_type ) {
            case 'payment.created':
            case 'payment.updated':
                $this->handle_payment_webhook( $event_type, $event_data );
                break;

            case 'refund.created':
            case 'refund.updated':
                $this->handle_refund_webhook( $event_type, $event_data );
                break;

            case 'dispute.created':
            case 'dispute.updated':
                $this->handle_dispute_webhook( $event_type, $event_data );
                break;

            case 'order.created':
            case 'order.updated':
            case 'order.fulfilled':
                $this->handle_order_webhook( $event_type, $event_data );
                break;

            case 'inventory.count.updated':
                $this->handle_inventory_webhook( $event_type, $event_data );
                break;

            case 'catalog.version.updated':
                $this->handle_catalog_webhook( $event_type, $event_data );
                break;

            default:
                $this->logger->log( 'warning', 'webhook_unhandled', 'Unhandled webhook event: ' . $event_type );
        }
    }

    /**
     * Handle payment webhooks
     *
     * @param string $event_type
     * @param array $event_data
     */
    private function handle_payment_webhook( $event_type, $event_data ) {
        $payment_id = $event_data['id'] ?? '';
        $order_id = $event_data['reference_id'] ?? '';

        if ( empty( $payment_id ) || empty( $order_id ) ) {
            throw new Exception( 'Missing payment ID or order reference' );
        }

        // Get WooCommerce order
        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            throw new Exception( 'Order not found: ' . $order_id );
        }

        // Log payment webhook
        $this->payment_logger->log( 'info', 'webhook_' . str_replace( '.', '_', $event_type ), 
            'Payment webhook received: ' . $event_type, array(
                'payment_id' => $payment_id,
                'order_id' => $order_id,
                'payment_status' => $event_data['status'] ?? '',
                'amount' => isset( $event_data['amount_money'] ) ? $event_data['amount_money']['amount'] / 100 : null,
                'currency' => $event_data['amount_money']['currency'] ?? null
            )
        );

        // Update order based on payment status
        $payment_status = $event_data['status'] ?? '';
        
        switch ( $payment_status ) {
            case 'COMPLETED':
                if ( ! $order->is_paid() ) {
                    $order->payment_complete( $payment_id );
                    $order->add_order_note( sprintf( 
                        __( 'Payment completed via Square webhook. Payment ID: %s', 'squarekit' ), 
                        $payment_id 
                    ) );
                }
                break;

            case 'FAILED':
                $order->update_status( 'failed', __( 'Payment failed (Square webhook)', 'squarekit' ) );
                break;

            case 'CANCELED':
                $order->update_status( 'cancelled', __( 'Payment cancelled (Square webhook)', 'squarekit' ) );
                break;
        }
    }

    /**
     * Handle refund webhooks
     *
     * @param string $event_type
     * @param array $event_data
     */
    private function handle_refund_webhook( $event_type, $event_data ) {
        $refund_id = $event_data['id'] ?? '';
        $payment_id = $event_data['payment_id'] ?? '';
        $amount = isset( $event_data['amount_money'] ) ? $event_data['amount_money']['amount'] / 100 : 0;

        // Find order by payment ID
        $orders = wc_get_orders( array(
            'meta_key' => '_transaction_id',
            'meta_value' => $payment_id,
            'limit' => 1
        ) );

        if ( empty( $orders ) ) {
            throw new Exception( 'Order not found for payment ID: ' . $payment_id );
        }

        $order = $orders[0];

        // Log refund webhook
        $this->payment_logger->log( 'info', 'webhook_' . str_replace( '.', '_', $event_type ), 
            'Refund webhook received: ' . $event_type, array(
                'refund_id' => $refund_id,
                'payment_id' => $payment_id,
                'order_id' => $order->get_id(),
                'amount' => $amount,
                'status' => $event_data['status'] ?? ''
            )
        );

        // Create WooCommerce refund if not exists
        if ( $event_data['status'] === 'COMPLETED' ) {
            $existing_refunds = $order->get_refunds();
            $refund_exists = false;

            foreach ( $existing_refunds as $refund ) {
                if ( $refund->get_meta( '_square_refund_id' ) === $refund_id ) {
                    $refund_exists = true;
                    break;
                }
            }

            if ( ! $refund_exists ) {
                $refund = wc_create_refund( array(
                    'order_id' => $order->get_id(),
                    'amount' => $amount,
                    'reason' => __( 'Refund processed via Square', 'squarekit' )
                ) );

                if ( $refund && ! is_wp_error( $refund ) ) {
                    $refund->update_meta_data( '_square_refund_id', $refund_id );
                    $refund->save();
                }
            }
        }
    }

    /**
     * Send webhook response
     *
     * @param int $status_code
     * @param string $message
     */
    private function send_webhook_response( $status_code, $message ) {
        status_header( $status_code );
        header( 'Content-Type: application/json' );
        
        echo wp_json_encode( array(
            'status' => $status_code,
            'message' => $message,
            'timestamp' => current_time( 'mysql' )
        ) );
        
        exit;
    }

    /**
     * Check if event is duplicate
     *
     * @param string $event_id
     * @return bool
     */
    private function is_duplicate_event( $event_id ) {
        return get_transient( 'squarekit_webhook_' . $event_id ) !== false;
    }

    /**
     * Mark event as processed
     *
     * @param string $event_id
     */
    private function mark_event_processed( $event_id ) {
        set_transient( 'squarekit_webhook_' . $event_id, true, 24 * HOUR_IN_SECONDS );
    }

    /**
     * Get webhook URL
     *
     * @return string
     */
    public function get_webhook_url() {
        return home_url( '/' . $this->endpoint_slug . '/' );
    }

    /**
     * Handle dispute webhooks
     *
     * @param string $event_type
     * @param array $event_data
     */
    private function handle_dispute_webhook( $event_type, $event_data ) {
        $dispute_id = $event_data['id'] ?? '';
        $payment_id = $event_data['disputed_payment']['payment_id'] ?? '';

        // Find order by payment ID
        $orders = wc_get_orders( array(
            'meta_key' => '_transaction_id',
            'meta_value' => $payment_id,
            'limit' => 1
        ) );

        if ( empty( $orders ) ) {
            throw new Exception( 'Order not found for disputed payment ID: ' . $payment_id );
        }

        $order = $orders[0];

        // Log dispute webhook
        $this->payment_logger->log( 'warning', 'webhook_' . str_replace( '.', '_', $event_type ),
            'Dispute webhook received: ' . $event_type, array(
                'dispute_id' => $dispute_id,
                'payment_id' => $payment_id,
                'order_id' => $order->get_id(),
                'dispute_state' => $event_data['state'] ?? '',
                'reason' => $event_data['reason'] ?? ''
            )
        );

        // Add order note about dispute
        $order->add_order_note( sprintf(
            __( 'Payment dispute %s. Dispute ID: %s. Reason: %s', 'squarekit' ),
            strtolower( str_replace( 'dispute.', '', $event_type ) ),
            $dispute_id,
            $event_data['reason'] ?? 'Unknown'
        ) );

        // Update order status if dispute is created
        if ( $event_type === 'dispute.created' ) {
            $order->update_status( 'on-hold', __( 'Payment disputed - requires attention', 'squarekit' ) );
        }
    }

    /**
     * Handle order webhooks
     *
     * @param string $event_type
     * @param array $event_data
     */
    private function handle_order_webhook( $event_type, $event_data ) {
        $square_order_id = $event_data['id'] ?? '';
        $reference_id = $event_data['reference_id'] ?? '';

        // Log order webhook
        $this->logger->log( 'info', 'webhook_' . str_replace( '.', '_', $event_type ),
            'Order webhook received: ' . $event_type, array(
                'square_order_id' => $square_order_id,
                'reference_id' => $reference_id,
                'state' => $event_data['state'] ?? ''
            )
        );

        // Check if sync is enabled for orders
        if ( ! $this->settings->get( 'enable_order_sync' ) ) {
            $this->logger->log( 'info', 'webhook_order_sync_disabled', 'Order sync disabled, skipping webhook' );
            return;
        }

        // Handle different order events
        switch ( $event_type ) {
            case 'order.created':
                $this->sync_square_order_to_woocommerce( $event_data );
                break;

            case 'order.updated':
                $this->update_woocommerce_order_from_square( $event_data );
                break;

            case 'order.fulfilled':
                $this->mark_order_fulfilled( $event_data );
                break;
        }
    }

    /**
     * Handle inventory webhooks
     *
     * @param string $event_type
     * @param array $event_data
     */
    private function handle_inventory_webhook( $event_type, $event_data ) {
        $catalog_object_id = $event_data['catalog_object_id'] ?? '';
        $location_id = $event_data['location_id'] ?? '';
        $quantity = $event_data['quantity'] ?? '';

        // Log inventory webhook
        $this->logger->log( 'info', 'webhook_inventory_updated',
            'Inventory webhook received', array(
                'catalog_object_id' => $catalog_object_id,
                'location_id' => $location_id,
                'quantity' => $quantity
            )
        );

        // Check if inventory sync is enabled
        if ( ! $this->settings->get( 'enable_inventory_sync' ) ) {
            $this->logger->log( 'info', 'webhook_inventory_sync_disabled', 'Inventory sync disabled, skipping webhook' );
            return;
        }

        // Find WooCommerce product by Square catalog object ID
        $products = get_posts( array(
            'post_type' => 'product',
            'meta_key' => '_square_item_variation_id',
            'meta_value' => $catalog_object_id,
            'posts_per_page' => 1
        ) );

        if ( empty( $products ) ) {
            $this->logger->log( 'warning', 'webhook_product_not_found',
                'Product not found for Square catalog object: ' . $catalog_object_id );
            return;
        }

        $product = wc_get_product( $products[0]->ID );
        if ( ! $product ) {
            return;
        }

        // Update inventory
        $product->set_stock_quantity( intval( $quantity ) );
        $product->set_manage_stock( true );
        $product->set_stock_status( intval( $quantity ) > 0 ? 'instock' : 'outofstock' );
        $product->save();

        $this->logger->log( 'info', 'webhook_inventory_synced',
            'Inventory updated from Square webhook', array(
                'product_id' => $product->get_id(),
                'square_catalog_id' => $catalog_object_id,
                'new_quantity' => $quantity
            )
        );
    }

    /**
     * Handle catalog webhooks
     *
     * @param string $event_type
     * @param array $event_data
     */
    private function handle_catalog_webhook( $event_type, $event_data ) {
        $updated_at = $event_data['updated_at'] ?? '';

        // Log catalog webhook
        $this->logger->log( 'info', 'webhook_catalog_updated',
            'Catalog webhook received', array(
                'updated_at' => $updated_at
            )
        );

        // Check if product sync is enabled
        if ( ! $this->settings->get( 'enable_product_sync' ) ) {
            $this->logger->log( 'info', 'webhook_catalog_sync_disabled', 'Product sync disabled, skipping webhook' );
            return;
        }

        // Schedule catalog sync (don't do it immediately to avoid overwhelming the server)
        wp_schedule_single_event( time() + 300, 'squarekit_sync_catalog_from_webhook' ); // 5 minutes delay
    }

    /**
     * Sync Square order to WooCommerce
     *
     * @param array $order_data
     */
    private function sync_square_order_to_woocommerce( $order_data ) {
        // This would integrate with the existing order sync functionality
        // For now, just log that we received the webhook
        $this->logger->log( 'info', 'webhook_order_sync_needed',
            'Square order needs to be synced to WooCommerce', array(
                'square_order_id' => $order_data['id'] ?? '',
                'reference_id' => $order_data['reference_id'] ?? ''
            )
        );
    }

    /**
     * Update WooCommerce order from Square
     *
     * @param array $order_data
     */
    private function update_woocommerce_order_from_square( $order_data ) {
        $reference_id = $order_data['reference_id'] ?? '';

        if ( empty( $reference_id ) ) {
            return;
        }

        $order = wc_get_order( $reference_id );
        if ( ! $order ) {
            return;
        }

        // Update order based on Square order state
        $square_state = $order_data['state'] ?? '';

        switch ( $square_state ) {
            case 'COMPLETED':
                if ( $order->get_status() !== 'completed' ) {
                    $order->update_status( 'completed', __( 'Order completed in Square', 'squarekit' ) );
                }
                break;

            case 'CANCELED':
                if ( $order->get_status() !== 'cancelled' ) {
                    $order->update_status( 'cancelled', __( 'Order cancelled in Square', 'squarekit' ) );
                }
                break;
        }
    }

    /**
     * Mark order as fulfilled
     *
     * @param array $order_data
     */
    private function mark_order_fulfilled( $order_data ) {
        $reference_id = $order_data['reference_id'] ?? '';

        if ( empty( $reference_id ) ) {
            return;
        }

        $order = wc_get_order( $reference_id );
        if ( ! $order ) {
            return;
        }

        // Update order status to completed
        $order->update_status( 'completed', __( 'Order fulfilled in Square', 'squarekit' ) );

        // Add fulfillment note
        $fulfillments = $order_data['fulfillments'] ?? array();
        foreach ( $fulfillments as $fulfillment ) {
            $order->add_order_note( sprintf(
                __( 'Order fulfilled via %s. Tracking: %s', 'squarekit' ),
                $fulfillment['type'] ?? 'Square',
                $fulfillment['shipment_details']['tracking_number'] ?? 'N/A'
            ) );
        }
    }

    /**
     * Get webhook statistics
     *
     * @return array
     */
    public function get_webhook_stats() {
        global $wpdb;

        // Get webhook events from logs
        $table_name = $wpdb->prefix . 'squarekit_logs';

        $stats = $wpdb->get_results(
            "SELECT event_type, COUNT(*) as count
             FROM {$table_name}
             WHERE event_type LIKE 'webhook_%'
             AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY event_type
             ORDER BY count DESC"
        );

        $webhook_stats = array();
        foreach ( $stats as $stat ) {
            $webhook_stats[ $stat->event_type ] = intval( $stat->count );
        }

        return $webhook_stats;
    }

    /**
     * Register webhook settings
     */
    public function register_webhook_settings() {
        // This will be called by the settings page to display webhook URL
    }
}
