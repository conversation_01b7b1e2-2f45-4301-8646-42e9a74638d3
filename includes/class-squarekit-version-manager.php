<?php
/**
 * SquareKit Version Manager
 * Handles version tracking, database migrations, and changelog management
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 * @since 1.1.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Version Manager Class
 * 
 * Manages plugin versioning, database migrations, and changelog tracking
 */
class SquareKit_Version_Manager {

    /**
     * Current plugin version
     * @var string
     */
    private $current_version;

    /**
     * Installed version option key
     * @var string
     */
    private $version_option = 'squarekit_version';

    /**
     * Database version option key
     * @var string
     */
    private $db_version_option = 'squarekit_db_version';

    /**
     * Current database version
     * @var string
     */
    private $current_db_version = '1.1.0';

    /**
     * Constructor
     */
    public function __construct() {
        $this->current_version = SQUAREKIT_VERSION;
        
        // Hook into WordPress
        add_action( 'plugins_loaded', array( $this, 'check_version' ) );
        add_action( 'admin_notices', array( $this, 'display_update_notices' ) );
    }

    /**
     * Check if plugin version has changed and handle updates
     */
    public function check_version() {
        $installed_version = get_option( $this->version_option );
        $installed_db_version = get_option( $this->db_version_option );

        // First installation
        if ( ! $installed_version ) {
            $this->handle_first_installation();
            return;
        }

        // Version upgrade
        if ( version_compare( $installed_version, $this->current_version, '<' ) ) {
            $this->handle_version_upgrade( $installed_version, $this->current_version );
        }

        // Database upgrade
        if ( version_compare( $installed_db_version, $this->current_db_version, '<' ) ) {
            $this->handle_database_upgrade( $installed_db_version, $this->current_db_version );
        }
    }

    /**
     * Handle first installation
     */
    private function handle_first_installation() {
        // Set version options
        update_option( $this->version_option, $this->current_version );
        update_option( $this->db_version_option, $this->current_db_version );

        // Log installation
        $this->log_version_event( 'installation', array(
            'version' => $this->current_version,
            'db_version' => $this->current_db_version
        ) );

        // Set installation notice
        set_transient( 'squarekit_installation_notice', true, 300 );
    }

    /**
     * Handle version upgrade
     *
     * @param string $from_version
     * @param string $to_version
     */
    private function handle_version_upgrade( $from_version, $to_version ) {
        // Log upgrade
        $this->log_version_event( 'upgrade', array(
            'from_version' => $from_version,
            'to_version' => $to_version
        ) );

        // Run version-specific upgrade routines
        $this->run_version_upgrades( $from_version, $to_version );

        // Update version option
        update_option( $this->version_option, $to_version );

        // Set upgrade notice
        set_transient( 'squarekit_upgrade_notice', array(
            'from' => $from_version,
            'to' => $to_version
        ), 300 );
    }

    /**
     * Handle database upgrade
     *
     * @param string $from_db_version
     * @param string $to_db_version
     */
    private function handle_database_upgrade( $from_db_version, $to_db_version ) {
        // Log database upgrade
        $this->log_version_event( 'database_upgrade', array(
            'from_db_version' => $from_db_version,
            'to_db_version' => $to_db_version
        ) );

        // Run database migrations
        $this->run_database_migrations( $from_db_version, $to_db_version );

        // Update database version option
        update_option( $this->db_version_option, $to_db_version );
    }

    /**
     * Run version-specific upgrade routines
     *
     * @param string $from_version
     * @param string $to_version
     */
    private function run_version_upgrades( $from_version, $to_version ) {
        // Version 1.1.0 upgrades
        if ( version_compare( $from_version, '1.1.0', '<' ) && version_compare( $to_version, '1.1.0', '>=' ) ) {
            $this->upgrade_to_1_1_0();
        }

        // Future version upgrades can be added here
        // if ( version_compare( $from_version, '1.2.0', '<' ) && version_compare( $to_version, '1.2.0', '>=' ) ) {
        //     $this->upgrade_to_1_2_0();
        // }
    }

    /**
     * Run database migrations
     *
     * @param string $from_db_version
     * @param string $to_db_version
     */
    private function run_database_migrations( $from_db_version, $to_db_version ) {
        // Database version 1.1.0 migrations
        if ( version_compare( $from_db_version, '1.1.0', '<' ) && version_compare( $to_db_version, '1.1.0', '>=' ) ) {
            $this->migrate_database_to_1_1_0();
        }
    }

    /**
     * Upgrade to version 1.1.0
     */
    private function upgrade_to_1_1_0() {
        // Clear old caches
        wp_cache_flush();

        // Update settings if needed
        $this->migrate_settings_to_1_1_0();

        // Log specific upgrade actions
        $this->log_version_event( 'upgrade_1_1_0_complete', array(
            'features_added' => array(
                'Advanced Payment Security',
                'Comprehensive Logging System',
                'Enhanced Error Handling',
                'Conflict Detection System'
            )
        ) );
    }

    /**
     * Migrate database to version 1.1.0
     */
    private function migrate_database_to_1_1_0() {
        // Create payment logs table
        $payment_logger = new SquareKit_Payment_Logger();
        
        // Create conflicts table
        $conflict_manager = new SquareKit_Conflict_Manager();

        // Log database migration
        $this->log_version_event( 'database_migration_1_1_0', array(
            'tables_created' => array(
                'squarekit_payment_logs',
                'squarekit_conflicts'
            )
        ) );
    }

    /**
     * Migrate settings to version 1.1.0
     */
    private function migrate_settings_to_1_1_0() {
        // Add new default settings for security features
        $settings = new SquareKit_Settings();
        
        // Enable logging by default for new installations
        if ( ! $settings->get( 'enable_advanced_logging' ) ) {
            $settings->set( 'enable_advanced_logging', true );
        }

        // Enable conflict detection by default
        if ( ! $settings->get( 'enable_conflict_detection' ) ) {
            $settings->set( 'enable_conflict_detection', true );
        }
    }

    /**
     * Display update notices
     */
    public function display_update_notices() {
        // Installation notice
        if ( get_transient( 'squarekit_installation_notice' ) ) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>SquareKit ' . esc_html( $this->current_version ) . ' installed successfully!</strong></p>';
            echo '<p>Thank you for choosing SquareKit. Visit the <a href="' . admin_url( 'admin.php?page=squarekit-settings' ) . '">Settings page</a> to configure your Square integration.</p>';
            echo '</div>';
            delete_transient( 'squarekit_installation_notice' );
        }

        // Upgrade notice
        $upgrade_notice = get_transient( 'squarekit_upgrade_notice' );
        if ( $upgrade_notice ) {
            echo '<div class="notice notice-info is-dismissible">';
            echo '<p><strong>SquareKit updated from ' . esc_html( $upgrade_notice['from'] ) . ' to ' . esc_html( $upgrade_notice['to'] ) . '!</strong></p>';
            echo '<p>New features have been added. Check the <a href="' . admin_url( 'admin.php?page=squarekit-changelog' ) . '">Changelog</a> for details.</p>';
            echo '</div>';
            delete_transient( 'squarekit_upgrade_notice' );
        }
    }

    /**
     * Get version information
     *
     * @return array
     */
    public function get_version_info() {
        return array(
            'current_version' => $this->current_version,
            'installed_version' => get_option( $this->version_option ),
            'current_db_version' => $this->current_db_version,
            'installed_db_version' => get_option( $this->db_version_option ),
            'last_update' => get_option( 'squarekit_last_update' ),
            'installation_date' => get_option( 'squarekit_installation_date' )
        );
    }

    /**
     * Get changelog data
     *
     * @return array
     */
    public function get_changelog() {
        return array(
            '1.1.0' => array(
                'date' => '2025-07-16',
                'type' => 'major',
                'title' => 'Security & Logging Enhancement',
                'features' => array(
                    'Added comprehensive payment security features',
                    'Implemented advanced logging system with analytics',
                    'Enhanced error handling and validation',
                    'Added conflict detection and resolution system',
                    'Improved CSRF protection and session security',
                    'Added payment transaction tracking',
                    'Implemented fraud detection patterns',
                    'Added compliance audit trails'
                ),
                'improvements' => array(
                    'Enhanced input validation and sanitization',
                    'Improved rate limiting and suspicious activity detection',
                    'Better error messages and user feedback',
                    'Optimized database queries and performance'
                ),
                'fixes' => array(
                    'Fixed token validation edge cases',
                    'Resolved session integrity issues',
                    'Improved error logging accuracy'
                )
            ),
            '1.0.0' => array(
                'date' => '2025-07-15',
                'type' => 'major',
                'title' => 'Initial Release',
                'features' => array(
                    'Square Web Payments SDK integration',
                    'WooCommerce payment gateway',
                    'Product and inventory synchronization',
                    'Order management and sync',
                    'Customer data synchronization',
                    'Digital wallet support (Google Pay, Apple Pay)',
                    'Saved payment methods',
                    'Subscription support'
                )
            )
        );
    }

    /**
     * Log version-related events
     *
     * @param string $event_type
     * @param array $context
     */
    private function log_version_event( $event_type, $context = array() ) {
        if ( class_exists( 'SquareKit_Logger' ) ) {
            $logger = new SquareKit_Logger();
            $logger->log( 'info', 'version_' . $event_type, 'Version management event: ' . $event_type, $context );
        }
    }

    /**
     * Force version check (for manual triggers)
     */
    public function force_version_check() {
        delete_option( $this->version_option );
        delete_option( $this->db_version_option );
        $this->check_version();
    }

    /**
     * Get database schema version requirements
     *
     * @return array
     */
    public function get_schema_requirements() {
        return array(
            '1.0.0' => array(
                'tables' => array(),
                'options' => array( 'squarekit_settings' )
            ),
            '1.1.0' => array(
                'tables' => array(
                    'squarekit_payment_logs',
                    'squarekit_conflicts'
                ),
                'options' => array(
                    'squarekit_settings',
                    'squarekit_version',
                    'squarekit_db_version'
                )
            )
        );
    }
}
