<?php
/**
 * SquareKit Import Bridge Class
 *
 * Provides a unified interface between existing import methods and SWEVER architecture.
 * Resolves conflicts and allows both approaches to coexist seamlessly.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Import Bridge Class
 *
 * Acts as a compatibility layer between the existing import_product_from_square method
 * and the new SWEVER-based import architecture, allowing both to coexist.
 */
class SquareKit_Import_Bridge {

    /**
     * WooCommerce integration instance (legacy approach)
     *
     * @var SquareKit_WooCommerce
     */
    private $wc_integration;

    /**
     * Product importer instance (SWEVER approach)
     *
     * @var SquareKit_Product_Importer
     */
    private $product_importer;

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Import statistics
     *
     * @var array
     */
    private $import_stats = array(
        'method_used' => '',
        'success' => false,
        'product_id' => null,
        'created' => false,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }

        if ( ! class_exists( 'SquareKit_Product_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
        }

        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $this->wc_integration = new SquareKit_WooCommerce();
        $this->product_importer = new SquareKit_Product_Importer();
        $this->square_api = new SquareKit_Square_API();
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Unified import method - automatically detects input type and uses appropriate approach
     *
     * @param mixed $input Square item ID (string) or Square item data (array)
     * @param array $options Import options and configuration
     * @return array|WP_Error Import result or error
     */
    public function import_product( $input, $options = array() ) {
        $this->logger->log( 'import_bridge', 'info', 'Starting unified import process' );

        // Reset stats
        $this->import_stats = array(
            'method_used' => '',
            'success' => false,
            'product_id' => null,
            'created' => false,
            'errors' => array()
        );

        try {
            if ( is_string( $input ) ) {
                // SWEVER-style: input is Square item ID
                return $this->import_by_id( $input, $options );
            } elseif ( is_array( $input ) && isset( $input['id'] ) ) {
                // Legacy-style: input is Square item data
                return $this->import_by_data( $input, $options );
            } else {
                $error = new WP_Error( 'invalid_input', 'Invalid input: must be Square item ID (string) or item data (array)' );
                $this->import_stats['errors'][] = $error->get_error_message();
                return $error;
            }
        } catch ( Exception $e ) {
            $error = new WP_Error( 'import_exception', 'Import failed with exception: ' . $e->getMessage() );
            $this->import_stats['errors'][] = $error->get_error_message();
            $this->logger->log( 'import_bridge', 'error', $error->get_error_message() );
            return $error;
        }
    }

    /**
     * Import product using SWEVER architecture (by Square item ID)
     *
     * @param string $square_item_id Square item ID
     * @param array $options Import options
     * @return array|WP_Error Import result or error
     */
    private function import_by_id( $square_item_id, $options = array() ) {
        $this->import_stats['method_used'] = 'SWEVER';
        $this->logger->log( 'import_bridge', 'info', "Using SWEVER approach for item ID: {$square_item_id}" );

        // Convert options to SWEVER format
        $data_to_import = $this->convert_options_to_swever_format( $options );
        $update_only = isset( $options['update_only'] ) ? $options['update_only'] : false;

        // Use SWEVER import method
        $result = $this->product_importer->import_product_swever_style( 
            $square_item_id, 
            $data_to_import, 
            $update_only 
        );

        if ( is_wp_error( $result ) ) {
            $this->import_stats['errors'][] = $result->get_error_message();
            return $result;
        }

        // Convert SWEVER result to unified format
        return $this->normalize_swever_result( $result );
    }

    /**
     * Import product using legacy architecture (by Square item data)
     *
     * @param array $item_data Square item data
     * @param array $options Import options
     * @return array|WP_Error Import result or error
     */
    private function import_by_data( $item_data, $options = array() ) {
        $this->import_stats['method_used'] = 'Legacy';
        $this->logger->log( 'import_bridge', 'info', "Using Legacy approach for item: " . ($item_data['id'] ?? 'unknown') );

        // Extract legacy parameters
        $image_map = isset( $options['image_map'] ) ? $options['image_map'] : array();
        $existing_product_id = isset( $options['existing_product_id'] ) ? $options['existing_product_id'] : null;

        // Use legacy import method
        $result = $this->wc_integration->import_product_from_square( 
            $item_data, 
            $image_map, 
            $existing_product_id 
        );

        if ( is_wp_error( $result ) ) {
            $this->import_stats['errors'][] = $result->get_error_message();
            return $result;
        }

        // Convert legacy result to unified format
        return $this->normalize_legacy_result( $result, $item_data, $existing_product_id );
    }

    /**
     * Convert options to SWEVER data_to_import format
     *
     * @param array $options Input options
     * @return array SWEVER-formatted configuration
     */
    private function convert_options_to_swever_format( $options ) {
        $default_config = array(
            'name' => true,
            'description' => true,
            'price' => true,
            'sku' => true,
            'categories' => true,
            'images' => true,
            'attributesDisabled' => false
        );

        // Map common option names
        $mapping = array(
            'import_name' => 'name',
            'import_description' => 'description',
            'import_price' => 'price',
            'import_sku' => 'sku',
            'import_categories' => 'categories',
            'import_images' => 'images',
            'disable_attributes' => 'attributesDisabled'
        );

        $swever_config = $default_config;

        foreach ( $mapping as $option_key => $swever_key ) {
            if ( isset( $options[$option_key] ) ) {
                $swever_config[$swever_key] = $options[$option_key];
            }
        }

        return $swever_config;
    }

    /**
     * Normalize SWEVER result to unified format
     *
     * @param array $swever_result SWEVER import result
     * @return array Normalized result
     */
    private function normalize_swever_result( $swever_result ) {
        $this->import_stats['success'] = true;
        $this->import_stats['product_id'] = $swever_result['product_id'] ?? null;
        $this->import_stats['created'] = $swever_result['created'] ?? false;

        return array(
            'success' => true,
            'method' => 'SWEVER',
            'product_id' => $swever_result['product_id'] ?? null,
            'created' => $swever_result['created'] ?? false,
            'updated' => !($swever_result['created'] ?? false),
            'message' => $swever_result['created'] ? 'Product created successfully' : 'Product updated successfully',
            'stats' => $this->import_stats,
            'swever_result' => $swever_result
        );
    }

    /**
     * Normalize legacy result to unified format
     *
     * @param mixed $legacy_result Legacy import result (usually product ID)
     * @param array $item_data Original item data
     * @param int|null $existing_product_id Existing product ID if updating
     * @return array Normalized result
     */
    private function normalize_legacy_result( $legacy_result, $item_data, $existing_product_id = null ) {
        $product_id = is_numeric( $legacy_result ) ? intval( $legacy_result ) : null;
        $created = is_null( $existing_product_id );

        $this->import_stats['success'] = !is_null( $product_id );
        $this->import_stats['product_id'] = $product_id;
        $this->import_stats['created'] = $created;

        return array(
            'success' => !is_null( $product_id ),
            'method' => 'Legacy',
            'product_id' => $product_id,
            'created' => $created,
            'updated' => !$created,
            'message' => $created ? 'Product created successfully' : 'Product updated successfully',
            'stats' => $this->import_stats,
            'legacy_result' => $legacy_result,
            'square_id' => $item_data['id'] ?? null
        );
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_stats() {
        return $this->import_stats;
    }

    /**
     * Check if SWEVER architecture is available
     *
     * @return bool True if SWEVER classes are loaded
     */
    public function is_swever_available() {
        return class_exists( 'SquareKit_Product_Importer' ) && 
               class_exists( 'SquareKit_Create_Product' ) && 
               class_exists( 'SquareKit_Square_Import' );
    }

    /**
     * Get recommended import method for given input
     *
     * @param mixed $input Square item ID or data
     * @return string 'SWEVER' or 'Legacy'
     */
    public function get_recommended_method( $input ) {
        if ( ! $this->is_swever_available() ) {
            return 'Legacy';
        }

        if ( is_string( $input ) ) {
            return 'SWEVER'; // ID-based imports work better with SWEVER
        }

        return 'Legacy'; // Data-based imports work with existing method
    }
}
