<?php
/**
 * WooCommerce integration for Square Kit
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/integrations
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * WooCommerce integration class.
 * Lightweight coordinator that delegates to specialized sync modules.
 *
 * @since 1.0.0
 */
class SquareKit_WooCommerce {

    /**
     * Settings instance
     * @var SquareKit_Settings
     */
    protected $settings;

    /**
     * Logger instance
     * @var SquareKit_Logger
     */
    protected $logger;

    /**
     * Sync modules instances
     * @var array
     */
    protected $sync_modules = array();

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = new SquareKit_Settings();

        // Initialize logger
        if (!class_exists('SquareKit_Logger')) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }
        $this->logger = SquareKit_Logger::get_instance();

        // Initialize sync modules
        $this->init_sync_modules();
    }

    /**
     * Initialize sync modules
     */
    private function init_sync_modules() {
        // Load sync module files
        $sync_files = array(
            'product' => 'includes/sync/class-squarekit-product-sync.php',
            'inventory' => 'includes/sync/class-squarekit-inventory-sync.php',
            'order' => 'includes/sync/class-squarekit-order-sync.php',
            'customer' => 'includes/sync/class-squarekit-customer-sync.php',
            'category' => 'includes/sync/class-squarekit-category-sync.php',
            'webhook' => 'includes/sync/class-squarekit-webhook-handler.php',
            'coordinator' => 'includes/sync/class-squarekit-sync-coordinator.php'
        );

        foreach ( $sync_files as $key => $file_path ) {
            $full_path = SQUAREKIT_PLUGIN_DIR . $file_path;
            if ( file_exists( $full_path ) ) {
                require_once $full_path;
            }
        }

        // Initialize all sync modules
        $module_classes = array(
            'product' => 'SquareKit_Product_Sync',
            'inventory' => 'SquareKit_Inventory_Sync',
            'order' => 'SquareKit_Order_Sync',
            'customer' => 'SquareKit_Customer_Sync',
            'category' => 'SquareKit_Category_Sync',
            'webhook' => 'SquareKit_Webhook_Handler',
            'coordinator' => 'SquareKit_Sync_Coordinator'
        );

        foreach ( $module_classes as $key => $class_name ) {
            if ( class_exists( $class_name ) ) {
                try {
                    $this->sync_modules[ $key ] = new $class_name();
                } catch ( Exception $e ) {
                    $this->logger->log( 'woocommerce_integration', 'error', "Failed to initialize {$class_name}: " . $e->getMessage() );
                }
            } else {
                $this->logger->log( 'woocommerce_integration', 'warning', "Sync module class {$class_name} not found" );
            }
        }
    }

    /**
     * Initialize WooCommerce integration hooks
     * Delegates to specialized sync modules
     */
    public function init() {
        // Check sync direction settings before registering hooks
        $sync_direction_settings = $this->settings->get_sync_direction_settings();

        // Determine if WooCommerce to Square sync should be enabled
        // Priority: sync direction settings > legacy settings (for backward compatibility)
        $woo_to_square_enabled = $this->is_woo_to_square_sync_enabled( $sync_direction_settings );

        // Product sync hooks - only register if WooCommerce to Square sync is enabled
        if ( isset( $this->sync_modules['product'] ) && $woo_to_square_enabled ) {
            add_action( 'save_post_product', array( $this->sync_modules['product'], 'sync_product_to_square' ), 10, 3 );
            add_action( 'before_delete_post', array( $this->sync_modules['product'], 'delete_product_from_square' ), 10, 1 );
            $this->logger->log( 'woocommerce_integration', 'info', 'Registered product sync hooks' );
        }

        // Order sync hooks - only register if WooCommerce to Square sync is enabled
        if ( isset( $this->sync_modules['order'] ) && $woo_to_square_enabled ) {
            add_action( 'woocommerce_order_status_changed', array( $this->sync_modules['order'], 'sync_order_to_square' ), 10, 4 );
            $this->logger->log( 'woocommerce_integration', 'info', 'Registered order sync hooks' );
        }

        // Customer sync hooks - only register if WooCommerce to Square sync is enabled
        if ( isset( $this->sync_modules['customer'] ) && $woo_to_square_enabled ) {
            add_action( 'user_register', array( $this->sync_modules['customer'], 'sync_customer_to_square' ), 10, 1 );
            add_action( 'profile_update', array( $this->sync_modules['customer'], 'sync_customer_to_square' ), 10, 1 );
            $this->logger->log( 'woocommerce_integration', 'info', 'Registered customer sync hooks' );
        }

        // Inventory sync hooks - only register if WooCommerce to Square sync is enabled
        if ( isset( $this->sync_modules['inventory'] ) && $woo_to_square_enabled ) {
            add_action( 'woocommerce_product_set_stock', array( $this->sync_modules['inventory'], 'sync_inventory_to_square' ), 10, 1 );
            add_action( 'woocommerce_variation_set_stock', array( $this->sync_modules['inventory'], 'sync_inventory_to_square' ), 10, 1 );
        }

        // Legacy hooks for modifiers UI - always register these as they're for display, not sync
        $this->init_legacy_hooks();
    }

    /**
     * Emergency method to disable all WooCommerce to Square sync hooks
     * Call this if sync hooks are accidentally enabled and causing issues
     */
    public function emergency_disable_woo_to_square_hooks() {
        // Remove product sync hooks
        if ( isset( $this->sync_modules['product'] ) ) {
            remove_action( 'save_post_product', array( $this->sync_modules['product'], 'sync_product_to_square' ), 10 );
            remove_action( 'before_delete_post', array( $this->sync_modules['product'], 'delete_product_from_square' ), 10 );
        }

        // Remove order sync hooks
        if ( isset( $this->sync_modules['order'] ) ) {
            remove_action( 'woocommerce_order_status_changed', array( $this->sync_modules['order'], 'sync_order_to_square' ), 10 );
        }

        // Remove customer sync hooks
        if ( isset( $this->sync_modules['customer'] ) ) {
            remove_action( 'user_register', array( $this->sync_modules['customer'], 'sync_customer_to_square' ), 10 );
            remove_action( 'profile_update', array( $this->sync_modules['customer'], 'sync_customer_to_square' ), 10 );
        }

        // Remove inventory sync hooks
        if ( isset( $this->sync_modules['inventory'] ) ) {
            remove_action( 'woocommerce_product_set_stock', array( $this->sync_modules['inventory'], 'sync_inventory_to_square' ), 10 );
            remove_action( 'woocommerce_variation_set_stock', array( $this->sync_modules['inventory'], 'sync_inventory_to_square' ), 10 );
        }

        $this->logger->log( 'woocommerce_integration', 'info', 'Emergency disabled all WooCommerce to Square sync hooks' );
    }

    /**
     * Determine if WooCommerce to Square sync should be enabled
     * Checks sync direction settings first, then falls back to legacy settings for backward compatibility
     *
     * @param array $sync_direction_settings Sync direction settings
     * @return bool True if sync should be enabled, false otherwise
     */
    private function is_woo_to_square_sync_enabled( $sync_direction_settings ) {
        // If sync direction setting is explicitly set (true or false), use that
        if ( isset( $sync_direction_settings['woo_to_square'] ) ) {
            return (bool) $sync_direction_settings['woo_to_square'];
        }

        // Fallback to legacy settings for backward compatibility
        // This should only happen if sync direction settings haven't been configured yet
        $legacy_sync_enabled = (
            $this->settings->get( 'sync_orders', false ) ||
            $this->settings->get( 'sync_customers', false ) ||
            $this->settings->get( 'sync_inventory', false )
        );

        // Note: Legacy sync settings are being used as fallback
        // Consider configuring sync direction settings for better control

        return $legacy_sync_enabled;
    }

    /**
     * Initialize legacy hooks for backward compatibility
     */
    private function init_legacy_hooks() {
        // Modifiers tab and panel in product data
        add_filter( 'woocommerce_product_data_tabs', array( $this, 'add_modifiers_tab' ) );
        add_action( 'woocommerce_product_data_panels', array( $this, 'add_modifiers_panel' ) );
        add_action( 'woocommerce_process_product_meta', array( $this, 'save_modifiers_meta' ), 20, 2 );

        // Frontend modifiers display and handling - positioned above add to cart button
        add_action( 'woocommerce_before_add_to_cart_button', array( $this, 'display_product_modifiers' ) );
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_frontend_scripts' ) );
        add_filter( 'woocommerce_add_cart_item_data', array( $this, 'add_modifiers_to_cart_item' ), 10, 3 );
        add_action( 'woocommerce_before_calculate_totals', array( $this, 'adjust_cart_item_price' ), 20 );
        add_filter( 'woocommerce_get_item_data', array( $this, 'display_modifiers_in_cart' ), 10, 2 );
        add_action( 'woocommerce_checkout_create_order_line_item', array( $this, 'add_modifiers_to_order_item' ), 10, 4 );
    }

    // ===== DELEGATION METHODS =====
    // All actual work is delegated to sync modules

    /**
     * Delegate product sync to Product Sync module
     */
    public function sync_product_to_square( $post_id, $post, $update ) {
        if ( isset( $this->sync_modules['product'] ) ) {
            return $this->sync_modules['product']->sync_product_to_square( $post_id, $post, $update );
        }
        return false;
    }

    /**
     * Delegate product deletion to Product Sync module
     */
    public function delete_product_from_square( $post_id ) {
        if ( isset( $this->sync_modules['product'] ) ) {
            return $this->sync_modules['product']->delete_product_from_square( $post_id );
        }
        return false;
    }

    /**
     * Delegate order sync to Order Sync module
     */
    public function sync_order_to_square( $order_id, $old_status, $new_status, $order ) {
        if ( isset( $this->sync_modules['order'] ) ) {
            return $this->sync_modules['order']->sync_order_to_square( $order_id, $old_status, $new_status, $order );
        }
        return false;
    }

    /**
     * Delegate customer sync to Customer Sync module
     */
    public function sync_customer_to_square( $user_id ) {
        if ( isset( $this->sync_modules['customer'] ) ) {
            return $this->sync_modules['customer']->sync_customer_to_square( $user_id );
        }
        return false;
    }

    /**
     * Delegate inventory sync to Inventory Sync module
     */
    public function sync_inventory_to_square( $product_id, $quantity_change = 0 ) {
        if ( isset( $this->sync_modules['inventory'] ) ) {
            return $this->sync_modules['inventory']->sync_inventory_to_square( $product_id, $quantity_change );
        }
        return false;
    }

    // ===== IMPORT DELEGATION METHODS =====

    /**
     * Legacy method: Import single product from Square data (backward compatibility)
     *
     * @param array $item_data Square item data
     * @param array $image_map Image mapping data
     * @param int|null $existing_product_id Existing product ID to update
     * @return int|WP_Error Product ID on success, WP_Error on failure
     */
    public function import_product_from_square( $item_data, $image_map = array(), $existing_product_id = null ) {
        if ( isset( $this->sync_modules['product'] ) ) {
            // Delegate to Product Sync module's single product import method
            $result = $this->sync_modules['product']->import_single_product( $item_data, array(
                'image_map' => $image_map,
                'existing_product_id' => $existing_product_id,
                'include_images' => !empty( $image_map ),
                'include_variations' => true,
                'include_modifiers' => true,
                'update_existing' => true
            ) );

            // Convert new format result to legacy format (just return product ID)
            if ( is_wp_error( $result ) ) {
                return $result;
            }

            if ( isset( $result['success'] ) && $result['success'] && isset( $result['product_id'] ) ) {
                return $result['product_id'];
            }

            return new WP_Error( 'import_failed', 'Product import failed with unknown error' );
        }
        return new WP_Error( 'module_unavailable', 'Product sync module not available' );
    }

    /**
     * Delegate import operations to appropriate sync modules
     */
    public function import_products_from_square() {
        if ( isset( $this->sync_modules['product'] ) ) {
            return $this->sync_modules['product']->import_products_from_square();
        }
        return array( 'success' => false, 'message' => 'Product sync module not available' );
    }

    public function import_orders_from_square() {
        if ( isset( $this->sync_modules['order'] ) ) {
            return $this->sync_modules['order']->import_orders_from_square();
        }
        return array( 'success' => false, 'message' => 'Order sync module not available' );
    }

    public function import_customers_from_square() {
        if ( isset( $this->sync_modules['customer'] ) ) {
            return $this->sync_modules['customer']->import_customers_from_square();
        }
        return array( 'success' => false, 'message' => 'Customer sync module not available' );
    }

    public function import_categories_from_square() {
        if ( isset( $this->sync_modules['category'] ) ) {
            return $this->sync_modules['category']->import_categories_from_square();
        }
        return array( 'success' => false, 'message' => 'Category sync module not available' );
    }

    /**
     * Import category hierarchy and assign to product
     *
     * @param string $square_category_id Square category ID
     * @param int $product_id WooCommerce product ID
     * @return array Array of assigned WooCommerce category IDs
     */
    public function import_category_hierarchy_and_assign( $square_category_id, $product_id ) {
        if ( isset( $this->sync_modules['category'] ) ) {
            return $this->sync_modules['category']->handle_category_hierarchy( array( $square_category_id ), $product_id );
        }

        // Fallback: create simple category without hierarchy
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        $square_api = new SquareKit_Square_API();
        $category_data = $square_api->get_catalog_item( $square_category_id );

        if ( is_wp_error( $category_data ) || empty( $category_data['category_data'] ) ) {
            return array();
        }

        $category_info = $category_data['category_data'];
        $category_name = $category_info['name'] ?? '';

        if ( empty( $category_name ) ) {
            return array();
        }

        // Create simple category
        $existing_category = get_term_by( 'name', $category_name, 'product_cat' );

        if ( $existing_category ) {
            $category_id = $existing_category->term_id;
        } else {
            $term_result = wp_insert_term( $category_name, 'product_cat', array(
                'description' => $category_info['description'] ?? '',
            ) );

            if ( is_wp_error( $term_result ) ) {
                return array();
            }

            $category_id = $term_result['term_id'];
            update_term_meta( $category_id, '_square_category_id', $square_category_id );
        }

        // Assign to product
        wp_set_object_terms( $product_id, $category_id, 'product_cat', false );

        return array( $category_id );
    }

    public function import_inventory_from_square() {
        if ( isset( $this->sync_modules['inventory'] ) ) {
            return $this->sync_modules['inventory']->import_inventory_from_square();
        }
        return array( 'success' => false, 'message' => 'Inventory sync module not available' );
    }

    // ===== AJAX DELEGATION METHODS =====

    /**
     * Delegate conflict resolution to Sync Coordinator module
     */
    public function ajax_resolve_inventory_conflict() {
        if ( isset( $this->sync_modules['coordinator'] ) ) {
            return $this->sync_modules['coordinator']->ajax_resolve_inventory_conflict();
        }
        wp_send_json_error( array( 'message' => 'Sync coordinator not available' ) );
    }

    public function ajax_sync_inventory_manual() {
        if ( isset( $this->sync_modules['coordinator'] ) ) {
            return $this->sync_modules['coordinator']->ajax_sync_inventory_manual();
        }
        wp_send_json_error( array( 'message' => 'Sync coordinator not available' ) );
    }

    public function ajax_apply_role_mapping_to_all() {
        if ( isset( $this->sync_modules['customer'] ) ) {
            return $this->sync_modules['customer']->ajax_apply_role_mapping_to_all();
        }
        wp_send_json_error( array( 'message' => 'Customer sync module not available' ) );
    }

    // ===== LEGACY MODIFIER METHODS =====
    // These remain here for backward compatibility until UI is refactored

    /**
     * Add modifiers tab to product data tabs
     */
    public function add_modifiers_tab( $tabs ) {
        $tabs['squarekit_modifiers'] = array(
            'label'  => __( 'Square Modifiers', 'squarekit' ),
            'target' => 'squarekit_modifiers_panel',
            'class'  => array( 'show_if_simple', 'show_if_variable' ),
        );
        return $tabs;
    }

    /**
     * Add modifiers panel to product data panels
     */
    public function add_modifiers_panel() {
        global $post;

        // Get modifiers in SWEVER-compatible format
        $modifier_sets = get_post_meta( $post->ID, '_squarekit_modifier_sets', true );
        if ( ! is_array( $modifier_sets ) ) {
            $modifier_sets = array();
        }

        echo '<div id="squarekit_modifiers_panel" class="panel woocommerce_options_panel">';
        echo '<div class="options_group">';
        echo '<p><strong>' . __( 'Square Modifiers', 'squarekit' ) . '</strong></p>';
        echo '<p>' . __( 'Manage modifier sets imported from Square. Changes will be saved when you update the product.', 'squarekit' ) . '</p>';

        // Add re-import button
        echo '<button type="button" id="squarekit_reimport_modifiers" class="button button-secondary" style="margin-bottom: 15px;">';
        echo __( 'Re-import from Square', 'squarekit' );
        echo '</button>';

        // Add new set button
        echo '<button type="button" id="squarekit_add_modifier_set" class="button button-primary" style="margin-bottom: 15px; margin-left: 10px;">';
        echo __( 'Add Modifier Set', 'squarekit' );
        echo '</button>';

        // Container for modifier sets
        echo '<div id="squarekit_modifier_sets_container">';
        echo '</div>';

        // Hidden input to store JSON data
        echo '<input type="hidden" id="squarekit_modifier_sets_data" name="squarekit_modifier_sets" value="' . esc_attr( wp_json_encode( $modifier_sets ) ) . '" />';

        echo '</div>';
        echo '</div>';

        // Add inline JavaScript for modifier management
        $this->add_modifier_admin_script( $modifier_sets );
    }

    /**
     * Add modifier admin script
     */
    private function add_modifier_admin_script( $modifier_sets ) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var modifierSets = <?php echo wp_json_encode( $modifier_sets ); ?>;
            var $container = $('#squarekit_modifier_sets_container');
            var $dataInput = $('#squarekit_modifier_sets_data');
            var setIndex = 0;

            // Render all modifier sets
            function renderModifierSets() {
                $container.empty();

                if (modifierSets.length === 0) {
                    $container.html('<p><?php echo esc_js( __( 'No modifier sets yet.', 'squarekit' ) ); ?></p>');
                    return;
                }

                $.each(modifierSets, function(index, setData) {
                    renderModifierSet(setData, index);
                });
            }

            // Render a single modifier set
            function renderModifierSet(setData, index) {
                var setName = setData.set_name || '';
                var squareListId = setData.square_mod_list_id || '';
                var singleChoice = setData.single_choice ? 'checked' : '';
                var options = setData.options || [];
                var isSquareManaged = setData.source === 'square';

                var setHTML = `
                    <div class="squarekit-modifier-set" data-set-index="${index}" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; background: #f9f9f9;">
                        <div class="squarekit-modifier-set-header" style="display: flex; gap: 15px; align-items: flex-end; margin-bottom: 15px;">
                            <div>
                                <label><strong><?php echo esc_js( __( 'Set Name:', 'squarekit' ) ); ?></strong></label><br/>
                                <input type="text" class="set-name" value="${setName}" placeholder="<?php echo esc_js( __( 'e.g. Extras', 'squarekit' ) ); ?>" ${isSquareManaged ? 'readonly' : ''} style="width: 200px;"/>
                            </div>
                            <div>
                                <label><strong><?php echo esc_js( __( 'Square Mod List ID:', 'squarekit' ) ); ?></strong></label><br/>
                                <input type="text" class="square-list-id" value="${squareListId}" placeholder="<?php echo esc_js( __( 'WEYL7DNTWBB4MQOIXMYWPK2J', 'squarekit' ) ); ?>" readonly style="width: 250px; background: #f0f0f0;"/>
                            </div>
                            <div>
                                <label style="display:block;"><strong><?php echo esc_js( __( 'Single Choice?', 'squarekit' ) ); ?></strong></label>
                                <input type="checkbox" class="single-choice" ${singleChoice} ${isSquareManaged ? 'disabled' : ''} />
                            </div>
                            <div>
                                <button type="button" class="button link-delete remove-set-btn"><?php echo esc_js( __( 'Remove Set', 'squarekit' ) ); ?></button>
                            </div>
                        </div>

                        <table class="widefat squarekit-options-table">
                            <thead>
                                <tr>
                                    <th><?php echo esc_js( __( 'Option Name', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Price', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Stock (optional)', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Square Modifier ID', 'squarekit' ) ); ?></th>
                                    <th><?php echo esc_js( __( 'Actions', 'squarekit' ) ); ?></th>
                                </tr>
                            </thead>
                            <tbody class="options-tbody">
                            </tbody>
                        </table>
                        ${!isSquareManaged ? '<button type="button" class="button add-option-btn" style="margin-top: 10px;">' + '<?php echo esc_js( __( 'Add Option', 'squarekit' ) ); ?>' + '</button>' : ''}
                    </div>
                `;

                $container.append(setHTML);

                // Render options for this set
                var $setElement = $container.find('.squarekit-modifier-set[data-set-index="' + index + '"]');
                var $tbody = $setElement.find('.options-tbody');

                $.each(options, function(optIndex, optData) {
                    renderOption($tbody, optData, isSquareManaged);
                });
            }

            // Render a single option row
            function renderOption($tbody, optData, isSquareManaged) {
                var optionName = optData.name || '';
                var optionPrice = optData.price || '';
                var optionStock = optData.stock || '';
                var squareModId = optData.square_mod_id || '';

                var optionHTML = `
                    <tr>
                        <td><input type="text" class="option-name" value="${optionName}" placeholder="<?php echo esc_js( __( 'e.g. Cheese', 'squarekit' ) ); ?>" ${isSquareManaged ? 'readonly' : ''} /></td>
                        <td><input type="number" step="0.01" class="option-price" value="${optionPrice}" placeholder="0.00" ${isSquareManaged ? 'readonly' : ''} /></td>
                        <td><input type="text" class="option-stock" value="${optionStock}" placeholder="<?php echo esc_js( __( 'optional', 'squarekit' ) ); ?>" ${isSquareManaged ? 'readonly' : ''} /></td>
                        <td><input type="text" class="option-square-id" value="${squareModId}" placeholder="<?php echo esc_js( __( 'Square Modifier ID', 'squarekit' ) ); ?>" readonly style="background: #f0f0f0;" /></td>
                        <td><button type="button" class="button link-delete remove-option-btn" ${isSquareManaged ? 'disabled' : ''}><?php echo esc_js( __( 'Remove', 'squarekit' ) ); ?></button></td>
                    </tr>
                `;

                $tbody.append(optionHTML);
            }

            // Save data to hidden input
            function saveData() {
                $dataInput.val(JSON.stringify(modifierSets));
            }

            // Update modifier sets from UI
            function updateModifierSetsFromUI() {
                modifierSets = [];

                $container.find('.squarekit-modifier-set').each(function() {
                    var $set = $(this);
                    var setData = {
                        set_name: $set.find('.set-name').val(),
                        square_mod_list_id: $set.find('.square-list-id').val(),
                        single_choice: $set.find('.single-choice').is(':checked'),
                        source: $set.find('.square-list-id').val() ? 'square' : 'wc',
                        options: []
                    };

                    $set.find('.options-tbody tr').each(function() {
                        var $row = $(this);
                        var optionData = {
                            name: $row.find('.option-name').val(),
                            price: parseFloat($row.find('.option-price').val()) || 0,
                            stock: $row.find('.option-stock').val(),
                            square_mod_id: $row.find('.option-square-id').val()
                        };

                        if (optionData.name) {
                            setData.options.push(optionData);
                        }
                    });

                    if (setData.set_name) {
                        modifierSets.push(setData);
                    }
                });

                saveData();
            }

            // Event handlers
            $('#squarekit_add_modifier_set').on('click', function() {
                var newSet = {
                    set_name: '',
                    square_mod_list_id: '',
                    single_choice: false,
                    source: 'wc',
                    options: []
                };
                modifierSets.push(newSet);
                renderModifierSets();
                saveData();
            });

            $(document).on('click', '.remove-set-btn', function() {
                var index = $(this).closest('.squarekit-modifier-set').data('set-index');
                modifierSets.splice(index, 1);
                renderModifierSets();
                saveData();
            });

            $(document).on('click', '.add-option-btn', function() {
                var $tbody = $(this).siblings('.squarekit-options-table').find('.options-tbody');
                renderOption($tbody, {}, false);
            });

            $(document).on('click', '.remove-option-btn', function() {
                $(this).closest('tr').remove();
                updateModifierSetsFromUI();
            });

            $(document).on('change', '.set-name, .single-choice, .option-name, .option-price, .option-stock', function() {
                updateModifierSetsFromUI();
            });

            // Re-import from Square
            $('#squarekit_reimport_modifiers').on('click', function() {
                var $btn = $(this);
                var productId = $('#post_ID').val();

                $btn.prop('disabled', true).text('<?php echo esc_js( __( 'Re-importing...', 'squarekit' ) ); ?>');

                $.post(ajaxurl, {
                    action: 'squarekit_reimport_modifiers',
                    product_id: productId,
                    nonce: '<?php echo wp_create_nonce( 'squarekit_admin' ); ?>'
                }, function(response) {
                    if (response.success && response.data.modifiers) {
                        // Replace Square-managed sets with fresh data
                        modifierSets = modifierSets.filter(function(set) {
                            return set.source !== 'square';
                        });

                        // Add fresh Square data
                        $.each(response.data.modifiers, function(i, set) {
                            set.source = 'square';
                            modifierSets.push(set);
                        });

                        renderModifierSets();
                        saveData();
                        alert('<?php echo esc_js( __( 'Modifiers re-imported successfully!', 'squarekit' ) ); ?>');
                    } else {
                        alert('<?php echo esc_js( __( 'Failed to re-import modifiers from Square.', 'squarekit' ) ); ?>');
                    }
                }).fail(function() {
                    alert('<?php echo esc_js( __( 'Error communicating with server.', 'squarekit' ) ); ?>');
                }).always(function() {
                    $btn.prop('disabled', false).text('<?php echo esc_js( __( 'Re-import from Square', 'squarekit' ) ); ?>');
                });
            });

            // Initial render
            renderModifierSets();
        });
        </script>
        <?php
    }

    /**
     * Save modifiers meta
     */
    public function save_modifiers_meta( $post_id, $post ) {
        if ( ! isset( $_POST['squarekit_modifier_sets'] ) ) {
            return;
        }

        $modifier_sets = json_decode( wp_unslash( $_POST['squarekit_modifier_sets'] ), true );
        if ( is_array( $modifier_sets ) ) {
            // Save in SWEVER-compatible format
            update_post_meta( $post_id, '_squarekit_modifier_sets', $modifier_sets );

            // Also save in legacy format for backward compatibility
            $legacy_format = $this->convert_to_legacy_format( $modifier_sets );
            update_post_meta( $post_id, '_squarekit_modifiers', $legacy_format );
        }
    }

    /**
     * Convert SWEVER format to legacy format for backward compatibility
     */
    private function convert_to_legacy_format( $modifier_sets ) {
        $legacy_format = array();

        foreach ( $modifier_sets as $set ) {
            $legacy_format[] = array(
                'name' => $set['set_name'] ?? '',
                'single' => $set['single_choice'] ?? false,
                'square_modifier_list_id' => $set['square_mod_list_id'] ?? '',
                'options' => $set['options'] ?? array()
            );
        }

        return $legacy_format;
    }

    /**
     * Migrate existing products to SWEVER-compatible format
     */
    public function migrate_existing_modifiers() {
        global $wpdb;

        // Find all products with old modifier format
        $products_with_modifiers = $wpdb->get_results(
            "SELECT post_id, meta_value FROM {$wpdb->postmeta}
             WHERE meta_key = '_squarekit_modifiers'
             AND post_id NOT IN (
                 SELECT post_id FROM {$wpdb->postmeta}
                 WHERE meta_key = '_squarekit_modifier_sets'
             )"
        );

        $migrated_count = 0;

        foreach ( $products_with_modifiers as $product_meta ) {
            $product_id = $product_meta->post_id;
            $old_modifiers = maybe_unserialize( $product_meta->meta_value );

            if ( is_array( $old_modifiers ) && ! empty( $old_modifiers ) ) {
                $new_format = $this->convert_legacy_to_swever_format( $old_modifiers );
                update_post_meta( $product_id, '_squarekit_modifier_sets', $new_format );
                $migrated_count++;
            }
        }

        return $migrated_count;
    }

    /**
     * Convert legacy format to SWEVER format
     */
    private function convert_legacy_to_swever_format( $legacy_modifiers ) {
        $swever_format = array();

        foreach ( $legacy_modifiers as $modifier ) {
            $swever_format[] = array(
                'set_name' => $modifier['name'] ?? '',
                'square_mod_list_id' => $modifier['square_modifier_list_id'] ?? '',
                'single_choice' => $modifier['single'] ?? false,
                'source' => 'square',
                'options' => $modifier['options'] ?? array()
            );
        }

        return $swever_format;
    }

    /**
     * Display product modifiers on frontend (SWEVER-compatible)
     */
    public function display_product_modifiers() {
        global $product;
        if ( ! $product ) return;

        // Use SWEVER-compatible format
        $modifier_sets = get_post_meta( $product->get_id(), '_squarekit_modifier_sets', true );
        if ( empty( $modifier_sets ) || ! is_array( $modifier_sets ) ) return;

        echo '<div class="squarekit-modifiers-wrapper">';
        echo '<h4>' . esc_html__( 'Customization Options', 'squarekit' ) . '</h4>';

        foreach ( $modifier_sets as $set_index => $set_data ) {
            $set_name = $set_data['set_name'] ?? '';
            $single_choice = ! empty( $set_data['single_choice'] );
            $options = $set_data['options'] ?? array();

            if ( empty( $options ) || empty( $set_name ) ) {
                continue;
            }

            $input_name_base = "squarekit_modifier_sets[{$set_index}]";

            echo '<div class="squarekit-modifier-set-frontend">';
            echo '<h5>' . esc_html( $set_name ) . '</h5>';

            foreach ( $options as $opt_index => $option ) {
                $opt_name = $option['name'] ?? '';
                $opt_price = floatval( $option['price'] ?? 0 );
                $opt_stock = $option['stock'] ?? '';

                if ( empty( $opt_name ) ) continue;

                // Check stock availability
                $disabled = ( is_numeric( $opt_stock ) && intval( $opt_stock ) <= 0 ) ? 'disabled' : '';

                // Format price display
                $sign = $opt_price >= 0 ? '+' : '';
                $price_text = $opt_price != 0 ? sprintf( '%s%s', $sign, wc_price( $opt_price ) ) : '';

                // Input type and name
                $input_type = $single_choice ? 'radio' : 'checkbox';
                $input_name = $single_choice ? $input_name_base : $input_name_base . '[]';
                $option_id = $input_name_base . '_' . $opt_index;

                echo '<label class="squarekit-modifier-option-label" for="' . esc_attr( $option_id ) . '">';
                echo '<input type="' . esc_attr( $input_type ) . '" ';
                echo 'id="' . esc_attr( $option_id ) . '" ';
                echo 'name="' . esc_attr( $input_name ) . '" ';
                echo 'value="' . esc_attr( $opt_name ) . '" ';
                echo 'data-price="' . esc_attr( $opt_price ) . '" ';
                echo 'data-set-index="' . esc_attr( $set_index ) . '" ';
                echo 'data-option-index="' . esc_attr( $opt_index ) . '" ';
                echo $disabled . ' />';

                // Display option name and price
                echo '<span class="option-name">' . esc_html( $opt_name ) . '</span>';
                if ( $price_text ) {
                    echo ' <span class="option-price">(' . $price_text . ')</span>';
                }

                // Show stock info if available
                if ( is_numeric( $opt_stock ) && intval( $opt_stock ) > 0 ) {
                    echo ' <small class="stock-info">(' .
                         sprintf( esc_html__( 'In stock: %d', 'squarekit' ), intval( $opt_stock ) ) .
                         ')</small>';
                }

                echo '</label>';
            }

            echo '</div>';
        }

        // Price breakdown display
        echo '<div class="squarekit-price-breakdown" style="display: none; margin-top: 15px; padding: 10px; background: #f8f8f8; border: 1px solid #ddd;">';
        echo '<div class="squarekit-base-price-line">';
        echo '<span class="squarekit-price-label">' . esc_html__( 'Base Price:', 'squarekit' ) . '</span>';
        echo '<span class="squarekit-base-price-display"></span>';
        echo '</div>';
        echo '<div class="squarekit-modifiers-price-list"></div>';
        echo '<div class="squarekit-total-price-line" style="border-top: 1px solid #ccc; padding-top: 5px; margin-top: 5px; font-weight: bold;">';
        echo '<span class="squarekit-price-label">' . esc_html__( 'Total:', 'squarekit' ) . '</span>';
        echo '<span class="squarekit-total-price-display"></span>';
        echo '</div>';
        echo '</div>';

        echo '</div>';
    }

    /**
     * Enqueue frontend scripts and styles for modifiers
     */
    public function enqueue_frontend_scripts() {
        if ( is_product() ) {
            // Get the current product properly
            $product = wc_get_product();

            // If no product from wc_get_product(), try global
            if ( ! $product ) {
                global $product;
            }

            // Ensure we have a valid WooCommerce product object
            if ( ! $product || ! is_a( $product, 'WC_Product' ) ) {
                return;
            }

            // Enqueue the frontend CSS with cache busting
            wp_enqueue_style(
                'squarekit-frontend',
                SQUAREKIT_PLUGIN_URL . 'assets/css/squarekit-frontend.css',
                array(),
                '2.3.1-' . time()
            );

            // Enqueue the frontend script with cache busting
            wp_enqueue_script(
                'squarekit-frontend',
                SQUAREKIT_PLUGIN_URL . 'assets/js/squarekit-frontend.js',
                array( 'jquery' ),
                '2.2.0-' . time(),
                true
            );

            // Localize script with product data
            wp_localize_script( 'squarekit-frontend', 'squarekit_frontend', array(
                'base_price' => $product->get_price(),
                'currency_symbol' => get_woocommerce_currency_symbol(),
                'currency_position' => get_option( 'woocommerce_currency_pos' ),
                'thousand_separator' => wc_get_price_thousand_separator(),
                'decimal_separator' => wc_get_price_decimal_separator(),
                'decimals' => wc_get_price_decimals()
            ) );
        }
    }

    /**
     * Add modifiers to cart item data (SWEVER-compatible)
     */
    public function add_modifiers_to_cart_item( $cart_item_data, $product_id, $variation_id ) {
        if ( isset( $_POST['squarekit_modifier_sets'] ) && is_array( $_POST['squarekit_modifier_sets'] ) ) {
            $raw_modifiers = wc_clean( wp_unslash( $_POST['squarekit_modifier_sets'] ) );
            $cart_item_data['squarekit_chosen_sets'] = $raw_modifiers;
        }

        return $cart_item_data;
    }

    /**
     * Adjust cart item price based on selected modifiers (SWEVER-compatible)
     */
    public function adjust_cart_item_price( $cart ) {
        if ( is_admin() && ! defined( 'DOING_AJAX' ) ) {
            return;
        }

        if ( did_action( 'woocommerce_before_calculate_totals' ) >= 2 ) {
            return;
        }

        if ( ! is_object( $cart ) ) {
            return;
        }

        foreach ( $cart->get_cart() as $cart_item ) {
            if ( ! empty( $cart_item['squarekit_chosen_sets'] ) ) {
                $chosen_sets = $cart_item['squarekit_chosen_sets'];
                $product_id = $cart_item['product_id'];
                $available_sets = get_post_meta( $product_id, '_squarekit_modifier_sets', true );

                if ( ! is_array( $available_sets ) ) {
                    $available_sets = array();
                }

                // Create lookup array for faster processing
                $sets_lookup = array();
                foreach ( $available_sets as $set_index => $set_data ) {
                    $set_options = array();
                    if ( ! empty( $set_data['options'] ) && is_array( $set_data['options'] ) ) {
                        foreach ( $set_data['options'] as $option ) {
                            if ( isset( $option['name'], $option['price'] ) ) {
                                $set_options[ $option['name'] ] = floatval( $option['price'] );
                            }
                        }
                    }
                    $sets_lookup[ $set_index ] = array(
                        'set_name' => $set_data['set_name'] ?? '',
                        'single' => ! empty( $set_data['single_choice'] ),
                        'options' => $set_options
                    );
                }

                $base_price = floatval( $cart_item['data']->get_price() );
                $price_extra = 0.0;

                // Calculate modifier price additions
                foreach ( $chosen_sets as $set_index => $selected ) {
                    if ( ! isset( $sets_lookup[ $set_index ] ) ) {
                        continue;
                    }

                    $option_map = $sets_lookup[ $set_index ]['options'];

                    if ( is_array( $selected ) ) {
                        // Multiple choice (checkboxes)
                        foreach ( $selected as $opt_name ) {
                            if ( isset( $option_map[ $opt_name ] ) ) {
                                $price_extra += $option_map[ $opt_name ];
                            }
                        }
                    } else {
                        // Single choice (radio)
                        $opt_name = $selected;
                        if ( isset( $option_map[ $opt_name ] ) ) {
                            $price_extra += $option_map[ $opt_name ];
                        }
                    }
                }

                $new_price = $base_price + $price_extra;
                $cart_item['data']->set_price( $new_price );
            }
        }
    }

    /**
     * Display modifiers in cart (SWEVER-compatible)
     */
    public function display_modifiers_in_cart( $item_data, $cart_item ) {
        if ( isset( $cart_item['squarekit_chosen_sets'] ) && ! empty( $cart_item['squarekit_chosen_sets'] ) ) {
            $chosen_sets = $cart_item['squarekit_chosen_sets'];
            $available_sets = get_post_meta( $cart_item['product_id'], '_squarekit_modifier_sets', true );

            if ( ! is_array( $available_sets ) ) {
                $available_sets = array();
            }

            // Create lookup for set names and option details
            $sets_lookup = array();
            foreach ( $available_sets as $set_index => $set_data ) {
                $sets_lookup[ $set_index ] = array(
                    'set_name' => $set_data['set_name'] ?? '',
                    'options' => array()
                );

                if ( ! empty( $set_data['options'] ) && is_array( $set_data['options'] ) ) {
                    foreach ( $set_data['options'] as $option ) {
                        if ( isset( $option['name'] ) ) {
                            $sets_lookup[ $set_index ]['options'][ $option['name'] ] = array(
                                'name' => $option['name'],
                                'price' => floatval( $option['price'] ?? 0 )
                            );
                        }
                    }
                }
            }

            // Display selected modifiers
            foreach ( $chosen_sets as $set_index => $selected ) {
                if ( ! isset( $sets_lookup[ $set_index ] ) ) {
                    continue;
                }

                $set_name = $sets_lookup[ $set_index ]['set_name'];
                $options_map = $sets_lookup[ $set_index ]['options'];

                if ( is_array( $selected ) ) {
                    // Multiple choice
                    $selected_options = array();
                    foreach ( $selected as $opt_name ) {
                        if ( isset( $options_map[ $opt_name ] ) ) {
                            $option_data = $options_map[ $opt_name ];
                            $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';
                            $selected_options[] = $option_data['name'] . $price_text;
                        }
                    }

                    if ( ! empty( $selected_options ) ) {
                        $item_data[] = array(
                            'key' => $set_name,
                            'value' => implode( ', ', $selected_options ),
                            'display' => ''
                        );
                    }
                } else {
                    // Single choice
                    $opt_name = $selected;
                    if ( isset( $options_map[ $opt_name ] ) ) {
                        $option_data = $options_map[ $opt_name ];
                        $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';

                        $item_data[] = array(
                            'key' => $set_name,
                            'value' => $option_data['name'] . $price_text,
                            'display' => ''
                        );
                    }
                }
            }
        }

        return $item_data;
    }

    /**
     * Add modifiers to order item (SWEVER-compatible)
     */
    public function add_modifiers_to_order_item( $item, $cart_item_key, $values, $order ) {
        if ( isset( $values['squarekit_chosen_sets'] ) && ! empty( $values['squarekit_chosen_sets'] ) ) {
            $chosen_sets = $values['squarekit_chosen_sets'];
            $available_sets = get_post_meta( $values['product_id'], '_squarekit_modifier_sets', true );

            if ( ! is_array( $available_sets ) ) {
                $available_sets = array();
            }

            // Store the raw modifier data for future reference
            $item->add_meta_data( '_squarekit_chosen_sets', $chosen_sets );

            // Create lookup for set names and option details
            $sets_lookup = array();
            foreach ( $available_sets as $set_index => $set_data ) {
                $sets_lookup[ $set_index ] = array(
                    'set_name' => $set_data['set_name'] ?? '',
                    'options' => array()
                );

                if ( ! empty( $set_data['options'] ) && is_array( $set_data['options'] ) ) {
                    foreach ( $set_data['options'] as $option ) {
                        if ( isset( $option['name'] ) ) {
                            $sets_lookup[ $set_index ]['options'][ $option['name'] ] = array(
                                'name' => $option['name'],
                                'price' => floatval( $option['price'] ?? 0 )
                            );
                        }
                    }
                }
            }

            // Add human-readable modifier data to order
            foreach ( $chosen_sets as $set_index => $selected ) {
                if ( ! isset( $sets_lookup[ $set_index ] ) ) {
                    continue;
                }

                $set_name = $sets_lookup[ $set_index ]['set_name'];
                $options_map = $sets_lookup[ $set_index ]['options'];

                if ( is_array( $selected ) ) {
                    // Multiple choice
                    $selected_options = array();
                    foreach ( $selected as $opt_name ) {
                        if ( isset( $options_map[ $opt_name ] ) ) {
                            $option_data = $options_map[ $opt_name ];
                            $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';
                            $selected_options[] = $option_data['name'] . $price_text;
                        }
                    }

                    if ( ! empty( $selected_options ) ) {
                        $item->add_meta_data( $set_name, implode( ', ', $selected_options ) );
                    }
                } else {
                    // Single choice
                    $opt_name = $selected;
                    if ( isset( $options_map[ $opt_name ] ) ) {
                        $option_data = $options_map[ $opt_name ];
                        $price_text = $option_data['price'] > 0 ? ' (+' . wc_price( $option_data['price'] ) . ')' : '';
                        $item->add_meta_data( $set_name, $option_data['name'] . $price_text );
                    }
                }
            }
        }
    }

    /**
     * Get sync module instance
     */
    public function get_sync_module( $module_name ) {
        return isset( $this->sync_modules[ $module_name ] ) ? $this->sync_modules[ $module_name ] : null;
    }
}
