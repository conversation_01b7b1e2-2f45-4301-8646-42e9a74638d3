<?php
/**
 * Square Payment Gateway for WooCommerce
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/integrations
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Only load the gateway class if WooCommerce is available
if ( ! class_exists( 'WC_Payment_Gateway' ) ) {
    return;
}

add_filter( 'woocommerce_payment_gateways', function( $gateways ) {
    $gateways[] = 'WC_Gateway_SquareKit';
    return $gateways;
} );

/**
 * SquareKit Payment Gateway
 */
class WC_Gateway_SquareKit extends WC_Payment_Gateway {

    public function __construct() {
        $this->id                 = 'squarekit';
        $this->icon               = '';
        $this->has_fields         = true;
        $this->method_title       = __( 'Square (via Square Kit)', 'squarekit' );
        $this->method_description = __( 'Accept credit card payments via Square.', 'squarekit' );

        // Load the settings
        $this->init_form_fields();
        $this->init_settings();

        $this->title        = $this->get_option( 'title' );
        $this->description  = $this->get_option( 'description' );
        $this->enabled      = $this->get_option( 'enabled' );

        add_action( 'woocommerce_update_options_payment_gateways_' . $this->id, array( $this, 'process_admin_options' ) );
    }

    /**
     * Initialize Gateway Settings Form Fields
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title'   => __( 'Enable/Disable', 'squarekit' ),
                'type'    => 'checkbox',
                'label'   => __( 'Enable Square Payment', 'squarekit' ),
                'default' => 'no',
            ),
            'title' => array(
                'title'       => __( 'Title', 'squarekit' ),
                'type'        => 'text',
                'description' => __( 'This controls the title which the user sees during checkout.', 'squarekit' ),
                'default'     => __( 'Credit Card (Square)', 'squarekit' ),
                'desc_tip'    => true,
            ),
            'description' => array(
                'title'       => __( 'Description', 'squarekit' ),
                'type'        => 'textarea',
                'description' => __( 'This controls the description which the user sees during checkout.', 'squarekit' ),
                'default'     => __( 'Pay securely using your credit card via Square.', 'squarekit' ),
            ),
        );
    }

    /**
     * Output payment fields on the checkout
     */
    public function payment_fields() {
        echo '<p>' . esc_html( $this->description ) . '</p>';
        $settings = new SquareKit_Settings();
        $payment_methods = $settings->get_payment_methods();
        ?>
        <div id="squarekit-card-container"></div>
        <div id="squarekit-googlepay-container" style="margin-top:10px;"></div>
        <div id="squarekit-applepay-container" style="margin-top:10px;"></div>
        <div id="squarekit-afterpay-container" style="margin-top:10px;"></div>
        <input type="hidden" id="squarekit-payment-token" name="squarekit_payment_token" value="" />
        <input type="hidden" id="squarekit-payment-method" name="squarekit_payment_method" value="card" />
        <div id="squarekit-card-errors" style="color:red;"></div>
        <script src="https://sandbox.web.squarecdn.com/v1/square.js"></script>
        <script>
        jQuery(document).ready(function($) {
            var appId = '<?php echo esc_js( $settings->get_application_id() ); ?>';
            var locationId = '<?php echo esc_js( $settings->get('location_id') ); ?>';
            if (!window.Square) {
                $('#squarekit-card-errors').text('Square Web Payments SDK failed to load.');
                return;
            }
            var payments = window.Square.payments(appId, locationId);
            var card, googlePay, applePay, afterpay;
            // Card
            payments.card().then(function(cardInstance) {
                card = cardInstance;
                return card.attach('#squarekit-card-container');
            });
            // Google Pay
            <?php if ( !empty($payment_methods['google_pay']) ) : ?>
            payments.googlePay().then(function(gpay) {
                googlePay = gpay;
                return googlePay.attach('#squarekit-googlepay-container');
            });
            $('#squarekit-googlepay-container').on('click', 'button', function(e) {
                e.preventDefault();
                googlePay.tokenize().then(function(result) {
                    if (result.status === 'OK') {
                        $('#squarekit-payment-token').val(result.token);
                        $('#squarekit-payment-method').val('google_pay');
                        $('form.checkout').submit();
                    } else {
                        $('#squarekit-card-errors').text(result.errors ? result.errors[0].message : 'Google Pay error.');
                    }
                });
            });
            <?php endif; ?>
            // Apple Pay
            <?php if ( !empty($payment_methods['apple_pay']) ) : ?>
            payments.applePay().then(function(apay) {
                applePay = apay;
                return applePay.attach('#squarekit-applepay-container');
            });
            $('#squarekit-applepay-container').on('click', 'button', function(e) {
                e.preventDefault();
                applePay.tokenize().then(function(result) {
                    if (result.status === 'OK') {
                        $('#squarekit-payment-token').val(result.token);
                        $('#squarekit-payment-method').val('apple_pay');
                        $('form.checkout').submit();
                    } else {
                        $('#squarekit-card-errors').text(result.errors ? result.errors[0].message : 'Apple Pay error.');
                    }
                });
            });
            <?php endif; ?>
            // Afterpay
            <?php if ( !empty($payment_methods['afterpay']) ) : ?>
            payments.afterpayClearpay().then(function(apay) {
                afterpay = apay;
                return afterpay.attach('#squarekit-afterpay-container');
            });
            $('#squarekit-afterpay-container').on('click', 'button', function(e) {
                e.preventDefault();
                afterpay.tokenize().then(function(result) {
                    if (result.status === 'OK') {
                        $('#squarekit-payment-token').val(result.token);
                        $('#squarekit-payment-method').val('afterpay');
                        $('form.checkout').submit();
                    } else {
                        $('#squarekit-card-errors').text(result.errors ? result.errors[0].message : 'Afterpay error.');
                    }
                });
            });
            <?php endif; ?>
            // Card form submit
            $('form.checkout').on('checkout_place_order_squarekit', function(e) {
                e.preventDefault();
                $('#squarekit-card-errors').text('');
                card.tokenize().then(function(result) {
                    if (result.status === 'OK') {
                        $('#squarekit-payment-token').val(result.token);
                        $('#squarekit-payment-method').val('card');
                        $('form.checkout').off('checkout_place_order_squarekit');
                        $('form.checkout').submit();
                    } else {
                        $('#squarekit-card-errors').text(result.errors ? result.errors[0].message : 'Card error.');
                    }
                });
                return false;
            });
        });
        </script>
        <?php
    }

    /**
     * Process payment for SquareKit gateway
     *
     * @param int $order_id WooCommerce order ID
     * @return array Result array for WooCommerce
     */
    public function process_payment( $order_id ) {
        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wc_add_notice( __( 'Order not found.', 'squarekit' ), 'error' );
            return array( 'result' => 'fail' );
        }
        
        // Check if this is a subscription or renewal order
        $is_subscription = false;
        $is_renewal = false;
        $subscription_id = null;
        
        if ( class_exists( 'WC_Subscriptions' ) ) {
            // Check if order contains a subscription
            $subscriptions = wcs_get_subscriptions_for_order( $order_id );
            if ( ! empty( $subscriptions ) ) {
                $is_subscription = true;
                $subscription_id = array_keys( $subscriptions )[0];
            }
            
            // Check if this is a renewal order
            $renewal_orders = wcs_get_renewal_orders( $order_id );
            if ( ! empty( $renewal_orders ) ) {
                $is_renewal = true;
            }
        }
        
        // For renewal orders, try to get stored payment token
        $payment_token = '';
        if ( $is_renewal && $subscription_id ) {
            $stored_token = get_post_meta( $subscription_id, '_square_payment_token', true );
            if ( $stored_token ) {
                $payment_token = $stored_token;
            }
        }
        
        // If no stored token, get from POST data
        if ( empty( $payment_token ) ) {
            if ( empty( $_POST['squarekit_payment_token'] ) ) {
                wc_add_notice( __( 'Payment token missing. Please try again.', 'squarekit' ), 'error' );
                return array( 'result' => 'fail' );
            }
            $payment_token = sanitize_text_field( $_POST['squarekit_payment_token'] );
        }
        
        $payment_method = isset($_POST['squarekit_payment_method']) ? sanitize_text_field($_POST['squarekit_payment_method']) : 'card';
        $settings = new SquareKit_Settings();
        $square_api = new SquareKit_Square_API();
        $amount = intval( $order->get_total() * 100 );
        $currency = $order->get_currency();
        $body = array(
            'idempotency_key' => uniqid('sqkit_', true),
            'amount_money' => array(
                'amount' => $amount,
                'currency' => $currency,
            ),
            'source_id' => $payment_token,
            'autocomplete' => true,
            'location_id' => $settings->get('location_id'),
            'reference_id' => (string) $order_id,
        );
        
        // Log payment attempts based on type
        $db = new SquareKit_DB();
        if ( $is_subscription || $is_renewal ) {
            $log_type = $is_renewal ? 'subscription_renewal' : 'subscription_initial';
            $db->add_log(
                $log_type,
                sprintf( '%s payment attempt', $is_renewal ? 'Subscription renewal' : 'Subscription initial' ),
                array(
                    'order_id' => $order_id,
                    'subscription_id' => $subscription_id,
                    'amount' => $amount,
                    'currency' => $currency,
                    'body' => $body
                )
            );
        } else {
            // Log standard payment attempts
            if ($payment_method === 'google_pay') {
                $db->add_log(
                    'google_pay',
                    'Google Pay payment attempt',
                    array(
                        'order_id' => $order_id,
                        'amount' => $amount,
                        'currency' => $currency,
                        'body' => $body
                    )
                );
            }
            if ($payment_method === 'apple_pay') {
                $db->add_log(
                    'apple_pay',
                    'Apple Pay payment attempt',
                    array(
                        'order_id' => $order_id,
                        'amount' => $amount,
                        'currency' => $currency,
                        'body' => $body
                    )
                );
            }
            if ($payment_method === 'afterpay') {
                $db->add_log(
                    'afterpay',
                    'Afterpay payment attempt',
                    array(
                        'order_id' => $order_id,
                        'amount' => $amount,
                        'currency' => $currency,
                        'body' => $body
                    )
                );
            }
        }
        
        $response = $square_api->request('/payments', 'POST', $body);
        if ( is_wp_error( $response ) ) {
            // Log payment errors
            if ( $is_subscription || $is_renewal ) {
                $log_type = $is_renewal ? 'subscription_renewal' : 'subscription_initial';
                $db->add_log(
                    $log_type,
                    sprintf( '%s payment error', $is_renewal ? 'Subscription renewal' : 'Subscription initial' ),
                    array(
                        'order_id' => $order_id,
                        'subscription_id' => $subscription_id,
                        'error' => $response->get_error_message(),
                        'body' => $body
                    )
                );
            } else {
                if ($payment_method === 'google_pay') {
                    $db->add_log(
                        'google_pay',
                        'Google Pay payment error',
                        array(
                            'order_id' => $order_id,
                            'error' => $response->get_error_message(),
                            'body' => $body
                        )
                    );
                }
                if ($payment_method === 'apple_pay') {
                    $db->add_log(
                        'apple_pay',
                        'Apple Pay payment error',
                        array(
                            'order_id' => $order_id,
                            'error' => $response->get_error_message(),
                            'body' => $body
                        )
                    );
                }
                if ($payment_method === 'afterpay') {
                    $db->add_log(
                        'afterpay',
                        'Afterpay payment error',
                        array(
                            'order_id' => $order_id,
                            'error' => $response->get_error_message(),
                            'body' => $body
                        )
                    );
                }
            }
            wc_add_notice( __( 'Square payment error: ', 'squarekit' ) . $response->get_error_message(), 'error' );
            return array( 'result' => 'fail' );
        }
        
        if ( !empty($response['payment']['id']) ) {
            // Log payment success
            if ( $is_subscription || $is_renewal ) {
                $log_type = $is_renewal ? 'subscription_renewal' : 'subscription_initial';
                $db->add_log(
                    $log_type,
                    sprintf( '%s payment success', $is_renewal ? 'Subscription renewal' : 'Subscription initial' ),
                    array(
                        'order_id' => $order_id,
                        'subscription_id' => $subscription_id,
                        'payment_id' => $response['payment']['id'],
                        'amount' => $amount,
                        'currency' => $currency
                    )
                );
                
                // Store payment token for future renewals if this is initial subscription
                if ( $is_subscription && !$is_renewal && $subscription_id ) {
                    update_post_meta( $subscription_id, '_square_payment_token', $payment_token );
                }
            } else {
                if ($payment_method === 'google_pay') {
                    $db->add_log(
                        'google_pay',
                        'Google Pay payment success',
                        array(
                            'order_id' => $order_id,
                            'payment_id' => $response['payment']['id'],
                            'amount' => $amount,
                            'currency' => $currency
                        )
                    );
                }
                if ($payment_method === 'apple_pay') {
                    $db->add_log(
                        'apple_pay',
                        'Apple Pay payment success',
                        array(
                            'order_id' => $order_id,
                            'payment_id' => $response['payment']['id'],
                            'amount' => $amount,
                            'currency' => $currency
                        )
                    );
                }
                if ($payment_method === 'afterpay') {
                    $db->add_log(
                        'afterpay',
                        'Afterpay payment success',
                        array(
                            'order_id' => $order_id,
                            'payment_id' => $response['payment']['id'],
                            'amount' => $amount,
                            'currency' => $currency
                        )
                    );
                }
            }
            
            $order->payment_complete( $response['payment']['id'] );
            $order->add_order_note( __( 'Paid via Square. Payment ID: ', 'squarekit' ) . $response['payment']['id'] );
            WC()->cart->empty_cart();
            return array(
                'result'   => 'success',
                'redirect' => $this->get_return_url( $order ),
            );
        } else {
            // Log payment failure
            if ( $is_subscription || $is_renewal ) {
                $log_type = $is_renewal ? 'subscription_renewal' : 'subscription_initial';
                $db->add_log(
                    $log_type,
                    sprintf( '%s payment failed (no payment ID)', $is_renewal ? 'Subscription renewal' : 'Subscription initial' ),
                    array(
                        'order_id' => $order_id,
                        'subscription_id' => $subscription_id,
                        'response' => $response,
                        'body' => $body
                    )
                );
            } else {
                if ($payment_method === 'google_pay') {
                    $db->add_log(
                        'google_pay',
                        'Google Pay payment failed (no payment ID)',
                        array(
                            'order_id' => $order_id,
                            'response' => $response,
                            'body' => $body
                        )
                    );
                }
                if ($payment_method === 'apple_pay') {
                    $db->add_log(
                        'apple_pay',
                        'Apple Pay payment failed (no payment ID)',
                        array(
                            'order_id' => $order_id,
                            'response' => $response,
                            'body' => $body
                        )
                    );
                }
                if ($payment_method === 'afterpay') {
                    $db->add_log(
                        'afterpay',
                        'Afterpay payment failed (no payment ID)',
                        array(
                            'order_id' => $order_id,
                            'response' => $response,
                            'body' => $body
                        )
                    );
                }
            }
            wc_add_notice( __( 'Square payment failed. Please try again.', 'squarekit' ), 'error' );
            return array( 'result' => 'fail' );
        }
    }
} 