<?php
/**
 * Modern Square Payment Gateway for WooCommerce
 * Production-ready implementation with SCA compliance and modern features
 *
 * @package SquareKit
 * @subpackage SquareKit/includes/integrations
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Only load the gateway class if WooCommerce is available
if ( ! class_exists( 'WC_Payment_Gateway_CC' ) ) {
    return;
}

/**
 * Register the SquareKit Payment Gateway with WooCommerce
 */
add_filter( 'woocommerce_payment_gateways', function( $gateways ) {
    $gateways[] = 'WC_Gateway_SquareKit';
    return $gateways;
} );

/**
 * Modern SquareKit Payment Gateway
 *
 * Features:
 * - Square Web Payments SDK integration
 * - Strong Customer Authentication (SCA) compliance
 * - Tokenization and saved payment methods
 * - Digital wallet support (Google Pay, Apple Pay, Afterpay)
 * - Subscription support
 * - Modern error handling and logging
 */
class WC_Gateway_SquareKit extends WC_Payment_Gateway_CC {

    /**
     * Square application ID
     * @var string
     */
    private $square_application_id;

    /**
     * Square environment (sandbox/production)
     * @var string
     */
    private $square_environment;

    /**
     * Square location ID
     * @var string
     */
    private $location_id;

    /**
     * SquareKit settings instance
     * @var SquareKit_Settings
     */
    private $squarekit_settings;

    /**
     * Constructor
     */
    public function __construct() {
        $this->id                 = 'squarekit';
        $this->icon               = '';
        $this->has_fields         = true;
        $this->method_title       = __( 'Square (via Square Kit)', 'squarekit' );
        $this->method_description = __( 'Accept secure payments via Square with support for credit cards, digital wallets, and saved payment methods.', 'squarekit' );

        // Set supported features
        $this->supports = array(
            'products',
            'tokenization',
            'add_payment_method',
            'subscriptions',
            'subscription_cancellation',
            'subscription_suspension',
            'subscription_reactivation',
            'subscription_amount_changes',
            'subscription_date_changes',
            'multiple_subscriptions',
            'subscription_payment_method_change',
            'subscription_payment_method_change_customer',
            'subscription_payment_method_change_admin',
            'refunds',
        );

        // Initialize settings
        $this->init_form_fields();
        $this->init_settings();

        // Load gateway settings
        $this->title       = $this->get_option( 'title' );
        $this->description = $this->get_option( 'description' );
        $this->enabled     = $this->get_option( 'enabled' );

        // Initialize SquareKit settings
        $this->squarekit_settings = new SquareKit_Settings();

        // Load Square configuration
        $this->load_square_config();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load Square configuration from SquareKit settings
     */
    private function load_square_config() {
        $this->square_environment = $this->squarekit_settings->get( 'environment', 'sandbox' );
        $this->location_id = $this->squarekit_settings->get( 'location_id' );

        // Set application ID based on environment
        if ( $this->square_environment === 'sandbox' ) {
            $this->square_application_id = $this->squarekit_settings->get( 'sandbox_application_id' );
        } else {
            $this->square_application_id = $this->squarekit_settings->get( 'application_id' );
        }
    }

    /**
     * Initialize hooks and actions
     */
    private function init_hooks() {
        // Admin options save hook
        add_action( 'woocommerce_update_options_payment_gateways_' . $this->id, array( $this, 'process_admin_options' ) );

        // Enqueue scripts
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );

        // AJAX handlers for payment processing
        add_action( 'wp_ajax_squarekit_process_payment', array( $this, 'ajax_process_payment' ) );
        add_action( 'wp_ajax_nopriv_squarekit_process_payment', array( $this, 'ajax_process_payment' ) );

        // Add payment method from account page
        add_action( 'woocommerce_add_payment_method', array( $this, 'add_payment_method' ) );

        // Subscription payment processing
        add_action( 'woocommerce_scheduled_subscription_payment_' . $this->id, array( $this, 'scheduled_subscription_payment' ), 10, 2 );
    }

    /**
     * Initialize Gateway Settings Form Fields
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title'   => __( 'Enable/Disable', 'squarekit' ),
                'type'    => 'checkbox',
                'label'   => __( 'Enable Square Payment Gateway', 'squarekit' ),
                'default' => 'no',
                'description' => __( 'Enable Square payment processing for your store.', 'squarekit' ),
            ),
            'title' => array(
                'title'       => __( 'Title', 'squarekit' ),
                'type'        => 'text',
                'description' => __( 'Payment method title that customers see during checkout.', 'squarekit' ),
                'default'     => __( 'Credit Card (Square)', 'squarekit' ),
                'desc_tip'    => true,
            ),
            'description' => array(
                'title'       => __( 'Description', 'squarekit' ),
                'type'        => 'textarea',
                'description' => __( 'Payment method description that customers see during checkout.', 'squarekit' ),
                'default'     => __( 'Pay securely with your credit card, debit card, or digital wallet via Square.', 'squarekit' ),
                'desc_tip'    => true,
            ),
            'capture_method' => array(
                'title'       => __( 'Capture Method', 'squarekit' ),
                'type'        => 'select',
                'description' => __( 'Choose when to capture payments.', 'squarekit' ),
                'default'     => 'automatic',
                'desc_tip'    => true,
                'options'     => array(
                    'automatic' => __( 'Automatic - Capture payment immediately', 'squarekit' ),
                    'manual'    => __( 'Manual - Authorize now, capture later', 'squarekit' ),
                ),
            ),
            'enable_saved_cards' => array(
                'title'       => __( 'Saved Payment Methods', 'squarekit' ),
                'type'        => 'checkbox',
                'label'       => __( 'Enable saved payment methods', 'squarekit' ),
                'description' => __( 'Allow customers to save payment methods for future purchases.', 'squarekit' ),
                'default'     => 'yes',
                'desc_tip'    => true,
            ),
            'enable_digital_wallets' => array(
                'title'       => __( 'Digital Wallets', 'squarekit' ),
                'type'        => 'checkbox',
                'label'       => __( 'Enable digital wallets', 'squarekit' ),
                'description' => __( 'Accept payments from Google Pay, Apple Pay, and other digital wallets.', 'squarekit' ),
                'default'     => 'yes',
                'desc_tip'    => true,
            ),
            'enable_logging' => array(
                'title'       => __( 'Debug Logging', 'squarekit' ),
                'type'        => 'checkbox',
                'label'       => __( 'Enable debug logging', 'squarekit' ),
                'description' => __( 'Log payment events for debugging. Logs can be found in SquareKit > Logs.', 'squarekit' ),
                'default'     => 'no',
                'desc_tip'    => true,
            ),
        );
    }

    /**
     * Enqueue payment scripts and styles
     */
    public function enqueue_scripts() {
        // Only load on checkout, cart, and account pages
        if ( ! is_checkout() && ! is_cart() && ! is_account_page() ) {
            return;
        }

        // Determine Square SDK URL based on environment
        $sdk_url = $this->square_environment === 'sandbox'
            ? 'https://sandbox.web.squarecdn.com/v1/square.js'
            : 'https://web.squarecdn.com/v1/square.js';

        // Enqueue Square Web Payments SDK
        wp_enqueue_script(
            'square-web-payments-sdk',
            $sdk_url,
            array(),
            null,
            true
        );

        // Enqueue SquareKit payment styles
        wp_enqueue_style(
            'squarekit-payment-styles',
            plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'assets/css/payment-gateway.css',
            array(),
            SQUAREKIT_VERSION
        );

        // Enqueue SquareKit payment script
        wp_enqueue_script(
            'squarekit-payment-gateway',
            plugin_dir_url( dirname( dirname( __FILE__ ) ) ) . 'assets/js/payment-gateway.js',
            array( 'jquery', 'square-web-payments-sdk' ),
            SQUAREKIT_VERSION,
            true
        );

        // Localize script with configuration
        wp_localize_script( 'squarekit-payment-gateway', 'SquareKitPayment', array(
            'applicationId'     => $this->square_application_id,
            'locationId'        => $this->location_id,
            'environment'       => $this->square_environment,
            'ajaxUrl'           => admin_url( 'admin-ajax.php' ),
            'nonce'             => wp_create_nonce( 'squarekit_payment_nonce' ),
            'enabledWallets'    => $this->get_enabled_digital_wallets(),
            'enableSavedCards'  => $this->get_option( 'enable_saved_cards' ) === 'yes',
            'currency'          => get_woocommerce_currency(),
            'countryCode'       => WC()->countries->get_base_country(),
            'messages'          => array(
                'sdkLoadError'      => __( 'Square Web Payments SDK failed to load. Please refresh the page and try again.', 'squarekit' ),
                'tokenizeError'     => __( 'Payment processing failed. Please check your payment details and try again.', 'squarekit' ),
                'networkError'      => __( 'Network error occurred. Please check your connection and try again.', 'squarekit' ),
                'processingPayment' => __( 'Processing payment...', 'squarekit' ),
                'verifyingPayment'  => __( 'Verifying payment...', 'squarekit' ),
            ),
        ) );
    }

    /**
     * Get enabled digital wallets
     */
    private function get_enabled_digital_wallets() {
        $enabled_wallets = array();

        if ( $this->get_option( 'enable_digital_wallets' ) === 'yes' ) {
            $payment_methods = $this->squarekit_settings->get_payment_methods();

            if ( ! empty( $payment_methods['google_pay'] ) ) {
                $enabled_wallets[] = 'googlePay';
            }
            if ( ! empty( $payment_methods['apple_pay'] ) ) {
                $enabled_wallets[] = 'applePay';
            }
            if ( ! empty( $payment_methods['afterpay'] ) ) {
                $enabled_wallets[] = 'afterpay';
            }
        }

        return $enabled_wallets;
    }

    /**
     * Output payment fields on the checkout
     */
    public function payment_fields() {
        // Display description
        if ( $this->description ) {
            echo '<div class="squarekit-payment-description">';
            echo '<p>' . wp_kses_post( $this->description ) . '</p>';
            echo '</div>';
        }

        // Check if gateway is properly configured
        if ( empty( $this->square_application_id ) || empty( $this->location_id ) ) {
            echo '<div class="squarekit-payment-error">';
            echo '<p>' . esc_html__( 'Payment gateway is not properly configured. Please contact the store administrator.', 'squarekit' ) . '</p>';
            echo '</div>';
            return;
        }

        // Display saved payment methods for logged-in users
        if ( is_user_logged_in() && $this->get_option( 'enable_saved_cards' ) === 'yes' ) {
            $this->saved_payment_methods();
        }

        // Payment form container
        echo '<div id="squarekit-payment-form" class="squarekit-payment-form">';

        // Digital wallet buttons container
        if ( $this->get_option( 'enable_digital_wallets' ) === 'yes' ) {
            echo '<div id="squarekit-digital-wallets" class="squarekit-digital-wallets">';
            echo '<div id="squarekit-google-pay-button" class="squarekit-wallet-button"></div>';
            echo '<div id="squarekit-apple-pay-button" class="squarekit-wallet-button"></div>';
            echo '<div id="squarekit-afterpay-button" class="squarekit-wallet-button"></div>';
            echo '</div>';

            echo '<div class="squarekit-payment-divider">';
            echo '<span>' . esc_html__( 'or pay with card', 'squarekit' ) . '</span>';
            echo '</div>';
        }

        // Card payment form
        echo '<div id="squarekit-card-container" class="squarekit-card-container"></div>';

        // Hidden fields for payment processing
        echo '<input type="hidden" id="squarekit-payment-token" name="squarekit_payment_token" value="" />';
        echo '<input type="hidden" id="squarekit-payment-method" name="squarekit_payment_method" value="card" />';
        echo '<input type="hidden" id="squarekit-verification-token" name="squarekit_verification_token" value="" />';

        // Error display container
        echo '<div id="squarekit-payment-errors" class="squarekit-payment-errors" style="display: none;"></div>';

        // Loading indicator
        echo '<div id="squarekit-payment-loading" class="squarekit-payment-loading" style="display: none;">';
        echo '<div class="squarekit-spinner"></div>';
        echo '<span>' . esc_html__( 'Processing payment...', 'squarekit' ) . '</span>';
        echo '</div>';

        echo '</div>'; // End payment form container
    }
    /**
     * Display saved payment methods for logged-in users
     */
    public function saved_payment_methods() {
        $tokens = WC_Payment_Tokens::get_customer_tokens( get_current_user_id(), $this->id );

        if ( empty( $tokens ) ) {
            return;
        }

        echo '<div class="squarekit-saved-payment-methods">';
        echo '<h4>' . esc_html__( 'Saved Payment Methods', 'squarekit' ) . '</h4>';

        foreach ( $tokens as $token ) {
            $card_type = $token->get_card_type();
            $last_four = $token->get_last4();
            $expiry_month = $token->get_expiry_month();
            $expiry_year = $token->get_expiry_year();

            echo '<div class="squarekit-saved-card">';
            echo '<label>';
            echo '<input type="radio" name="wc-' . esc_attr( $this->id ) . '-payment-token" value="' . esc_attr( $token->get_id() ) . '" />';
            echo '<span class="card-details">';
            echo esc_html( ucfirst( $card_type ) ) . ' ending in ' . esc_html( $last_four );
            echo ' (expires ' . esc_html( $expiry_month ) . '/' . esc_html( $expiry_year ) . ')';
            echo '</span>';
            echo '</label>';
            echo '</div>';
        }

        echo '<div class="squarekit-new-card">';
        echo '<label>';
        echo '<input type="radio" name="wc-' . esc_attr( $this->id ) . '-payment-token" value="new" checked="checked" />';
        echo esc_html__( 'Use a new payment method', 'squarekit' );
        echo '</label>';
        echo '</div>';

        echo '</div>';
    }

    /**
     * Process payment for SquareKit gateway
     *
     * @param int $order_id WooCommerce order ID
     * @return array Result array for WooCommerce
     */
    public function process_payment( $order_id ) {
        try {
            $order = wc_get_order( $order_id );
            if ( ! $order ) {
                throw new Exception( __( 'Order not found.', 'squarekit' ) );
            }

            // Validate nonce for security
            if ( ! wp_verify_nonce( $_POST['squarekit_payment_nonce'] ?? '', 'squarekit_payment_nonce' ) ) {
                throw new Exception( __( 'Security check failed. Please refresh the page and try again.', 'squarekit' ) );
            }

            // Check if using saved payment method
            $using_saved_method = false;
            $payment_token = '';

            if ( isset( $_POST['wc-' . $this->id . '-payment-token'] ) && 'new' !== $_POST['wc-' . $this->id . '-payment-token'] ) {
                $token_id = wc_clean( $_POST['wc-' . $this->id . '-payment-token'] );
                $token = WC_Payment_Tokens::get( $token_id );

                if ( $token && $token->get_user_id() === get_current_user_id() ) {
                    $payment_token = $token->get_token();
                    $using_saved_method = true;
                }
            }

            // If not using saved method, get token from form
            if ( ! $using_saved_method ) {
                if ( empty( $_POST['squarekit_payment_token'] ) ) {
                    throw new Exception( __( 'Payment token missing. Please try again.', 'squarekit' ) );
                }
                $payment_token = sanitize_text_field( $_POST['squarekit_payment_token'] );
            }

            // Get payment method type
            $payment_method = isset( $_POST['squarekit_payment_method'] ) ? sanitize_text_field( $_POST['squarekit_payment_method'] ) : 'card';

            // Validate required configuration
            if ( empty( $this->location_id ) ) {
                throw new Exception( __( 'Payment gateway is not properly configured.', 'squarekit' ) );
            }

            // Process the payment
            $payment_result = $this->create_square_payment( $order, $payment_token, $payment_method );

            // Handle successful payment
            if ( $payment_result['success'] ) {
                $payment_id = $payment_result['payment']['id'];

                // Mark order as paid
                $order->payment_complete( $payment_id );

                // Add order note
                $order->add_order_note(
                    sprintf(
                        __( 'Payment completed via Square. Payment ID: %s', 'squarekit' ),
                        $payment_id
                    )
                );

                // Save payment method if requested and not already saved
                if ( ! $using_saved_method && isset( $_POST['wc-' . $this->id . '-new-payment-method'] ) && is_user_logged_in() ) {
                    $this->save_payment_method( $payment_result['payment'], get_current_user_id() );
                }

                // Handle subscription setup
                if ( $this->order_contains_subscription( $order ) ) {
                    $this->setup_subscription_payment_method( $order, $payment_result['payment'] );
                }

                // Log successful payment
                $this->log_payment_event( 'payment_success', array(
                    'order_id' => $order_id,
                    'payment_id' => $payment_id,
                    'payment_method' => $payment_method,
                    'amount' => $order->get_total(),
                ) );

                // Clear cart
                WC()->cart->empty_cart();

                return array(
                    'result'   => 'success',
                    'redirect' => $this->get_return_url( $order ),
                );
            } else {
                throw new Exception( $payment_result['error'] ?? __( 'Payment processing failed.', 'squarekit' ) );
            }

        } catch ( Exception $e ) {
            // Log error
            $this->log_payment_event( 'payment_error', array(
                'order_id' => $order_id,
                'error' => $e->getMessage(),
                'payment_method' => $payment_method ?? 'unknown',
            ) );

            // Display error to customer
            wc_add_notice( $e->getMessage(), 'error' );

            return array( 'result' => 'fail' );
        }
    }

    /**
     * Create Square payment using the Payments API
     *
     * @param WC_Order $order
     * @param string $payment_token
     * @param string $payment_method
     * @return array
     */
    private function create_square_payment( $order, $payment_token, $payment_method ) {
        try {
            $square_api = new SquareKit_Square_API();
            $amount = intval( $order->get_total() * 100 ); // Convert to cents
            $currency = $order->get_currency();

            // Build payment request
            $payment_data = array(
                'idempotency_key' => wp_generate_uuid4(),
                'amount_money' => array(
                    'amount' => $amount,
                    'currency' => $currency,
                ),
                'source_id' => $payment_token,
                'location_id' => $this->location_id,
                'reference_id' => (string) $order->get_id(),
                'note' => sprintf( 'Order #%s from %s', $order->get_order_number(), get_bloginfo( 'name' ) ),
            );

            // Set autocomplete based on capture method
            $payment_data['autocomplete'] = ( $this->get_option( 'capture_method' ) === 'automatic' );

            // Add buyer information for SCA compliance
            $billing_address = $order->get_address( 'billing' );
            if ( ! empty( $billing_address ) ) {
                $payment_data['buyer_email_address'] = $order->get_billing_email();

                $payment_data['billing_address'] = array(
                    'address_line_1' => $billing_address['address_1'],
                    'address_line_2' => $billing_address['address_2'],
                    'locality' => $billing_address['city'],
                    'administrative_district_level_1' => $billing_address['state'],
                    'postal_code' => $billing_address['postcode'],
                    'country' => $billing_address['country'],
                );
            }

            // Make API request
            $response = $square_api->request( '/payments', 'POST', $payment_data );

            if ( is_wp_error( $response ) ) {
                return array(
                    'success' => false,
                    'error' => $response->get_error_message(),
                );
            }

            if ( isset( $response['payment'] ) && isset( $response['payment']['id'] ) ) {
                return array(
                    'success' => true,
                    'payment' => $response['payment'],
                );
            } else {
                return array(
                    'success' => false,
                    'error' => __( 'Invalid response from Square API.', 'squarekit' ),
                );
            }

        } catch ( Exception $e ) {
            return array(
                'success' => false,
                'error' => $e->getMessage(),
            );
        }
    }
    /**
     * Save payment method for future use
     *
     * @param array $payment Square payment response
     * @param int $user_id User ID
     */
    private function save_payment_method( $payment, $user_id ) {
        if ( empty( $payment['source_details']['card'] ) ) {
            return;
        }

        $card = $payment['source_details']['card'];

        $token = new WC_Payment_Token_CC();
        $token->set_token( $payment['id'] );
        $token->set_gateway_id( $this->id );
        $token->set_user_id( $user_id );
        $token->set_card_type( strtolower( $card['card_brand'] ) );
        $token->set_last4( $card['last_4'] );
        $token->set_expiry_month( $card['exp_month'] );
        $token->set_expiry_year( $card['exp_year'] );

        $token->save();
    }

    /**
     * Check if order contains subscription
     *
     * @param WC_Order $order
     * @return bool
     */
    private function order_contains_subscription( $order ) {
        if ( ! class_exists( 'WC_Subscriptions' ) ) {
            return false;
        }

        return wcs_order_contains_subscription( $order );
    }

    /**
     * Setup subscription payment method
     *
     * @param WC_Order $order
     * @param array $payment Square payment response
     */
    private function setup_subscription_payment_method( $order, $payment ) {
        $subscriptions = wcs_get_subscriptions_for_order( $order );

        foreach ( $subscriptions as $subscription ) {
            // Store payment token for future renewals
            $subscription->update_meta_data( '_square_payment_token', $payment['id'] );
            $subscription->save();
        }
    }

    /**
     * Log payment events
     *
     * @param string $event_type
     * @param array $data
     */
    private function log_payment_event( $event_type, $data ) {
        if ( $this->get_option( 'enable_logging' ) !== 'yes' ) {
            return;
        }

        $db = new SquareKit_DB();
        $db->add_log( 'payment_gateway', $event_type, $data );
    }

    /**
     * Process scheduled subscription payment
     *
     * @param float $amount_to_charge
     * @param WC_Order $renewal_order
     */
    public function scheduled_subscription_payment( $amount_to_charge, $renewal_order ) {
        try {
            $subscriptions = wcs_get_subscriptions_for_renewal_order( $renewal_order );
            $subscription = array_shift( $subscriptions );

            if ( ! $subscription ) {
                throw new Exception( __( 'Subscription not found for renewal order.', 'squarekit' ) );
            }

            // Get stored payment token
            $payment_token = $subscription->get_meta( '_square_payment_token' );

            if ( empty( $payment_token ) ) {
                throw new Exception( __( 'No payment method found for subscription.', 'squarekit' ) );
            }

            // Process renewal payment
            $payment_result = $this->create_square_payment( $renewal_order, $payment_token, 'card' );

            if ( $payment_result['success'] ) {
                $payment_id = $payment_result['payment']['id'];

                $renewal_order->payment_complete( $payment_id );
                $renewal_order->add_order_note(
                    sprintf(
                        __( 'Subscription renewal payment completed via Square. Payment ID: %s', 'squarekit' ),
                        $payment_id
                    )
                );

                $this->log_payment_event( 'subscription_renewal_success', array(
                    'subscription_id' => $subscription->get_id(),
                    'renewal_order_id' => $renewal_order->get_id(),
                    'payment_id' => $payment_id,
                    'amount' => $amount_to_charge,
                ) );
            } else {
                throw new Exception( $payment_result['error'] );
            }

        } catch ( Exception $e ) {
            $renewal_order->update_status( 'failed', $e->getMessage() );

            $this->log_payment_event( 'subscription_renewal_failed', array(
                'subscription_id' => $subscription->get_id() ?? null,
                'renewal_order_id' => $renewal_order->get_id(),
                'error' => $e->getMessage(),
                'amount' => $amount_to_charge,
            ) );
        }
    }

    /**
     * Add payment method from account page
     *
     * @return array
     */
    public function add_payment_method() {
        // This would be implemented for adding payment methods from the account page
        // For now, return a basic response
        return array(
            'result'   => 'success',
            'redirect' => wc_get_endpoint_url( 'payment-methods' ),
        );
    }
}