<?php
/**
 * SquareKit Import Manager Class
 *
 * High-level manager that provides a simple interface for product imports
 * while handling the complexity of choosing between different import methods.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Import Manager Class
 *
 * Provides a simplified interface for product imports and manages the transition
 * between legacy and SWEVER import architectures.
 */
class SquareKit_Import_Manager {

    /**
     * Import bridge instance
     *
     * @var SquareKit_Import_Bridge
     */
    private $import_bridge;

    /**
     * Settings instance
     *
     * @var SquareKit_Settings
     */
    private $settings;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Import preferences
     *
     * @var array
     */
    private $preferences = array(
        'preferred_method' => 'auto', // 'auto', 'swever', 'legacy'
        'fallback_enabled' => true,
        'validation_enabled' => true,
        'logging_enabled' => true
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
        $this->load_preferences();
    }

    /**
     * Initialize dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Import_Bridge' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-import-bridge.php';
        }

        if ( ! class_exists( 'SquareKit_Settings' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-settings.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';
        }

        $this->import_bridge = new SquareKit_Import_Bridge();
        $this->settings = new SquareKit_Settings();
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Load import preferences from settings
     */
    private function load_preferences() {
        $saved_preferences = get_option( 'squarekit_import_preferences', array() );
        $this->preferences = array_merge( $this->preferences, $saved_preferences );
    }

    /**
     * Import a single product with automatic method selection
     *
     * @param mixed $input Square item ID (string) or item data (array)
     * @param array $options Import options
     * @return array|WP_Error Import result or error
     */
    public function import_product( $input, $options = array() ) {
        $this->logger->log( 'import_manager', 'info', 'Starting managed product import' );

        // Validate input
        if ( $this->preferences['validation_enabled'] ) {
            $validation_result = $this->validate_import_input( $input, $options );
            if ( is_wp_error( $validation_result ) ) {
                return $validation_result;
            }
        }

        // Determine import method
        $method = $this->determine_import_method( $input );
        $this->logger->log( 'import_manager', 'info', "Selected import method: {$method}" );

        // Prepare options with method preference
        $enhanced_options = $this->prepare_import_options( $options, $method );

        // Attempt import
        $result = $this->import_bridge->import_product( $input, $enhanced_options );

        // Handle fallback if enabled and primary method failed
        if ( is_wp_error( $result ) && $this->preferences['fallback_enabled'] && $method !== 'auto' ) {
            $this->logger->log( 'import_manager', 'warning', "Primary method failed, attempting fallback" );
            $fallback_method = $method === 'swever' ? 'legacy' : 'swever';
            $fallback_options = $this->prepare_import_options( $options, $fallback_method );
            $result = $this->import_bridge->import_product( $input, $fallback_options );
        }

        // Log result
        if ( $this->preferences['logging_enabled'] ) {
            $this->log_import_result( $result, $input );
        }

        return $result;
    }

    /**
     * Import multiple products with batch processing
     *
     * @param array $inputs Array of Square item IDs or item data
     * @param array $options Import options
     * @return array Batch import results
     */
    public function import_products_batch( $inputs, $options = array() ) {
        $batch_size = isset( $options['batch_size'] ) ? intval( $options['batch_size'] ) : 10;
        $results = array(
            'total' => count( $inputs ),
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'results' => array(),
            'errors' => array()
        );

        $this->logger->log( 'import_manager', 'info', "Starting batch import of {$results['total']} products" );

        // Process in batches
        $batches = array_chunk( $inputs, $batch_size );
        
        foreach ( $batches as $batch_index => $batch ) {
            $this->logger->log( 'import_manager', 'info', "Processing batch " . ($batch_index + 1) . " of " . count($batches) );
            
            foreach ( $batch as $input ) {
                $result = $this->import_product( $input, $options );
                
                $results['processed']++;
                
                if ( is_wp_error( $result ) ) {
                    $results['failed']++;
                    $results['errors'][] = array(
                        'input' => $input,
                        'error' => $result->get_error_message()
                    );
                } else {
                    $results['successful']++;
                }
                
                $results['results'][] = array(
                    'input' => $input,
                    'result' => $result
                );
            }
            
            // Small delay between batches to prevent overwhelming the API
            if ( $batch_index < count($batches) - 1 ) {
                sleep( 1 );
            }
        }

        $this->logger->log( 'import_manager', 'info', "Batch import completed: {$results['successful']} successful, {$results['failed']} failed" );

        return $results;
    }

    /**
     * Validate import input
     *
     * @param mixed $input Import input
     * @param array $options Import options
     * @return bool|WP_Error True if valid, WP_Error if invalid
     */
    private function validate_import_input( $input, $options ) {
        if ( is_string( $input ) ) {
            // Validate Square item ID format
            if ( empty( $input ) || strlen( $input ) < 10 ) {
                return new WP_Error( 'invalid_square_id', 'Invalid Square item ID format' );
            }
        } elseif ( is_array( $input ) ) {
            // Validate Square item data structure
            if ( ! isset( $input['id'] ) || ! isset( $input['item_data'] ) ) {
                return new WP_Error( 'invalid_item_data', 'Invalid Square item data structure' );
            }
        } else {
            return new WP_Error( 'invalid_input_type', 'Input must be Square item ID (string) or item data (array)' );
        }

        return true;
    }

    /**
     * Determine the best import method for given input
     *
     * @param mixed $input Import input
     * @return string Import method ('swever', 'legacy', or 'auto')
     */
    private function determine_import_method( $input ) {
        // Check user preference first
        if ( $this->preferences['preferred_method'] !== 'auto' ) {
            return $this->preferences['preferred_method'];
        }

        // Auto-detection logic
        if ( ! $this->import_bridge->is_swever_available() ) {
            return 'legacy';
        }

        return $this->import_bridge->get_recommended_method( $input );
    }

    /**
     * Prepare import options based on method
     *
     * @param array $options Original options
     * @param string $method Import method
     * @return array Enhanced options
     */
    private function prepare_import_options( $options, $method ) {
        $enhanced_options = $options;
        $enhanced_options['_preferred_method'] = $method;
        
        // Add method-specific defaults
        if ( $method === 'swever' ) {
            $enhanced_options = array_merge( array(
                'import_name' => true,
                'import_description' => true,
                'import_price' => true,
                'import_sku' => true,
                'import_categories' => true,
                'import_images' => true,
                'disable_attributes' => false
            ), $enhanced_options );
        }

        return $enhanced_options;
    }

    /**
     * Log import result
     *
     * @param mixed $result Import result
     * @param mixed $input Original input
     */
    private function log_import_result( $result, $input ) {
        $input_id = is_string( $input ) ? $input : ( $input['id'] ?? 'unknown' );
        
        if ( is_wp_error( $result ) ) {
            $this->logger->log( 'import_manager', 'error', "Import failed for {$input_id}: " . $result->get_error_message() );
        } else {
            $method = $result['method'] ?? 'unknown';
            $product_id = $result['product_id'] ?? 'unknown';
            $action = $result['created'] ? 'created' : 'updated';
            $this->logger->log( 'import_manager', 'info', "Import successful for {$input_id}: {$action} product {$product_id} using {$method} method" );
        }
    }

    /**
     * Update import preferences
     *
     * @param array $new_preferences New preferences
     * @return bool Success status
     */
    public function update_preferences( $new_preferences ) {
        // Store old preferences for comparison
        $old_preferences = $this->preferences;

        // Merge new preferences
        $this->preferences = array_merge( $this->preferences, $new_preferences );

        // Always try to update the option, even if values are the same
        // This ensures the option exists in the database
        $result = update_option( 'squarekit_import_preferences', $this->preferences );

        // If update_option returns false, check if it's because values are the same
        if ( ! $result ) {
            // Get the current option value from database
            $stored_preferences = get_option( 'squarekit_import_preferences', array() );

            // If stored preferences match our current preferences, consider it a success
            if ( $stored_preferences === $this->preferences ) {
                return true;
            }

            // If they don't match, try add_option in case the option doesn't exist
            $result = add_option( 'squarekit_import_preferences', $this->preferences );
        }

        return $result;
    }

    /**
     * Get current import preferences
     *
     * @return array Current preferences
     */
    public function get_preferences() {
        return $this->preferences;
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_statistics() {
        return array(
            'bridge_stats' => $this->import_bridge->get_import_stats(),
            'swever_available' => $this->import_bridge->is_swever_available(),
            'preferences' => $this->preferences
        );
    }

    /**
     * Test import methods availability
     *
     * @return array Test results
     */
    public function test_import_methods() {
        return array(
            'legacy_available' => class_exists( 'SquareKit_WooCommerce' ),
            'swever_available' => $this->import_bridge->is_swever_available(),
            'bridge_available' => class_exists( 'SquareKit_Import_Bridge' ),
            'recommended_method' => $this->determine_import_method( 'test_id' )
        );
    }
}
