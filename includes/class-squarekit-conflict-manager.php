<?php
/**
 * Conflict Manager Class
 *
 * Handles detection, storage, and resolution of sync conflicts
 * between Square and WooCommerce data.
 *
 * @package SquareKit
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Conflict Manager
 */
class SquareKit_Conflict_Manager {

    /**
     * Database table name for conflicts
     *
     * @var string
     */
    private $table_name;

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'squarekit_conflicts';
        
        // Create table if it doesn't exist
        $this->maybe_create_table();
    }

    /**
     * Create conflicts table if it doesn't exist
     */
    private function maybe_create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            conflict_type varchar(50) NOT NULL,
            object_id bigint(20) NOT NULL,
            object_type varchar(50) NOT NULL,
            square_value longtext,
            woocommerce_value longtext,
            square_last_modified datetime,
            woocommerce_last_modified datetime,
            detected_at datetime DEFAULT CURRENT_TIMESTAMP,
            status varchar(20) DEFAULT 'pending',
            resolved_at datetime NULL,
            resolved_by bigint(20) NULL,
            resolution varchar(20) NULL,
            notes text NULL,
            PRIMARY KEY (id),
            KEY conflict_type (conflict_type),
            KEY object_id (object_id),
            KEY status (status),
            KEY detected_at (detected_at)
        ) $charset_collate;";
        
        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Detect and log a conflict
     *
     * @param string $conflict_type Type of conflict (inventory, product, etc.)
     * @param int $object_id ID of the object (product ID, etc.)
     * @param string $object_type Type of object (product, order, etc.)
     * @param mixed $square_value Value from Square
     * @param mixed $woocommerce_value Value from WooCommerce
     * @param string $square_last_modified Square last modified timestamp
     * @param string $woocommerce_last_modified WooCommerce last modified timestamp
     * @return int|false Conflict ID on success, false on failure
     */
    public function detect_conflict( $conflict_type, $object_id, $object_type, $square_value, $woocommerce_value, $square_last_modified = null, $woocommerce_last_modified = null ) {
        global $wpdb;
        
        // Check if conflict already exists for this object and type
        $existing = $wpdb->get_var( $wpdb->prepare(
            "SELECT id FROM {$this->table_name} 
             WHERE conflict_type = %s 
             AND object_id = %d 
             AND object_type = %s 
             AND status = 'pending'",
            $conflict_type,
            $object_id,
            $object_type
        ) );
        
        if ( $existing ) {
            // Update existing conflict
            $result = $wpdb->update(
                $this->table_name,
                array(
                    'square_value' => maybe_serialize( $square_value ),
                    'woocommerce_value' => maybe_serialize( $woocommerce_value ),
                    'square_last_modified' => $square_last_modified,
                    'woocommerce_last_modified' => $woocommerce_last_modified,
                    'detected_at' => current_time( 'mysql' )
                ),
                array( 'id' => $existing ),
                array( '%s', '%s', '%s', '%s', '%s' ),
                array( '%d' )
            );
            
            return $result !== false ? $existing : false;
        }
        
        // Create new conflict
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'conflict_type' => $conflict_type,
                'object_id' => $object_id,
                'object_type' => $object_type,
                'square_value' => maybe_serialize( $square_value ),
                'woocommerce_value' => maybe_serialize( $woocommerce_value ),
                'square_last_modified' => $square_last_modified,
                'woocommerce_last_modified' => $woocommerce_last_modified,
                'detected_at' => current_time( 'mysql' ),
                'status' => 'pending'
            ),
            array( '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s' )
        );
        
        if ( $result ) {
            $conflict_id = $wpdb->insert_id;
            
            // Log the conflict
            if ( class_exists( 'SquareKit_Logger' ) ) {
                $logger = new SquareKit_Logger();
                $logger->log( 'conflict', 'warning', "Conflict detected: {$conflict_type} for {$object_type} #{$object_id}", array(
                    'conflict_id' => $conflict_id,
                    'square_value' => $square_value,
                    'woocommerce_value' => $woocommerce_value
                ) );
            }
            
            // Trigger notification
            $this->trigger_conflict_notification( $conflict_id );
            
            return $conflict_id;
        }
        
        return false;
    }

    /**
     * Get pending conflicts
     *
     * @param array $args Query arguments
     * @return array Array of conflict objects
     */
    public function get_pending_conflicts( $args = array() ) {
        global $wpdb;
        
        $defaults = array(
            'limit' => 50,
            'offset' => 0,
            'conflict_type' => '',
            'object_type' => '',
            'order_by' => 'detected_at',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args( $args, $defaults );
        
        $where_clauses = array( "status = 'pending'" );
        $where_values = array();
        
        if ( ! empty( $args['conflict_type'] ) ) {
            $where_clauses[] = "conflict_type = %s";
            $where_values[] = $args['conflict_type'];
        }
        
        if ( ! empty( $args['object_type'] ) ) {
            $where_clauses[] = "object_type = %s";
            $where_values[] = $args['object_type'];
        }
        
        $where_sql = implode( ' AND ', $where_clauses );
        $order_sql = sprintf( 'ORDER BY %s %s', esc_sql( $args['order_by'] ), esc_sql( $args['order'] ) );
        $limit_sql = sprintf( 'LIMIT %d OFFSET %d', intval( $args['limit'] ), intval( $args['offset'] ) );
        
        $sql = "SELECT * FROM {$this->table_name} WHERE {$where_sql} {$order_sql} {$limit_sql}";
        
        if ( ! empty( $where_values ) ) {
            $sql = $wpdb->prepare( $sql, $where_values );
        }
        
        $results = $wpdb->get_results( $sql );
        
        // Unserialize values
        foreach ( $results as $conflict ) {
            $conflict->square_value = maybe_unserialize( $conflict->square_value );
            $conflict->woocommerce_value = maybe_unserialize( $conflict->woocommerce_value );
        }
        
        return $results;
    }

    /**
     * Get conflict count
     *
     * @param string $status Conflict status (pending, resolved, etc.)
     * @return int Number of conflicts
     */
    public function get_conflict_count( $status = 'pending' ) {
        global $wpdb;
        
        return (int) $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE status = %s",
            $status
        ) );
    }

    /**
     * Resolve a conflict
     *
     * @param int $conflict_id Conflict ID
     * @param string $resolution Resolution choice ('square_wins', 'woocommerce_wins', 'custom')
     * @param mixed $custom_value Custom resolution value (if resolution is 'custom')
     * @param string $notes Optional resolution notes
     * @return bool Success status
     */
    public function resolve_conflict( $conflict_id, $resolution, $custom_value = null, $notes = '' ) {
        global $wpdb;
        
        // Get conflict details
        $conflict = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE id = %d",
            $conflict_id
        ) );
        
        if ( ! $conflict ) {
            return false;
        }
        
        // Determine final value based on resolution
        $final_value = null;
        switch ( $resolution ) {
            case 'square_wins':
                $final_value = maybe_unserialize( $conflict->square_value );
                break;
            case 'woocommerce_wins':
                $final_value = maybe_unserialize( $conflict->woocommerce_value );
                break;
            case 'custom':
                $final_value = $custom_value;
                break;
        }
        
        // Apply the resolution
        $applied = $this->apply_resolution( $conflict, $resolution, $final_value );
        
        if ( $applied ) {
            // Mark conflict as resolved
            $result = $wpdb->update(
                $this->table_name,
                array(
                    'status' => 'resolved',
                    'resolved_at' => current_time( 'mysql' ),
                    'resolved_by' => get_current_user_id(),
                    'resolution' => $resolution,
                    'notes' => $notes
                ),
                array( 'id' => $conflict_id ),
                array( '%s', '%s', '%d', '%s', '%s' ),
                array( '%d' )
            );
            
            // Log the resolution
            if ( class_exists( 'SquareKit_Logger' ) ) {
                $logger = new SquareKit_Logger();
                $logger->log( 'conflict', 'info', "Conflict #{$conflict_id} resolved with '{$resolution}'", array(
                    'conflict_id' => $conflict_id,
                    'resolution' => $resolution,
                    'final_value' => $final_value,
                    'notes' => $notes
                ) );
            }
            
            return $result !== false;
        }
        
        return false;
    }

    /**
     * Apply conflict resolution to the actual data
     *
     * @param object $conflict Conflict object
     * @param string $resolution Resolution type
     * @param mixed $final_value Final value to apply
     * @return bool Success status
     */
    private function apply_resolution( $conflict, $resolution, $final_value ) {
        // This method will be implemented based on conflict type
        switch ( $conflict->conflict_type ) {
            case 'inventory':
                return $this->apply_inventory_resolution( $conflict, $final_value );
            case 'product':
                return $this->apply_product_resolution( $conflict, $final_value );
            default:
                return false;
        }
    }

    /**
     * Apply inventory conflict resolution
     *
     * @param object $conflict Conflict object
     * @param mixed $final_value Final inventory value
     * @return bool Success status
     */
    private function apply_inventory_resolution( $conflict, $final_value ) {
        // Update WooCommerce inventory
        if ( $conflict->object_type === 'product' ) {
            $product = wc_get_product( $conflict->object_id );
            if ( $product ) {
                $product->set_stock_quantity( $final_value );
                $product->save();
                return true;
            }
        }
        
        return false;
    }

    /**
     * Apply product conflict resolution
     *
     * @param object $conflict Conflict object
     * @param mixed $final_value Final product value
     * @return bool Success status
     */
    private function apply_product_resolution( $conflict, $final_value ) {
        // Implementation depends on what product field is in conflict
        // This would be expanded based on specific product conflicts
        return true;
    }

    /**
     * Trigger conflict notification
     *
     * @param int $conflict_id Conflict ID
     */
    private function trigger_conflict_notification( $conflict_id ) {
        // Set a transient to show admin notice
        set_transient( 'squarekit_new_conflict_' . $conflict_id, true, DAY_IN_SECONDS );
        
        // Could also send email notifications here if configured
    }

    /**
     * Get conflict by ID
     *
     * @param int $conflict_id Conflict ID
     * @return object|null Conflict object or null
     */
    public function get_conflict( $conflict_id ) {
        global $wpdb;
        
        $conflict = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE id = %d",
            $conflict_id
        ) );
        
        if ( $conflict ) {
            $conflict->square_value = maybe_unserialize( $conflict->square_value );
            $conflict->woocommerce_value = maybe_unserialize( $conflict->woocommerce_value );
        }
        
        return $conflict;
    }

    /**
     * Delete old resolved conflicts
     *
     * @param int $days_old Number of days old to delete
     * @return int Number of conflicts deleted
     */
    public function cleanup_old_conflicts( $days_old = 30 ) {
        global $wpdb;
        
        $cutoff_date = date( 'Y-m-d H:i:s', strtotime( "-{$days_old} days" ) );
        
        $deleted = $wpdb->query( $wpdb->prepare(
            "DELETE FROM {$this->table_name} 
             WHERE status = 'resolved' 
             AND resolved_at < %s",
            $cutoff_date
        ) );
        
        return $deleted;
    }

    /**
     * Get resolved conflicts
     *
     * @param array $args Query arguments
     * @return array Array of resolved conflict objects
     */
    public function get_resolved_conflicts( $args = array() ) {
        global $wpdb;

        $defaults = array(
            'limit' => 50,
            'offset' => 0,
            'order_by' => 'resolved_at',
            'order' => 'DESC'
        );

        $args = wp_parse_args( $args, $defaults );

        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name}
             WHERE status = 'resolved'
             ORDER BY {$args['order_by']} {$args['order']}
             LIMIT %d OFFSET %d",
            $args['limit'],
            $args['offset']
        );

        return $wpdb->get_results( $sql );
    }

    /**
     * Get all conflicts (pending and resolved)
     *
     * @param array $args Query arguments
     * @return array Array of all conflict objects
     */
    public function get_all_conflicts( $args = array() ) {
        global $wpdb;

        $defaults = array(
            'limit' => 100,
            'offset' => 0,
            'order_by' => 'created_at',
            'order' => 'DESC'
        );

        $args = wp_parse_args( $args, $defaults );

        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name}
             ORDER BY {$args['order_by']} {$args['order']}
             LIMIT %d OFFSET %d",
            $args['limit'],
            $args['offset']
        );

        return $wpdb->get_results( $sql );
    }

    /**
     * Clear all conflicts
     *
     * @return int Number of conflicts cleared
     */
    public function clear_all_conflicts() {
        global $wpdb;

        $count = $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name}" );

        $wpdb->query( "DELETE FROM {$this->table_name}" );

        return (int) $count;
    }

    /**
     * Get table name
     *
     * @return string Full table name with prefix
     */
    public function get_table_name() {
        return $this->table_name;
    }

    /**
     * Ignore a conflict
     *
     * @param int $conflict_id Conflict ID
     * @return bool Success status
     */
    public function ignore_conflict( $conflict_id ) {
        global $wpdb;

        $result = $wpdb->update(
            $this->table_name,
            array(
                'status' => 'ignored',
                'resolved_at' => current_time( 'mysql' ),
                'resolution' => 'ignored_by_user'
            ),
            array( 'id' => $conflict_id ),
            array( '%s', '%s', '%s' ),
            array( '%d' )
        );

        return $result !== false;
    }

    /**
     * Get conflicts by type
     *
     * @param string $type Conflict type
     * @param string $status Conflict status (optional)
     * @return array Array of conflict objects
     */
    public function get_conflicts_by_type( $type, $status = null ) {
        global $wpdb;

        if ( $status ) {
            $sql = $wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE type = %s AND status = %s ORDER BY created_at DESC",
                $type,
                $status
            );
        } else {
            $sql = $wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE type = %s ORDER BY created_at DESC",
                $type
            );
        }

        return $wpdb->get_results( $sql );
    }

    /**
     * Get conflict statistics
     *
     * @return array Statistics array
     */
    public function get_statistics() {
        global $wpdb;

        $stats = array();

        // Total conflicts
        $stats['total'] = (int) $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name}" );

        // By status
        $stats['pending'] = (int) $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'pending'" );
        $stats['resolved'] = (int) $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'resolved'" );
        $stats['ignored'] = (int) $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE status = 'ignored'" );

        // By type
        $type_stats = $wpdb->get_results(
            "SELECT type, COUNT(*) as count FROM {$this->table_name} GROUP BY type"
        );

        $stats['by_type'] = array();
        foreach ( $type_stats as $type_stat ) {
            $stats['by_type'][ $type_stat->type ] = (int) $type_stat->count;
        }

        return $stats;
    }
}
