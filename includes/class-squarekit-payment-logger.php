<?php
/**
 * SquareKit Payment Logger
 * Advanced logging system for payment transactions and compliance
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Payment Logger Class
 * 
 * Provides comprehensive logging for payment transactions, security events,
 * compliance auditing, and production monitoring.
 */
class SquareKit_Payment_Logger {

    /**
     * Database table name for payment logs
     * @var string
     */
    private $table_name;

    /**
     * Log levels
     * @var array
     */
    private $log_levels = array(
        'emergency' => 0,
        'alert'     => 1,
        'critical'  => 2,
        'error'     => 3,
        'warning'   => 4,
        'notice'    => 5,
        'info'      => 6,
        'debug'     => 7
    );

    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'squarekit_payment_logs';
        $this->maybe_create_table();
    }

    /**
     * Create payment logs table if it doesn't exist
     */
    private function maybe_create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            log_level varchar(20) NOT NULL,
            event_type varchar(50) NOT NULL,
            message text NOT NULL,
            context longtext,
            order_id bigint(20) NULL,
            user_id bigint(20) NULL,
            payment_id varchar(255) NULL,
            transaction_id varchar(255) NULL,
            amount decimal(10,2) NULL,
            currency varchar(3) NULL,
            payment_method varchar(50) NULL,
            gateway_response longtext NULL,
            ip_address varchar(45) NULL,
            user_agent text NULL,
            request_data longtext NULL,
            response_data longtext NULL,
            execution_time float NULL,
            memory_usage bigint(20) NULL,
            stack_trace longtext NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY log_level (log_level),
            KEY event_type (event_type),
            KEY order_id (order_id),
            KEY user_id (user_id),
            KEY payment_id (payment_id),
            KEY created_at (created_at),
            KEY ip_address (ip_address)
        ) $charset_collate;";
        
        require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
        dbDelta( $sql );
    }

    /**
     * Log payment event
     *
     * @param string $level Log level
     * @param string $event_type Event type
     * @param string $message Log message
     * @param array $context Additional context data
     * @return bool Success status
     */
    public function log( $level, $event_type, $message, $context = array() ) {
        global $wpdb;

        // Validate log level
        if ( ! isset( $this->log_levels[ $level ] ) ) {
            $level = 'info';
        }

        // Extract common fields from context
        $order_id = $context['order_id'] ?? null;
        $user_id = $context['user_id'] ?? get_current_user_id();
        $payment_id = $context['payment_id'] ?? null;
        $transaction_id = $context['transaction_id'] ?? null;
        $amount = $context['amount'] ?? null;
        $currency = $context['currency'] ?? null;
        $payment_method = $context['payment_method'] ?? null;
        $gateway_response = isset( $context['gateway_response'] ) ? wp_json_encode( $context['gateway_response'] ) : null;
        $request_data = isset( $context['request_data'] ) ? wp_json_encode( $context['request_data'] ) : null;
        $response_data = isset( $context['response_data'] ) ? wp_json_encode( $context['response_data'] ) : null;
        $execution_time = $context['execution_time'] ?? null;
        $memory_usage = $context['memory_usage'] ?? memory_get_usage( true );
        $stack_trace = $context['stack_trace'] ?? null;

        // Get request information
        $ip_address = $this->get_client_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // Prepare context data (remove extracted fields to avoid duplication)
        $context_data = $context;
        unset( $context_data['order_id'], $context_data['user_id'], $context_data['payment_id'], 
               $context_data['transaction_id'], $context_data['amount'], $context_data['currency'],
               $context_data['payment_method'], $context_data['gateway_response'], $context_data['request_data'],
               $context_data['response_data'], $context_data['execution_time'], $context_data['memory_usage'],
               $context_data['stack_trace'] );

        $result = $wpdb->insert(
            $this->table_name,
            array(
                'log_level' => $level,
                'event_type' => $event_type,
                'message' => $message,
                'context' => wp_json_encode( $context_data ),
                'order_id' => $order_id,
                'user_id' => $user_id,
                'payment_id' => $payment_id,
                'transaction_id' => $transaction_id,
                'amount' => $amount,
                'currency' => $currency,
                'payment_method' => $payment_method,
                'gateway_response' => $gateway_response,
                'ip_address' => $ip_address,
                'user_agent' => substr( $user_agent, 0, 500 ), // Truncate long user agents
                'request_data' => $request_data,
                'response_data' => $response_data,
                'execution_time' => $execution_time,
                'memory_usage' => $memory_usage,
                'stack_trace' => $stack_trace,
                'created_at' => current_time( 'mysql' )
            ),
            array(
                '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s', '%f', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%d', '%s', '%s'
            )
        );

        // Also log to WordPress error log for critical events
        if ( in_array( $level, array( 'emergency', 'alert', 'critical', 'error' ), true ) ) {
            error_log( sprintf( 
                'SquareKit Payment [%s] %s: %s - Context: %s', 
                strtoupper( $level ), 
                $event_type, 
                $message, 
                wp_json_encode( $context )
            ) );
        }

        return $result !== false;
    }

    /**
     * Log payment transaction start
     *
     * @param int $order_id Order ID
     * @param array $payment_data Payment data
     * @return string Transaction ID for tracking
     */
    public function log_transaction_start( $order_id, $payment_data ) {
        $transaction_id = wp_generate_uuid4();
        
        $this->log( 'info', 'transaction_start', 'Payment transaction initiated', array(
            'order_id' => $order_id,
            'transaction_id' => $transaction_id,
            'payment_method' => $payment_data['method'] ?? 'unknown',
            'amount' => $payment_data['amount'] ?? null,
            'currency' => $payment_data['currency'] ?? null,
            'using_saved_method' => $payment_data['using_saved_method'] ?? false,
            'request_data' => $payment_data
        ) );

        return $transaction_id;
    }

    /**
     * Log payment transaction success
     *
     * @param string $transaction_id Transaction ID
     * @param array $payment_result Payment result
     * @param float $execution_time Execution time in seconds
     */
    public function log_transaction_success( $transaction_id, $payment_result, $execution_time = null ) {
        $payment = $payment_result['payment'] ?? array();
        
        $this->log( 'info', 'transaction_success', 'Payment transaction completed successfully', array(
            'transaction_id' => $transaction_id,
            'payment_id' => $payment['id'] ?? null,
            'order_id' => $payment['reference_id'] ?? null,
            'amount' => isset( $payment['amount_money'] ) ? $payment['amount_money']['amount'] / 100 : null,
            'currency' => $payment['amount_money']['currency'] ?? null,
            'payment_method' => $this->extract_payment_method( $payment ),
            'gateway_response' => $payment,
            'execution_time' => $execution_time
        ) );
    }

    /**
     * Log payment transaction failure
     *
     * @param string $transaction_id Transaction ID
     * @param string $error_message Error message
     * @param array $error_context Error context
     * @param float $execution_time Execution time in seconds
     */
    public function log_transaction_failure( $transaction_id, $error_message, $error_context = array(), $execution_time = null ) {
        $this->log( 'error', 'transaction_failure', $error_message, array_merge( $error_context, array(
            'transaction_id' => $transaction_id,
            'execution_time' => $execution_time,
            'stack_trace' => wp_debug_backtrace_summary()
        ) ) );
    }

    /**
     * Log security event
     *
     * @param string $event_type Security event type
     * @param string $message Event message
     * @param array $context Event context
     */
    public function log_security_event( $event_type, $message, $context = array() ) {
        $this->log( 'warning', 'security_' . $event_type, $message, array_merge( $context, array(
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? ''
        ) ) );
    }

    /**
     * Log compliance event
     *
     * @param string $event_type Compliance event type
     * @param string $message Event message
     * @param array $context Event context
     */
    public function log_compliance_event( $event_type, $message, $context = array() ) {
        $this->log( 'notice', 'compliance_' . $event_type, $message, $context );
    }

    /**
     * Get client IP address
     *
     * @return string
     */
    private function get_client_ip() {
        $ip_headers = array(
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ( $ip_headers as $header ) {
            if ( ! empty( $_SERVER[ $header ] ) ) {
                $ip = $_SERVER[ $header ];
                if ( strpos( $ip, ',' ) !== false ) {
                    $ip = trim( explode( ',', $ip )[0] );
                }
                if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Extract payment method from Square payment response
     *
     * @param array $payment Square payment data
     * @return string
     */
    private function extract_payment_method( $payment ) {
        if ( isset( $payment['source_details']['card'] ) ) {
            return 'card';
        } elseif ( isset( $payment['source_details']['digital_wallet'] ) ) {
            $wallet_type = $payment['source_details']['digital_wallet']['type'] ?? 'digital_wallet';
            return strtolower( str_replace( '_', '', $wallet_type ) );
        }
        
        return 'unknown';
    }

    /**
     * Get payment logs with filtering
     *
     * @param array $args Query arguments
     * @return array
     */
    public function get_logs( $args = array() ) {
        global $wpdb;

        $defaults = array(
            'limit' => 50,
            'offset' => 0,
            'log_level' => '',
            'event_type' => '',
            'order_id' => '',
            'user_id' => '',
            'payment_id' => '',
            'date_from' => '',
            'date_to' => '',
            'order_by' => 'created_at',
            'order' => 'DESC'
        );

        $args = wp_parse_args( $args, $defaults );

        $where_clauses = array( '1=1' );
        $where_values = array();

        if ( ! empty( $args['log_level'] ) ) {
            $where_clauses[] = 'log_level = %s';
            $where_values[] = $args['log_level'];
        }

        if ( ! empty( $args['event_type'] ) ) {
            $where_clauses[] = 'event_type LIKE %s';
            $where_values[] = '%' . $args['event_type'] . '%';
        }

        if ( ! empty( $args['order_id'] ) ) {
            $where_clauses[] = 'order_id = %d';
            $where_values[] = $args['order_id'];
        }

        if ( ! empty( $args['user_id'] ) ) {
            $where_clauses[] = 'user_id = %d';
            $where_values[] = $args['user_id'];
        }

        if ( ! empty( $args['payment_id'] ) ) {
            $where_clauses[] = 'payment_id = %s';
            $where_values[] = $args['payment_id'];
        }

        if ( ! empty( $args['date_from'] ) ) {
            $where_clauses[] = 'created_at >= %s';
            $where_values[] = $args['date_from'];
        }

        if ( ! empty( $args['date_to'] ) ) {
            $where_clauses[] = 'created_at <= %s';
            $where_values[] = $args['date_to'];
        }

        $where_sql = implode( ' AND ', $where_clauses );
        $order_sql = sprintf( 'ORDER BY %s %s', esc_sql( $args['order_by'] ), esc_sql( $args['order'] ) );
        $limit_sql = sprintf( 'LIMIT %d OFFSET %d', intval( $args['limit'] ), intval( $args['offset'] ) );

        $sql = "SELECT * FROM {$this->table_name} WHERE {$where_sql} {$order_sql} {$limit_sql}";

        if ( ! empty( $where_values ) ) {
            $sql = $wpdb->prepare( $sql, $where_values );
        }

        return $wpdb->get_results( $sql );
    }

    /**
     * Get payment analytics
     *
     * @param array $args Query arguments
     * @return array
     */
    public function get_analytics( $args = array() ) {
        global $wpdb;

        $defaults = array(
            'date_from' => date( 'Y-m-d', strtotime( '-30 days' ) ),
            'date_to' => date( 'Y-m-d' ),
            'group_by' => 'day' // day, hour, month
        );

        $args = wp_parse_args( $args, $defaults );

        $analytics = array();

        // Transaction success rate
        $success_rate = $wpdb->get_row( $wpdb->prepare(
            "SELECT
                COUNT(CASE WHEN event_type = 'transaction_success' THEN 1 END) as successful,
                COUNT(CASE WHEN event_type = 'transaction_failure' THEN 1 END) as failed,
                COUNT(*) as total
             FROM {$this->table_name}
             WHERE event_type IN ('transaction_success', 'transaction_failure')
             AND created_at BETWEEN %s AND %s",
            $args['date_from'] . ' 00:00:00',
            $args['date_to'] . ' 23:59:59'
        ) );

        $analytics['success_rate'] = array(
            'successful' => intval( $success_rate->successful ?? 0 ),
            'failed' => intval( $success_rate->failed ?? 0 ),
            'total' => intval( $success_rate->total ?? 0 ),
            'percentage' => $success_rate->total > 0 ? round( ( $success_rate->successful / $success_rate->total ) * 100, 2 ) : 0
        );

        // Payment methods breakdown
        $payment_methods = $wpdb->get_results( $wpdb->prepare(
            "SELECT payment_method, COUNT(*) as count, SUM(amount) as total_amount
             FROM {$this->table_name}
             WHERE event_type = 'transaction_success'
             AND created_at BETWEEN %s AND %s
             AND payment_method IS NOT NULL
             GROUP BY payment_method
             ORDER BY count DESC",
            $args['date_from'] . ' 00:00:00',
            $args['date_to'] . ' 23:59:59'
        ) );

        $analytics['payment_methods'] = array();
        foreach ( $payment_methods as $method ) {
            $analytics['payment_methods'][ $method->payment_method ] = array(
                'count' => intval( $method->count ),
                'total_amount' => floatval( $method->total_amount )
            );
        }

        // Error patterns
        $error_patterns = $wpdb->get_results( $wpdb->prepare(
            "SELECT event_type, COUNT(*) as count
             FROM {$this->table_name}
             WHERE log_level IN ('error', 'critical', 'alert', 'emergency')
             AND created_at BETWEEN %s AND %s
             GROUP BY event_type
             ORDER BY count DESC
             LIMIT 10",
            $args['date_from'] . ' 00:00:00',
            $args['date_to'] . ' 23:59:59'
        ) );

        $analytics['error_patterns'] = array();
        foreach ( $error_patterns as $pattern ) {
            $analytics['error_patterns'][ $pattern->event_type ] = intval( $pattern->count );
        }

        // Performance metrics
        $performance = $wpdb->get_row( $wpdb->prepare(
            "SELECT
                AVG(execution_time) as avg_execution_time,
                MAX(execution_time) as max_execution_time,
                MIN(execution_time) as min_execution_time,
                AVG(memory_usage) as avg_memory_usage,
                MAX(memory_usage) as max_memory_usage
             FROM {$this->table_name}
             WHERE execution_time IS NOT NULL
             AND created_at BETWEEN %s AND %s",
            $args['date_from'] . ' 00:00:00',
            $args['date_to'] . ' 23:59:59'
        ) );

        $analytics['performance'] = array(
            'avg_execution_time' => floatval( $performance->avg_execution_time ?? 0 ),
            'max_execution_time' => floatval( $performance->max_execution_time ?? 0 ),
            'min_execution_time' => floatval( $performance->min_execution_time ?? 0 ),
            'avg_memory_usage' => intval( $performance->avg_memory_usage ?? 0 ),
            'max_memory_usage' => intval( $performance->max_memory_usage ?? 0 )
        );

        return $analytics;
    }

    /**
     * Get security events summary
     *
     * @param int $days Number of days to look back
     * @return array
     */
    public function get_security_summary( $days = 7 ) {
        global $wpdb;

        $date_from = date( 'Y-m-d H:i:s', strtotime( "-{$days} days" ) );

        $security_events = $wpdb->get_results( $wpdb->prepare(
            "SELECT event_type, COUNT(*) as count, ip_address
             FROM {$this->table_name}
             WHERE event_type LIKE 'security_%'
             AND created_at >= %s
             GROUP BY event_type, ip_address
             ORDER BY count DESC",
            $date_from
        ) );

        $summary = array(
            'total_events' => 0,
            'unique_ips' => array(),
            'event_types' => array()
        );

        foreach ( $security_events as $event ) {
            $summary['total_events'] += intval( $event->count );
            $summary['unique_ips'][ $event->ip_address ] = ( $summary['unique_ips'][ $event->ip_address ] ?? 0 ) + intval( $event->count );
            $summary['event_types'][ $event->event_type ] = ( $summary['event_types'][ $event->event_type ] ?? 0 ) + intval( $event->count );
        }

        // Sort by frequency
        arsort( $summary['unique_ips'] );
        arsort( $summary['event_types'] );

        return $summary;
    }

    /**
     * Clean up old logs
     *
     * @param int $days_to_keep Number of days to keep logs
     * @return int Number of logs deleted
     */
    public function cleanup_old_logs( $days_to_keep = 90 ) {
        global $wpdb;

        $cutoff_date = date( 'Y-m-d H:i:s', strtotime( "-{$days_to_keep} days" ) );

        $deleted = $wpdb->query( $wpdb->prepare(
            "DELETE FROM {$this->table_name} WHERE created_at < %s",
            $cutoff_date
        ) );

        if ( $deleted > 0 ) {
            $this->log( 'info', 'log_cleanup', "Cleaned up {$deleted} old payment logs", array(
                'cutoff_date' => $cutoff_date,
                'days_kept' => $days_to_keep
            ) );
        }

        return intval( $deleted );
    }

    /**
     * Export logs to CSV
     *
     * @param array $args Query arguments
     * @return string CSV content
     */
    public function export_logs_csv( $args = array() ) {
        $logs = $this->get_logs( array_merge( $args, array( 'limit' => 10000 ) ) );

        $csv_data = array();
        $csv_data[] = array(
            'ID', 'Date/Time', 'Level', 'Event Type', 'Message', 'Order ID',
            'User ID', 'Payment ID', 'Amount', 'Currency', 'Payment Method',
            'IP Address', 'Execution Time'
        );

        foreach ( $logs as $log ) {
            $csv_data[] = array(
                $log->id,
                $log->created_at,
                $log->log_level,
                $log->event_type,
                $log->message,
                $log->order_id,
                $log->user_id,
                $log->payment_id,
                $log->amount,
                $log->currency,
                $log->payment_method,
                $log->ip_address,
                $log->execution_time
            );
        }

        $output = fopen( 'php://temp', 'w' );
        foreach ( $csv_data as $row ) {
            fputcsv( $output, $row );
        }
        rewind( $output );
        $csv_content = stream_get_contents( $output );
        fclose( $output );

        return $csv_content;
    }
}
