<?php
/**
 * SquareKit Secure Storage Class
 *
 * Handles secure encryption and storage of sensitive OAuth tokens and secrets
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Secure Storage Class
 *
 * Provides AES-256-GCM encryption for OAuth tokens and sensitive data
 */
class SquareKit_Secure_Storage {

    /**
     * Option name for encrypted tokens
     */
    const OPTION_NAME = 'squarekit_secure_tokens';

    /**
     * Option name for site encryption key
     */
    const KEY_OPTION = 'squarekit_encryption_key';

    /**
     * Get or generate site-specific encryption key
     *
     * @return string 32-byte encryption key
     */
    private static function get_encryption_key() {
        $key = get_option( self::KEY_OPTION );
        
        if ( ! $key ) {
            // Generate a secure 32-byte key
            $key = wp_generate_password( 64, true, true );
            add_option( self::KEY_OPTION, $key, '', 'no' ); // Don't autoload
        }
        
        return $key;
    }

    /**
     * Encrypt data using AES-256-GCM
     *
     * @param string $data Data to encrypt
     * @return string Base64 encoded encrypted data
     */
    private static function encrypt( $data ) {
        if ( empty( $data ) ) {
            return '';
        }

        $key = self::get_encryption_key();
        $iv = random_bytes( 12 ); // 96-bit IV for GCM
        $tag = '';

        $encrypted = openssl_encrypt( 
            $data, 
            'aes-256-gcm', 
            $key, 
            OPENSSL_RAW_DATA, 
            $iv, 
            $tag 
        );

        if ( $encrypted === false ) {
            error_log( 'SquareKit: Encryption failed' );
            return '';
        }

        // Combine IV + tag + encrypted data and base64 encode
        return base64_encode( $iv . $tag . $encrypted );
    }

    /**
     * Decrypt data using AES-256-GCM
     *
     * @param string $encrypted_data Base64 encoded encrypted data
     * @return string|false Decrypted data or false on failure
     */
    private static function decrypt( $encrypted_data ) {
        if ( empty( $encrypted_data ) ) {
            return false;
        }

        $raw = base64_decode( $encrypted_data, true );
        if ( ! $raw || strlen( $raw ) < 29 ) { // 12 (IV) + 16 (tag) + 1 (min data)
            return false;
        }

        $iv = substr( $raw, 0, 12 );
        $tag = substr( $raw, 12, 16 );
        $encrypted = substr( $raw, 28 );
        $key = self::get_encryption_key();

        $decrypted = openssl_decrypt( 
            $encrypted, 
            'aes-256-gcm', 
            $key, 
            OPENSSL_RAW_DATA, 
            $iv, 
            $tag 
        );

        if ( $decrypted === false ) {
            error_log( 'SquareKit: Decryption failed' );
            return false;
        }

        return $decrypted;
    }

    /**
     * Store OAuth tokens securely
     *
     * @param string $access_token OAuth access token
     * @param string $refresh_token OAuth refresh token
     * @param string $environment Environment (sandbox/production)
     * @return bool Success status
     */
    public static function store_oauth_tokens( $access_token, $refresh_token, $environment ) {
        $tokens = get_option( self::OPTION_NAME, array() );

        $tokens[ $environment ] = array(
            'access_token' => self::encrypt( $access_token ),
            'refresh_token' => self::encrypt( $refresh_token ),
            'stored_at' => time(),
        );

        return update_option( self::OPTION_NAME, $tokens );
    }

    /**
     * Get OAuth access token
     *
     * @param string $environment Environment (sandbox/production)
     * @return string|false Access token or false if not found
     */
    public static function get_access_token( $environment ) {
        $tokens = get_option( self::OPTION_NAME, array() );

        if ( ! isset( $tokens[ $environment ]['access_token'] ) ) {
            return false;
        }

        return self::decrypt( $tokens[ $environment ]['access_token'] );
    }

    /**
     * Get OAuth refresh token
     *
     * @param string $environment Environment (sandbox/production)
     * @return string|false Refresh token or false if not found
     */
    public static function get_refresh_token( $environment ) {
        $tokens = get_option( self::OPTION_NAME, array() );

        if ( ! isset( $tokens[ $environment ]['refresh_token'] ) ) {
            return false;
        }

        return self::decrypt( $tokens[ $environment ]['refresh_token'] );
    }

    /**
     * Check if OAuth tokens exist for environment
     *
     * @param string $environment Environment (sandbox/production)
     * @return bool True if tokens exist
     */
    public static function has_tokens( $environment ) {
        $tokens = get_option( self::OPTION_NAME, array() );
        return isset( $tokens[ $environment ]['access_token'] ) && 
               isset( $tokens[ $environment ]['refresh_token'] );
    }

    /**
     * Clear OAuth tokens for environment
     *
     * @param string $environment Environment (sandbox/production)
     * @return bool Success status
     */
    public static function clear_tokens( $environment ) {
        $tokens = get_option( self::OPTION_NAME, array() );
        
        if ( isset( $tokens[ $environment ] ) ) {
            unset( $tokens[ $environment ] );
            return update_option( self::OPTION_NAME, $tokens );
        }
        
        return true;
    }

    /**
     * Clear all stored tokens
     *
     * @return bool Success status
     */
    public static function clear_all_tokens() {
        return delete_option( self::OPTION_NAME );
    }

    /**
     * Get client credentials from constants or environment variables
     *
     * @param string $environment Environment (sandbox/production)
     * @return array|false Array with client_id and client_secret or false
     */
    public static function get_client_credentials( $environment ) {
        $env_prefix = strtoupper( $environment );
        
        // Try constants first (wp-config.php)
        $client_id_constant = "SQUAREKIT_{$env_prefix}_APPLICATION_ID";
        $client_secret_constant = "SQUAREKIT_{$env_prefix}_CLIENT_SECRET";
        
        if ( defined( $client_id_constant ) && defined( $client_secret_constant ) ) {
            return array(
                'client_id' => constant( $client_id_constant ),
                'client_secret' => constant( $client_secret_constant ),
            );
        }
        
        // Try environment variables
        $client_id = getenv( $client_id_constant );
        $client_secret = getenv( $client_secret_constant );
        
        if ( $client_id && $client_secret ) {
            return array(
                'client_id' => $client_id,
                'client_secret' => $client_secret,
            );
        }
        
        return false;
    }

    /**
     * Check if client credentials are configured securely
     *
     * @param string $environment Environment (sandbox/production)
     * @return bool True if credentials are available from constants/env vars
     */
    public static function has_secure_credentials( $environment ) {
        return self::get_client_credentials( $environment ) !== false;
    }

    /**
     * Get token storage info for debugging
     *
     * @return array Storage information
     */
    public static function get_storage_info() {
        $tokens = get_option( self::OPTION_NAME, array() );
        $info = array(
            'encryption_key_exists' => ! empty( get_option( self::KEY_OPTION ) ),
            'environments' => array(),
        );

        foreach ( array( 'sandbox', 'production' ) as $env ) {
            $info['environments'][ $env ] = array(
                'has_access_token' => isset( $tokens[ $env ]['access_token'] ),
                'has_refresh_token' => isset( $tokens[ $env ]['refresh_token'] ),
                'stored_at' => $tokens[ $env ]['stored_at'] ?? null,
                'has_secure_credentials' => self::has_secure_credentials( $env ),
            );
        }

        return $info;
    }
}
