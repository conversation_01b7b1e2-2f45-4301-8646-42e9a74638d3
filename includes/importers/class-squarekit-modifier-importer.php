<?php
/**
 * SquareKit Modifier Importer
 * 
 * <PERSON>les importing Square modifier lists to WooCommerce modifiers
 * with proper structure and naming.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Modifier Importer Class
 */
class SquareKit_Modifier_Importer {

    /**
     * Square API instance
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Import statistics
     * @var array
     */
    private $import_stats = array(
        'modifier_sets_created' => 0,
        'modifier_sets_updated' => 0,
        'modifier_options_created' => 0,
        'modifier_options_updated' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize required dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        $this->square_api = new SquareKit_Square_API();
    }

    /**
     * Import modifiers for a WooCommerce product
     *
     * @param int $product_id WooCommerce product ID
     * @param array $modifier_list_info Square modifier list info
     * @param array $related_objects Related Square objects
     * @return bool|WP_Error Success status or error
     */
    public function import_modifiers( $product_id, $modifier_list_info, $related_objects = array() ) {
        $this->reset_import_stats();

        if ( empty( $modifier_list_info ) || ! is_array( $modifier_list_info ) ) {
            return new WP_Error( 'invalid_modifier_info', __( 'Invalid modifier list info provided.', 'squarekit' ) );
        }

        $modifier_sets = array();

        foreach ( $modifier_list_info as $modifier_info ) {
            $modifier_list_id = $modifier_info['modifier_list_id'] ?? '';
            
            if ( empty( $modifier_list_id ) ) {
                $this->import_stats['errors'][] = __( 'Missing modifier list ID.', 'squarekit' );
                continue;
            }

            $modifier_set = $this->process_modifier_list( $modifier_list_id, $related_objects );
            
            if ( is_wp_error( $modifier_set ) ) {
                $this->import_stats['errors'][] = sprintf(
                    __( 'Failed to process modifier list %s: %s', 'squarekit' ),
                    $modifier_list_id,
                    $modifier_set->get_error_message()
                );
                continue;
            }

            if ( $modifier_set ) {
                $modifier_sets[] = $modifier_set;
                $this->import_stats['modifier_sets_created']++;
            }
        }

        if ( empty( $modifier_sets ) ) {
            return new WP_Error( 'no_modifiers_imported', __( 'No modifiers could be imported.', 'squarekit' ) );
        }

        // Store modifiers in WooCommerce product
        $this->store_modifiers_in_product( $product_id, $modifier_sets );

        return true;
    }

    /**
     * Process a single Square modifier list
     *
     * @param string $modifier_list_id Square modifier list ID
     * @param array $related_objects Related Square objects
     * @return array|WP_Error|false Processed modifier set, error, or false if not found
     */
    private function process_modifier_list( $modifier_list_id, $related_objects ) {
        // Get modifier list data
        $modifier_list_data = $this->get_modifier_list_data( $modifier_list_id, $related_objects );
        
        if ( ! $modifier_list_data ) {
            return new WP_Error( 'modifier_list_not_found', sprintf(
                __( 'Modifier list %s not found.', 'squarekit' ),
                $modifier_list_id
            ) );
        }

        $list_data = $modifier_list_data['modifier_list_data'] ?? array();
        $set_name = $list_data['name'] ?? '';
        $selection_type = $list_data['selection_type'] ?? 'MULTIPLE';
        $modifiers = $list_data['modifiers'] ?? array();

        if ( empty( $set_name ) ) {
            return new WP_Error( 'missing_modifier_set_name', __( 'Modifier set name is required.', 'squarekit' ) );
        }

        if ( empty( $modifiers ) ) {
            return false; // Skip empty modifier sets
        }

        // Process modifier options
        $modifier_options = array();
        foreach ( $modifiers as $modifier ) {
            $option = $this->process_modifier_option( $modifier );
            
            if ( $option ) {
                $modifier_options[] = $option;
                $this->import_stats['modifier_options_created']++;
            }
        }

        if ( empty( $modifier_options ) ) {
            return false; // Skip sets with no valid options
        }

        return array(
            'set_name' => $set_name,
            'square_modifier_list_id' => $modifier_list_id,
            'single_choice' => ( $selection_type === 'SINGLE' ),
            'required' => $this->determine_if_required( $list_data ),
            'source' => 'square',
            'options' => $modifier_options
        );
    }

    /**
     * Process a single modifier option
     *
     * @param array $modifier Square modifier data
     * @return array|false Processed modifier option or false if invalid
     */
    private function process_modifier_option( $modifier ) {
        $modifier_data = $modifier['modifier_data'] ?? array();
        $modifier_id = $modifier['id'] ?? '';
        $modifier_name = $modifier_data['name'] ?? '';

        if ( empty( $modifier_name ) || empty( $modifier_id ) ) {
            return false;
        }

        // Calculate price
        $price = 0;
        if ( isset( $modifier_data['price_money']['amount'] ) ) {
            $price = $modifier_data['price_money']['amount'] / 100;
        }

        // Import SKU for modifier
        $modifier_sku = $this->import_modifier_sku( $modifier );

        // Check sold out status
        $stock_status = $this->determine_modifier_stock_status( $modifier );

        return array(
            'name' => $modifier_name,
            'price' => $price,
            'square_modifier_id' => $modifier_id,
            'sku' => $modifier_sku,
            'stock' => $stock_status,
            'description' => $modifier_data['description'] ?? '',
            'sold_out' => $stock_status === 'sold_out'
        );
    }

    /**
     * Get modifier list data from related objects or API
     *
     * @param string $modifier_list_id Square modifier list ID
     * @param array $related_objects Related Square objects
     * @return array|false Modifier list data or false if not found
     */
    private function get_modifier_list_data( $modifier_list_id, $related_objects ) {
        // First check in related objects
        foreach ( $related_objects as $object ) {
            if ( $object['id'] === $modifier_list_id && $object['type'] === 'MODIFIER_LIST' ) {
                return $object;
            }
        }

        // If not found, try to fetch from API
        try {
            return $this->square_api->get_catalog_object( $modifier_list_id );
        } catch ( Exception $e ) {
            $this->import_stats['errors'][] = sprintf(
                __( 'API error fetching modifier list %s: %s', 'squarekit' ),
                $modifier_list_id,
                $e->getMessage()
            );
            return false;
        }
    }

    /**
     * Determine modifier stock status based on Square sold out data
     *
     * @param array $modifier Square modifier data
     * @return string Stock status: 'optional', 'sold_out', or 'required'
     */
    private function determine_modifier_stock_status( $modifier ) {
        $modifier_data = $modifier['modifier_data'] ?? array();

        // Check global sold out status
        if ( isset( $modifier_data['sold_out'] ) && $modifier_data['sold_out'] === true ) {
            return 'sold_out';
        }

        // Check location-specific sold out status
        $location_overrides = $modifier['location_overrides'] ?? array();
        if ( ! empty( $location_overrides ) ) {
            foreach ( $location_overrides as $override ) {
                if ( isset( $override['sold_out'] ) && $override['sold_out'] === true ) {
                    return 'sold_out';
                }
            }
        }

        // Check if modifier is required (based on modifier list settings)
        // This would need to be determined from the parent modifier list
        // For now, default to 'optional'
        return 'optional';
    }

    /**
     * Import SKU for modifier option
     *
     * @param array $modifier Square modifier data
     * @return string|null SKU or null if not available
     */
    private function import_modifier_sku( $modifier ) {
        $modifier_data = $modifier['modifier_data'] ?? array();
        $sku = $modifier_data['sku'] ?? '';

        if ( empty( $sku ) ) {
            return null;
        }

        // Validate and sanitize SKU
        $sku = sanitize_text_field( $sku );
        
        // Check for conflicts (basic check)
        if ( $this->sku_exists_in_woocommerce( $sku ) ) {
            // Add suffix to make it unique
            $sku = $sku . '_' . substr( $modifier['id'], -6 );
        }

        return $sku;
    }

    /**
     * Check if SKU exists in WooCommerce
     *
     * @param string $sku SKU to check
     * @return bool True if exists, false otherwise
     */
    private function sku_exists_in_woocommerce( $sku ) {
        global $wpdb;

        $count = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key = '_sku' AND meta_value = %s",
            $sku
        ) );

        return $count > 0;
    }

    /**
     * Determine if modifier set is required
     *
     * @param array $list_data Square modifier list data
     * @return bool True if required, false otherwise
     */
    private function determine_if_required( $list_data ) {
        // Check if Square provides required information
        if ( isset( $list_data['required'] ) ) {
            return (bool) $list_data['required'];
        }

        // Default to not required
        return false;
    }

    /**
     * Store modifiers in WooCommerce product (SWEVER-compatible format)
     *
     * @param int $product_id WooCommerce product ID
     * @param array $modifier_sets Processed modifier sets
     */
    private function store_modifiers_in_product( $product_id, $modifier_sets ) {
        // Convert to SWEVER-compatible format
        $swever_format = $this->convert_to_swever_format( $modifier_sets );

        // Store in SWEVER-compatible format (primary)
        update_post_meta( $product_id, '_squarekit_modifier_sets', $swever_format );

        // Also store in legacy format for backward compatibility
        update_post_meta( $product_id, '_squarekit_modifiers', $modifier_sets );

        // Store modifier count for quick reference
        update_post_meta( $product_id, '_squarekit_modifier_count', count( $modifier_sets ) );
    }

    /**
     * Convert modifier sets to SWEVER-compatible format
     *
     * @param array $modifier_sets Processed modifier sets
     * @return array SWEVER-compatible format
     */
    private function convert_to_swever_format( $modifier_sets ) {
        $swever_format = array();

        foreach ( $modifier_sets as $set ) {
            $swever_format[] = array(
                'set_name' => $set['name'] ?? $set['set_name'] ?? '',
                'square_mod_list_id' => $set['square_modifier_list_id'] ?? '',
                'single_choice' => ( $set['selection_type'] ?? 'MULTIPLE' ) === 'SINGLE',
                'source' => 'square', // Mark as Square-managed
                'options' => $this->convert_options_to_swever_format( $set['options'] ?? array() )
            );
        }

        return $swever_format;
    }

    /**
     * Convert options to SWEVER-compatible format
     *
     * @param array $options Original options
     * @return array SWEVER-compatible options
     */
    private function convert_options_to_swever_format( $options ) {
        $swever_options = array();

        foreach ( $options as $option ) {
            $swever_options[] = array(
                'name' => $option['name'] ?? '',
                'price' => floatval( $option['price'] ?? 0 ),
                'stock' => $option['stock'] ?? '',
                'square_mod_id' => $option['square_modifier_id'] ?? ''
            );
        }

        return $swever_options;
    }

    /**
     * Validate modifier list data
     *
     * @param array $modifier_list_data Modifier list data to validate
     * @return bool|WP_Error True if valid, WP_Error if invalid
     */
    public function validate_modifier_list_data( $modifier_list_data ) {
        if ( empty( $modifier_list_data['modifier_list_data']['name'] ) ) {
            return new WP_Error( 'missing_modifier_list_name', __( 'Modifier list name is required.', 'squarekit' ) );
        }

        if ( empty( $modifier_list_data['id'] ) ) {
            return new WP_Error( 'missing_modifier_list_id', __( 'Modifier list ID is required.', 'squarekit' ) );
        }

        return true;
    }

    /**
     * Validate modifier option data
     *
     * @param array $modifier_data Modifier option data to validate
     * @return bool|WP_Error True if valid, WP_Error if invalid
     */
    public function validate_modifier_option_data( $modifier_data ) {
        if ( empty( $modifier_data['modifier_data']['name'] ) ) {
            return new WP_Error( 'missing_modifier_option_name', __( 'Modifier option name is required.', 'squarekit' ) );
        }

        if ( empty( $modifier_data['id'] ) ) {
            return new WP_Error( 'missing_modifier_option_id', __( 'Modifier option ID is required.', 'squarekit' ) );
        }

        return true;
    }

    /**
     * Reset import statistics
     */
    private function reset_import_stats() {
        $this->import_stats = array(
            'modifier_sets_created' => 0,
            'modifier_sets_updated' => 0,
            'modifier_options_created' => 0,
            'modifier_options_updated' => 0,
            'errors' => array()
        );
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_stats() {
        return $this->import_stats;
    }

    /**
     * Process Square modifiers from WooCommerce product data (SWEVER-style)
     * Handles the "Entourage Effect" type modifier lists with proper pricing
     *
     * @param int $product_id WooCommerce product ID
     * @param array $wc_product_data WooCommerce product data with modifiers
     * @return bool|WP_Error Success status or error
     */
    public function process_square_modifiers( $product_id, $wc_product_data ) {
        $this->reset_import_stats();

        if ( empty( $wc_product_data['modifiers'] ) || ! is_array( $wc_product_data['modifiers'] ) ) {
            return true; // No modifiers to process
        }

        $processed_modifier_sets = array();

        foreach ( $wc_product_data['modifiers'] as $modifier_data ) {
            $modifier_set = $this->process_modifier_set_from_wc_data( $modifier_data );

            if ( $modifier_set && ! is_wp_error( $modifier_set ) ) {
                $processed_modifier_sets[] = $modifier_set;
                $this->import_stats['modifier_sets_created']++;
            } elseif ( is_wp_error( $modifier_set ) ) {
                $this->import_stats['errors'][] = $modifier_set->get_error_message();
            }
        }

        if ( ! empty( $processed_modifier_sets ) ) {
            $this->store_modifiers_in_product( $product_id, $processed_modifier_sets );
        }

        return true;
    }

    /**
     * Process modifier set from WooCommerce product data
     *
     * @param array $modifier_data Modifier data from WooCommerce product data
     * @return array|WP_Error Processed modifier set or error
     */
    private function process_modifier_set_from_wc_data( $modifier_data ) {
        $square_modifier_list_id = $modifier_data['square_id'] ?? '';
        $modifier_list_name = $modifier_data['name'] ?? '';
        $modifiers = $modifier_data['modifiers'] ?? array();

        if ( empty( $square_modifier_list_id ) || empty( $modifier_list_name ) ) {
            return new WP_Error( 'invalid_modifier_data', 'Invalid modifier data: missing ID or name' );
        }

        $processed_options = array();

        foreach ( $modifiers as $modifier ) {
            $option = $this->process_modifier_option_from_wc_data( $modifier );
            if ( $option ) {
                $processed_options[] = $option;
                $this->import_stats['modifier_options_created']++;
            }
        }

        return array(
            'square_modifier_list_id' => $square_modifier_list_id,
            'name' => $modifier_list_name,
            'selection_type' => $modifier_data['selection_type'] ?? 'MULTIPLE',
            'required' => $this->determine_if_required_from_wc_data( $modifier_data ),
            'options' => $processed_options
        );
    }

    /**
     * Process modifier option from WooCommerce data
     *
     * @param array $modifier Modifier option data
     * @return array|false Processed modifier option or false if invalid
     */
    private function process_modifier_option_from_wc_data( $modifier ) {
        $modifier_id = $modifier['id'] ?? '';
        $modifier_name = $modifier['modifier_data']['name'] ?? '';
        $price_money = $modifier['modifier_data']['price_money'] ?? array();

        if ( empty( $modifier_name ) || empty( $modifier_id ) ) {
            return false;
        }

        $price = 0;
        if ( isset( $price_money['amount'] ) ) {
            $price = $price_money['amount'] / 100; // Convert from cents to dollars
        }

        return array(
            'square_modifier_id' => $modifier_id,
            'name' => $modifier_name,
            'price' => $price,
            'sku' => $modifier['modifier_data']['sku'] ?? ''
        );
    }

    /**
     * Determine selection type from modifiers
     *
     * @param array $modifiers Array of modifiers
     * @return string Selection type (SINGLE or MULTIPLE)
     */
    private function determine_selection_type( $modifiers ) {
        // For now, default to MULTIPLE for modifier lists like "Entourage Effect"
        // This can be enhanced based on Square API data if available
        return 'MULTIPLE';
    }

    /**
     * Determine if modifier set is required from WooCommerce data
     *
     * @param array $modifier_data Modifier data
     * @return bool True if required, false otherwise
     */
    private function determine_if_required_from_wc_data( $modifier_data ) {
        // Check if required information is available
        if ( isset( $modifier_data['required'] ) ) {
            return (bool) $modifier_data['required'];
        }

        // Default to not required
        return false;
    }
}
