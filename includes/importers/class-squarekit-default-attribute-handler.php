<?php
/**
 * Default Attribute Handler for Square Kit
 * 
 * <PERSON>les creation of default WooCommerce attributes for Square products
 * that use the "Variation Name Strategy" (no Square Options)
 *
 * @package SquareKit
 * @subpackage Importers
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class SquareKit_Default_Attribute_Handler
 * 
 * Creates default "Product Options" attributes for Square products using variation names
 * instead of Square's Item Options system.
 */
class SquareKit_Default_Attribute_Handler {

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Default attribute name for variation name strategy products
     *
     * @var string
     */
    public $default_attribute_name = 'Product Options';

    /**
     * Constructor
     */
    public function __construct() {
        if ( class_exists( 'SquareKit_Logger' ) ) {
            $this->logger = new SquareKit_Logger();
        }
    }

    /**
     * Check if Square item uses Variation Name Strategy
     * 
     * @param array $square_item Square item data
     * @return bool True if uses variation name strategy
     */
    public function uses_variation_name_strategy( $square_item ) {
        $variations = $square_item['item_data']['variations'] ?? array();
        
        // Must have multiple variations
        if ( count( $variations ) <= 1 ) {
            return false;
        }
        
        // Check if ALL variations have empty item_option_values
        foreach ( $variations as $variation ) {
            $option_values = $variation['item_variation_data']['item_option_values'] ?? array();
            if ( ! empty( $option_values ) ) {
                return false; // Has options, uses Item Options Strategy
            }
        }
        
        $this->log( 'info', 'Product uses Variation Name Strategy - will create default attributes' );
        return true; // All variations have empty options = Variation Name Strategy
    }

    /**
     * Create default "Product Options" attribute for variation name strategy
     * 
     * @param array $square_item Square item data
     * @param int $product_id WooCommerce product ID
     * @return WC_Product_Attribute|false Attribute object or false on failure
     */
    public function create_default_product_options_attribute( $square_item, $product_id ) {
        try {
            // Extract variation names
            $variation_names = $this->extract_variation_names( $square_item );
            
            if ( empty( $variation_names ) ) {
                $this->log( 'error', 'No variation names found for product ' . $product_id );
                return false;
            }
            
            $this->log( 'info', 'Creating default attribute with values: ' . implode( ', ', $variation_names ) );
            
            // Create "Product Options" attribute
            $attribute = $this->create_product_options_attribute( $variation_names, $product_id );
            
            if ( $attribute ) {
                $this->log( 'success', 'Successfully created default "Product Options" attribute for product ' . $product_id );
            }
            
            return $attribute;
            
        } catch ( Exception $e ) {
            $this->log( 'error', 'Failed to create default attribute: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Extract variation names from Square data
     * 
     * @param array $square_item Square item data
     * @return array Array of variation names
     */
    private function extract_variation_names( $square_item ) {
        $names = array();
        $variations = $square_item['item_data']['variations'] ?? array();
        
        foreach ( $variations as $variation ) {
            $name = $variation['item_variation_data']['name'] ?? '';
            if ( ! empty( $name ) ) {
                $sanitized_name = sanitize_text_field( $name );
                if ( ! in_array( $sanitized_name, $names ) ) {
                    $names[] = $sanitized_name;
                }
            }
        }
        
        return $names;
    }

    /**
     * Create WooCommerce attribute with variation names as values
     * 
     * @param array $variation_names Array of variation names
     * @param int $product_id WooCommerce product ID
     * @return WC_Product_Attribute|false Attribute object or false on failure
     */
    private function create_product_options_attribute( $variation_names, $product_id ) {
        if ( empty( $variation_names ) ) {
            return false;
        }
        
        try {
            // Create attribute object
            $attribute = new WC_Product_Attribute();
            $attribute->set_name( $this->default_attribute_name );
            $attribute->set_options( $variation_names );
            $attribute->set_visible( true );
            $attribute->set_variation( true );
            
            $this->log( 'info', 'Created attribute object with ' . count( $variation_names ) . ' options' );
            
            return $attribute;
            
        } catch ( Exception $e ) {
            $this->log( 'error', 'Failed to create attribute object: ' . $e->getMessage() );
            return false;
        }
    }

    /**
     * Get attribute slug for variation linking
     * 
     * @return string Attribute slug
     */
    public function get_default_attribute_slug() {
        return sanitize_title( $this->default_attribute_name );
    }

    /**
     * Get variation attribute value for linking
     * 
     * @param string $variation_name Square variation name
     * @return string Sanitized attribute value
     */
    public function get_variation_attribute_value( $variation_name ) {
        return sanitize_title( $variation_name );
    }

    /**
     * Validate that variation names are suitable for attributes
     * 
     * @param array $variation_names Array of variation names
     * @return bool True if valid
     */
    public function validate_variation_names( $variation_names ) {
        if ( empty( $variation_names ) ) {
            return false;
        }
        
        // Check for duplicates
        if ( count( $variation_names ) !== count( array_unique( $variation_names ) ) ) {
            $this->log( 'warning', 'Duplicate variation names detected' );
            return false;
        }
        
        // Check for empty names
        foreach ( $variation_names as $name ) {
            if ( empty( trim( $name ) ) ) {
                $this->log( 'warning', 'Empty variation name detected' );
                return false;
            }
        }
        
        return true;
    }

    /**
     * Log message with context
     * 
     * @param string $level Log level
     * @param string $message Log message
     */
    private function log( $level, $message ) {
        if ( $this->logger ) {
            $this->logger->log( 'default_attributes', $level, $message );
        }
    }
}
