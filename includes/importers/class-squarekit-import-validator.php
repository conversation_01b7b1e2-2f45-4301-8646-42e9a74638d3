<?php
/**
 * SquareKit Import Validator Class
 *
 * Validates imported products to ensure they have all required data and function correctly.
 * Provides comprehensive validation for attributes, variations, pricing, and other product data.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Import Validator Class
 *
 * Validates imported products to ensure successful import completion.
 */
class SquareKit_Import_Validator {

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Validation results
     *
     * @var array
     */
    private $validation_results = array();

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize required dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/core/class-squarekit-logger.php';
        }

        $this->logger = new SquareKit_Logger();
    }

    /**
     * Validate imported product
     *
     * @param int $product_id WooCommerce product ID
     * @param string $square_item_id Square item ID
     * @param array $expected_data Expected product data
     * @return array Validation results
     */
    public function validate_imported_product( $product_id, $square_item_id, $expected_data = array() ) {
        $this->reset_validation_results();
        
        $this->logger->log( 'validation', 'info', "Starting validation for product {$product_id} (Square ID: {$square_item_id})" );

        $product = wc_get_product( $product_id );
        
        if ( ! $product ) {
            $this->add_validation_error( 'product_not_found', 'Product not found in WooCommerce' );
            return $this->validation_results;
        }

        // Basic product validation
        $this->validate_basic_product_data( $product, $square_item_id, $expected_data );

        // Attribute validation
        $this->validate_product_attributes( $product, $expected_data );

        // Variation validation (for variable products)
        if ( $product->is_type( 'variable' ) ) {
            $this->validate_product_variations( $product, $expected_data );
        }

        // Pricing validation
        $this->validate_product_pricing( $product, $expected_data );

        // Meta data validation
        $this->validate_product_meta_data( $product, $square_item_id );

        // Frontend functionality validation
        $this->validate_frontend_functionality( $product );

        $this->logger->log( 'validation', 'info', "Validation completed for product {$product_id}" );

        return $this->validation_results;
    }

    /**
     * Validate basic product data
     *
     * @param WC_Product $product WooCommerce product
     * @param string $square_item_id Square item ID
     * @param array $expected_data Expected data
     */
    private function validate_basic_product_data( $product, $square_item_id, $expected_data ) {
        // Check product name
        if ( isset( $expected_data['name'] ) ) {
            if ( $product->get_name() !== $expected_data['name'] ) {
                $this->add_validation_error( 'name_mismatch', "Product name mismatch. Expected: {$expected_data['name']}, Got: {$product->get_name()}" );
            } else {
                $this->add_validation_success( 'name_match', 'Product name matches expected value' );
            }
        }

        // Check product status
        if ( $product->get_status() !== 'publish' ) {
            $this->add_validation_warning( 'product_not_published', 'Product is not published' );
        } else {
            $this->add_validation_success( 'product_published', 'Product is published' );
        }

        // Check Square ID meta
        $stored_square_id = $product->get_meta( '_square_item_id' );
        if ( $stored_square_id !== $square_item_id ) {
            $this->add_validation_error( 'square_id_mismatch', "Square ID mismatch. Expected: {$square_item_id}, Got: {$stored_square_id}" );
        } else {
            $this->add_validation_success( 'square_id_match', 'Square ID correctly stored' );
        }
    }

    /**
     * Validate product attributes
     *
     * @param WC_Product $product WooCommerce product
     * @param array $expected_data Expected data
     */
    private function validate_product_attributes( $product, $expected_data ) {
        $attributes = $product->get_attributes();

        if ( isset( $expected_data['attributes'] ) && ! empty( $expected_data['attributes'] ) ) {
            foreach ( $expected_data['attributes'] as $attr_name => $attr_values ) {
                $found_attribute = false;
                
                foreach ( $attributes as $attribute ) {
                    if ( strtolower( $attribute->get_name() ) === strtolower( $attr_name ) || 
                         $attribute->get_name() === 'pa_' . wc_sanitize_taxonomy_name( $attr_name ) ) {
                        $found_attribute = true;
                        
                        // Check if attribute has expected values (case-insensitive)
                        $attribute_options = $attribute->get_options();

                        // Convert both arrays to lowercase for comparison
                        $expected_values_lower = array_map( 'strtolower', $attr_values );
                        $actual_values_lower = array_map( 'strtolower', $attribute_options );

                        $missing_values = array_diff( $expected_values_lower, $actual_values_lower );

                        if ( empty( $missing_values ) ) {
                            $this->add_validation_success( 'attribute_values_complete', "Attribute '{$attr_name}' has all expected values" );
                        } else {
                            // Show the original case for missing values
                            $missing_original = array();
                            foreach ( $missing_values as $missing_lower ) {
                                $key = array_search( $missing_lower, $expected_values_lower );
                                if ( $key !== false ) {
                                    $missing_original[] = $attr_values[$key];
                                }
                            }
                            $this->add_validation_error( 'attribute_values_missing', "Attribute '{$attr_name}' missing values: " . implode( ', ', $missing_original ) );
                        }
                        break;
                    }
                }
                
                if ( ! $found_attribute ) {
                    $this->add_validation_error( 'attribute_missing', "Expected attribute '{$attr_name}' not found" );
                }
            }
        }

        if ( empty( $attributes ) && isset( $expected_data['attributes'] ) && ! empty( $expected_data['attributes'] ) ) {
            $this->add_validation_error( 'no_attributes', 'Product has no attributes but attributes were expected' );
        } elseif ( ! empty( $attributes ) ) {
            $this->add_validation_success( 'attributes_exist', 'Product has attributes' );
        }
    }

    /**
     * Validate product variations
     *
     * @param WC_Product $product WooCommerce product
     * @param array $expected_data Expected data
     */
    private function validate_product_variations( $product, $expected_data ) {
        $variations = $product->get_children();

        if ( isset( $expected_data['variations'] ) && ! empty( $expected_data['variations'] ) ) {
            $expected_count = count( $expected_data['variations'] );
            $actual_count = count( $variations );
            
            if ( $actual_count !== $expected_count ) {
                $this->add_validation_error( 'variation_count_mismatch', "Variation count mismatch. Expected: {$expected_count}, Got: {$actual_count}" );
            } else {
                $this->add_validation_success( 'variation_count_match', "Correct number of variations ({$actual_count})" );
            }

            // Validate each variation
            foreach ( $variations as $variation_id ) {
                $this->validate_single_variation( $variation_id, $expected_data['variations'] );
            }
        }

        if ( empty( $variations ) && isset( $expected_data['variations'] ) && ! empty( $expected_data['variations'] ) ) {
            $this->add_validation_error( 'no_variations', 'Variable product has no variations' );
        }
    }

    /**
     * Validate single variation
     *
     * @param int $variation_id Variation ID
     * @param array $expected_variations Expected variation data
     */
    private function validate_single_variation( $variation_id, $expected_variations ) {
        $variation = wc_get_product( $variation_id );
        
        if ( ! $variation ) {
            $this->add_validation_error( 'variation_not_found', "Variation {$variation_id} not found" );
            return;
        }

        // Check if variation has Square ID
        $square_variation_id = $variation->get_meta( '_square_variation_id' );
        if ( empty( $square_variation_id ) ) {
            $this->add_validation_error( 'variation_missing_square_id', "Variation {$variation_id} missing Square ID" );
        } else {
            $this->add_validation_success( 'variation_has_square_id', "Variation {$variation_id} has Square ID" );
        }

        // Check variation attributes
        $variation_attributes = $variation->get_attributes();
        if ( empty( $variation_attributes ) ) {
            $this->add_validation_error( 'variation_no_attributes', "Variation {$variation_id} has no attributes" );
        } else {
            $this->add_validation_success( 'variation_has_attributes', "Variation {$variation_id} has attributes" );
        }

        // Check variation price
        $price = $variation->get_price();
        if ( empty( $price ) || $price <= 0 ) {
            $this->add_validation_warning( 'variation_no_price', "Variation {$variation_id} has no price or zero price" );
        } else {
            $this->add_validation_success( 'variation_has_price', "Variation {$variation_id} has valid price: \${$price}" );
        }
    }

    /**
     * Validate product pricing
     *
     * @param WC_Product $product WooCommerce product
     * @param array $expected_data Expected data
     */
    private function validate_product_pricing( $product, $expected_data ) {
        if ( $product->is_type( 'variable' ) ) {
            // For variable products, check price range
            $min_price = $product->get_variation_price( 'min' );
            $max_price = $product->get_variation_price( 'max' );
            
            if ( $min_price > 0 && $max_price > 0 ) {
                $this->add_validation_success( 'variable_pricing_valid', "Variable product has valid price range: \${$min_price} - \${$max_price}" );
            } else {
                $this->add_validation_error( 'variable_pricing_invalid', 'Variable product has invalid price range' );
            }
        } else {
            // For simple products, check regular price
            $price = $product->get_price();
            if ( $price > 0 ) {
                $this->add_validation_success( 'simple_pricing_valid', "Simple product has valid price: \${$price}" );
            } else {
                $this->add_validation_warning( 'simple_pricing_invalid', 'Simple product has no price or zero price' );
            }
        }
    }

    /**
     * Validate product meta data
     *
     * @param WC_Product $product WooCommerce product
     * @param string $square_item_id Square item ID
     */
    private function validate_product_meta_data( $product, $square_item_id ) {
        // Check required meta fields
        $required_meta = array(
            '_square_item_id' => $square_item_id
        );

        foreach ( $required_meta as $meta_key => $expected_value ) {
            $actual_value = $product->get_meta( $meta_key );
            if ( $actual_value !== $expected_value ) {
                $this->add_validation_error( 'meta_mismatch', "Meta {$meta_key} mismatch. Expected: {$expected_value}, Got: {$actual_value}" );
            } else {
                $this->add_validation_success( 'meta_correct', "Meta {$meta_key} is correct" );
            }
        }
    }

    /**
     * Validate frontend functionality
     *
     * @param WC_Product $product WooCommerce product
     */
    private function validate_frontend_functionality( $product ) {
        // Check if product can be added to cart
        if ( $product->is_purchasable() ) {
            $this->add_validation_success( 'product_purchasable', 'Product is purchasable' );
        } else {
            $this->add_validation_warning( 'product_not_purchasable', 'Product is not purchasable' );
        }

        // Check stock status
        if ( $product->is_in_stock() ) {
            $this->add_validation_success( 'product_in_stock', 'Product is in stock' );
        } else {
            $this->add_validation_warning( 'product_out_of_stock', 'Product is out of stock' );
        }
    }

    /**
     * Add validation error
     *
     * @param string $code Error code
     * @param string $message Error message
     */
    private function add_validation_error( $code, $message ) {
        $this->validation_results['errors'][] = array(
            'code' => $code,
            'message' => $message
        );
        $this->logger->log( 'validation', 'error', "Validation error [{$code}]: {$message}" );
    }

    /**
     * Add validation warning
     *
     * @param string $code Warning code
     * @param string $message Warning message
     */
    private function add_validation_warning( $code, $message ) {
        $this->validation_results['warnings'][] = array(
            'code' => $code,
            'message' => $message
        );
        $this->logger->log( 'validation', 'warning', "Validation warning [{$code}]: {$message}" );
    }

    /**
     * Add validation success
     *
     * @param string $code Success code
     * @param string $message Success message
     */
    private function add_validation_success( $code, $message ) {
        $this->validation_results['successes'][] = array(
            'code' => $code,
            'message' => $message
        );
        $this->logger->log( 'validation', 'info', "Validation success [{$code}]: {$message}" );
    }

    /**
     * Reset validation results
     */
    private function reset_validation_results() {
        $this->validation_results = array(
            'errors' => array(),
            'warnings' => array(),
            'successes' => array(),
            'is_valid' => true,
            'summary' => array()
        );
    }

    /**
     * Get validation results
     *
     * @return array Validation results
     */
    public function get_validation_results() {
        // Update is_valid based on errors
        $this->validation_results['is_valid'] = empty( $this->validation_results['errors'] );

        // Generate summary
        $this->validation_results['summary'] = array(
            'total_checks' => count( $this->validation_results['errors'] ) +
                             count( $this->validation_results['warnings'] ) +
                             count( $this->validation_results['successes'] ),
            'errors' => count( $this->validation_results['errors'] ),
            'warnings' => count( $this->validation_results['warnings'] ),
            'successes' => count( $this->validation_results['successes'] )
        );

        return $this->validation_results;
    }

    /**
     * Validate multiple products
     *
     * @param array $products Array of product data to validate
     * @return array Validation results for all products
     */
    public function validate_multiple_products( $products ) {
        $results = array();

        foreach ( $products as $product_data ) {
            $product_id = $product_data['product_id'];
            $square_id = $product_data['square_id'];
            $expected_data = $product_data['expected_data'] ?? array();

            $results[$product_id] = $this->validate_imported_product( $product_id, $square_id, $expected_data );
        }

        return $results;
    }

    /**
     * Generate validation report
     *
     * @param array $validation_results Validation results
     * @return string HTML validation report
     */
    public function generate_validation_report( $validation_results ) {
        $html = '<div class="squarekit-validation-report">';
        $html .= '<h3>Import Validation Report</h3>';

        // Summary
        $summary = $validation_results['summary'];
        $html .= '<div class="validation-summary">';
        $html .= '<p><strong>Total Checks:</strong> ' . $summary['total_checks'] . '</p>';
        $html .= '<p><strong>Errors:</strong> <span class="error-count">' . $summary['errors'] . '</span></p>';
        $html .= '<p><strong>Warnings:</strong> <span class="warning-count">' . $summary['warnings'] . '</span></p>';
        $html .= '<p><strong>Successes:</strong> <span class="success-count">' . $summary['successes'] . '</span></p>';
        $html .= '<p><strong>Overall Status:</strong> <span class="' . ( $validation_results['is_valid'] ? 'valid' : 'invalid' ) . '">' .
                 ( $validation_results['is_valid'] ? 'VALID' : 'INVALID' ) . '</span></p>';
        $html .= '</div>';

        // Errors
        if ( ! empty( $validation_results['errors'] ) ) {
            $html .= '<div class="validation-errors">';
            $html .= '<h4>Errors</h4>';
            $html .= '<ul>';
            foreach ( $validation_results['errors'] as $error ) {
                $html .= '<li class="error"><strong>' . $error['code'] . ':</strong> ' . $error['message'] . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        // Warnings
        if ( ! empty( $validation_results['warnings'] ) ) {
            $html .= '<div class="validation-warnings">';
            $html .= '<h4>Warnings</h4>';
            $html .= '<ul>';
            foreach ( $validation_results['warnings'] as $warning ) {
                $html .= '<li class="warning"><strong>' . $warning['code'] . ':</strong> ' . $warning['message'] . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        // Successes (collapsible)
        if ( ! empty( $validation_results['successes'] ) ) {
            $html .= '<div class="validation-successes">';
            $html .= '<h4>Successes <small>(click to expand)</small></h4>';
            $html .= '<ul style="display: none;">';
            foreach ( $validation_results['successes'] as $success ) {
                $html .= '<li class="success"><strong>' . $success['code'] . ':</strong> ' . $success['message'] . '</li>';
            }
            $html .= '</ul>';
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}
