<?php
/**
 * SquareKit Variation Importer
 * 
 * Handles importing Square item variations to WooCommerce product variations
 * with proper pricing and attribute assignment.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Variation Importer Class
 */
class SquareKit_Variation_Importer {

    /**
     * Attribute importer instance
     * @var SquareKit_Attribute_Importer
     */
    private $attribute_importer;

    /**
     * Import statistics
     * @var array
     */
    private $import_stats = array(
        'variations_created' => 0,
        'variations_updated' => 0,
        'variations_skipped' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize required dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Attribute_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-attribute-importer.php';
        }

        $this->attribute_importer = new SquareKit_Attribute_Importer();
    }

    /**
     * Import variations for a WooCommerce product
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_variations Square item variations
     * @param array $related_objects Related Square objects
     * @return bool|WP_Error Success status or error
     */
    public function import_variations( $product_id, $square_variations, $related_objects = array() ) {
        $this->reset_import_stats();

        $product = wc_get_product( $product_id );
        if ( ! $product || ! $product->is_type( 'variable' ) ) {
            return new WP_Error( 'invalid_product', __( 'Product is not a variable product.', 'squarekit' ) );
        }

        if ( empty( $square_variations ) || ! is_array( $square_variations ) ) {
            return new WP_Error( 'invalid_variations', __( 'Invalid variations data provided.', 'squarekit' ) );
        }

        // Step 1: Import attributes first
        $attribute_result = $this->attribute_importer->import_attributes_from_variations( $square_variations, $related_objects );
        
        if ( is_wp_error( $attribute_result ) ) {
            return $attribute_result;
        }

        $attribute_mappings = $attribute_result['mappings'];

        // Step 2: Set product attributes
        $this->set_product_attributes( $product, $attribute_mappings );

        // Step 3: Create individual variations
        foreach ( $square_variations as $square_variation ) {
            $result = $this->create_variation( $product_id, $square_variation, $attribute_mappings );
            
            if ( is_wp_error( $result ) ) {
                $this->import_stats['errors'][] = sprintf(
                    __( 'Failed to create variation %s: %s', 'squarekit' ),
                    $square_variation['id'] ?? 'unknown',
                    $result->get_error_message()
                );
                $this->import_stats['variations_skipped']++;
                continue;
            }

            $this->import_stats['variations_created']++;
        }

        // Step 4: Update product price range
        $this->update_product_price_range( $product );

        return true;
    }

    /**
     * Set product attributes from attribute mappings
     *
     * @param WC_Product $product WooCommerce product
     * @param array $attribute_mappings Attribute mappings
     */
    private function set_product_attributes( $product, $attribute_mappings ) {
        $product_attributes = array();

        foreach ( $attribute_mappings as $option_set_id => $mapping ) {
            $attribute_id = $mapping['attribute_id'];
            $attribute_name = $mapping['attribute_name'];
            $term_mappings = $mapping['term_mappings'];

            // Get WooCommerce attribute taxonomy name
            $attribute_taxonomy = wc_attribute_taxonomy_name_by_id( $attribute_id );
            
            if ( ! $attribute_taxonomy ) {
                continue;
            }

            // Create WC_Product_Attribute
            $attribute = new WC_Product_Attribute();
            $attribute->set_id( $attribute_id );
            $attribute->set_name( $attribute_taxonomy );
            $attribute->set_options( array_values( $term_mappings ) );
            $attribute->set_visible( true );
            $attribute->set_variation( true );

            $product_attributes[ $attribute_taxonomy ] = $attribute;
        }

        $product->set_attributes( $product_attributes );
        $product->save();
    }

    /**
     * Create a single WooCommerce variation
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_variation Square variation data
     * @param array $attribute_mappings Attribute mappings
     * @return int|WP_Error Variation ID or error
     */
    private function create_variation( $product_id, $square_variation, $attribute_mappings ) {
        $square_variation_id = $square_variation['id'] ?? '';
        $variation_data = $square_variation['item_variation_data'] ?? array();

        if ( empty( $square_variation_id ) ) {
            return new WP_Error( 'invalid_variation_id', __( 'Invalid Square variation ID.', 'squarekit' ) );
        }

        // Check if variation already exists
        $existing_variation_id = $this->get_existing_variation_id( $square_variation_id );
        if ( $existing_variation_id ) {
            return $this->update_existing_variation( $existing_variation_id, $square_variation, $attribute_mappings );
        }

        // Create new variation
        $variation = new WC_Product_Variation();
        $variation->set_parent_id( $product_id );

        // Set variation attributes - detect which strategy to use
        $variation_attributes = $this->build_variation_attributes_smart( $product_id, $square_variation, $attribute_mappings );
        $variation->set_attributes( $variation_attributes );

        // Set pricing
        $this->set_variation_pricing( $variation, $variation_data );

        // Set other variation data
        $this->set_variation_data( $variation, $square_variation );

        // Save variation
        $variation_id = $variation->save();
        if ( ! $variation_id ) {
            return new WP_Error( 'variation_save_failed', __( 'Failed to save variation.', 'squarekit' ) );
        }

        // Store Square variation ID mapping
        $square_variation_id = $square_variation['id'] ?? '';
        if ( $square_variation_id ) {
            update_post_meta( $variation_id, '_square_variation_id', $square_variation_id );
        }

        return $variation_id;
    }

    /**
     * Build variation attributes from Square variation data
     *
     * @param array $square_variation Square variation data
     * @param array $attribute_mappings Attribute mappings
     * @return array Variation attributes
     */
    private function build_variation_attributes( $square_variation, $attribute_mappings ) {
        $variation_attributes = array();
        $item_option_values = $square_variation['item_variation_data']['item_option_values'] ?? array();

        foreach ( $item_option_values as $option_value ) {
            $option_id = $option_value['item_option_id'] ?? '';

            // Square API uses different field names - try multiple possibilities
            $option_value_id = $option_value['item_option_value_id'] ??
                              $option_value['value'] ??
                              $option_value['id'] ?? '';

            // Also try to get the value name
            $option_value_name = $option_value['value'] ??
                                $option_value['name'] ??
                                $option_value['item_option_value_data']['name'] ?? '';

            if ( empty( $option_id ) ) {
                continue;
            }

            if ( empty( $option_value_id ) && empty( $option_value_name ) ) {
                continue;
            }

            // Use the value name as ID if no proper ID is found
            if ( empty( $option_value_id ) && ! empty( $option_value_name ) ) {
                $option_value_id = sanitize_title( $option_value_name );
            }

            // Find the corresponding attribute mapping
            if ( ! isset( $attribute_mappings[ $option_id ] ) ) {
                continue;
            }

            $mapping = $attribute_mappings[ $option_id ];
            $attribute_id = $mapping['attribute_id'];
            $term_mappings = $mapping['term_mappings'];

            // Get the term ID for this option value
            if ( ! isset( $term_mappings[ $option_value_id ] ) ) {
                continue;
            }

            $term_id = $term_mappings[ $option_value_id ];
            $term = get_term( $term_id );
            
            if ( ! $term || is_wp_error( $term ) ) {
                continue;
            }

            // Get attribute taxonomy name
            $attribute_taxonomy = wc_attribute_taxonomy_name_by_id( $attribute_id );
            if ( $attribute_taxonomy ) {
                $variation_attributes[ $attribute_taxonomy ] = $term->slug;
            }
        }

        return $variation_attributes;
    }

    /**
     * Smart variation attribute builder - detects strategy and uses appropriate method
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_variation Square variation data
     * @param array $attribute_mappings Attribute mappings (for Item Options Strategy)
     * @return array Variation attributes
     */
    private function build_variation_attributes_smart( $product_id, $square_variation, $attribute_mappings ) {
        // Check if this variation has item_option_values (Item Options Strategy)
        $item_option_values = $square_variation['item_variation_data']['item_option_values'] ?? array();

        if ( ! empty( $item_option_values ) ) {
            // Use Item Options Strategy
            return $this->build_variation_attributes( $square_variation, $attribute_mappings );
        } else {
            // Use Variation Name Strategy
            return $this->build_variation_attributes_for_name_strategy_enhanced( $product_id, $square_variation );
        }

        return array();
    }

    /**
     * Build variation attributes for Variation Name Strategy products
     * Creates attributes using the default "Product Options" attribute
     *
     * @param array $square_variation Square variation data
     * @param array $product_attributes Product's attributes (should contain default attribute)
     * @return array Variation attributes
     */
    private function build_variation_attributes_for_name_strategy( $square_variation, $product_attributes ) {
        $variation_attributes = array();

        // Get the variation name
        $variation_name = $square_variation['item_variation_data']['name'] ?? '';

        if ( empty( $variation_name ) ) {
            return $variation_attributes;
        }

        // Load default attribute handler to get the attribute slug
        if ( ! class_exists( 'SquareKit_Default_Attribute_Handler' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-default-attribute-handler.php';
        }

        $default_handler = new SquareKit_Default_Attribute_Handler();
        $attribute_slug = $default_handler->get_default_attribute_slug();

        // Check if the default attribute exists in product attributes
        if ( isset( $product_attributes[$attribute_slug] ) ) {
            // Link this variation to the "Product Options" attribute
            $attribute_value = $default_handler->get_variation_attribute_value( $variation_name );
            $variation_attributes[$attribute_slug] = $attribute_value;

            if ( class_exists( 'SquareKit_Logger' ) ) {
                $logger = new SquareKit_Logger();
                $logger->log( 'variation_import', 'info', 'Linked variation "' . $variation_name . '" to default attribute with value: ' . $attribute_value );
            }
        }

        return $variation_attributes;
    }

    /**
     * Enhanced variation attribute builder for Variation Name Strategy products
     * This method ensures proper attribute linking even when product attributes aren't fully refreshed
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_variation Square variation data
     * @return array Variation attributes
     */
    private function build_variation_attributes_for_name_strategy_enhanced( $product_id, $square_variation ) {
        $variation_attributes = array();

        // Get the variation name
        $variation_name = $square_variation['item_variation_data']['name'] ?? '';

        if ( empty( $variation_name ) ) {
            return $variation_attributes;
        }

        // Load default attribute handler
        if ( ! class_exists( 'SquareKit_Default_Attribute_Handler' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-default-attribute-handler.php';
        }

        $default_handler = new SquareKit_Default_Attribute_Handler();
        $attribute_slug = $default_handler->get_default_attribute_slug();
        $attribute_value = $default_handler->get_variation_attribute_value( $variation_name );

        // Get fresh product object to ensure we have latest attributes
        $product = wc_get_product( $product_id );
        if ( ! $product ) {
            return $variation_attributes;
        }

        $product_attributes = $product->get_attributes();

        // First, try to find the attribute by the expected slug
        if ( isset( $product_attributes[$attribute_slug] ) ) {
            $variation_attributes[$attribute_slug] = $attribute_value;

            if ( class_exists( 'SquareKit_Logger' ) ) {
                $logger = new SquareKit_Logger();
                $logger->log( 'variation_import', 'info', 'Found default attribute by slug. Linked variation "' . $variation_name . '" with value: ' . $attribute_value );
            }

            return $variation_attributes;
        }

        // If not found by slug, search by attribute name (fallback)
        foreach ( $product_attributes as $attr_key => $attribute ) {
            if ( $attribute instanceof WC_Product_Attribute ) {
                $attr_name = $attribute->get_name();

                // Check if this is our default attribute by name
                if ( $attr_name === $default_handler->default_attribute_name ||
                     $attr_name === 'Product Options' ||
                     sanitize_title( $attr_name ) === $attribute_slug ) {

                    $variation_attributes[$attr_key] = $attribute_value;

                    if ( class_exists( 'SquareKit_Logger' ) ) {
                        $logger = new SquareKit_Logger();
                        $logger->log( 'variation_import', 'info', 'Found default attribute by name search. Linked variation "' . $variation_name . '" to attribute "' . $attr_key . '" with value: ' . $attribute_value );
                    }

                    return $variation_attributes;
                }
            }
        }

        // If still not found, log warning but create the attribute link anyway
        // This handles cases where the attribute exists but wasn't found due to timing issues
        $variation_attributes[$attribute_slug] = $attribute_value;

        if ( class_exists( 'SquareKit_Logger' ) ) {
            $logger = new SquareKit_Logger();
            $logger->log( 'variation_import', 'warning', 'Default attribute not found in product attributes. Creating link anyway for variation "' . $variation_name . '" with slug: ' . $attribute_slug );
        }

        return $variation_attributes;
    }

    /**
     * Set variation pricing from Square data
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $variation_data Square variation data
     */
    private function set_variation_pricing( $variation, $variation_data ) {
        // Regular price
        if ( isset( $variation_data['price_money']['amount'] ) ) {
            $price = $variation_data['price_money']['amount'] / 100;
            $variation->set_regular_price( $price );
            $variation->set_price( $price );
        }

        // Sale price (if available)
        if ( isset( $variation_data['sale_price_money']['amount'] ) ) {
            $sale_price = $variation_data['sale_price_money']['amount'] / 100;
            $variation->set_sale_price( $sale_price );
            $variation->set_price( $sale_price );
        }
    }

    /**
     * Set other variation data from Square
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $square_variation Square variation data
     */
    private function set_variation_data( $variation, $square_variation ) {
        $variation_data = $square_variation['item_variation_data'] ?? array();

        // SKU
        if ( ! empty( $variation_data['sku'] ) ) {
            $variation->set_sku( $variation_data['sku'] );
        }

        // Stock management
        if ( isset( $variation_data['track_inventory'] ) && $variation_data['track_inventory'] ) {
            $variation->set_manage_stock( true );
            
            if ( isset( $square_variation['inventory_count'] ) ) {
                $variation->set_stock_quantity( intval( $square_variation['inventory_count'] ) );
            }
        }

        // Variation name/description
        if ( ! empty( $variation_data['name'] ) ) {
            $variation->set_name( $variation_data['name'] );
        }

        // Weight and dimensions (if available)
        if ( isset( $variation_data['item_variation_vendor_info']['weight'] ) ) {
            $weight = $variation_data['item_variation_vendor_info']['weight'];
            $variation->set_weight( $weight );
        }
    }

    /**
     * Update product price range based on variations
     *
     * @param WC_Product $product WooCommerce product
     */
    private function update_product_price_range( $product ) {
        // Force WooCommerce to recalculate price range
        WC_Product_Variable::sync( $product->get_id() );
        
        // Clear any cached data
        wc_delete_product_transients( $product->get_id() );
    }

    /**
     * Get existing variation ID by Square variation ID
     *
     * @param string $square_variation_id Square variation ID
     * @return int|false Variation ID or false if not found
     */
    private function get_existing_variation_id( $square_variation_id ) {
        global $wpdb;

        $variation_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = '_square_variation_id' AND meta_value = %s",
            $square_variation_id
        ) );

        return $variation_id ? intval( $variation_id ) : false;
    }

    /**
     * Update existing variation
     *
     * @param int $variation_id Existing variation ID
     * @param array $square_variation Square variation data
     * @param array $attribute_mappings Attribute mappings
     * @return int|WP_Error Variation ID or error
     */
    private function update_existing_variation( $variation_id, $square_variation, $attribute_mappings ) {
        $variation = wc_get_product( $variation_id );
        if ( ! $variation || ! $variation->is_type( 'variation' ) ) {
            return new WP_Error( 'invalid_existing_variation', __( 'Existing variation is invalid.', 'squarekit' ) );
        }

        $variation_data = $square_variation['item_variation_data'] ?? array();

        // Update pricing
        $this->set_variation_pricing( $variation, $variation_data );

        // Update other data
        $this->set_variation_data( $variation, $square_variation );

        // Update attributes - detect which strategy to use
        $product_id = $variation->get_parent_id();
        $variation_attributes = $this->build_variation_attributes_smart( $product_id, $square_variation, $attribute_mappings );
        $variation->set_attributes( $variation_attributes );

        $variation->save();

        // Update Square variation ID mapping
        $square_variation_id = $square_variation['id'] ?? '';
        if ( $square_variation_id ) {
            update_post_meta( $variation_id, '_square_variation_id', $square_variation_id );
        }

        $this->import_stats['variations_updated']++;

        return $variation_id;
    }

    /**
     * Reset import statistics
     */
    private function reset_import_stats() {
        $this->import_stats = array(
            'variations_created' => 0,
            'variations_updated' => 0,
            'variations_skipped' => 0,
            'errors' => array()
        );
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_stats() {
        return $this->import_stats;
    }

    /**
     * Process Square variations using transformed data (SWEVER-style)
     * This method expects Square data that has been preprocessed with option_name and option_value
     *
     * @param WC_Product $product WooCommerce product object
     * @param array $square_item Transformed Square item data
     * @return bool|WP_Error Success status or error
     */
    public function process_square_variations( $product, $square_item ) {
        $this->reset_import_stats();

        if ( ! isset( $square_item['item_data']['variations'] ) || ! is_array( $square_item['item_data']['variations'] ) ) {
            return new WP_Error( 'no_variations', 'No variations found in Square item data' );
        }

        $product_id = $product->get_id();
        $existing_attributes = $product->get_attributes();

        // Process each variation
        foreach ( $square_item['item_data']['variations'] as $variation_data ) {
            $square_variation_id = $variation_data['id'] ?? '';

            if ( empty( $square_variation_id ) ) {
                $this->import_stats['variations_skipped']++;
                continue;
            }

            // Check if variation already exists
            $existing_variation_id = $this->get_existing_variation_by_square_id( $square_variation_id );

            if ( $existing_variation_id ) {
                // Update existing variation
                $variation = wc_get_product( $existing_variation_id );
            } else {
                // Create new variation
                $variation = new WC_Product_Variation();
                $variation->set_parent_id( $product_id );
            }

            // Set variation attributes using transformed data
            $variation_attributes = $this->build_variation_attributes_from_transformed_data(
                $variation_data,
                $existing_attributes
            );

            if ( ! empty( $variation_attributes ) ) {
                $variation->set_attributes( $variation_attributes );

                if ( class_exists( 'SquareKit_Logger' ) ) {
                    $logger = new SquareKit_Logger();
                    $variation_name = $variation_data['item_variation_data']['name'] ?? 'Unknown';
                    $logger->log( 'variation_import', 'info', 'Set attributes for variation "' . $variation_name . '": ' . print_r( $variation_attributes, true ) );
                }
            } else {
                if ( class_exists( 'SquareKit_Logger' ) ) {
                    $logger = new SquareKit_Logger();
                    $variation_name = $variation_data['item_variation_data']['name'] ?? 'Unknown';
                    $has_option_values = isset( $variation_data['item_variation_data']['item_option_values'] ) && ! empty( $variation_data['item_variation_data']['item_option_values'] );
                    $logger->log( 'variation_import', 'warning', 'No attributes set for variation "' . $variation_name . '". Has option values: ' . ( $has_option_values ? 'YES' : 'NO' ) . '. Existing attributes count: ' . count( $existing_attributes ) );
                }
            }

            // Set pricing
            $this->set_variation_pricing_from_transformed_data( $variation, $variation_data );

            // Set other variation data
            $this->set_variation_meta_data( $variation, $variation_data, $square_variation_id );

            // Save the variation
            $variation->save();

            if ( $existing_variation_id ) {
                $this->import_stats['variations_updated']++;
            } else {
                $this->import_stats['variations_created']++;
            }
        }

        // Update product price range
        $this->update_product_price_range( $product );

        return true;
    }

    /**
     * Build variation attributes from transformed Square data (SWEVER-style)
     *
     * @param array $variation_data Square variation data with resolved option names/values
     * @param array $existing_attributes Product's existing attributes
     * @return array Variation attributes
     */
    private function build_variation_attributes_from_transformed_data( $variation_data, $existing_attributes ) {
        $variation_attributes = array();

        // Check if this variation has item_option_values (Item Options Strategy)
        if ( ! isset( $variation_data['item_variation_data']['item_option_values'] ) ||
             empty( $variation_data['item_variation_data']['item_option_values'] ) ) {

            // This is a Variation Name Strategy product - use variation name as attribute
            return $this->build_variation_attributes_for_name_strategy_from_data( $variation_data, $existing_attributes );
        }

        foreach ( $variation_data['item_variation_data']['item_option_values'] as $option_value ) {
            // This is the key: we expect option_name and option_value to be resolved
            if ( ! isset( $option_value['option_name'] ) || ! isset( $option_value['option_value'] ) ) {
                continue;
            }

            $attribute_name = strtolower( $option_value['option_name'] );
            $attribute_option = $this->normalize_attribute_value( $option_value['option_value'] );
            $attribute_slug = wc_sanitize_taxonomy_name( $attribute_name );

            // Check if it's a local attribute
            if ( isset( $existing_attributes[$attribute_name] ) ) {
                $variation_attributes[$attribute_name] = $attribute_option;
            } else {
                // Check if it's a global attribute
                $taxonomy = 'pa_' . $attribute_slug;
                if ( taxonomy_exists( $taxonomy ) ) {
                    // Try to find term by name first (for title case), then by slug (for lowercase)
                    $term = get_term_by( 'name', $attribute_option, $taxonomy );
                    if ( ! $term ) {
                        $term = get_term_by( 'slug', sanitize_title( $attribute_option ), $taxonomy );
                    }
                    if ( ! $term ) {
                        $term = get_term_by( 'slug', strtolower( $attribute_option ), $taxonomy );
                    }

                    if ( $term ) {
                        $variation_attributes[$taxonomy] = $term->slug;
                    } else {
                        // Create the term if it doesn't exist
                        $term_data = wp_insert_term( $attribute_option, $taxonomy );
                        if ( ! is_wp_error( $term_data ) ) {
                            $variation_attributes[$taxonomy] = get_term( $term_data['term_id'] )->slug;
                        } else {
                            $variation_attributes[$taxonomy] = sanitize_title( $attribute_option );
                        }
                    }
                } else {
                    $variation_attributes[$attribute_slug] = $attribute_option;
                }
            }
        }

        return $variation_attributes;
    }

    /**
     * Build variation attributes for Variation Name Strategy from transformed data
     * This handles products that don't use Square Options but have variation names
     *
     * @param array $variation_data Square variation data
     * @param array $existing_attributes Product's existing attributes
     * @return array Variation attributes
     */
    private function build_variation_attributes_for_name_strategy_from_data( $variation_data, $existing_attributes ) {
        $variation_attributes = array();

        // Get the variation name
        $variation_name = $variation_data['item_variation_data']['name'] ?? '';

        if ( empty( $variation_name ) ) {
            return $variation_attributes;
        }

        // Load default attribute handler
        if ( ! class_exists( 'SquareKit_Default_Attribute_Handler' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-default-attribute-handler.php';
        }

        $default_handler = new SquareKit_Default_Attribute_Handler();
        $attribute_slug = $default_handler->get_default_attribute_slug();
        $attribute_value = $default_handler->get_variation_attribute_value( $variation_name );

        // First, try to find the attribute by the expected slug
        if ( isset( $existing_attributes[$attribute_slug] ) ) {
            $variation_attributes[$attribute_slug] = $attribute_value;

            if ( class_exists( 'SquareKit_Logger' ) ) {
                $logger = new SquareKit_Logger();
                $logger->log( 'variation_import', 'info', 'Found default attribute by slug in transformed data. Linked variation "' . $variation_name . '" with value: ' . $attribute_value );
            }

            return $variation_attributes;
        }

        // If not found by slug, search by attribute name (fallback)
        foreach ( $existing_attributes as $attr_key => $attribute ) {
            if ( $attribute instanceof WC_Product_Attribute ) {
                $attr_name = $attribute->get_name();

                // Check if this is our default attribute by name
                if ( $attr_name === $default_handler->default_attribute_name ||
                     $attr_name === 'Product Options' ||
                     sanitize_title( $attr_name ) === $attribute_slug ) {

                    $variation_attributes[$attr_key] = $attribute_value;

                    if ( class_exists( 'SquareKit_Logger' ) ) {
                        $logger = new SquareKit_Logger();
                        $logger->log( 'variation_import', 'info', 'Found default attribute by name search in transformed data. Linked variation "' . $variation_name . '" to attribute "' . $attr_key . '" with value: ' . $attribute_value );
                    }

                    return $variation_attributes;
                }
            }
        }

        // If still not found, create the attribute link anyway
        // This handles cases where the attribute exists but wasn't found due to timing issues
        $variation_attributes[$attribute_slug] = $attribute_value;

        if ( class_exists( 'SquareKit_Logger' ) ) {
            $logger = new SquareKit_Logger();
            $logger->log( 'variation_import', 'warning', 'Default attribute not found in existing attributes for transformed data. Creating link anyway for variation "' . $variation_name . '" with slug: ' . $attribute_slug );
        }

        return $variation_attributes;
    }

    /**
     * Set variation pricing from transformed Square data
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $variation_data Square variation data
     */
    private function set_variation_pricing_from_transformed_data( $variation, $variation_data ) {
        if ( isset( $variation_data['item_variation_data']['price_money']['amount'] ) ) {
            $price = $variation_data['item_variation_data']['price_money']['amount'] / 100;
            $variation->set_regular_price( $price );
            $variation->set_price( $price );
        }
    }

    /**
     * Set variation meta data
     *
     * @param WC_Product_Variation $variation WooCommerce variation
     * @param array $variation_data Square variation data
     * @param string $square_variation_id Square variation ID
     */
    private function set_variation_meta_data( $variation, $variation_data, $square_variation_id ) {
        // Store Square variation ID
        $variation->update_meta_data( '_square_variation_id', $square_variation_id );

        // Set SKU if available
        if ( ! empty( $variation_data['item_variation_data']['sku'] ) ) {
            $sku = $variation_data['item_variation_data']['sku'];
            // Check if SKU is unique
            if ( ! wc_get_product_id_by_sku( $sku ) ) {
                $variation->set_sku( $sku );
            }
        }

        // Set stock status
        $variation->set_stock_status( 'instock' );
        $variation->set_manage_stock( false );
    }

    /**
     * Get existing variation ID by Square variation ID
     *
     * @param string $square_variation_id Square variation ID
     * @return int|false Variation ID or false if not found
     */
    private function get_existing_variation_by_square_id( $square_variation_id ) {
        global $wpdb;

        $variation_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = '_square_variation_id' AND meta_value = %s",
            $square_variation_id
        ) );

        return $variation_id ? (int) $variation_id : false;
    }

    /**
     * Normalize attribute value to proper title case
     *
     * @param string $value Raw attribute value from Square
     * @return string Normalized attribute value
     */
    private function normalize_attribute_value( $value ) {
        // Trim whitespace
        $value = trim( $value );

        // Convert to title case for consistency
        // Handle special cases for common size/measurement values
        $value = strtolower( $value );

        // Special handling for common attribute values
        $special_cases = array(
            'xs' => 'XS',
            'sm' => 'SM',
            's' => 'S',
            'm' => 'M',
            'l' => 'L',
            'xl' => 'XL',
            'xxl' => 'XXL',
            'xxxl' => 'XXXL',
            'small' => 'Small',
            'medium' => 'Medium',
            'large' => 'Large',
            'extra small' => 'Extra Small',
            'extra large' => 'Extra Large',
            'light' => 'Light',
            'medium roast' => 'Medium Roast',
            'dark' => 'Dark',
            'dark roast' => 'Dark Roast',
            'decaf' => 'Decaf',
            'regular' => 'Regular'
        );

        // Check for exact matches first
        if ( isset( $special_cases[ $value ] ) ) {
            return $special_cases[ $value ];
        }

        // Default to title case
        return ucwords( $value );
    }
}
