<?php
/**
 * SquareKit Product Importer
 *
 * Handles importing products from Square to WooCommerce with proper
 * separation of concerns and clear import flow.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Product Importer Class
 *
 * Orchestrates the complete product import process from Square to WooCommerce.
 * Handles both simple and variable products with proper attribute and variation creation.
 */
class SquareKit_Product_Importer {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * WooCommerce integration instance
     *
     * @var SquareKit_WooCommerce
     */
    private $wc_integration;

    /**
     * Attribute importer instance
     *
     * @var SquareKit_Attribute_Importer
     */
    private $attribute_importer;

    /**
     * Variation importer instance
     *
     * @var SquareKit_Variation_Importer
     */
    private $variation_importer;

    /**
     * Modifier importer instance
     *
     * @var SquareKit_Modifier_Importer
     */
    private $modifier_importer;

    /**
     * Option resolver instance
     *
     * @var SquareKit_Option_Resolver
     */
    private $option_resolver;

    /**
     * Square import orchestrator instance
     *
     * @var SquareKit_Square_Import
     */
    private $square_import;

    /**
     * Import statistics
     *
     * @var array
     */
    private $import_stats;

    /**
     * Constructor
     */
    public function __construct() {
        $this->load_dependencies();
        $this->init_importers();
        $this->reset_import_stats();
    }

    /**
     * Load required dependencies
     */
    private function load_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        if ( ! class_exists( 'SquareKit_WooCommerce' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/integrations/class-squarekit-woocommerce.php';
        }
        if ( ! class_exists( 'SquareKit_Attribute_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-attribute-importer.php';
        }
        if ( ! class_exists( 'SquareKit_Variation_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-variation-importer.php';
        }
        if ( ! class_exists( 'SquareKit_Modifier_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-modifier-importer.php';
        }

        if ( ! class_exists( 'SquareKit_Option_Resolver' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-option-resolver.php';
        }

        if ( ! class_exists( 'SquareKit_Square_Import' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-square-import.php';
        }
    }

    /**
     * Initialize importer instances
     */
    private function init_importers() {
        $this->square_api = new SquareKit_Square_API();
        $this->wc_integration = new SquareKit_WooCommerce();
        $this->attribute_importer = new SquareKit_Attribute_Importer();
        $this->variation_importer = new SquareKit_Variation_Importer();
        $this->modifier_importer = new SquareKit_Modifier_Importer();
        $this->option_resolver = new SquareKit_Option_Resolver( $this->square_api );
        $this->square_import = new SquareKit_Square_Import();
    }

    /**
     * Import a single product from Square
     *
     * @param string $square_item_id Square item ID
     * @param int|null $existing_product_id Existing WooCommerce product ID (for updates)
     * @return array|WP_Error Import result or error
     */
    public function import_product( $square_item_id, $existing_product_id = null ) {
        $this->reset_import_stats();

        try {
            // Step 1: Fetch complete Square item data with related objects
            $square_data = $this->fetch_square_item_data( $square_item_id );
            if ( is_wp_error( $square_data ) ) {
                return $square_data;
            }

            $square_item = $square_data['item'];
            $related_objects = $square_data['related_objects'];

            // CRITICAL: Transform Square data to resolve option IDs to names (SWEVER-style preprocessing)
            $square_item = $this->option_resolver->transform_product_data( $square_item );

            // Step 2: Determine product type and create/update WooCommerce product
            $product_result = $this->create_or_update_woocommerce_product( $square_item, $existing_product_id );
            if ( is_wp_error( $product_result ) ) {
                return $product_result;
            }

            // Get the WooCommerce product object for attribute processing
            $wc_product = wc_get_product( $product_id );
            if ( ! $wc_product ) {
                return new WP_Error( 'product_not_found', 'Created product could not be retrieved' );
            }

            // Process attributes using the transformed Square data
            $attributes = $this->attribute_importer->process_square_attributes( $square_item, $wc_product );

            if ( ! empty( $attributes ) ) {
                // Set attributes on the product
                $wc_product->set_attributes( $attributes );

                // Convert to variable product if it has variations
                if ( isset( $square_item['item_data']['variations'] ) && count( $square_item['item_data']['variations'] ) > 1 ) {
                    // Convert to variable product
                    wp_set_object_terms( $product_id, 'variable', 'product_type' );
                    $wc_product = wc_get_product( $product_id ); // Refresh product object
                }

                $wc_product->save();

                // Refresh product object to ensure attributes are properly loaded
                $wc_product = wc_get_product( $wc_product->get_id() );
            }

            // Process variations if this is a variable product
            if ( isset( $square_item['item_data']['variations'] ) && count( $square_item['item_data']['variations'] ) > 1 ) {
                $variation_result = $this->variation_importer->process_square_variations( $wc_product, $square_item );

                if ( is_wp_error( $variation_result ) ) {
                    error_log( 'SquareKit: Failed to process variations: ' . $variation_result->get_error_message() );
                }
            }

            $product_id = $product_result['product_id'];
            $product = $product_result['product'];
            $is_variable = $product_result['is_variable'];

            // Step 3: Import attributes and variations (for variable products)
            if ( $is_variable ) {
                $variation_result = $this->import_variations( $product_id, $square_item, $related_objects );
                if ( is_wp_error( $variation_result ) ) {
                    $this->import_stats['errors'][] = 'Variation import failed: ' . $variation_result->get_error_message();
                } else {
                    $this->import_stats['variations_imported'] = $variation_result['variations_created'];
                    $this->import_stats['attributes_imported'] = $variation_result['attributes_created'];
                }
            }

            // Step 4: Import modifiers
            $modifier_result = $this->import_modifiers( $product_id, $square_item, $related_objects );
            if ( is_wp_error( $modifier_result ) ) {
                $this->import_stats['errors'][] = 'Modifier import failed: ' . $modifier_result->get_error_message();
            } else {
                $this->import_stats['modifiers_imported'] = $modifier_result['modifiers_created'];
            }

            // Step 5: Import images
            $image_result = $this->import_images( $product_id, $square_item, $related_objects );
            if ( is_wp_error( $image_result ) ) {
                $this->import_stats['errors'][] = 'Image import failed: ' . $image_result->get_error_message();
            } else {
                $this->import_stats['images_imported'] = $image_result['images_imported'];
            }

            // Step 6: Import categories
            $category_result = $this->import_categories( $product_id, $square_item, $related_objects );
            if ( is_wp_error( $category_result ) ) {
                $this->import_stats['errors'][] = 'Category import failed: ' . $category_result->get_error_message();
            }

            // Step 7: Set Square tracking meta
            $this->set_square_tracking_meta( $product_id, $square_item );

            // Step 8: Update sync timestamp
            update_post_meta( $product_id, '_square_last_sync', current_time( 'mysql' ) );

            $this->import_stats['success'] = true;
            $this->import_stats['product_id'] = $product_id;

            return $this->import_stats;

        } catch ( Exception $e ) {
            return new WP_Error( 'import_exception', 'Import failed with exception: ' . $e->getMessage() );
        }
    }

    /**
     * Fetch complete Square item data with related objects
     *
     * @param string $square_item_id Square item ID
     * @return array|WP_Error Square data or error
     */
    private function fetch_square_item_data( $square_item_id ) {
        $complete_data = $this->square_api->get_catalog_item_with_relations( $square_item_id );

        if ( is_wp_error( $complete_data ) ) {
            return new WP_Error( 'fetch_failed', 'Failed to fetch Square item data: ' . $complete_data->get_error_message() );
        }



        if ( empty( $complete_data['item'] ) ) {
            return new WP_Error( 'no_item_data', 'No item data found in Square response. Available keys: ' . implode( ', ', array_keys( $complete_data ) ) );
        }

        return $complete_data;
    }

    /**
     * Create or update WooCommerce product
     *
     * @param array $square_item Square item data
     * @param int|null $existing_product_id Existing product ID
     * @return array|WP_Error Product creation result or error
     */
    private function create_or_update_woocommerce_product( $square_item, $existing_product_id = null ) {
        $variations = $square_item['item_data']['variations'] ?? array();
        $is_variable = $this->should_be_variable_product( $square_item );

        // Find or create product
        if ( $existing_product_id ) {
            $product = wc_get_product( $existing_product_id );
            if ( ! $product ) {
                return new WP_Error( 'product_not_found', 'Existing product not found' );
            }
            $product_id = $existing_product_id;
        } else {
            // Check if product already exists by Square ID
            $existing_id = $this->find_existing_product_by_square_id( $square_item['id'] );
            if ( $existing_id ) {
                $product = wc_get_product( $existing_id );
                $product_id = $existing_id;
            } else {
                // Create new product
                $product = $is_variable ? new WC_Product_Variable() : new WC_Product_Simple();
                $product_id = null;
            }
        }

        // Handle product type changes
        if ( $product_id && $product->get_type() !== ( $is_variable ? 'variable' : 'simple' ) ) {
            $product = $this->convert_product_type( $product, $is_variable );
        }

        // Set basic product data
        $this->set_basic_product_data( $product, $square_item );

        // Save product
        $product_id = $product->save();
        if ( ! $product_id ) {
            return new WP_Error( 'product_save_failed', 'Failed to save product' );
        }

        return array(
            'product_id' => $product_id,
            'product' => $product,
            'is_variable' => $is_variable,
            'was_existing' => ! empty( $existing_product_id )
        );
    }

    /**
     * Determine if Square item should be a variable product
     * Updated to handle both Square variation strategies:
     * 1. Item Options Strategy - variations have item_option_values
     * 2. Variation Name Strategy - variations have unique names but no item_option_values
     *
     * @param array $square_item Square item data
     * @return bool True if should be variable
     */
    private function should_be_variable_product( $square_item ) {
        $variations = $square_item['item_data']['variations'] ?? array();

        // Must have more than 1 variation to be considered variable
        if ( count( $variations ) <= 1 ) {
            return false;
        }

        // Multiple variations should be treated as variable product
        // This handles both Square variation strategies:
        // 1. Item Options Strategy: variations have item_option_values populated
        // 2. Variation Name Strategy: variations have unique names but empty item_option_values
        //
        // According to Square documentation, both are valid ways to create variations
        // and both should result in variable products in WooCommerce
        return true;
    }

    /**
     * Reset import statistics
     */
    private function reset_import_stats() {
        $this->import_stats = array(
            'success' => false,
            'product_id' => null,
            'variations_imported' => 0,
            'attributes_imported' => 0,
            'modifiers_imported' => 0,
            'images_imported' => 0,
            'errors' => array()
        );
    }

    /**
     * Import variations for variable products
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_item Square item data
     * @param array $related_objects Related Square objects
     * @return array|WP_Error Import result or error
     */
    private function import_variations( $product_id, $square_item, $related_objects ) {
        $variations = $square_item['item_data']['variations'] ?? array();

        if ( empty( $variations ) ) {
            return new WP_Error( 'no_variations', 'No variations found in Square item' );
        }

        return $this->variation_importer->import_variations( $product_id, $variations, $related_objects );
    }

    /**
     * Import modifiers
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_item Square item data
     * @param array $related_objects Related Square objects
     * @return array|WP_Error Import result or error
     */
    private function import_modifiers( $product_id, $square_item, $related_objects ) {
        $modifier_list_info = $square_item['item_data']['modifier_list_info'] ?? array();

        if ( empty( $modifier_list_info ) ) {
            return array( 'modifiers_created' => 0 );
        }

        return $this->modifier_importer->import_modifiers( $product_id, $modifier_list_info, $related_objects );
    }

    /**
     * Import images using enhanced image handler with product name-based naming
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_item Square item data
     * @param array $related_objects Related Square objects
     * @return array|WP_Error Import result or error
     */
    private function import_images( $product_id, $square_item, $related_objects ) {
        $image_ids = $square_item['item_data']['image_ids'] ?? array();

        if ( empty( $image_ids ) ) {
            return array( 'images_imported' => 0 );
        }

        // Load enhanced image handler if not already loaded
        if ( ! class_exists( 'SquareKit_Image_Handler' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-image-handler.php';
        }

        $image_handler = new SquareKit_Image_Handler();

        // Get product name for filename generation
        $product = wc_get_product( $product_id );
        $product_name = $product ? $product->get_name() : '';

        // Get image URLs from Square API
        $image_map = $this->square_api->get_image_urls( $image_ids );
        $imported_count = 0;

        foreach ( $image_ids as $index => $image_id ) {
            if ( isset( $image_map[ $image_id ] ) ) {
                $image_url = $image_map[ $image_id ];

                // Use enhanced image handler with product name and position for proper naming
                $attachment_id = $image_handler->import_image_from_url(
                    $image_url,
                    $product_name,
                    $index
                );

                if ( $attachment_id && ! is_wp_error( $attachment_id ) ) {
                    $imported_count++;

                    // Store Square image ID
                    update_post_meta( $attachment_id, '_square_image_id', $image_id );

                    // Attach image to product
                    wp_update_post( array(
                        'ID' => $attachment_id,
                        'post_parent' => $product_id
                    ) );

                    // Set first image as featured image
                    if ( $index === 0 && $product ) {
                        $product->set_image_id( $attachment_id );
                        $product->save();
                    }
                }
            }
        }

        return array( 'images_imported' => $imported_count );
    }

    /**
     * Import categories
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_item Square item data
     * @param array $related_objects Related Square objects
     * @return array|WP_Error Import result or error
     */
    private function import_categories( $product_id, $square_item, $related_objects ) {
        $category_id = $square_item['item_data']['category_id'] ?? '';

        if ( empty( $category_id ) ) {
            return array( 'categories_imported' => 0 );
        }

        // Use existing WooCommerce integration method
        $assigned_categories = $this->wc_integration->import_category_hierarchy_and_assign( $category_id, $product_id );

        return array( 'categories_imported' => count( $assigned_categories ) );
    }

    /**
     * Set Square tracking meta fields
     *
     * @param int $product_id WooCommerce product ID
     * @param array $square_item Square item data
     */
    private function set_square_tracking_meta( $product_id, $square_item ) {
        // Set main Square item ID
        update_post_meta( $product_id, '_square_id', $square_item['id'] );

        // For simple products, also set the variation ID
        $variations = $square_item['item_data']['variations'] ?? array();
        if ( count( $variations ) === 1 ) {
            update_post_meta( $product_id, '_square_variation_id', $variations[0]['id'] );
        }

        // Set version for sync tracking
        if ( isset( $square_item['version'] ) ) {
            update_post_meta( $product_id, '_square_version', $square_item['version'] );
        }
    }

    /**
     * Find existing product by Square ID
     *
     * @param string $square_id Square item ID
     * @return int|false Product ID or false if not found
     */
    private function find_existing_product_by_square_id( $square_id ) {
        $query = new WP_Query( array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_square_id',
                    'value' => $square_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'fields' => 'ids'
        ) );

        return $query->have_posts() ? $query->posts[0] : false;
    }

    /**
     * Convert product type
     *
     * @param WC_Product $product Current product
     * @param bool $to_variable Convert to variable (true) or simple (false)
     * @return WC_Product New product object
     */
    private function convert_product_type( $product, $to_variable ) {
        $new_product = $to_variable ? new WC_Product_Variable() : new WC_Product_Simple();

        // Copy basic data
        $new_product->set_id( $product->get_id() );
        $new_product->set_name( $product->get_name() );
        $new_product->set_description( $product->get_description() );
        $new_product->set_short_description( $product->get_short_description() );
        $new_product->set_sku( $product->get_sku() );
        $new_product->set_status( $product->get_status() );

        return $new_product;
    }

    /**
     * Set basic product data from Square item
     *
     * @param WC_Product $product WooCommerce product
     * @param array $square_item Square item data
     */
    private function set_basic_product_data( $product, $square_item ) {
        $item_data = $square_item['item_data'];

        // Set name and description
        $product->set_name( $item_data['name'] ?? '' );
        $product->set_description( $item_data['description'] ?? '' );

        // Set pricing for simple products
        if ( $product->is_type( 'simple' ) ) {
            $variations = $item_data['variations'] ?? array();
            if ( ! empty( $variations[0]['item_variation_data']['price_money']['amount'] ) ) {
                $price = $variations[0]['item_variation_data']['price_money']['amount'] / 100;
                $product->set_regular_price( $price );
            }

            // Set SKU for simple products
            if ( ! empty( $variations[0]['item_variation_data']['sku'] ) ) {
                $product->set_sku( $variations[0]['item_variation_data']['sku'] );
            }
        }
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_stats() {
        return $this->import_stats;
    }

    /**
     * Import product using SWEVER-style architecture
     * This is the new recommended method that uses the proven SWEVER approach
     *
     * @param string $square_item_id Square item ID
     * @param array $data_to_import Import configuration
     * @param bool $update_only Whether to only update existing products
     * @return array|WP_Error Import result or error
     */
    public function import_product_swever_style( $square_item_id, $data_to_import = array(), $update_only = false ) {
        // Set default import configuration
        $default_config = array(
            'name' => true,
            'description' => true,
            'price' => true,
            'sku' => true,
            'categories' => true,
            'images' => true,
            'modifiers' => true,  // CRITICAL: Include modifiers in default config
            'attributesDisabled' => false
        );

        $data_to_import = array_merge( $default_config, $data_to_import );

        // Use the SWEVER-style orchestrator
        $result = $this->square_import->import_square_product( $square_item_id, $data_to_import, $update_only );

        // Update our import stats
        if ( is_wp_error( $result ) ) {
            if ( ! isset( $this->import_stats['failed'] ) ) {
                $this->import_stats['failed'] = 0;
            }
            $this->import_stats['failed']++;
            $this->import_stats['errors'][] = $result->get_error_message();
        } else {
            if ( ! isset( $this->import_stats['created'] ) ) {
                $this->import_stats['created'] = 0;
            }
            if ( ! isset( $this->import_stats['updated'] ) ) {
                $this->import_stats['updated'] = 0;
            }
            if ( ! isset( $this->import_stats['processed'] ) ) {
                $this->import_stats['processed'] = 0;
            }

            if ( $result['created'] ) {
                $this->import_stats['created']++;
            } else {
                $this->import_stats['updated']++;
            }
            $this->import_stats['processed']++;
        }

        return $result;
    }
}
