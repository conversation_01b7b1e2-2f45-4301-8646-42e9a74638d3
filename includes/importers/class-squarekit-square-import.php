<?php
/**
 * SquareKit Square Import Class
 *
 * Main orchestration class for Square product imports.
 * Based on SWEVER's proven architecture for successful Square-to-WooCommerce imports.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Square Import Class
 *
 * Orchestrates the complete Square product import process using SWEVER's proven approach.
 */
class SquareKit_Square_Import {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Option resolver instance
     *
     * @var SquareKit_Option_Resolver
     */
    private $option_resolver;

    /**
     * Product creator instance
     *
     * @var SquareKit_Create_Product
     */
    private $product_creator;

    /**
     * Import statistics
     *
     * @var array
     */
    private $import_stats = array(
        'products_processed' => 0,
        'products_created' => 0,
        'products_updated' => 0,
        'products_failed' => 0,
        'errors' => array()
    );

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Import validator instance
     *
     * @var SquareKit_Import_Validator
     */
    private $validator;

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize required dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        if ( ! class_exists( 'SquareKit_Option_Resolver' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-option-resolver.php';
        }

        if ( ! class_exists( 'SquareKit_Create_Product' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-create-product.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/core/class-squarekit-logger.php';
        }

        if ( ! class_exists( 'SquareKit_Import_Validator' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-import-validator.php';
        }

        $this->logger = new SquareKit_Logger();
        $this->validator = new SquareKit_Import_Validator();
        $this->square_api = new SquareKit_Square_API();
        $this->option_resolver = new SquareKit_Option_Resolver( $this->square_api );
        $this->product_creator = new SquareKit_Create_Product();
    }

    /**
     * Import a single Square product (SWEVER-style orchestration)
     *
     * @param string $square_item_id Square item ID
     * @param array $data_to_import Import configuration
     * @param bool $update_only Whether to only update existing products
     * @return array|WP_Error Import result or error
     */
    public function import_square_product( $square_item_id, $data_to_import = array(), $update_only = false ) {
        $this->import_stats['products_processed']++;

        $this->logger->log( 'import', 'info', "Starting import for Square item: {$square_item_id}" );

        try {
            // Step 1: Fetch Square product data with relations
            $this->logger->log( 'import', 'info', "Fetching Square product data for: {$square_item_id}" );
            $square_data = $this->square_api->get_catalog_item_with_relations( $square_item_id );

            if ( is_wp_error( $square_data ) ) {
                $error_message = $square_data->get_error_message();
                $this->logger->log( 'import', 'error', "Failed to fetch Square data: {$error_message}" );
                $this->import_stats['products_failed']++;
                $this->import_stats['errors'][] = $error_message;
                return $square_data;
            }

            $this->logger->log( 'import', 'info', "Successfully fetched Square data for: {$square_item_id}" );

            // Step 2: Validate Square product data
            $this->logger->log( 'import', 'info', "Validating Square product data for: {$square_item_id}" );
            if ( ! $this->is_product_valid( $square_data['item'] ) ) {
                $error_message = 'Square product data is invalid';
                $this->logger->log( 'import', 'error', "Validation failed: {$error_message}" );
                $error = new WP_Error( 'invalid_product', $error_message );
                $this->import_stats['products_failed']++;
                $this->import_stats['errors'][] = $error->get_error_message();
                return $error;
            }

            // Step 3: Transform Square product data (resolve option IDs to names)
            $this->logger->log( 'import', 'info', "Transforming Square product data (resolving option IDs)" );
            $transformed_item = $this->option_resolver->transform_product_data( $square_data['item'] );

            // Step 4: Map Square product to WooCommerce format
            $this->logger->log( 'import', 'info', "Mapping Square product to WooCommerce format" );
            $wc_product_data = $this->map_square_product_to_woocommerce(
                $transformed_item,
                $square_data['related_objects']
            );

            // Step 5: Create or update WooCommerce product
            $this->logger->log( 'import', 'info', "Creating/updating WooCommerce product" );
            $result = $this->product_creator->create_or_update_product(
                $wc_product_data,
                $data_to_import,
                $update_only
            );

            if ( is_wp_error( $result ) ) {
                $error_message = $result->get_error_message();
                $this->logger->log( 'import', 'error', "Product creation/update failed: {$error_message}" );
                $this->import_stats['products_failed']++;
                $this->import_stats['errors'][] = $error_message;
                return $result;
            }

            // Step 6: Validate imported product
            $this->logger->log( 'import', 'info', "Validating imported product: {$result['product_id']}" );
            $validation_results = $this->validator->validate_imported_product(
                $result['product_id'],
                $square_item_id,
                $this->extract_expected_data_from_wc_product_data( $wc_product_data )
            );

            // Update statistics
            if ( $result['created'] ) {
                $this->import_stats['products_created']++;
                $this->logger->log( 'import', 'success', "Successfully created product: {$result['product_id']} for Square item: {$square_item_id}" );
            } else {
                $this->import_stats['products_updated']++;
                $this->logger->log( 'import', 'success', "Successfully updated product: {$result['product_id']} for Square item: {$square_item_id}" );
            }

            // Add validation results to the result
            $result['validation'] = $validation_results;

            // Log validation summary
            if ( $validation_results['is_valid'] ) {
                $successes = $validation_results['summary']['successes'] ?? 0;
                $warnings = $validation_results['summary']['warnings'] ?? 0;
                $this->logger->log( 'import', 'success', "Product validation passed: {$successes} successes, {$warnings} warnings" );
            } else {
                $errors = $validation_results['summary']['errors'] ?? 0;
                $warnings = $validation_results['summary']['warnings'] ?? 0;
                $this->logger->log( 'import', 'warning', "Product validation failed: {$errors} errors, {$warnings} warnings" );
            }

            return $result;

        } catch ( Exception $e ) {
            $error_message = 'Import failed: ' . $e->getMessage();
            $this->logger->log( 'import', 'error', "Exception during import: {$error_message}" );
            $this->logger->log( 'import', 'error', "Stack trace: " . $e->getTraceAsString() );
            $error = new WP_Error( 'import_exception', $error_message );
            $this->import_stats['products_failed']++;
            $this->import_stats['errors'][] = $error->get_error_message();
            return $error;
        }
    }

    /**
     * Validate Square product data (SWEVER-style validation)
     *
     * @param array $square_item Square item data
     * @return bool True if valid, false otherwise
     */
    private function is_product_valid( $square_item ) {
        // Check basic required fields
        if ( empty( $square_item['id'] ) ) {
            return false;
        }

        if ( empty( $square_item['item_data']['name'] ) ) {
            return false;
        }

        // Check if item is deleted
        if ( isset( $square_item['is_deleted'] ) && $square_item['is_deleted'] ) {
            return false;
        }

        // Check if item has variations (required for our import)
        if ( empty( $square_item['item_data']['variations'] ) ) {
            return false;
        }

        return true;
    }

    /**
     * Map Square product to WooCommerce format (SWEVER-style mapping)
     *
     * @param array $square_item Transformed Square item data
     * @param array $related_objects Related Square objects
     * @return array WooCommerce product data
     */
    private function map_square_product_to_woocommerce( $square_item, $related_objects ) {
        // Determine product type using SWEVER-style logic
        $product_type = $this->determine_product_type( $square_item );

        $wc_product_data = array(
            'square_id' => $square_item['id'],
            'name' => $square_item['item_data']['name'],
            'description' => $square_item['item_data']['description'] ?? '',
            'type' => $product_type,
            'variations' => array(),
            'attributes' => array(),
            'categories' => array(),
            'images' => array(),
            'modifiers' => array()
        );

        // Map variations
        foreach ( $square_item['item_data']['variations'] as $variation ) {
            $wc_variation = array(
                'variation_square_id' => $variation['id'],
                'name' => $variation['item_variation_data']['name'] ?? '',
                'sku' => $variation['item_variation_data']['sku'] ?? '',
                'price' => 0,
                'attributes' => array()
            );

            // Set price
            if ( isset( $variation['item_variation_data']['price_money']['amount'] ) ) {
                $wc_variation['price'] = $variation['item_variation_data']['price_money']['amount'] / 100;
            }

            // Map attributes from resolved option data
            if ( isset( $variation['item_variation_data']['item_option_values'] ) && ! empty( $variation['item_variation_data']['item_option_values'] ) ) {
                // Item Options Strategy: Use Square options
                foreach ( $variation['item_variation_data']['item_option_values'] as $option_value ) {
                    if ( isset( $option_value['option_name'] ) && isset( $option_value['option_value'] ) ) {
                        $wc_variation['attributes'][] = array(
                            'name' => $option_value['option_name'],
                            'option' => $option_value['option_value']
                        );
                    }
                }
            } else {
                // Variation Name Strategy: Use variation name as default attribute
                $variation_name = $variation['item_variation_data']['name'] ?? '';
                if ( ! empty( $variation_name ) ) {
                    $wc_variation['attributes'][] = array(
                        'name' => 'Product Options',
                        'option' => $variation_name
                    );
                }
            }

            $wc_product_data['variations'][] = $wc_variation;
        }

        // Create product-level attributes for variation name strategy products
        if ( $product_type === 'variable' && ! empty( $wc_product_data['variations'] ) ) {
            $this->create_product_attributes_for_variations( $wc_product_data );
        }

        // Map categories with full hierarchy
        if ( isset( $related_objects['categories'] ) ) {
            $all_category_hierarchies = array();

            foreach ( $related_objects['categories'] as $category ) {
                // Get complete hierarchy for this category (including parents)
                $hierarchy = $this->get_category_hierarchy( $category['id'] );

                // Merge hierarchies, avoiding duplicates
                foreach ( $hierarchy as $hierarchy_category ) {
                    $category_id = $hierarchy_category['square_id'];
                    if ( ! isset( $all_category_hierarchies[$category_id] ) ) {
                        $all_category_hierarchies[$category_id] = $hierarchy_category;
                    }
                }
            }

            // Convert to indexed array for product creation
            $wc_product_data['categories'] = array_values( $all_category_hierarchies );
        }

        // Map images
        if ( isset( $related_objects['images'] ) ) {
            foreach ( $related_objects['images'] as $image ) {
                $wc_product_data['images'][] = array(
                    'square_id' => $image['id'],
                    'url' => $image['image_data']['url'] ?? ''
                );
            }
        }

        // Map modifiers - get modifiers directly from modifier list data
        if ( isset( $related_objects['modifier_lists'] ) ) {
            foreach ( $related_objects['modifier_lists'] as $modifier_list ) {
                // Get modifiers directly from the modifier list data (Square API structure)
                $modifier_objects = $modifier_list['modifier_list_data']['modifiers'] ?? array();

                $wc_product_data['modifiers'][] = array(
                    'square_id' => $modifier_list['id'],
                    'name' => $modifier_list['modifier_list_data']['name'] ?? '',
                    'selection_type' => $modifier_list['modifier_list_data']['selection_type'] ?? 'MULTIPLE',
                    'modifiers' => $modifier_objects
                );
            }
        }

        return $wc_product_data;
    }

    /**
     * Extract expected data from WooCommerce product data for validation
     *
     * @param array $wc_product_data WooCommerce product data
     * @return array Expected data for validation
     */
    private function extract_expected_data_from_wc_product_data( $wc_product_data ) {
        $expected_data = array(
            'name' => $wc_product_data['name'],
            'type' => $wc_product_data['type'],
            'variations' => $wc_product_data['variations'],
            'attributes' => array()
        );

        // Extract expected attributes from variations
        foreach ( $wc_product_data['variations'] as $variation ) {
            foreach ( $variation['attributes'] as $attribute ) {
                $attr_name = $attribute['name'];
                $attr_value = $attribute['option'];

                if ( ! isset( $expected_data['attributes'][$attr_name] ) ) {
                    $expected_data['attributes'][$attr_name] = array();
                }

                if ( ! in_array( $attr_value, $expected_data['attributes'][$attr_name] ) ) {
                    $expected_data['attributes'][$attr_name][] = $attr_value;
                }
            }
        }

        return $expected_data;
    }

    /**
     * Get complete category hierarchy for a Square category
     *
     * @param string $category_id Square category ID
     * @return array Complete hierarchy from root to leaf
     */
    private function get_category_hierarchy( $category_id ) {
        $hierarchy = array();
        $current_id = $category_id;

        // Build hierarchy from leaf to root
        while ( $current_id ) {
            $category_data = $this->square_api->get_catalog_item( $current_id );

            if ( is_wp_error( $category_data ) || empty( $category_data['category_data'] ) ) {
                $this->logger->log( 'import', 'warning', "Could not fetch category data for ID: {$current_id}" );
                break;
            }

            $category_info = $category_data['category_data'];
            $hierarchy[] = array(
                'square_id' => $current_id,
                'name' => $category_info['name'] ?? '',
                'description' => $category_info['description'] ?? '',
                'parent_square_id' => isset( $category_info['parent_category'] ) ? $category_info['parent_category']['id'] : null
            );

            // Move to parent category
            $current_id = isset( $category_info['parent_category'] ) ? $category_info['parent_category']['id'] : null;
        }

        // Reverse to get root-to-leaf order (parents first)
        return array_reverse( $hierarchy );
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_stats() {
        return $this->import_stats;
    }

    /**
     * Reset import statistics
     */
    public function reset_import_stats() {
        $this->import_stats = array(
            'products_processed' => 0,
            'products_created' => 0,
            'products_updated' => 0,
            'products_failed' => 0,
            'errors' => array()
        );
    }

    /**
     * Determine product type using SWEVER-style logic
     * Updated to handle both Square variation strategies:
     * 1. Item Options Strategy - variations have item_option_values
     * 2. Variation Name Strategy - variations have unique names but no item_option_values
     *
     * @param array $square_item Square item data
     * @return string Product type ('simple' or 'variable')
     */
    private function determine_product_type( $square_item ) {
        $variations = $square_item['item_data']['variations'] ?? array();

        // If only one variation, it's always simple
        if ( count( $variations ) <= 1 ) {
            return 'simple';
        }

        // Multiple variations should be treated as variable product
        // This handles both Square variation strategies:
        // 1. Item Options Strategy: variations have item_option_values populated
        // 2. Variation Name Strategy: variations have unique names but empty item_option_values
        //
        // According to Square documentation, both are valid ways to create variations
        // and both should result in variable products in WooCommerce
        return 'variable';
    }

    /**
     * Create product-level attributes from variation data
     * This ensures that variation name strategy products have proper attributes
     *
     * @param array $wc_product_data WooCommerce product data (passed by reference)
     */
    private function create_product_attributes_for_variations( &$wc_product_data ) {
        $attributes_by_name = array();

        // Collect all unique attributes from variations
        foreach ( $wc_product_data['variations'] as $variation ) {
            foreach ( $variation['attributes'] as $attribute ) {
                $attr_name = $attribute['name'];
                $attr_option = $attribute['option'];

                if ( ! isset( $attributes_by_name[$attr_name] ) ) {
                    $attributes_by_name[$attr_name] = array();
                }

                if ( ! in_array( $attr_option, $attributes_by_name[$attr_name] ) ) {
                    $attributes_by_name[$attr_name][] = $attr_option;
                }
            }
        }

        // Convert to product attribute format
        foreach ( $attributes_by_name as $attr_name => $options ) {
            $wc_product_data['attributes'][] = array(
                'name' => $attr_name,
                'options' => $options,
                'visible' => true,
                'variation' => true
            );
        }

        $this->logger->log( 'import', 'info', 'Created product attributes for variation name strategy: ' . implode( ', ', array_keys( $attributes_by_name ) ) );
    }
}
