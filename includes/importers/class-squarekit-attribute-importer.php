<?php
/**
 * SquareKit Attribute Importer
 * 
 * Handles importing Square item options to WooCommerce attributes
 * with proper validation and error handling.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Attribute Importer Class
 */
class SquareKit_Attribute_Importer {

    /**
     * Attribute mapper instance
     * @var SquareKit_Attribute_Mapper
     */
    private $attribute_mapper;

    /**
     * Square API instance
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Import statistics
     * @var array
     */
    private $import_stats = array(
        'attributes_created' => 0,
        'attributes_updated' => 0,
        'terms_created' => 0,
        'terms_updated' => 0,
        'errors' => array()
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize required dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Attribute_Mapper' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-attribute-mapper.php';
        }
        
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }

        $this->attribute_mapper = new SquareKit_Attribute_Mapper();
        $this->square_api = new SquareKit_Square_API();
    }

    /**
     * Import attributes from Square item variations
     *
     * @param array $variations Square item variations
     * @param array $related_objects Related Square objects (item options, etc.)
     * @return array|WP_Error Array of attribute mappings or error
     */
    public function import_attributes_from_variations( $variations, $related_objects = array() ) {
        $this->reset_import_stats();
        
        if ( empty( $variations ) || ! is_array( $variations ) ) {
            return new WP_Error( 'invalid_variations', __( 'Invalid variations data provided.', 'squarekit' ) );
        }

        $attribute_mappings = array();
        $processed_option_sets = array();

        // Step 1: Extract all unique item option values from variations
        $option_values_by_set = $this->extract_option_values_from_variations( $variations );
        
        if ( empty( $option_values_by_set ) ) {
            return new WP_Error( 'no_options', __( 'No item options found in variations.', 'squarekit' ) );
        }

        // Step 2: Process each option set
        foreach ( $option_values_by_set as $option_set_id => $option_values ) {
            $result = $this->process_option_set( $option_set_id, $option_values, $related_objects );
            
            if ( is_wp_error( $result ) ) {
                $this->import_stats['errors'][] = sprintf(
                    __( 'Failed to process option set %s: %s', 'squarekit' ),
                    $option_set_id,
                    $result->get_error_message()
                );
                continue;
            }

            $attribute_mappings[ $option_set_id ] = $result;
            $processed_option_sets[] = $option_set_id;
        }

        if ( empty( $attribute_mappings ) ) {
            return new WP_Error( 'no_attributes_created', __( 'No attributes could be created from the provided variations.', 'squarekit' ) );
        }

        return array(
            'mappings' => $attribute_mappings,
            'stats' => $this->import_stats,
            'processed_sets' => $processed_option_sets
        );
    }

    /**
     * Extract option values from variations grouped by option set
     *
     * @param array $variations Square item variations
     * @return array Option values grouped by option set ID
     */
    private function extract_option_values_from_variations( $variations ) {
        $option_values_by_set = array();

        foreach ( $variations as $variation ) {
            $item_option_values = $variation['item_variation_data']['item_option_values'] ?? array();
            
            foreach ( $item_option_values as $option_value ) {
                $option_id = $option_value['item_option_id'] ?? '';

                // Square API uses different field names - try multiple possibilities
                $option_value_id = $option_value['item_option_value_id'] ??
                                  $option_value['value'] ??
                                  $option_value['id'] ?? '';

                // Also try to get the value name for debugging
                $option_value_name = $option_value['value'] ??
                                    $option_value['name'] ??
                                    $option_value['item_option_value_data']['name'] ?? '';

                if ( empty( $option_id ) ) {
                    error_log( 'SquareKit: Missing item_option_id in variation option value: ' . print_r( $option_value, true ) );
                    continue;
                }

                if ( empty( $option_value_id ) && empty( $option_value_name ) ) {
                    error_log( 'SquareKit: Missing option value identifier in: ' . print_r( $option_value, true ) );
                    continue;
                }

                // Use the value name as ID if no proper ID is found
                if ( empty( $option_value_id ) && ! empty( $option_value_name ) ) {
                    $option_value_id = sanitize_title( $option_value_name );
                }

                // Group by option set (item_option_id represents the option set)
                if ( ! isset( $option_values_by_set[ $option_id ] ) ) {
                    $option_values_by_set[ $option_id ] = array();
                }

                // Store unique option values for this set
                if ( ! in_array( $option_value_id, $option_values_by_set[ $option_id ] ) ) {
                    $option_values_by_set[ $option_id ][] = $option_value_id;
                    error_log( 'SquareKit: Added option value ' . $option_value_id . ' to set ' . $option_id );
                }
            }
        }

        return $option_values_by_set;
    }

    /**
     * Process a single option set and create WooCommerce attribute
     *
     * @param string $option_set_id Square option set ID
     * @param array $option_value_ids Array of option value IDs
     * @param array $related_objects Related Square objects
     * @return array|WP_Error Attribute mapping data or error
     */
    private function process_option_set( $option_set_id, $option_value_ids, $related_objects ) {
        // Get option set data from related objects or API
        $option_set_data = $this->get_option_set_data( $option_set_id, $related_objects );
        
        if ( ! $option_set_data ) {
            return new WP_Error( 'option_set_not_found', sprintf(
                __( 'Option set %s not found in related objects or API.', 'squarekit' ),
                $option_set_id
            ) );
        }

        // Create or get WooCommerce attribute
        $attribute_id = $this->attribute_mapper->map_option_set_to_attribute( $option_set_data, true );
        
        if ( ! $attribute_id ) {
            return new WP_Error( 'attribute_creation_failed', sprintf(
                __( 'Failed to create WooCommerce attribute for option set %s.', 'squarekit' ),
                $option_set_id
            ) );
        }

        $this->import_stats['attributes_created']++;

        // Process option values (terms)
        $term_mappings = array();
        foreach ( $option_value_ids as $option_value_id ) {
            $option_value_data = $this->get_option_value_data( $option_value_id, $related_objects );
            
            if ( ! $option_value_data ) {
                $this->import_stats['errors'][] = sprintf(
                    __( 'Option value %s not found for option set %s.', 'squarekit' ),
                    $option_value_id,
                    $option_set_id
                );
                continue;
            }

            $term_id = $this->attribute_mapper->map_option_to_term( $option_value_data, $attribute_id, true );
            
            if ( $term_id ) {
                $term_mappings[ $option_value_id ] = $term_id;
                $this->import_stats['terms_created']++;
            } else {
                $this->import_stats['errors'][] = sprintf(
                    __( 'Failed to create term for option value %s.', 'squarekit' ),
                    $option_value_id
                );
            }
        }

        return array(
            'attribute_id' => $attribute_id,
            'option_set_id' => $option_set_id,
            'term_mappings' => $term_mappings,
            'attribute_name' => $option_set_data['item_option_data']['name'] ?? ''
        );
    }

    /**
     * Get option set data from related objects or API
     *
     * @param string $option_set_id Square option set ID
     * @param array $related_objects Related Square objects
     * @return array|false Option set data or false if not found
     */
    private function get_option_set_data( $option_set_id, $related_objects ) {
        // First check in related objects
        foreach ( $related_objects as $object ) {
            if ( $object['id'] === $option_set_id && $object['type'] === 'ITEM_OPTION' ) {
                return $object;
            }
        }

        // If not found, try to fetch from API
        try {
            return $this->square_api->get_catalog_object( $option_set_id );
        } catch ( Exception $e ) {
            $this->import_stats['errors'][] = sprintf(
                __( 'API error fetching option set %s: %s', 'squarekit' ),
                $option_set_id,
                $e->getMessage()
            );
            return false;
        }
    }

    /**
     * Get option value data from related objects or API
     *
     * @param string $option_value_id Square option value ID
     * @param array $related_objects Related Square objects
     * @return array|false Option value data or false if not found
     */
    private function get_option_value_data( $option_value_id, $related_objects ) {
        // First check in related objects
        foreach ( $related_objects as $object ) {
            if ( $object['id'] === $option_value_id && $object['type'] === 'ITEM_OPTION_VAL' ) {
                return $object;
            }
        }

        // If not found, try to fetch from API
        try {
            return $this->square_api->get_catalog_object( $option_value_id );
        } catch ( Exception $e ) {
            $this->import_stats['errors'][] = sprintf(
                __( 'API error fetching option value %s: %s', 'squarekit' ),
                $option_value_id,
                $e->getMessage()
            );
            return false;
        }
    }

    /**
     * Reset import statistics
     */
    private function reset_import_stats() {
        $this->import_stats = array(
            'attributes_created' => 0,
            'attributes_updated' => 0,
            'terms_created' => 0,
            'terms_updated' => 0,
            'errors' => array()
        );
    }

    /**
     * Get import statistics
     *
     * @return array Import statistics
     */
    public function get_import_stats() {
        return $this->import_stats;
    }

    /**
     * Get or create WooCommerce attribute (SWEVER-style implementation)
     * This is the proven method that ensures successful attribute creation
     *
     * @param string $attribute_name The name of the attribute
     * @param array $terms Array of term slugs or term IDs to associate with the attribute
     * @param WC_Product $product The product object for local attributes
     * @return WC_Product_Attribute|bool The attribute object if successful, or false on failure
     */
    public function get_or_create_attribute( $attribute_name, $terms, $product ) {
        // Normalize attribute name casing
        $attribute_name = strtolower( $attribute_name );

        // Sanitize attribute name for taxonomy
        $attribute_taxonomy = wc_sanitize_taxonomy_name( $attribute_name );
        $taxonomy = 'pa_' . $attribute_taxonomy;

        // Convert slugs to term IDs and keep existing IDs
        $terms_with_ids = array();
        foreach ( $terms as $term ) {
            if ( is_numeric( $term ) ) {
                $terms_with_ids[] = $term;
            } else {
                $term_obj = get_term_by( 'slug', $term, $taxonomy );
                if ( ! $term_obj ) {
                    $term_data = wp_insert_term( $term, $taxonomy );
                    if ( ! is_wp_error( $term_data ) ) {
                        $terms_with_ids[] = $term_data['term_id'];
                        $this->import_stats['terms_created']++;
                    }
                } else {
                    $terms_with_ids[] = $term_obj->term_id;
                }
            }
        }

        // Check if local attribute exists on the product
        $existing_attributes = $product->get_attributes();

        if ( isset( $existing_attributes[$attribute_name] ) ) {
            $local_attribute = $existing_attributes[$attribute_name];
            $existing_terms = $local_attribute->get_options();
            $merged_terms_with_ids = array_unique( array_merge( $existing_terms, $terms_with_ids ) );
            $local_attribute->set_options( $merged_terms_with_ids );
            $this->import_stats['attributes_updated']++;
            return $local_attribute;
        }

        // Check if the taxonomy exists globally
        if ( taxonomy_exists( $taxonomy ) ) {
            // Get the ID of the existing global attribute
            $attribute_id = wc_attribute_taxonomy_id_by_name( $taxonomy );

            // Check if attribute ID is valid
            if ( ! $attribute_id ) {
                error_log( "SquareKit: Invalid global attribute ID for taxonomy: $taxonomy" );
                return false;
            }

            // Fetch existing terms from the product for this attribute
            $existing_terms = array();
            if ( isset( $existing_attributes[$taxonomy] ) ) {
                $existing_terms = $existing_attributes[$taxonomy]->get_options();
            }

            $merged_terms_with_ids = array_unique( array_merge( $existing_terms, $terms_with_ids ) );

            $attribute = new WC_Product_Attribute();
            $attribute->set_id( $attribute_id );
            $attribute->set_name( $taxonomy );
            $attribute->set_options( $merged_terms_with_ids ); // Set term IDs as options
            $attribute->set_position( 0 );
            $attribute->set_visible( true );
            $attribute->set_variation( true );

            $this->import_stats['attributes_updated']++;
            return $attribute;
        } else {
            // Create the global attribute if it doesn't exist
            $attribute_data = array(
                'name'         => ucfirst( $attribute_name ),
                'slug'         => $attribute_taxonomy, // No 'pa_' prefix
                'type'         => 'select',
                'order_by'     => 'menu_order',
                'has_archives' => 1, // 1 means it will be a global attribute
            );

            // Check if the attribute data is correct
            if ( empty( $attribute_data['name'] ) ) {
                error_log( "SquareKit: Attribute name is missing in the attribute data" );
                return false;
            }

            $attribute_id = wc_create_attribute( $attribute_data );

            if ( is_wp_error( $attribute_id ) ) {
                error_log( "SquareKit: Error creating global attribute: " . $attribute_id->get_error_message() );
                $this->import_stats['errors'][] = $attribute_id->get_error_message();
                return false;
            }

            // Register the taxonomy for the new attribute
            register_taxonomy(
                $taxonomy,
                'product',
                array(
                    'labels' => array(
                        'name' => ucfirst( $attribute_name ),
                    ),
                    'hierarchical' => false,
                    'public' => true,
                    'show_ui' => false,
                    'show_in_nav_menus' => false,
                    'query_var' => true,
                    'rewrite' => false,
                    'sort' => false,
                )
            );

            $attribute = new WC_Product_Attribute();
            $attribute->set_id( $attribute_id );
            $attribute->set_name( $taxonomy );
            $attribute->set_options( $terms_with_ids );
            $attribute->set_position( 0 );
            $attribute->set_visible( true );
            $attribute->set_variation( true );

            $this->import_stats['attributes_created']++;
            return $attribute;
        }
    }

    /**
     * Process transformed Square product data to create WooCommerce attributes
     * This method handles both Square variation strategies:
     * 1. Item Options Strategy - uses Square options with option_name and option_value
     * 2. Variation Name Strategy - uses variation names as default "Product Options"
     *
     * @param array $square_item Transformed Square item data
     * @param WC_Product $product WooCommerce product object
     * @return array Array of created attributes
     */
    public function process_square_attributes( $square_item, $product ) {
        $attributes = array();

        if ( ! isset( $square_item['item_data']['variations'] ) || ! is_array( $square_item['item_data']['variations'] ) ) {
            return $attributes;
        }

        // Load default attribute handler
        if ( ! class_exists( 'SquareKit_Default_Attribute_Handler' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-default-attribute-handler.php';
        }

        $default_handler = new SquareKit_Default_Attribute_Handler();

        // Check if this uses Variation Name Strategy
        if ( $default_handler->uses_variation_name_strategy( $square_item ) ) {
            // Create default "Product Options" attribute
            $default_attribute = $default_handler->create_default_product_options_attribute(
                $square_item,
                $product->get_id()
            );

            if ( $default_attribute ) {
                $attribute_slug = $default_handler->get_default_attribute_slug();
                $attributes[$attribute_slug] = $default_attribute;

                if ( class_exists( 'SquareKit_Logger' ) ) {
                    $logger = new SquareKit_Logger();
                    $logger->log( 'attribute_import', 'success', 'Created default "Product Options" attribute for variation name strategy product' );
                }
            }
        } else {
            // Use existing logic for Item Options Strategy
            $attributes = $this->process_item_options_strategy( $square_item, $product );
        }

        return $attributes;
    }

    /**
     * Process Square products using Item Options Strategy
     * This is the original logic for products with Square options
     *
     * @param array $square_item Square item data
     * @param WC_Product $product WooCommerce product object
     * @return array Array of created attributes
     */
    private function process_item_options_strategy( $square_item, $product ) {
        $attributes = array();

        // Group option values by option name
        $options_by_name = array();

        foreach ( $square_item['item_data']['variations'] as $variation ) {
            if ( ! isset( $variation['item_variation_data']['item_option_values'] ) ) {
                continue;
            }

            foreach ( $variation['item_variation_data']['item_option_values'] as $option_value ) {
                // This is the key: we expect option_name and option_value to be resolved
                if ( isset( $option_value['option_name'] ) && isset( $option_value['option_value'] ) ) {
                    $option_name = $option_value['option_name'];
                    $option_value_name = $option_value['option_value'];

                    // Convert attribute values to proper title case for consistency
                    $option_value_name = $this->normalize_attribute_value( $option_value_name );

                    if ( ! isset( $options_by_name[$option_name] ) ) {
                        $options_by_name[$option_name] = array();
                    }

                    if ( ! in_array( $option_value_name, $options_by_name[$option_name] ) ) {
                        $options_by_name[$option_name][] = $option_value_name;
                    }
                }
            }
        }

        // Create attributes for each option
        foreach ( $options_by_name as $option_name => $option_values ) {
            $attribute = $this->get_or_create_attribute( $option_name, $option_values, $product );
            if ( $attribute ) {
                $attributes[$attribute->get_name()] = $attribute;
            }
        }

        return $attributes;
    }

    /**
     * Validate attribute data before import
     *
     * @param array $attribute_data Attribute data to validate
     * @return bool|WP_Error True if valid, WP_Error if invalid
     */
    public function validate_attribute_data( $attribute_data ) {
        if ( empty( $attribute_data['item_option_data']['name'] ) ) {
            return new WP_Error( 'missing_attribute_name', __( 'Attribute name is required.', 'squarekit' ) );
        }

        if ( empty( $attribute_data['id'] ) ) {
            return new WP_Error( 'missing_attribute_id', __( 'Attribute ID is required.', 'squarekit' ) );
        }

        return true;
    }

    /**
     * Validate option value data before import
     *
     * @param array $option_value_data Option value data to validate
     * @return bool|WP_Error True if valid, WP_Error if invalid
     */
    public function validate_option_value_data( $option_value_data ) {
        if ( empty( $option_value_data['item_option_value_data']['name'] ) ) {
            return new WP_Error( 'missing_option_value_name', __( 'Option value name is required.', 'squarekit' ) );
        }

        if ( empty( $option_value_data['id'] ) ) {
            return new WP_Error( 'missing_option_value_id', __( 'Option value ID is required.', 'squarekit' ) );
        }

        return true;
    }

    /**
     * Normalize attribute value to proper title case
     *
     * @param string $value Raw attribute value from Square
     * @return string Normalized attribute value
     */
    private function normalize_attribute_value( $value ) {
        // Trim whitespace
        $value = trim( $value );

        // Convert to title case for consistency
        // Handle special cases for common size/measurement values
        $value = strtolower( $value );

        // Special handling for common attribute values
        $special_cases = array(
            'xs' => 'XS',
            'sm' => 'SM',
            's' => 'S',
            'm' => 'M',
            'l' => 'L',
            'xl' => 'XL',
            'xxl' => 'XXL',
            'xxxl' => 'XXXL',
            'small' => 'Small',
            'medium' => 'Medium',
            'large' => 'Large',
            'extra small' => 'Extra Small',
            'extra large' => 'Extra Large',
            'light' => 'Light',
            'medium roast' => 'Medium Roast',
            'dark' => 'Dark',
            'dark roast' => 'Dark Roast',
            'decaf' => 'Decaf',
            'regular' => 'Regular'
        );

        // Check for exact matches first
        if ( isset( $special_cases[ $value ] ) ) {
            return $special_cases[ $value ];
        }

        // Default to title case
        return ucwords( $value );
    }
}
