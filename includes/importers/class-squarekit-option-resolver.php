<?php
/**
 * SquareKit Option Resolver Class
 *
 * Handles resolution of Square option IDs to meaningful names and values.
 * Based on SWEVER's proven approach for successful Square product imports.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Option Resolver Class
 *
 * Transforms raw Square API responses by resolving option IDs to names and values.
 * This preprocessing step is critical for successful WooCommerce attribute creation.
 */
class SquareKit_Option_Resolver {

    /**
     * Square API instance
     *
     * @var SquareKit_Square_API
     */
    private $square_api;

    /**
     * Cached option lookup table
     * Maps option_id => option_name
     *
     * @var array
     */
    private $option_lookup = array();

    /**
     * Cached option value lookup table
     * Maps option_value_id => option_value_name
     *
     * @var array
     */
    private $option_value_lookup = array();

    /**
     * Whether lookup tables have been initialized
     *
     * @var bool
     */
    private $initialized = false;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Constructor
     *
     * @param SquareKit_Square_API $square_api Square API instance
     */
    public function __construct( $square_api = null ) {
        if ( $square_api ) {
            $this->square_api = $square_api;
        } else {
            // Create default instance if none provided
            if ( ! class_exists( 'SquareKit_Square_API' ) ) {
                require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
            }
            $this->square_api = new SquareKit_Square_API();
        }

        // Initialize logger
        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/core/class-squarekit-logger.php';
        }
        $this->logger = new SquareKit_Logger();
    }

    /**
     * Initialize lookup tables by fetching all options and option values from Square
     * This is done once and cached for the entire import process
     *
     * @return bool True on success, false on failure
     */
    public function initialize_lookup_tables() {
        if ( $this->initialized ) {
            return true;
        }

        try {
            $this->logger->log( 'option_resolver', 'info', 'Initializing option lookup tables' );

            // Fetch all item options
            $item_options = $this->square_api->fetch_square_item_options();
            $this->logger->log( 'option_resolver', 'info', 'Fetched ' . count( $item_options ) . ' item options' );

            foreach ( $item_options as $option_id => $option_data ) {
                if ( isset( $option_data['item_option_data']['name'] ) ) {
                    $this->option_lookup[$option_id] = $option_data['item_option_data']['name'];
                }

                // Also extract option values from the option data
                if ( isset( $option_data['item_option_data']['values'] ) ) {
                    foreach ( $option_data['item_option_data']['values'] as $value_data ) {
                        if ( isset( $value_data['id'] ) && isset( $value_data['item_option_value_data']['name'] ) ) {
                            $this->option_value_lookup[$value_data['id']] = $value_data['item_option_value_data']['name'];
                        }
                    }
                }
            }

            // Fetch all item option values (additional coverage)
            $item_option_values = $this->square_api->fetch_square_item_option_values();
            $this->logger->log( 'option_resolver', 'info', 'Fetched ' . count( $item_option_values ) . ' item option values' );

            foreach ( $item_option_values as $value_id => $value_data ) {
                if ( isset( $value_data['item_option_value_data']['name'] ) ) {
                    $this->option_value_lookup[$value_id] = $value_data['item_option_value_data']['name'];
                }
            }

            $this->initialized = true;
            $this->logger->log( 'option_resolver', 'success', 'Successfully initialized lookup tables with ' . count( $this->option_lookup ) . ' options and ' . count( $this->option_value_lookup ) . ' option values' );
            return true;

        } catch ( Exception $e ) {
            $error_message = 'Failed to initialize lookup tables: ' . $e->getMessage();
            $this->logger->log( 'option_resolver', 'error', $error_message );
            $this->logger->log( 'option_resolver', 'error', 'Stack trace: ' . $e->getTraceAsString() );
            return false;
        }
    }

    /**
     * Transform Square product data by resolving option IDs to names
     * This is the key preprocessing step that makes import successful
     *
     * @param array $square_product Raw Square product data
     * @return array Enhanced Square product data with resolved option names
     */
    public function transform_product_data( $square_product ) {
        // Ensure lookup tables are initialized
        if ( ! $this->initialize_lookup_tables() ) {
            return $square_product; // Return unchanged if initialization failed
        }

        // Process variations to add option_name and option_value
        if ( isset( $square_product['item_data']['variations'] ) && is_array( $square_product['item_data']['variations'] ) ) {
            foreach ( $square_product['item_data']['variations'] as &$variation ) {
                if ( isset( $variation['item_variation_data']['item_option_values'] ) ) {
                    foreach ( $variation['item_variation_data']['item_option_values'] as &$option_value ) {
                        // Resolve option_id to option_name
                        if ( isset( $option_value['item_option_id'] ) ) {
                            $option_id = $option_value['item_option_id'];
                            if ( isset( $this->option_lookup[$option_id] ) ) {
                                $option_value['option_name'] = $this->option_lookup[$option_id];
                            }
                        }

                        // Resolve option_value_id to option_value
                        if ( isset( $option_value['item_option_value_id'] ) ) {
                            $value_id = $option_value['item_option_value_id'];
                            if ( isset( $this->option_value_lookup[$value_id] ) ) {
                                $option_value['option_value'] = $this->option_value_lookup[$value_id];
                            }
                        }
                    }
                }
            }
        }

        return $square_product;
    }

    /**
     * Get option name by option ID
     *
     * @param string $option_id Square option ID
     * @return string|null Option name or null if not found
     */
    public function get_option_name( $option_id ) {
        if ( ! $this->initialize_lookup_tables() ) {
            return null;
        }

        return isset( $this->option_lookup[$option_id] ) ? $this->option_lookup[$option_id] : null;
    }

    /**
     * Get option value name by option value ID
     *
     * @param string $option_value_id Square option value ID
     * @return string|null Option value name or null if not found
     */
    public function get_option_value_name( $option_value_id ) {
        if ( ! $this->initialize_lookup_tables() ) {
            return null;
        }

        return isset( $this->option_value_lookup[$option_value_id] ) ? $this->option_value_lookup[$option_value_id] : null;
    }

    /**
     * Get debug information about loaded lookup tables
     *
     * @return array Debug information
     */
    public function get_debug_info() {
        return array(
            'initialized' => $this->initialized,
            'option_count' => count( $this->option_lookup ),
            'option_value_count' => count( $this->option_value_lookup ),
            'options' => $this->option_lookup,
            'option_values' => $this->option_value_lookup
        );
    }
}
