<?php
/**
 * SquareKit Create Product Class
 *
 * Handles WooCommerce product creation and updates.
 * Based on SWEVER's proven CreateProduct class for successful Square-to-WooCommerce imports.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Create Product Class
 *
 * Creates and updates WooCommerce products from Square data using SWEVER's proven approach.
 */
class SquareKit_Create_Product {

    /**
     * Attribute importer instance
     *
     * @var SquareKit_Attribute_Importer
     */
    private $attribute_importer;

    /**
     * Variation importer instance
     *
     * @var SquareKit_Variation_Importer
     */
    private $variation_importer;

    /**
     * Modifier importer instance
     *
     * @var SquareKit_Modifier_Importer
     */
    private $modifier_importer;

    /**
     * Logger instance
     *
     * @var SquareKit_Logger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_dependencies();
    }

    /**
     * Initialize required dependencies
     */
    private function init_dependencies() {
        if ( ! class_exists( 'SquareKit_Attribute_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-attribute-importer.php';
        }

        if ( ! class_exists( 'SquareKit_Variation_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-variation-importer.php';
        }

        if ( ! class_exists( 'SquareKit_Modifier_Importer' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-modifier-importer.php';
        }

        if ( ! class_exists( 'SquareKit_Logger' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/core/class-squarekit-logger.php';
        }

        $this->logger = new SquareKit_Logger();
        $this->attribute_importer = new SquareKit_Attribute_Importer();
        $this->variation_importer = new SquareKit_Variation_Importer();
        $this->modifier_importer = new SquareKit_Modifier_Importer();
    }

    /**
     * Create or update WooCommerce product (SWEVER-style)
     *
     * @param array $wc_product_data WooCommerce product data
     * @param array $data_to_import Import configuration
     * @param bool $update_only Whether to only update existing products
     * @return array|WP_Error Result array or error
     */
    public function create_or_update_product( $wc_product_data, $data_to_import = array(), $update_only = false ) {
        $square_id = $wc_product_data['square_id'];
        
        // Check if product already exists
        $existing_product_id = $this->get_existing_product_id( $square_id );
        
        if ( $update_only && ! $existing_product_id ) {
            return new WP_Error( 'product_not_found', 'Product not found for update-only mode' );
        }

        try {
            if ( $existing_product_id ) {
                // Update existing product
                $product = wc_get_product( $existing_product_id );
                if ( ! $product ) {
                    return new WP_Error( 'invalid_product', 'Existing product is invalid' );
                }
                $created = false;
            } else {
                // Create new product with correct type
                $product_type = $wc_product_data['type'] === 'variable' ? 'variable' : 'simple';

                if ( $product_type === 'variable' ) {
                    $product = new WC_Product_Variable();
                } else {
                    $product = new WC_Product_Simple();
                }
                $created = true;
            }

            // Set basic product data
            $this->set_basic_product_data( $product, $wc_product_data, $data_to_import );

            // Save product to get ID
            $product->save();
            $product_id = $product->get_id();

            // Handle variable product
            if ( $wc_product_data['type'] === 'variable' ) {
                $this->handle_variable_product( $product, $wc_product_data, $data_to_import, $update_only );
            } else {
                $this->handle_simple_product( $product, $wc_product_data, $data_to_import );
            }

            // Import categories
            if ( isset( $data_to_import['categories'] ) && $data_to_import['categories'] && ! empty( $wc_product_data['categories'] ) ) {
                $this->import_product_categories( $product, $wc_product_data['categories'] );
            }

            // Import images
            if ( isset( $data_to_import['images'] ) && $data_to_import['images'] && ! empty( $wc_product_data['images'] ) ) {
                $this->import_product_images( $product, $wc_product_data['images'] );
            }

            // Import modifiers
            if ( isset( $data_to_import['modifiers'] ) && $data_to_import['modifiers'] && ! empty( $wc_product_data['modifiers'] ) ) {
                $this->modifier_importer->process_square_modifiers( $product_id, $wc_product_data );
            }

            // Final save
            $product->save();

            return array(
                'product_id' => $product_id,
                'created' => $created,
                'square_id' => $square_id
            );

        } catch ( Exception $e ) {
            $error_message = 'Product creation failed: ' . $e->getMessage();
            $this->logger->log( 'import', 'error', $error_message );
            $this->logger->log( 'import', 'error', 'Stack trace: ' . $e->getTraceAsString() );

            // If product creation failed and we created a new product, delete it
            if ( isset( $product_id ) && $created ) {
                $this->logger->log( 'import', 'info', "Cleaning up failed product creation: {$product_id}" );
                wp_delete_post( $product_id, true );
            }

            return new WP_Error( 'product_creation_failed', $error_message );
        }
    }

    /**
     * Set basic product data
     *
     * @param WC_Product $product WooCommerce product
     * @param array $wc_product_data Product data
     * @param array $data_to_import Import configuration
     */
    private function set_basic_product_data( $product, $wc_product_data, $data_to_import ) {
        // Set name
        if ( isset( $data_to_import['name'] ) && $data_to_import['name'] ) {
            $product->set_name( $wc_product_data['name'] );
        }

        // Set description
        if ( isset( $data_to_import['description'] ) && $data_to_import['description'] && ! empty( $wc_product_data['description'] ) ) {
            $product->set_description( $wc_product_data['description'] );
        }

        // Set Square ID meta
        $product->update_meta_data( '_square_item_id', $wc_product_data['square_id'] );

        // Set status
        $product->set_status( 'publish' );
        $product->set_catalog_visibility( 'visible' );
    }

    /**
     * Handle variable product (SWEVER-style)
     *
     * @param WC_Product $product WooCommerce product
     * @param array $wc_product_data Product data
     * @param array $data_to_import Import configuration
     * @param bool $update_only Update only flag
     */
    private function handle_variable_product( $product, $wc_product_data, $data_to_import, $update_only ) {
        $product_id = $product->get_id();

        // Convert to variable product if needed
        if ( $product->is_type( 'simple' ) ) {
            wp_set_object_terms( $product_id, 'variable', 'product_type' );
            $product = wc_get_product( $product_id ); // Refresh product object
        }

        // Create attributes from variations
        if ( ! isset( $data_to_import['attributesDisabled'] ) || ! $data_to_import['attributesDisabled'] ) {
            $attributes = $this->create_attributes_from_variations( $wc_product_data['variations'], $product );
            
            if ( ! empty( $attributes ) ) {
                $product->set_attributes( $attributes );
                $product->save();
            }
        }

        // Create variations
        $this->create_product_variations( $product, $wc_product_data['variations'], $data_to_import, $update_only );
    }

    /**
     * Handle simple product
     *
     * @param WC_Product $product WooCommerce product
     * @param array $wc_product_data Product data
     * @param array $data_to_import Import configuration
     */
    private function handle_simple_product( $product, $wc_product_data, $data_to_import ) {
        // Set price for simple product (use first variation's price)
        if ( ! empty( $wc_product_data['variations'] ) && isset( $data_to_import['price'] ) && $data_to_import['price'] ) {
            $first_variation = $wc_product_data['variations'][0];
            if ( isset( $first_variation['price'] ) ) {
                $product->set_regular_price( $first_variation['price'] );
                $product->set_price( $first_variation['price'] );
            }
        }

        // Set SKU for simple product
        if ( ! empty( $wc_product_data['variations'] ) && isset( $data_to_import['sku'] ) && $data_to_import['sku'] ) {
            $first_variation = $wc_product_data['variations'][0];
            if ( ! empty( $first_variation['sku'] ) && ! wc_get_product_id_by_sku( $first_variation['sku'] ) ) {
                $product->set_sku( $first_variation['sku'] );
            }
        }

        // Handle stock management for simple product (FIXED: was missing this logic)
        if ( ! empty( $wc_product_data['variations'] ) ) {
            $first_variation = $wc_product_data['variations'][0];

            // Check if Square tracks inventory for this variation
            $track_inventory = $first_variation['track_inventory'] ?? false;

            if ( $track_inventory ) {
                // Enable stock management
                $product->set_manage_stock( true );

                // Set stock quantity from Square inventory count
                if ( isset( $first_variation['inventory_count'] ) ) {
                    $inventory_count = intval( $first_variation['inventory_count'] );
                    $product->set_stock_quantity( $inventory_count );

                    // Set stock status based on quantity
                    $product->set_stock_status( $inventory_count > 0 ? 'instock' : 'outofstock' );
                } else {
                    // No inventory count available, assume in stock
                    $product->set_stock_status( 'instock' );
                }
            } else {
                // Square doesn't track inventory, set as in stock and don't manage stock
                $product->set_manage_stock( false );
                $product->set_stock_status( 'instock' );
            }
        }

        // Store Square variation ID
        if ( ! empty( $wc_product_data['variations'] ) ) {
            $first_variation = $wc_product_data['variations'][0];
            $product->update_meta_data( '_square_variation_id', $first_variation['variation_square_id'] );
        }
    }

    /**
     * Create attributes from variations (SWEVER-style)
     *
     * @param array $variations Variation data
     * @param WC_Product $product WooCommerce product
     * @return array Created attributes
     */
    private function create_attributes_from_variations( $variations, $product ) {
        $square_attributes = array();
        
        // Collect unique attribute options from variations
        foreach ( $variations as $variation ) {
            foreach ( $variation['attributes'] as $attribute ) {
                $attribute_name = strtolower( $attribute['name'] );
                $attribute_option = strtolower( $attribute['option'] );
                $square_attributes[$attribute_name][] = $attribute_option;
            }
        }

        // Make options unique
        foreach ( $square_attributes as &$options ) {
            $options = array_unique( $options );
        }

        $new_attributes = array();
        $existing_attributes = $product->get_attributes();

        foreach ( $square_attributes as $attribute_name => $terms ) {
            // Get existing terms
            $existing_terms = array();
            if ( isset( $existing_attributes[$attribute_name] ) ) {
                $existing_terms = $existing_attributes[$attribute_name]->get_options();
                $existing_terms = array_map( 'strtolower', $existing_terms );
            }

            // Merge with new terms
            $merged_terms = array_unique( array_merge( $existing_terms, $terms ) );

            // Create or update attribute
            $attribute = $this->attribute_importer->get_or_create_attribute( $attribute_name, $merged_terms, $product );
            
            if ( $attribute ) {
                $new_attributes[$attribute->get_name()] = $attribute;
            }
        }

        return $new_attributes;
    }

    /**
     * Create product variations
     *
     * @param WC_Product $product WooCommerce product
     * @param array $variations Variation data
     * @param array $data_to_import Import configuration
     * @param bool $update_only Update only flag
     */
    private function create_product_variations( $product, $variations, $data_to_import, $update_only ) {
        $product_id = $product->get_id();
        $existing_attributes = $product->get_attributes();

        foreach ( $variations as $variation_data ) {
            $square_variation_id = $variation_data['variation_square_id'];
            
            // Check if variation already exists
            $existing_variation_id = $this->get_existing_variation_id( $square_variation_id );
            
            if ( $update_only && ! $existing_variation_id ) {
                continue;
            }

            // Create or update variation
            if ( $existing_variation_id ) {
                $variation = wc_get_product( $existing_variation_id );
            } else {
                $variation = new WC_Product_Variation();
                $variation->set_parent_id( $product_id );
            }

            // Set variation attributes
            $variation_attributes = array();
            foreach ( $variation_data['attributes'] as $attribute ) {
                $attribute_name = strtolower( $attribute['name'] );
                $attribute_option = $attribute['option'];
                $attribute_slug = wc_sanitize_taxonomy_name( $attribute_name );

                // Check if it's a local or global attribute
                if ( isset( $existing_attributes[$attribute_name] ) ) {
                    $variation_attributes[$attribute_name] = $attribute_option;
                } else {
                    $taxonomy = 'pa_' . $attribute_slug;
                    if ( taxonomy_exists( $taxonomy ) ) {
                        $term = get_term_by( 'slug', strtolower( $attribute_option ), $taxonomy );
                        $variation_attributes[$taxonomy] = $term ? $term->slug : strtolower( $attribute_option );
                    } else {
                        $variation_attributes[$attribute_slug] = $attribute_option;
                    }
                }
            }

            $variation->set_attributes( $variation_attributes );

            // Set pricing
            if ( isset( $data_to_import['price'] ) && $data_to_import['price'] && isset( $variation_data['price'] ) ) {
                $variation->set_regular_price( $variation_data['price'] );
                $variation->set_price( $variation_data['price'] );
            }

            // Set SKU
            if ( isset( $data_to_import['sku'] ) && $data_to_import['sku'] && ! empty( $variation_data['sku'] ) ) {
                if ( ! wc_get_product_id_by_sku( $variation_data['sku'] ) ) {
                    $variation->set_sku( $variation_data['sku'] );
                }
            }

            // Set stock status
            $variation->set_stock_status( 'instock' );
            $variation->set_manage_stock( false );

            // Store Square variation ID
            $variation->update_meta_data( '_square_variation_id', $square_variation_id );

            // Save variation
            $variation->save();
        }

        // Update product price range
        WC_Product_Variable::sync( $product_id );
    }

    /**
     * Import product categories with hierarchical support
     *
     * @param WC_Product $product WooCommerce product
     * @param array $categories Category data with hierarchy
     */
    private function import_product_categories( $product, $categories ) {
        if ( empty( $categories ) ) {
            return;
        }

        // Load category sync module for hierarchical processing
        if ( ! class_exists( 'SquareKit_Category_Sync' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-category-sync.php';
        }

        $category_sync = new SquareKit_Category_Sync();
        $category_ids = array();

        // Process categories in hierarchy order (parents first)
        foreach ( $categories as $category_data ) {
            $square_id = $category_data['square_id'];
            $category_name = $category_data['name'];
            $category_description = $category_data['description'] ?? '';
            $parent_square_id = $category_data['parent_square_id'] ?? null;

            // Use category sync to create category with proper parent relationship
            $wc_category_id = $category_sync->import_single_category_with_parent(
                $square_id,
                $category_name,
                $category_description,
                $parent_square_id
            );

            if ( $wc_category_id ) {
                $category_ids[] = $wc_category_id;
            }
        }

        // Assign all categories in the hierarchy to the product
        if ( ! empty( $category_ids ) ) {
            wp_set_object_terms( $product->get_id(), $category_ids, 'product_cat', false );

            // Log successful category assignment
            if ( class_exists( 'SquareKit_Logger' ) ) {
                $logger = new SquareKit_Logger();
                $logger->log( 'import', 'info', 'Assigned hierarchical categories to product ' . $product->get_id() . ': ' . implode( ', ', $category_ids ) );
            }
        }
    }

    /**
     * Import product images using enhanced image handler with product name-based naming
     *
     * @param WC_Product $product WooCommerce product
     * @param array $images Image data
     */
    private function import_product_images( $product, $images ) {
        $image_ids = array();

        // Load enhanced image handler if not already loaded
        if ( ! class_exists( 'SquareKit_Image_Handler' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/sync/class-squarekit-image-handler.php';
        }

        $image_handler = new SquareKit_Image_Handler();
        $product_name = $product->get_name();

        foreach ( $images as $index => $image_data ) {
            if ( empty( $image_data['url'] ) ) {
                continue;
            }

            // Use enhanced image handler with product name and position for proper naming
            $image_id = $image_handler->import_image_from_url(
                $image_data['url'],
                $product_name,
                $index
            );

            if ( $image_id && ! is_wp_error( $image_id ) ) {
                $image_ids[] = $image_id;

                // Store Square image ID
                update_post_meta( $image_id, '_square_image_id', $image_data['square_id'] ?? '' );

                // Attach image to product
                wp_update_post( array(
                    'ID' => $image_id,
                    'post_parent' => $product->get_id()
                ) );
            }
        }

        if ( ! empty( $image_ids ) ) {
            // Set first image as featured image
            $product->set_image_id( $image_ids[0] );

            // Set remaining images as gallery
            if ( count( $image_ids ) > 1 ) {
                $product->set_gallery_image_ids( array_slice( $image_ids, 1 ) );
            }
        }
    }

    // Removed: download_and_attach_image() method
    // Now using enhanced SquareKit_Image_Handler for product name-based image naming

    /**
     * Get existing product ID by Square ID
     *
     * @param string $square_id Square item ID
     * @return int|false Product ID or false if not found
     */
    private function get_existing_product_id( $square_id ) {
        global $wpdb;

        $product_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = '_square_item_id' AND meta_value = %s",
            $square_id
        ) );

        return $product_id ? (int) $product_id : false;
    }

    /**
     * Get existing variation ID by Square variation ID
     *
     * @param string $square_variation_id Square variation ID
     * @return int|false Variation ID or false if not found
     */
    private function get_existing_variation_id( $square_variation_id ) {
        global $wpdb;

        $variation_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = '_square_variation_id' AND meta_value = %s",
            $square_variation_id
        ) );

        return $variation_id ? (int) $variation_id : false;
    }
}
