<?php
/**
 * SquareKit Price Calculator
 * 
 * Handles price calculations for variable products and ensures
 * proper price range display.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SquareKit Price Calculator Class
 */
class SquareKit_Price_Calculator {

    /**
     * Fix product price range for variable products
     *
     * @param int $product_id WooCommerce product ID
     * @return bool Success status
     */
    public static function fix_product_price_range( $product_id ) {
        $product = wc_get_product( $product_id );
        
        if ( ! $product || ! $product->is_type( 'variable' ) ) {
            return false;
        }

        // Get all variations
        $variations = $product->get_available_variations();
        
        if ( empty( $variations ) ) {
            return false;
        }

        $prices = array();
        $sale_prices = array();

        // Collect all variation prices
        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            
            if ( ! $variation ) {
                continue;
            }

            $regular_price = $variation->get_regular_price();
            $sale_price = $variation->get_sale_price();
            $price = $variation->get_price();

            if ( $regular_price !== '' ) {
                $prices[] = floatval( $regular_price );
            }

            if ( $sale_price !== '' ) {
                $sale_prices[] = floatval( $sale_price );
            }

            if ( $price !== '' ) {
                $prices[] = floatval( $price );
            }
        }

        if ( empty( $prices ) ) {
            return false;
        }

        // Calculate min and max prices
        $min_price = min( $prices );
        $max_price = max( $prices );

        // Update product meta
        update_post_meta( $product_id, '_price', $min_price );
        update_post_meta( $product_id, '_min_variation_price', $min_price );
        update_post_meta( $product_id, '_max_variation_price', $max_price );

        // Handle sale prices if they exist
        if ( ! empty( $sale_prices ) ) {
            $min_sale_price = min( $sale_prices );
            $max_sale_price = max( $sale_prices );
            
            update_post_meta( $product_id, '_min_variation_sale_price', $min_sale_price );
            update_post_meta( $product_id, '_max_variation_sale_price', $max_sale_price );
        }

        // Force WooCommerce to sync the product
        WC_Product_Variable::sync( $product_id );

        // Clear product cache
        wc_delete_product_transients( $product_id );
        wp_cache_delete( $product_id, 'products' );

        return true;
    }

    /**
     * Calculate price range for display
     *
     * @param int $product_id WooCommerce product ID
     * @return array|false Price range data or false if not variable
     */
    public static function calculate_price_range( $product_id ) {
        $product = wc_get_product( $product_id );
        
        if ( ! $product || ! $product->is_type( 'variable' ) ) {
            return false;
        }

        $variations = $product->get_available_variations();
        
        if ( empty( $variations ) ) {
            return false;
        }

        $regular_prices = array();
        $sale_prices = array();
        $current_prices = array();

        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            
            if ( ! $variation ) {
                continue;
            }

            $regular_price = $variation->get_regular_price();
            $sale_price = $variation->get_sale_price();
            $current_price = $variation->get_price();

            if ( $regular_price !== '' ) {
                $regular_prices[] = floatval( $regular_price );
            }

            if ( $sale_price !== '' ) {
                $sale_prices[] = floatval( $sale_price );
            }

            if ( $current_price !== '' ) {
                $current_prices[] = floatval( $current_price );
            }
        }

        $result = array(
            'has_variations' => true,
            'min_price' => ! empty( $current_prices ) ? min( $current_prices ) : 0,
            'max_price' => ! empty( $current_prices ) ? max( $current_prices ) : 0,
            'min_regular_price' => ! empty( $regular_prices ) ? min( $regular_prices ) : 0,
            'max_regular_price' => ! empty( $regular_prices ) ? max( $regular_prices ) : 0,
            'has_sale' => ! empty( $sale_prices ),
            'min_sale_price' => ! empty( $sale_prices ) ? min( $sale_prices ) : 0,
            'max_sale_price' => ! empty( $sale_prices ) ? max( $sale_prices ) : 0,
            'variation_count' => count( $variations )
        );

        return $result;
    }

    /**
     * Format price range for display
     *
     * @param array $price_range Price range data from calculate_price_range()
     * @return string Formatted price range
     */
    public static function format_price_range( $price_range ) {
        if ( ! $price_range || ! $price_range['has_variations'] ) {
            return '';
        }

        $min_price = $price_range['min_price'];
        $max_price = $price_range['max_price'];

        if ( $min_price === $max_price ) {
            // All variations have the same price
            return wc_price( $min_price );
        } else {
            // Price range
            return wc_price( $min_price ) . ' – ' . wc_price( $max_price );
        }
    }

    /**
     * Validate variation prices
     *
     * @param int $product_id WooCommerce product ID
     * @return array Validation results
     */
    public static function validate_variation_prices( $product_id ) {
        $product = wc_get_product( $product_id );
        
        if ( ! $product || ! $product->is_type( 'variable' ) ) {
            return array(
                'valid' => false,
                'error' => __( 'Product is not a variable product.', 'squarekit' )
            );
        }

        $variations = $product->get_available_variations();
        $issues = array();
        $valid_variations = 0;

        foreach ( $variations as $variation_data ) {
            $variation = wc_get_product( $variation_data['variation_id'] );
            
            if ( ! $variation ) {
                $issues[] = sprintf( __( 'Variation %d not found.', 'squarekit' ), $variation_data['variation_id'] );
                continue;
            }

            $price = $variation->get_price();
            $regular_price = $variation->get_regular_price();

            if ( $price === '' && $regular_price === '' ) {
                $issues[] = sprintf( __( 'Variation %d has no price set.', 'squarekit' ), $variation->get_id() );
                continue;
            }

            if ( $regular_price !== '' && floatval( $regular_price ) < 0 ) {
                $issues[] = sprintf( __( 'Variation %d has negative regular price.', 'squarekit' ), $variation->get_id() );
                continue;
            }

            $sale_price = $variation->get_sale_price();
            if ( $sale_price !== '' && floatval( $sale_price ) < 0 ) {
                $issues[] = sprintf( __( 'Variation %d has negative sale price.', 'squarekit' ), $variation->get_id() );
                continue;
            }

            $valid_variations++;
        }

        return array(
            'valid' => empty( $issues ),
            'issues' => $issues,
            'total_variations' => count( $variations ),
            'valid_variations' => $valid_variations
        );
    }

    /**
     * Recalculate all variable product prices
     *
     * @param array $product_ids Array of product IDs to recalculate
     * @return array Results summary
     */
    public static function recalculate_variable_product_prices( $product_ids = array() ) {
        if ( empty( $product_ids ) ) {
            // Get all variable products
            $product_ids = get_posts( array(
                'post_type' => 'product',
                'post_status' => 'publish',
                'meta_query' => array(
                    array(
                        'key' => '_product_type',
                        'value' => 'variable'
                    )
                ),
                'fields' => 'ids',
                'posts_per_page' => -1
            ) );
        }

        $results = array(
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'errors' => array()
        );

        foreach ( $product_ids as $product_id ) {
            $results['processed']++;
            
            $success = self::fix_product_price_range( $product_id );
            
            if ( $success ) {
                $results['successful']++;
            } else {
                $results['failed']++;
                $results['errors'][] = sprintf( __( 'Failed to recalculate prices for product %d.', 'squarekit' ), $product_id );
            }
        }

        return $results;
    }

    /**
     * Get product price summary
     *
     * @param int $product_id WooCommerce product ID
     * @return array Price summary
     */
    public static function get_product_price_summary( $product_id ) {
        $product = wc_get_product( $product_id );
        
        if ( ! $product ) {
            return array( 'error' => __( 'Product not found.', 'squarekit' ) );
        }

        $summary = array(
            'product_id' => $product_id,
            'product_type' => $product->get_type(),
            'price' => $product->get_price(),
            'regular_price' => $product->get_regular_price(),
            'sale_price' => $product->get_sale_price()
        );

        if ( $product->is_type( 'variable' ) ) {
            $price_range = self::calculate_price_range( $product_id );
            $summary['price_range'] = $price_range;
            $summary['formatted_price_range'] = self::format_price_range( $price_range );
            
            $validation = self::validate_variation_prices( $product_id );
            $summary['price_validation'] = $validation;
        }

        return $summary;
    }
}
