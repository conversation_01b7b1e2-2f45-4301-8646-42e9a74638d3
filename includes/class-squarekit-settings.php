<?php
/**
 * Settings class for the plugin
 *
 * @package SquareKit
 * @subpackage SquareKit/includes
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Settings class.
 *
 * This class handles all settings for the plugin.
 *
 * @since 1.0.0
 */
class SquareKit_Settings {

    /**
     * Option name
     *
     * @since 1.0.0
     * @access protected
     * @var string
     */
    protected $option_name = 'square-kit_settings';

    /**
     * Settings
     *
     * @since 1.0.0
     * @access protected
     * @var array
     */
    protected $settings = array();

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->settings = get_option( $this->option_name, array() );

        // Run migration if needed (only once)
        if ( ! get_option( 'squarekit_legacy_migration_done', false ) ) {
            $this->migrate_legacy_settings();
            update_option( 'squarekit_legacy_migration_done', true );
        }
    }

    /**
     * Get all settings
     *
     * @since 1.0.0
     * @return array Settings
     */
    public function get_all() {
        return $this->settings;
    }

    /**
     * Get setting
     *
     * @since 1.0.0
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed Setting value
     */
    public function get( $key, $default = false ) {
        // Check for configuration constants first
        $constant_value = $this->get_from_constants( $key );
        if ( $constant_value !== null ) {
            return $constant_value;
        }

        return isset( $this->settings[ $key ] ) ? $this->settings[ $key ] : $default;
    }

    /**
     * Set setting
     *
     * @since 1.0.0
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool True on success, false on failure
     */
    public function set( $key, $value ) {
        $this->settings[ $key ] = $value;
        return $this->save();
    }

    /**
     * Update settings
     *
     * @since 1.0.0
     * @param array $settings Settings
     * @return bool True on success, false on failure
     */
    public function update( $settings ) {
        $this->settings = array_merge( $this->settings, $settings );
        return $this->save();
    }

    /**
     * Save settings
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function save() {
        return update_option( $this->option_name, $this->settings );
    }

    /**
     * Delete setting
     *
     * @since 1.0.0
     * @param string $key Setting key
     * @return bool True on success, false on failure
     */
    public function delete( $key ) {
        if ( isset( $this->settings[ $key ] ) ) {
            unset( $this->settings[ $key ] );
            return $this->save();
        }
        return false;
    }

    /**
     * Get setting from configuration constants
     *
     * @since 1.0.0
     * @param string $key Setting key
     * @return mixed|null Constant value or null if not defined
     */
    private function get_from_constants( $key ) {
        $constant_map = array(
            'sandbox_application_id' => 'SQUAREKIT_SANDBOX_APPLICATION_ID',
            'sandbox_client_secret' => 'SQUAREKIT_SANDBOX_CLIENT_SECRET',
            'production_application_id' => 'SQUAREKIT_PRODUCTION_APPLICATION_ID',
            'production_client_secret' => 'SQUAREKIT_PRODUCTION_CLIENT_SECRET',
            'environment' => 'SQUAREKIT_DEFAULT_ENVIRONMENT',
            'webhook_url' => 'SQUAREKIT_WEBHOOK_URL',
            'webhook_signature_key' => 'SQUAREKIT_WEBHOOK_SIGNATURE_KEY',
            'debug' => 'SQUAREKIT_DEBUG',
            'api_timeout' => 'SQUAREKIT_API_TIMEOUT',
            'sync_batch_size' => 'SQUAREKIT_SYNC_BATCH_SIZE',
        );

        if ( isset( $constant_map[ $key ] ) && defined( $constant_map[ $key ] ) ) {
            return constant( $constant_map[ $key ] );
        }

        return null;
    }

    /**
     * Get Square environment
     *
     * @since 1.0.0
     * @return string Environment (sandbox or production)
     */
    public function get_environment() {
        return $this->get( 'environment', 'sandbox' );
    }

    /**
     * Check if environment is sandbox
     *
     * @since 1.0.0
     * @return bool True if sandbox, false otherwise
     */
    public function is_sandbox() {
        return 'sandbox' === $this->get_environment();
    }

    /**
     * Get Square API credentials
     *
     * @since 1.0.0
     * @return array API credentials
     */
    public function get_api_credentials() {
        $environment = $this->get_environment();
        
        return array(
            'access_token' => $this->get( $environment . '_access_token' ),
            'application_id' => $this->get( $environment . '_application_id' ),
            'location_id' => $this->get( 'location_id' ),
        );
    }

    /**
     * Get Square access token
     *
     * @since 1.0.0
     * @return string Access token
     */
    public function get_access_token() {
        $environment = $this->get_environment();

        // Try secure storage first
        if ( class_exists( 'SquareKit_Secure_Storage' ) ) {
            $secure_token = SquareKit_Secure_Storage::get_access_token( $environment );
            if ( $secure_token ) {
                return $secure_token;
            }
        }

        // Fallback to old storage method
        return $this->get( $environment . '_access_token' );
    }

    /**
     * Get Square application ID
     *
     * @since 1.0.0
     * @return string Application ID
     */
    public function get_application_id() {
        $environment = $this->get_environment();
        return $this->get( $environment . '_application_id' );
    }

    /**
     * Get Square location ID
     *
     * @since 1.0.0
     * @return string Location ID
     */
    public function get_location_id() {
        return $this->get( 'location_id' );
    }

    /**
     * Check if Square is connected
     *
     * @since 1.0.0
     * @return bool True if connected, false otherwise
     */
    public function is_connected() {
        $environment = $this->get_environment();

        // Check secure storage first
        if ( class_exists( 'SquareKit_Secure_Storage' ) ) {
            if ( SquareKit_Secure_Storage::has_tokens( $environment ) ) {
                $location_id = $this->get_location_id();
                return ! empty( $location_id );
            }
        }

        // Fallback to old storage method
        $access_token = $this->get_access_token();
        $location_id = $this->get_location_id();

        return ! empty( $access_token ) && ! empty( $location_id );
    }

    /**
     * Get sync preferences
     *
     * @since 1.0.0
     * @return array Sync preferences
     */
    public function get_sync_preferences() {
        return array(
            'products' => $this->get( 'sync_products', 'both' ),
            'orders' => $this->get( 'sync_orders', true ),
            'customers' => $this->get( 'sync_customers', true ),
            'automatic' => $this->get( 'enable_automatic_sync', true ),
            'cron_schedule' => $this->get( 'cron_schedule', 'hourly' ),
        );
    }

    /**
     * Get payment methods
     *
     * @since 1.0.0
     * @return array Payment methods
     */
    public function get_payment_methods() {
        $payment_methods = $this->get( 'payment_methods', array() );
        
        // Default payment methods
        $defaults = array(
            'square' => true,
            'google_pay' => false,
            'apple_pay' => false,
            'afterpay' => false,
        );
        
        return wp_parse_args( $payment_methods, $defaults );
    }

    /**
     * Check if payment method is enabled
     *
     * @since 1.0.0
     * @param string $method Payment method
     * @return bool True if enabled, false otherwise
     */
    public function is_payment_method_enabled( $method ) {
        $payment_methods = $this->get_payment_methods();
        return isset( $payment_methods[ $method ] ) ? (bool) $payment_methods[ $method ] : false;
    }

    /**
     * Get webhook status
     *
     * @since 1.0.0
     * @return bool Webhook status
     */
    public function get_webhook_status() {
        return (bool) $this->get( 'webhook_status', false );
    }

    /**
     * Get webhook signature key
     *
     * @since 1.0.0
     * @return string Webhook signature key
     */
    public function get_webhook_signature_key() {
        return $this->get( 'webhook_signature_key', '' );
    }

    /**
     * Get webhook URL
     *
     * @since 1.1.0
     * @return string Webhook URL
     */
    public function get_webhook_url() {
        return home_url( '/squarekit-webhook/' );
    }

    /**
     * Check if webhooks are enabled
     *
     * @since 1.1.0
     * @return bool
     */
    public function are_webhooks_enabled() {
        return $this->get_webhook_status() && ! empty( $this->get_webhook_signature_key() );
    }

    /**
     * Check if auto-import of new products from Square is enabled
     *
     * @since 1.1.0
     * @return bool
     */
    public function is_auto_import_new_products_enabled() {
        return (bool) $this->get( 'auto_import_new_products_from_square', false );
    }

    /**
     * Check if setup is complete
     *
     * @since 1.0.0
     * @return bool True if setup is complete, false otherwise
     */
    public function is_setup_complete() {
        return (bool) $this->get( 'setup_complete', false );
    }

    /**
     * Mark setup as complete
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function mark_setup_complete() {
        return $this->set( 'setup_complete', true );
    }

    /**
     * Reset settings
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function reset() {
        return delete_option( $this->option_name );
    }

    /**
     * Migrate legacy settings to new structure
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function migrate_legacy_settings() {
        $migrated = false;

        // Remove legacy sync settings that are no longer used
        $legacy_settings = array(
            'sync_inventory',
            'sync_orders',
            'sync_customers'
        );

        foreach ( $legacy_settings as $legacy_setting ) {
            if ( isset( $this->settings[ $legacy_setting ] ) ) {
                unset( $this->settings[ $legacy_setting ] );
                $migrated = true;
            }
        }

        // Set default conflict resolution if not set
        if ( ! isset( $this->settings['inventory_conflict_resolution'] ) ) {
            $this->settings['inventory_conflict_resolution'] = 'square_wins';
            $migrated = true;
        }

        if ( ! isset( $this->settings['product_conflict_resolution'] ) ) {
            $this->settings['product_conflict_resolution'] = 'square_wins';
            $migrated = true;
        }

        // Set default performance settings if not set
        if ( ! isset( $this->settings['sync_batch_size'] ) ) {
            $this->settings['sync_batch_size'] = 50;
            $migrated = true;
        }

        if ( ! isset( $this->settings['sync_timeout'] ) ) {
            $this->settings['sync_timeout'] = 60;
            $migrated = true;
        }

        if ( $migrated ) {
            $this->save();

            // Log the migration
            if ( class_exists('SquareKit_Logger') ) {
                $logger = new SquareKit_Logger();
                $logger->log('settings', 'info', 'Legacy settings migrated to new structure');
            }
        }

        return $migrated;
    }

    /**
     * Get attribute mapping (Square <-> WooCommerce)
     * @return array
     */
    public function get_attribute_mapping() {
        return $this->get('attribute_mapping', array());
    }

    /**
     * Set attribute mapping (Square <-> WooCommerce)
     * @param array $mapping
     * @return bool
     */
    public function set_attribute_mapping($mapping) {
        return $this->set('attribute_mapping', $mapping);
    }

    /**
     * Get order status mapping (WooCommerce <-> Square)
     * @return array
     */
    public function get_status_mapping() {
        return $this->get('status_mapping', array());
    }

    /**
     * Set order status mapping (WooCommerce <-> Square)
     * @param array $mapping
     * @return bool
     */
    public function set_status_mapping($mapping) {
        return $this->set('status_mapping', $mapping);
    }

    /**
     * Get selective sync settings
     *
     * @since 1.0.0
     * @return array Selective sync settings
     */
    public function get_selective_sync_settings() {
        return array(
            'enabled' => $this->get( 'enable_selective_sync', false ),
            'products_schedule' => $this->get( 'products_sync_schedule', 'hourly' ),
            'orders_schedule' => $this->get( 'orders_sync_schedule', 'hourly' ),
            'customers_schedule' => $this->get( 'customers_sync_schedule', 'hourly' ),
            'inventory_schedule' => $this->get( 'inventory_sync_schedule', 'hourly' ),
            'max_execution_time' => $this->get( 'max_sync_execution_time', 300 ),
            'batch_size' => $this->get( 'sync_batch_size', 50 ),
        );
    }

    /**
     * Check if selective sync is enabled
     *
     * @since 1.0.0
     * @return bool True if enabled, false otherwise
     */
    public function is_selective_sync_enabled() {
        return (bool) $this->get( 'enable_selective_sync', false );
    }

    /**
     * Get sync performance settings
     *
     * @since 1.0.0
     * @return array Performance settings
     */
    public function get_sync_performance_settings() {
        return array(
            'max_execution_time' => $this->get( 'max_sync_execution_time', 300 ),
            'batch_size' => $this->get( 'sync_batch_size', 50 ),
            'memory_limit' => $this->get( 'sync_memory_limit', '256M' ),
            'timeout' => $this->get( 'sync_timeout', 60 ),
        );
    }

    /**
     * Get advanced cron settings
     *
     * @since 1.0.0
     * @return array Advanced cron settings
     */
    public function get_advanced_cron_settings() {
        return array(
            'enable_retry_on_failure' => $this->get( 'enable_cron_retry', true ),
            'max_retry_attempts' => $this->get( 'max_cron_retry_attempts', 3 ),
            'retry_delay' => $this->get( 'cron_retry_delay', 300 ),
            'enable_parallel_processing' => $this->get( 'enable_parallel_processing', false ),
            'max_parallel_jobs' => $this->get( 'max_parallel_jobs', 2 ),
        );
    }

    /**
     * Get real-time inventory settings
     *
     * @since 1.0.0
     * @return array Real-time inventory settings
     */
    public function get_real_time_inventory_settings() {
        return array(
            'enable_real_time_inventory' => $this->get( 'enable_real_time_inventory', true ),
            'inventory_conflict_resolution' => $this->get( 'inventory_conflict_resolution', 'manual' ),
            'inventory_conflict_threshold' => $this->get( 'inventory_conflict_threshold', 1 ),
            'enable_inventory_notifications' => $this->get( 'enable_inventory_notifications', true ),
            'inventory_sync_frequency' => $this->get( 'inventory_sync_frequency', 'realtime' ),
            'enable_auto_resolve_conflicts' => $this->get( 'enable_auto_resolve_conflicts', false ),
        );
    }

    /**
     * Get inventory conflict resolution options
     *
     * @since 1.0.0
     * @return array Resolution options
     */
    public function get_inventory_conflict_resolution_options() {
        return array(
            'manual' => __( 'Manual Resolution', 'squarekit' ),
            'wc_wins' => __( 'WooCommerce Wins', 'squarekit' ),
            'square_wins' => __( 'Square Wins', 'squarekit' ),
        );
    }

    /**
     * Get customer role mapping settings
     *
     * @since 1.0.0
     * @return array Role mapping settings
     */
    public function get_customer_role_mapping() {
        return $this->get( 'customer_role_mapping', array() );
    }

    /**
     * Set customer role mapping settings
     *
     * @since 1.0.0
     * @param array $mapping Role mapping settings
     * @return bool True on success, false on failure
     */
    public function set_customer_role_mapping( $mapping ) {
        return $this->set( 'customer_role_mapping', $mapping );
    }

    /**
     * Get available customer role mapping criteria
     *
     * @since 1.0.0
     * @return array Available criteria
     */
    public function get_customer_role_mapping_criteria() {
        return array(
            'loyalty_points' => __( 'Loyalty Points', 'squarekit' ),
            'total_spent' => __( 'Total Spent', 'squarekit' ),
            'order_count' => __( 'Order Count', 'squarekit' ),
            'registration_date' => __( 'Registration Date', 'squarekit' ),
            'last_order_date' => __( 'Last Order Date', 'squarekit' ),
            'customer_note' => __( 'Customer Note', 'squarekit' ),
        );
    }

    /**
     * Get available WooCommerce user roles
     *
     * @since 1.0.0
     * @return array Available roles
     */
    public function get_available_wc_roles() {
        $roles = wp_roles()->get_names();
        
        // Filter out roles that shouldn't be assigned via sync
        $excluded_roles = array( 'administrator', 'editor', 'author', 'contributor' );
        foreach ( $excluded_roles as $role ) {
            unset( $roles[ $role ] );
        }
        
        return $roles;
    }

    /**
     * Check if customer role mapping is enabled
     *
     * @since 1.0.0
     * @return bool True if enabled, false otherwise
     */
    public function is_customer_role_mapping_enabled() {
        return (bool) $this->get( 'enable_customer_role_mapping', false );
    }

    /**
     * Get order fulfillment mapping settings
     *
     * @since 1.0.0
     * @return array Fulfillment mapping settings
     */
    public function get_fulfillment_mapping() {
        return $this->get( 'fulfillment_mapping', array() );
    }

    /**
     * Set order fulfillment mapping settings
     *
     * @since 1.0.0
     * @param array $mapping Fulfillment mapping settings
     * @return bool True on success, false on failure
     */
    public function set_fulfillment_mapping( $mapping ) {
        return $this->set( 'fulfillment_mapping', $mapping );
    }

    /**
     * Get pickup location mapping settings
     *
     * @since 1.0.0
     * @return array Pickup location mapping settings
     */
    public function get_pickup_location_mapping() {
        return $this->get( 'pickup_location_mapping', array() );
    }

    /**
     * Set pickup location mapping settings
     *
     * @since 1.0.0
     * @param array $mapping Pickup location mapping settings
     * @return bool True on success, false on failure
     */
    public function set_pickup_location_mapping( $mapping ) {
        return $this->set( 'pickup_location_mapping', $mapping );
    }

    /**
     * Get available Square fulfillment types
     *
     * @since 1.0.0
     * @return array Available fulfillment types
     */
    public function get_available_square_fulfillment_types() {
        return array(
            'PICKUP' => __( 'Pickup', 'squarekit' ),
            'SHIPMENT' => __( 'Shipment', 'squarekit' ),
            'DIGITAL' => __( 'Digital', 'squarekit' ),
        );
    }

    /**
     * Get available WooCommerce shipping methods
     *
     * @since 1.0.0
     * @return array Available shipping methods
     */
    public function get_available_wc_shipping_methods() {
        $shipping_methods = array();
        
        // Get all registered shipping methods
        $wc_shipping_methods = WC()->shipping()->get_shipping_methods();
        
        foreach ( $wc_shipping_methods as $method_id => $method ) {
            $shipping_methods[ $method_id ] = $method->get_method_title();
        }
        
        // Add common shipping methods
        $common_methods = array(
            'flat_rate' => __( 'Flat Rate', 'squarekit' ),
            'free_shipping' => __( 'Free Shipping', 'squarekit' ),
            'local_pickup' => __( 'Local Pickup', 'squarekit' ),
            'local_delivery' => __( 'Local Delivery', 'squarekit' ),
        );
        
        return array_merge( $common_methods, $shipping_methods );
    }

    /**
     * Get available Square locations for pickup
     *
     * @since 1.0.0
     * @return array Available locations
     */
    public function get_available_square_locations() {
        $locations = array();
        
        if ( ! class_exists( 'SquareKit_Square_API' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/api/class-squarekit-square-api.php';
        }
        
        $square_api = new SquareKit_Square_API();
        $square_locations = $square_api->get_locations();
        
        if ( ! is_wp_error( $square_locations ) && is_array( $square_locations ) ) {
            foreach ( $square_locations as $location ) {
                $locations[ $location['id'] ] = $location['name'];
            }
        }
        
        return $locations;
    }

    /**
     * Check if order fulfillment mapping is enabled
     *
     * @since 1.0.0
     * @return bool True if enabled, false otherwise
     */
    public function is_fulfillment_mapping_enabled() {
        return (bool) $this->get( 'enable_fulfillment_mapping', false );
    }

    /**
     * Check if pickup location mapping is enabled
     *
     * @since 1.0.0
     * @return bool True if enabled, false otherwise
     */
    public function is_pickup_location_mapping_enabled() {
        return (bool) $this->get( 'enable_pickup_location_mapping', false );
    }

    /**
     * Get SKU mapping settings
     *
     * @since 1.0.0
     * @return array SKU mapping settings
     */
    public function get_sku_mapping() {
        return $this->get( 'sku_mapping', array() );
    }

    /**
     * Set SKU mapping settings
     *
     * @since 1.0.0
     * @param array $mapping SKU mapping settings
     * @return bool True on success, false on failure
     */
    public function set_sku_mapping( $mapping ) {
        return $this->set( 'sku_mapping', $mapping );
    }

    /**
     * Check if SKU mapping is enabled
     *
     * @since 1.0.0
     * @return bool True if enabled, false otherwise
     */
    public function is_sku_mapping_enabled() {
        return (bool) $this->get( 'enable_sku_mapping', false );
    }

    /**
     * Get SKU conflict resolution settings
     *
     * @since 1.0.0
     * @return string Conflict resolution method
     */
    public function get_sku_conflict_resolution() {
        return $this->get( 'sku_conflict_resolution', 'skip' );
    }

    /**
     * Set SKU conflict resolution settings
     *
     * @since 1.0.0
     * @param string $resolution Conflict resolution method
     * @return bool True on success, false on failure
     */
    public function set_sku_conflict_resolution( $resolution ) {
        return $this->set( 'sku_conflict_resolution', $resolution );
    }

    /**
     * Get available SKU conflict resolution options
     *
     * @since 1.0.0
     * @return array Resolution options
     */
    public function get_sku_conflict_resolution_options() {
        return array(
            'skip' => __( 'Skip Import', 'squarekit' ),
            'overwrite' => __( 'Overwrite Existing', 'squarekit' ),
            'append_suffix' => __( 'Append Suffix', 'squarekit' ),
            'generate_new' => __( 'Generate New SKU', 'squarekit' ),
        );
    }

    /**
     * Get SKU mapping rules
     *
     * @since 1.0.0
     * @return array SKU mapping rules
     */
    public function get_sku_mapping_rules() {
        return $this->get( 'sku_mapping_rules', array() );
    }

    /**
     * Set SKU mapping rules
     *
     * @since 1.0.0
     * @param array $rules SKU mapping rules
     * @return bool True on success, false on failure
     */
    public function set_sku_mapping_rules( $rules ) {
        return $this->set( 'sku_mapping_rules', $rules );
    }

    /**
     * Get SKU validation settings
     *
     * @since 1.0.0
     * @return array SKU validation settings
     */
    public function get_sku_validation_settings() {
        return $this->get( 'sku_validation_settings', array(
            'enforce_unique' => true,
            'allow_empty' => false,
            'max_length' => 64,
            'allowed_characters' => 'alphanumeric_dash_underscore',
        ) );
    }

    /**
     * Set SKU validation settings
     *
     * @since 1.0.0
     * @param array $settings SKU validation settings
     * @return bool True on success, false on failure
     */
    public function set_sku_validation_settings( $settings ) {
        return $this->set( 'sku_validation_settings', $settings );
    }

    /**
     * Get SKU character sets
     *
     * @since 1.0.0
     * @return array Character sets
     */
    public function get_sku_character_sets() {
        return array(
            'alphanumeric' => __( 'Alphanumeric (A-Z, 0-9)', 'squarekit' ),
            'numeric' => __( 'Numeric (0-9)', 'squarekit' ),
            'alpha' => __( 'Alphabetic (A-Z)', 'squarekit' ),
            'custom' => __( 'Custom Pattern', 'squarekit' ),
        );
    }

    /**
     * Get sync direction settings
     *
     * @since 1.0.0
     * @return array Sync direction settings
     */
    public function get_sync_direction_settings() {
        return array(
            'woo_to_square' => $this->get( 'sync_woo_to_square', false ),
            'square_to_woo' => $this->get( 'sync_square_to_woo', false ),
        );
    }

    /**
     * Set sync direction settings
     *
     * @since 1.0.0
     * @param array $settings Sync direction settings
     * @return bool True on success, false on failure
     */
    public function set_sync_direction_settings( $settings ) {
        $this->settings['sync_woo_to_square'] = isset( $settings['woo_to_square'] ) ? (bool) $settings['woo_to_square'] : false;
        $this->settings['sync_square_to_woo'] = isset( $settings['square_to_woo'] ) ? (bool) $settings['square_to_woo'] : false;
        return $this->save();
    }

    /**
     * Check if WooCommerce to Square sync is enabled
     *
     * @since 1.0.0
     * @return bool True if enabled, false otherwise
     */
    public function is_woo_to_square_sync_enabled() {
        return (bool) $this->get( 'sync_woo_to_square', false );
    }

    /**
     * Check if Square to WooCommerce sync is enabled
     *
     * @since 1.0.0
     * @return bool True if enabled, false otherwise
     */
    public function is_square_to_woo_sync_enabled() {
        return (bool) $this->get( 'sync_square_to_woo', false );
    }

    /**
     * Get granular sync settings
     *
     * @since 1.0.0
     * @return array Granular sync settings
     */
    public function get_granular_sync_settings() {
        $defaults = array(
            'woo_to_square' => array(
                'enabled' => false,
                'data_types' => array(
                    'products' => false,
                    'inventory' => false,
                    'categories' => false,
                    'orders' => false,
                    'customers' => false,
                )
            ),
            'square_to_woo' => array(
                'enabled' => false,
                'data_types' => array(
                    'products' => false,
                    'inventory' => true, // Recommended default
                    'categories' => false,
                    'orders' => false,
                    'customers' => false,
                )
            )
        );

        return $this->get( 'granular_sync_settings', $defaults );
    }

    /**
     * Set granular sync settings
     *
     * @since 1.0.0
     * @param array $settings Granular sync settings
     * @return bool True on success, false on failure
     */
    public function set_granular_sync_settings( $settings ) {
        return $this->set( 'granular_sync_settings', $settings );
    }

    /**
     * Check if specific data type sync is enabled for a direction
     *
     * @since 1.0.0
     * @param string $direction 'woo_to_square' or 'square_to_woo'
     * @param string $data_type 'products', 'inventory', 'categories', 'orders', 'customers'
     * @return bool True if enabled, false otherwise
     */
    public function is_data_type_sync_enabled( $direction, $data_type ) {
        $granular_settings = $this->get_granular_sync_settings();

        if ( ! isset( $granular_settings[ $direction ] ) ) {
            return false;
        }

        if ( ! $granular_settings[ $direction ]['enabled'] ) {
            return false;
        }

        return isset( $granular_settings[ $direction ]['data_types'][ $data_type ] )
            && $granular_settings[ $direction ]['data_types'][ $data_type ];
    }

    /**
     * Enable WooCommerce to Square sync
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function enable_woo_to_square_sync() {
        return $this->set( 'sync_woo_to_square', true );
    }

    /**
     * Disable WooCommerce to Square sync
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function disable_woo_to_square_sync() {
        return $this->set( 'sync_woo_to_square', false );
    }

    /**
     * Enable Square to WooCommerce sync
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function enable_square_to_woo_sync() {
        return $this->set( 'sync_square_to_woo', true );
    }

    /**
     * Disable Square to WooCommerce sync
     *
     * @since 1.0.0
     * @return bool True on success, false on failure
     */
    public function disable_square_to_woo_sync() {
        return $this->set( 'sync_square_to_woo', false );
    }

    /**
     * Get sync direction descriptions
     *
     * @since 1.0.0
     * @return array Sync direction descriptions
     */
    public function get_sync_direction_descriptions() {
        return array(
            'woo_to_square' => array(
                'title' => __( 'WooCommerce to Square Sync', 'squarekit' ),
                'description' => __( 'Sync products, inventory, and orders from WooCommerce to Square. Changes made in WooCommerce will be reflected in Square.', 'squarekit' ),
                'features' => array(
                    __( 'Product creation and updates', 'squarekit' ),
                    __( 'Inventory level synchronization', 'squarekit' ),
                    __( 'Order creation in Square', 'squarekit' ),
                    __( 'Customer data sync', 'squarekit' ),
                ),
            ),
            'square_to_woo' => array(
                'title' => __( 'Square to WooCommerce Sync', 'squarekit' ),
                'description' => __( 'Sync products, inventory, and orders from Square to WooCommerce. Changes made in Square will be reflected in WooCommerce.', 'squarekit' ),
                'features' => array(
                    __( 'Product import and updates', 'squarekit' ),
                    __( 'Inventory level synchronization', 'squarekit' ),
                    __( 'Order import from Square', 'squarekit' ),
                    __( 'Customer data import', 'squarekit' ),
                ),
            ),
        );
    }
} 