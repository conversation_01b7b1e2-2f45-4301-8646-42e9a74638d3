<?php
/**
 * SquareKit Payment Logs Test
 * 
 * Test the advanced payment logging system
 * Access via: https://teapot.local/wp-content/plugins/squarekit/sk-test-payment-logs.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once(dirname(__FILE__) . '/../../../wp-load.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>SquareKit - Payment Logs Test</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; 
            margin: 20px; 
            background: #f1f1f1; 
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .header { 
            border-bottom: 2px solid #0073aa; 
            padding-bottom: 15px; 
            margin-bottom: 20px; 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { 
            background: #0073aa; 
            color: white; 
            padding: 8px 16px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #005a87; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .log-entry { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
            font-size: 12px;
        }
        .log-level { 
            display: inline-block; 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-weight: bold; 
            font-size: 10px; 
            text-transform: uppercase; 
        }
        .level-info { background: #d1ecf1; color: #0c5460; }
        .level-warning { background: #fff3cd; color: #856404; }
        .level-error { background: #f8d7da; color: #721c24; }
        .level-debug { background: #e2e3e5; color: #383d41; }
        .analytics-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin: 20px 0; 
        }
        .analytics-card { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            border: 1px solid #dee2e6; 
        }
        .metric { 
            font-size: 24px; 
            font-weight: bold; 
            color: #0073aa; 
        }
        .metric-label { 
            font-size: 12px; 
            color: #666; 
            text-transform: uppercase; 
        }
        pre { 
            background: #f4f4f4; 
            padding: 10px; 
            border-radius: 4px; 
            overflow-x: auto; 
            font-size: 11px; 
        }
        .filters { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 20px 0; 
        }
        .filter-row { 
            display: flex; 
            gap: 10px; 
            align-items: center; 
            margin: 10px 0; 
        }
        .filter-row label { 
            min-width: 100px; 
            font-weight: bold; 
        }
        .filter-row select, .filter-row input { 
            padding: 5px; 
            border: 1px solid #ccc; 
            border-radius: 3px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 SquareKit Payment Logs Test</h1>
            <p>Test and analyze the advanced payment logging system</p>
        </div>

        <?php
        // Initialize payment logger
        try {
            $payment_logger = new SquareKit_Payment_Logger();
            echo '<div class="test-section success"><strong>✅ Payment Logger Initialized Successfully</strong></div>';
        } catch (Exception $e) {
            echo '<div class="test-section error"><strong>❌ Failed to Initialize Payment Logger:</strong> ' . esc_html($e->getMessage()) . '</div>';
            exit;
        }

        // Handle actions
        if (isset($_POST['action'])) {
            $action = sanitize_text_field($_POST['action']);
            
            switch ($action) {
                case 'create_test_logs':
                    echo '<div class="test-section info"><h3>🧪 Creating Test Payment Logs...</h3>';
                    create_test_payment_logs($payment_logger);
                    echo '</div>';
                    break;
                    
                case 'clear_all_logs':
                    $result = clear_all_payment_logs($payment_logger);
                    echo '<div class="test-section warning"><strong>🗑️ All Payment Logs Cleared:</strong> ' . $result . ' logs removed</div>';
                    break;
                    
                case 'export_logs':
                    export_payment_logs($payment_logger);
                    break;
            }
        }

        // Get filter parameters
        $log_level = $_GET['log_level'] ?? '';
        $event_type = $_GET['event_type'] ?? '';
        $date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
        $date_to = $_GET['date_to'] ?? date('Y-m-d');

        // Display filters
        echo '<div class="filters">';
        echo '<h3>🔍 Log Filters</h3>';
        echo '<form method="get">';
        echo '<div class="filter-row">';
        echo '<label>Log Level:</label>';
        echo '<select name="log_level">';
        echo '<option value="">All Levels</option>';
        $levels = array('debug', 'info', 'notice', 'warning', 'error', 'critical', 'alert', 'emergency');
        foreach ($levels as $level) {
            $selected = $log_level === $level ? 'selected' : '';
            echo '<option value="' . $level . '" ' . $selected . '>' . ucfirst($level) . '</option>';
        }
        echo '</select>';
        
        echo '<label>Event Type:</label>';
        echo '<select name="event_type">';
        echo '<option value="">All Events</option>';
        $events = array('transaction_start', 'transaction_success', 'transaction_failure', 'security_', 'compliance_');
        foreach ($events as $event) {
            $selected = $event_type === $event ? 'selected' : '';
            echo '<option value="' . $event . '" ' . $selected . '>' . ucwords(str_replace('_', ' ', $event)) . '</option>';
        }
        echo '</select>';
        echo '</div>';
        
        echo '<div class="filter-row">';
        echo '<label>Date From:</label>';
        echo '<input type="date" name="date_from" value="' . esc_attr($date_from) . '">';
        echo '<label>Date To:</label>';
        echo '<input type="date" name="date_to" value="' . esc_attr($date_to) . '">';
        echo '<button type="submit" class="btn">Apply Filters</button>';
        echo '</div>';
        echo '</form>';
        echo '</div>';

        // Display analytics
        echo '<div class="test-section">';
        echo '<h3>📈 Payment Analytics</h3>';
        
        $analytics = $payment_logger->get_analytics(array(
            'date_from' => $date_from,
            'date_to' => $date_to
        ));
        
        echo '<div class="analytics-grid">';
        
        // Success rate card
        echo '<div class="analytics-card">';
        echo '<div class="metric">' . $analytics['success_rate']['percentage'] . '%</div>';
        echo '<div class="metric-label">Success Rate</div>';
        echo '<p>Successful: ' . $analytics['success_rate']['successful'] . '<br>';
        echo 'Failed: ' . $analytics['success_rate']['failed'] . '<br>';
        echo 'Total: ' . $analytics['success_rate']['total'] . '</p>';
        echo '</div>';
        
        // Performance metrics card
        echo '<div class="analytics-card">';
        echo '<div class="metric">' . number_format($analytics['performance']['avg_execution_time'], 3) . 's</div>';
        echo '<div class="metric-label">Avg Execution Time</div>';
        echo '<p>Max: ' . number_format($analytics['performance']['max_execution_time'], 3) . 's<br>';
        echo 'Min: ' . number_format($analytics['performance']['min_execution_time'], 3) . 's</p>';
        echo '</div>';
        
        // Payment methods card
        echo '<div class="analytics-card">';
        echo '<div class="metric-label">Payment Methods</div>';
        if (!empty($analytics['payment_methods'])) {
            foreach ($analytics['payment_methods'] as $method => $data) {
                echo '<p><strong>' . ucfirst($method) . ':</strong> ' . $data['count'] . ' transactions</p>';
            }
        } else {
            echo '<p>No payment method data available</p>';
        }
        echo '</div>';
        
        // Error patterns card
        echo '<div class="analytics-card">';
        echo '<div class="metric-label">Top Error Patterns</div>';
        if (!empty($analytics['error_patterns'])) {
            foreach (array_slice($analytics['error_patterns'], 0, 5) as $pattern => $count) {
                echo '<p><strong>' . ucwords(str_replace('_', ' ', $pattern)) . ':</strong> ' . $count . '</p>';
            }
        } else {
            echo '<p>No error patterns found</p>';
        }
        echo '</div>';
        
        echo '</div>';
        echo '</div>';

        // Display recent logs
        echo '<div class="test-section">';
        echo '<h3>📋 Recent Payment Logs</h3>';
        
        $logs = $payment_logger->get_logs(array(
            'limit' => 20,
            'log_level' => $log_level,
            'event_type' => $event_type,
            'date_from' => $date_from . ' 00:00:00',
            'date_to' => $date_to . ' 23:59:59'
        ));
        
        if (empty($logs)) {
            echo '<p class="info">No logs found matching the current filters.</p>';
        } else {
            foreach ($logs as $log) {
                display_log_entry($log);
            }
        }
        echo '</div>';

        // Security summary
        echo '<div class="test-section">';
        echo '<h3>🔒 Security Events Summary (Last 7 Days)</h3>';
        
        $security_summary = $payment_logger->get_security_summary(7);
        
        echo '<p><strong>Total Security Events:</strong> ' . $security_summary['total_events'] . '</p>';
        
        if (!empty($security_summary['event_types'])) {
            echo '<h4>Event Types:</h4>';
            foreach ($security_summary['event_types'] as $type => $count) {
                echo '<p><strong>' . ucwords(str_replace('_', ' ', $type)) . ':</strong> ' . $count . '</p>';
            }
        }
        
        if (!empty($security_summary['unique_ips'])) {
            echo '<h4>Top IPs by Event Count:</h4>';
            foreach (array_slice($security_summary['unique_ips'], 0, 5) as $ip => $count) {
                echo '<p><strong>' . esc_html($ip) . ':</strong> ' . $count . ' events</p>';
            }
        }
        echo '</div>';

        // Test actions
        echo '<div class="test-section">';
        echo '<h3>🧪 Test Actions</h3>';
        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="create_test_logs">';
        echo '<button type="submit" class="btn btn-success">Create Test Logs</button>';
        echo '</form>';
        
        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="clear_all_logs">';
        echo '<button type="submit" class="btn btn-danger" onclick="return confirm(\'Are you sure you want to clear all payment logs?\')">Clear All Logs</button>';
        echo '</form>';
        
        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="export_logs">';
        echo '<button type="submit" class="btn">Export Logs (CSV)</button>';
        echo '</form>';
        echo '</div>';

        /**
         * Create test payment logs
         */
        function create_test_payment_logs($payment_logger) {
            $test_logs = array(
                array(
                    'level' => 'info',
                    'event_type' => 'transaction_start',
                    'message' => 'Payment transaction initiated for order #1001',
                    'context' => array(
                        'order_id' => 1001,
                        'amount' => 99.99,
                        'currency' => 'USD',
                        'payment_method' => 'card',
                        'transaction_id' => 'txn_' . wp_generate_uuid4()
                    )
                ),
                array(
                    'level' => 'info',
                    'event_type' => 'transaction_success',
                    'message' => 'Payment completed successfully',
                    'context' => array(
                        'order_id' => 1001,
                        'payment_id' => 'sq_pay_' . wp_generate_uuid4(),
                        'amount' => 99.99,
                        'currency' => 'USD',
                        'execution_time' => 1.234
                    )
                ),
                array(
                    'level' => 'error',
                    'event_type' => 'transaction_failure',
                    'message' => 'Payment failed: Card declined',
                    'context' => array(
                        'order_id' => 1002,
                        'amount' => 149.99,
                        'currency' => 'USD',
                        'error_code' => 'CARD_DECLINED',
                        'execution_time' => 0.856
                    )
                ),
                array(
                    'level' => 'warning',
                    'event_type' => 'security_suspicious_activity',
                    'message' => 'Suspicious payment activity detected',
                    'context' => array(
                        'ip_address' => '*************',
                        'user_agent' => 'Suspicious Bot 1.0',
                        'attempts' => 15
                    )
                ),
                array(
                    'level' => 'notice',
                    'event_type' => 'compliance_pci_audit',
                    'message' => 'PCI compliance audit log entry',
                    'context' => array(
                        'audit_type' => 'token_storage',
                        'compliance_status' => 'passed'
                    )
                )
            );

            foreach ($test_logs as $log_data) {
                $result = $payment_logger->log(
                    $log_data['level'],
                    $log_data['event_type'],
                    $log_data['message'],
                    $log_data['context']
                );
                
                if ($result) {
                    echo '<p>✅ Created log: ' . esc_html($log_data['message']) . '</p>';
                } else {
                    echo '<p>❌ Failed to create log: ' . esc_html($log_data['message']) . '</p>';
                }
            }
        }

        /**
         * Display a log entry
         */
        function display_log_entry($log) {
            $level_class = 'level-' . $log->log_level;
            
            echo '<div class="log-entry">';
            echo '<div>';
            echo '<span class="log-level ' . $level_class . '">' . strtoupper($log->log_level) . '</span> ';
            echo '<strong>' . esc_html($log->event_type) . '</strong> ';
            echo '<span style="color: #666; font-size: 11px;">' . esc_html($log->created_at) . '</span>';
            echo '</div>';
            echo '<div style="margin: 5px 0;"><strong>Message:</strong> ' . esc_html($log->message) . '</div>';
            
            if ($log->order_id) {
                echo '<div><strong>Order ID:</strong> ' . esc_html($log->order_id) . '</div>';
            }
            
            if ($log->payment_id) {
                echo '<div><strong>Payment ID:</strong> ' . esc_html($log->payment_id) . '</div>';
            }
            
            if ($log->amount) {
                echo '<div><strong>Amount:</strong> ' . esc_html($log->amount) . ' ' . esc_html($log->currency) . '</div>';
            }
            
            if ($log->execution_time) {
                echo '<div><strong>Execution Time:</strong> ' . number_format($log->execution_time, 3) . 's</div>';
            }
            
            if ($log->ip_address) {
                echo '<div><strong>IP Address:</strong> ' . esc_html($log->ip_address) . '</div>';
            }
            
            if ($log->context && $log->context !== '[]') {
                echo '<div><strong>Context:</strong></div>';
                echo '<pre>' . esc_html(wp_json_encode(json_decode($log->context), JSON_PRETTY_PRINT)) . '</pre>';
            }
            echo '</div>';
        }

        /**
         * Clear all payment logs
         */
        function clear_all_payment_logs($payment_logger) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'squarekit_payment_logs';
            
            $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
            $wpdb->query("DELETE FROM {$table_name}");
            
            return intval($count);
        }

        /**
         * Export payment logs
         */
        function export_payment_logs($payment_logger) {
            $csv_content = $payment_logger->export_logs_csv(array(
                'limit' => 1000,
                'date_from' => $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days')),
                'date_to' => $_GET['date_to'] ?? date('Y-m-d')
            ));
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="squarekit-payment-logs-' . date('Y-m-d') . '.csv"');
            echo $csv_content;
            exit;
        }
        ?>

    </div>
</body>
</html>
