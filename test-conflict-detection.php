<?php
/**
 * Test Conflict Detection System
 * 
 * This file demonstrates the manual review conflict resolution system
 * by creating sample conflicts for testing purposes.
 */

// WordPress environment
require_once( dirname(__FILE__) . '/wp-config.php' );

// Load SquareKit classes
require_once( dirname(__FILE__) . '/wp-content/plugins/squarekit/includes/class-squarekit-conflict-manager.php' );
require_once( dirname(__FILE__) . '/wp-content/plugins/squarekit/includes/class-squarekit-logger.php' );

echo "<h1>SquareKit Conflict Detection Test</h1>";

// Initialize conflict manager
$conflict_manager = new SquareKit_Conflict_Manager();

echo "<h2>Creating Test Conflicts...</h2>";

// Test 1: Inventory Conflict
echo "<h3>Test 1: Inventory Conflict</h3>";
$conflict_id_1 = $conflict_manager->detect_conflict(
    'inventory',
    123, // Product ID
    'product',
    25, // Square value: 25 units
    30, // WooCommerce value: 30 units
    '2024-01-15 10:30:00', // Square last modified
    '2024-01-15 11:15:00'  // WooCommerce last modified
);

if ( $conflict_id_1 ) {
    echo "<p>✅ Inventory conflict created with ID: {$conflict_id_1}</p>";
    echo "<p><strong>Details:</strong> Product #123 has 25 units in Square but 30 units in WooCommerce</p>";
} else {
    echo "<p>❌ Failed to create inventory conflict</p>";
}

// Test 2: Product Price Conflict
echo "<h3>Test 2: Product Price Conflict</h3>";
$conflict_id_2 = $conflict_manager->detect_conflict(
    'price',
    456, // Product ID
    'product',
    19.99, // Square value: $19.99
    24.99, // WooCommerce value: $24.99
    '2024-01-15 09:45:00', // Square last modified
    '2024-01-15 10:20:00'  // WooCommerce last modified
);

if ( $conflict_id_2 ) {
    echo "<p>✅ Price conflict created with ID: {$conflict_id_2}</p>";
    echo "<p><strong>Details:</strong> Product #456 costs $19.99 in Square but $24.99 in WooCommerce</p>";
} else {
    echo "<p>❌ Failed to create price conflict</p>";
}

// Test 3: Product Name Conflict
echo "<h3>Test 3: Product Name Conflict</h3>";
$conflict_id_3 = $conflict_manager->detect_conflict(
    'product_name',
    789, // Product ID
    'product',
    'Premium Coffee Blend', // Square value
    'Deluxe Coffee Mix',    // WooCommerce value
    '2024-01-15 08:30:00', // Square last modified
    '2024-01-15 12:00:00'  // WooCommerce last modified
);

if ( $conflict_id_3 ) {
    echo "<p>✅ Product name conflict created with ID: {$conflict_id_3}</p>";
    echo "<p><strong>Details:</strong> Product #789 is named 'Premium Coffee Blend' in Square but 'Deluxe Coffee Mix' in WooCommerce</p>";
} else {
    echo "<p>❌ Failed to create product name conflict</p>";
}

// Display conflict statistics
echo "<h2>Conflict Statistics</h2>";
$pending_count = $conflict_manager->get_conflict_count('pending');
$resolved_count = $conflict_manager->get_conflict_count('resolved');

echo "<p><strong>Pending Conflicts:</strong> {$pending_count}</p>";
echo "<p><strong>Resolved Conflicts:</strong> {$resolved_count}</p>";

// Display pending conflicts
echo "<h2>Pending Conflicts</h2>";
$pending_conflicts = $conflict_manager->get_pending_conflicts();

if ( empty( $pending_conflicts ) ) {
    echo "<p>No pending conflicts found.</p>";
} else {
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>Type</th><th>Object</th><th>Square Value</th><th>WooCommerce Value</th><th>Detected</th>";
    echo "</tr>";
    
    foreach ( $pending_conflicts as $conflict ) {
        echo "<tr>";
        echo "<td>{$conflict->id}</td>";
        echo "<td>" . ucfirst($conflict->conflict_type) . "</td>";
        echo "<td>" . ucfirst($conflict->object_type) . " #{$conflict->object_id}</td>";
        echo "<td>" . esc_html($conflict->square_value) . "</td>";
        echo "<td>" . esc_html($conflict->woocommerce_value) . "</td>";
        echo "<td>" . date('M j, Y g:i A', strtotime($conflict->detected_at)) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Test resolution
if ( !empty( $pending_conflicts ) ) {
    echo "<h2>Testing Conflict Resolution</h2>";
    
    $first_conflict = $pending_conflicts[0];
    echo "<h3>Resolving Conflict #{$first_conflict->id}</h3>";
    
    // Resolve with Square wins
    $resolved = $conflict_manager->resolve_conflict(
        $first_conflict->id,
        'square_wins',
        null,
        'Test resolution - Square data is more recent'
    );
    
    if ( $resolved ) {
        echo "<p>✅ Conflict #{$first_conflict->id} resolved successfully using Square value</p>";
    } else {
        echo "<p>❌ Failed to resolve conflict #{$first_conflict->id}</p>";
    }
    
    // Update statistics
    $pending_count = $conflict_manager->get_conflict_count('pending');
    $resolved_count = $conflict_manager->get_conflict_count('resolved');
    
    echo "<p><strong>Updated Statistics:</strong></p>";
    echo "<p>Pending: {$pending_count} | Resolved: {$resolved_count}</p>";
}

echo "<h2>Next Steps</h2>";
echo "<p>1. Visit the <a href='" . admin_url('admin.php?page=squarekit-settings&tab=conflicts') . "'>Conflicts Tab</a> in SquareKit settings</p>";
echo "<p>2. Review and resolve the remaining conflicts</p>";
echo "<p>3. Configure conflict resolution preferences in sync settings</p>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 40px; }
h1 { color: #2271b1; }
h2 { color: #135e96; border-bottom: 2px solid #135e96; padding-bottom: 5px; }
h3 { color: #50575e; }
table { margin: 20px 0; }
th { background: #f0f0f0; }
td, th { padding: 10px; text-align: left; }
p { line-height: 1.6; }
</style>";
?>
