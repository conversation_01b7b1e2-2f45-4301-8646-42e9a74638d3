<?php
/**
 * SquareKit Conflict Detection Test
 * 
 * Test the conflict detection and resolution system
 * Access via: https://teapot.local/wp-content/plugins/squarekit/sk-test-conflict-detection.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once(dirname(__FILE__) . '/../../../wp-load.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>SquareKit - Conflict Detection Test</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; 
            margin: 20px; 
            background: #f1f1f1; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .header { 
            border-bottom: 2px solid #0073aa; 
            padding-bottom: 15px; 
            margin-bottom: 20px; 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { 
            background: #0073aa; 
            color: white; 
            padding: 8px 16px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
        }
        .btn:hover { background: #005a87; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .conflict-item { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
        }
        .conflict-actions { margin-top: 10px; }
        pre { 
            background: #f4f4f4; 
            padding: 10px; 
            border-radius: 4px; 
            overflow-x: auto; 
            font-size: 12px; 
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background: #ffc107; }
        .status-resolved { background: #28a745; }
        .status-ignored { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 SquareKit Conflict Detection Test</h1>
            <p>Test and demonstrate the conflict detection and resolution system</p>
        </div>

        <?php
        // Initialize conflict manager
        try {
            $conflict_manager = new SquareKit_Conflict_Manager();

            // Force recreate table to ensure correct structure (for testing)
            if (isset($_GET['recreate_table']) || isset($_POST['recreate_table'])) {
                $conflict_manager->recreate_table();
                echo '<div class="test-section warning"><strong>🔄 Table Recreated Successfully</strong></div>';
            }

            echo '<div class="test-section success"><strong>✅ Conflict Manager Initialized Successfully</strong></div>';
        } catch (Exception $e) {
            echo '<div class="test-section error"><strong>❌ Failed to Initialize Conflict Manager:</strong> ' . esc_html($e->getMessage()) . '</div>';
            exit;
        }

        // Handle actions
        if (isset($_POST['action'])) {
            $action = sanitize_text_field($_POST['action']);
            $conflict_id = isset($_POST['conflict_id']) ? intval($_POST['conflict_id']) : 0;
            
            switch ($action) {
                case 'create_test_conflicts':
                    echo '<div class="test-section info"><h3>🧪 Creating Test Conflicts...</h3>';
                    create_test_conflicts($conflict_manager);
                    echo '</div>';
                    break;
                    
                case 'resolve_conflict':
                    if ($conflict_id) {
                        $resolution = sanitize_text_field($_POST['resolution']);
                        $result = $conflict_manager->resolve_conflict($conflict_id, $resolution);
                        if ($result) {
                            echo '<div class="test-section success"><strong>✅ Conflict Resolved:</strong> ID #' . $conflict_id . '</div>';
                        } else {
                            echo '<div class="test-section error"><strong>❌ Failed to Resolve Conflict:</strong> ID #' . $conflict_id . '</div>';
                        }
                    }
                    break;
                    
                case 'ignore_conflict':
                    if ($conflict_id) {
                        $result = $conflict_manager->ignore_conflict($conflict_id);
                        if ($result) {
                            echo '<div class="test-section warning"><strong>⚠️ Conflict Ignored:</strong> ID #' . $conflict_id . '</div>';
                        } else {
                            echo '<div class="test-section error"><strong>❌ Failed to Ignore Conflict:</strong> ID #' . $conflict_id . '</div>';
                        }
                    }
                    break;
                    
                case 'clear_all_conflicts':
                    $result = $conflict_manager->clear_all_conflicts();
                    echo '<div class="test-section warning"><strong>🗑️ All Conflicts Cleared:</strong> ' . $result . ' conflicts removed</div>';
                    break;

                case 'recreate_table':
                    $conflict_manager->recreate_table();
                    echo '<div class="test-section warning"><strong>🔄 Table Recreated Successfully</strong></div>';
                    break;
            }
        }

        // Display current conflicts
        echo '<div class="test-section">';
        echo '<h3>📋 Current Conflicts</h3>';
        
        $conflicts = $conflict_manager->get_pending_conflicts();
        
        if (empty($conflicts)) {
            echo '<p class="info">No pending conflicts found.</p>';
        } else {
            foreach ($conflicts as $conflict) {
                display_conflict($conflict);
            }
        }
        echo '</div>';

        // Display resolved conflicts
        echo '<div class="test-section">';
        echo '<h3>✅ Resolved Conflicts</h3>';
        
        $resolved_conflicts = $conflict_manager->get_resolved_conflicts();
        
        if (empty($resolved_conflicts)) {
            echo '<p class="info">No resolved conflicts found.</p>';
        } else {
            echo '<p>Found ' . count($resolved_conflicts) . ' resolved conflicts:</p>';
            foreach (array_slice($resolved_conflicts, 0, 5) as $conflict) {
                display_conflict($conflict, true);
            }
            if (count($resolved_conflicts) > 5) {
                echo '<p><em>... and ' . (count($resolved_conflicts) - 5) . ' more</em></p>';
            }
        }
        echo '</div>';

        // Test actions
        echo '<div class="test-section">';
        echo '<h3>🧪 Test Actions</h3>';
        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="create_test_conflicts">';
        echo '<button type="submit" class="btn">Create Test Conflicts</button>';
        echo '</form>';
        
        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="clear_all_conflicts">';
        echo '<button type="submit" class="btn btn-danger" onclick="return confirm(\'Are you sure you want to clear all conflicts?\')">Clear All Conflicts</button>';
        echo '</form>';

        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="recreate_table">';
        echo '<button type="submit" class="btn btn-warning" onclick="return confirm(\'This will recreate the table and clear all data. Continue?\')">Recreate Table</button>';
        echo '</form>';
        echo '</div>';

        // System information
        echo '<div class="test-section">';
        echo '<h3>ℹ️ System Information</h3>';
        echo '<pre>';
        echo 'Conflict Manager Class: ' . (class_exists('SquareKit_Conflict_Manager') ? 'Available' : 'Missing') . "\n";
        echo 'Database Table: ' . $conflict_manager->get_table_name() . "\n";
        echo 'Total Conflicts: ' . count($conflict_manager->get_all_conflicts()) . "\n";
        echo 'Pending Conflicts: ' . count($conflicts) . "\n";
        echo 'Resolved Conflicts: ' . count($resolved_conflicts) . "\n";
        echo 'WordPress Version: ' . get_bloginfo('version') . "\n";
        echo 'SquareKit Version: ' . (defined('SQUAREKIT_VERSION') ? SQUAREKIT_VERSION : 'Unknown') . "\n";
        echo '</pre>';
        echo '</div>';

        /**
         * Create test conflicts for demonstration
         */
        function create_test_conflicts($conflict_manager) {
            $test_conflicts = array(
                array(
                    'type' => 'product_sync',
                    'description' => 'Product "Coffee Blend #1" exists in both Square and WooCommerce with different prices',
                    'data' => array(
                        'square_price' => 15.99,
                        'woocommerce_price' => 14.99,
                        'product_name' => 'Coffee Blend #1',
                        'square_id' => 'sq_prod_123',
                        'woocommerce_id' => 456
                    ),
                    'suggested_resolution' => 'use_square_data'
                ),
                array(
                    'type' => 'inventory_mismatch',
                    'description' => 'Inventory levels differ between Square and WooCommerce for "Tea Collection"',
                    'data' => array(
                        'square_quantity' => 25,
                        'woocommerce_quantity' => 30,
                        'product_name' => 'Tea Collection',
                        'square_id' => 'sq_prod_789',
                        'woocommerce_id' => 101
                    ),
                    'suggested_resolution' => 'use_square_data'
                ),
                array(
                    'type' => 'category_mapping',
                    'description' => 'Square category "Hot Beverages" cannot be mapped to existing WooCommerce categories',
                    'data' => array(
                        'square_category' => 'Hot Beverages',
                        'square_category_id' => 'sq_cat_456',
                        'available_wc_categories' => array('Beverages', 'Coffee', 'Tea')
                    ),
                    'suggested_resolution' => 'create_new_category'
                ),
                array(
                    'type' => 'duplicate_product',
                    'description' => 'Multiple WooCommerce products found for Square product "Espresso Machine"',
                    'data' => array(
                        'square_product' => 'Espresso Machine',
                        'square_id' => 'sq_prod_999',
                        'woocommerce_matches' => array(
                            array('id' => 201, 'name' => 'Espresso Machine Pro'),
                            array('id' => 202, 'name' => 'Espresso Machine Deluxe')
                        )
                    ),
                    'suggested_resolution' => 'manual_review'
                )
            );

            foreach ($test_conflicts as $conflict) {
                $result = $conflict_manager->add_conflict(
                    $conflict['type'],
                    $conflict['description'],
                    $conflict['data'],
                    $conflict['suggested_resolution']
                );
                
                if ($result) {
                    echo '<p>✅ Created conflict: ' . esc_html($conflict['description']) . '</p>';
                } else {
                    echo '<p>❌ Failed to create conflict: ' . esc_html($conflict['description']) . '</p>';
                }
            }
        }

        /**
         * Display a conflict item
         */
        function display_conflict($conflict, $is_resolved = false) {
            $status_class = $is_resolved ? 'status-resolved' : 'status-pending';
            $status_text = $is_resolved ? 'Resolved' : 'Pending';
            
            echo '<div class="conflict-item">';
            echo '<div><span class="status-indicator ' . $status_class . '"></span><strong>' . $status_text . ' - ' . esc_html(ucwords(str_replace('_', ' ', $conflict->type))) . '</strong></div>';
            echo '<div><strong>Description:</strong> ' . esc_html($conflict->description) . '</div>';
            echo '<div><strong>Created:</strong> ' . esc_html($conflict->created_at) . '</div>';
            
            if (!empty($conflict->data)) {
                echo '<div><strong>Data:</strong></div>';
                echo '<pre>' . esc_html(wp_json_encode(json_decode($conflict->data), JSON_PRETTY_PRINT)) . '</pre>';
            }
            
            if ($conflict->suggested_resolution) {
                echo '<div><strong>Suggested Resolution:</strong> ' . esc_html(ucwords(str_replace('_', ' ', $conflict->suggested_resolution))) . '</div>';
            }
            
            if ($is_resolved) {
                echo '<div><strong>Resolution:</strong> ' . esc_html($conflict->resolution) . '</div>';
                echo '<div><strong>Resolved At:</strong> ' . esc_html($conflict->resolved_at) . '</div>';
            } else {
                echo '<div class="conflict-actions">';
                echo '<form method="post" style="display: inline;">';
                echo '<input type="hidden" name="action" value="resolve_conflict">';
                echo '<input type="hidden" name="conflict_id" value="' . $conflict->id . '">';
                echo '<input type="hidden" name="resolution" value="' . esc_attr($conflict->suggested_resolution) . '">';
                echo '<button type="submit" class="btn">Resolve (Use Suggested)</button>';
                echo '</form>';
                
                echo '<form method="post" style="display: inline;">';
                echo '<input type="hidden" name="action" value="ignore_conflict">';
                echo '<input type="hidden" name="conflict_id" value="' . $conflict->id . '">';
                echo '<button type="submit" class="btn btn-warning">Ignore</button>';
                echo '</form>';
                echo '</div>';
            }
            
            echo '</div>';
        }
        ?>

    </div>
</body>
</html>
