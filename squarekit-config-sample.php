<?php
/**
 * Square Kit Configuration Sample
 *
 * Copy this file to 'squarekit-config.php' and fill in your Square OAuth credentials.
 * This file should be placed in your WordPress root directory or wp-content directory.
 *
 * @package SquareKit
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Square OAuth Configuration
 *
 * Get these credentials from your Square Developer Dashboard:
 * https://developer.squareup.com/apps
 */

// Sandbox Environment (for testing)
define( 'SQUAREKIT_SANDBOX_APPLICATION_ID', 'your_sandbox_application_id_here' );
define( 'SQUAREKIT_SANDBOX_CLIENT_SECRET', 'your_sandbox_client_secret_here' );

// Production Environment (for live transactions)
define( 'SQUAREKIT_PRODUCTION_APPLICATION_ID', 'your_production_application_id_here' );
define( 'SQUAREKIT_PRODUCTION_CLIENT_SECRET', 'your_production_client_secret_here' );

// Default Environment (sandbox or production)
define( 'SQUAREKIT_DEFAULT_ENVIRONMENT', 'sandbox' );

/**
 * Optional: Webhook Configuration
 * 
 * If you want to use Square webhooks for real-time sync,
 * configure these settings:
 */

// Webhook endpoint URL (leave empty to use default)
// define( 'SQUAREKIT_WEBHOOK_URL', 'https://yoursite.com/wp-json/squarekit/v1/webhook' );

// Webhook signature key (from Square Developer Dashboard)
// define( 'SQUAREKIT_WEBHOOK_SIGNATURE_KEY', 'your_webhook_signature_key_here' );

/**
 * Optional: Advanced Configuration
 */

// Enable debug logging (true/false)
define( 'SQUAREKIT_DEBUG', false );

// API request timeout in seconds
define( 'SQUAREKIT_API_TIMEOUT', 30 );

// Maximum number of items to sync per batch
define( 'SQUAREKIT_SYNC_BATCH_SIZE', 100 );

/**
 * Instructions:
 * 
 * 1. Rename this file to 'squarekit-config.php'
 * 2. Place it in your WordPress root directory or wp-content directory
 * 3. Fill in your Square OAuth credentials above
 * 4. The plugin will automatically detect and use these settings
 * 
 * Note: Settings in this file will override any settings configured
 * through the WordPress admin interface.
 */
