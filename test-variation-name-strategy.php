<?php
/**
 * Test file for Variation Name Strategy fix
 * 
 * This file tests the fix for Square products that have variations
 * without using Square's "Options" feature.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    // For testing outside WordPress, define basic constants
    define( 'ABSPATH', dirname( __FILE__ ) . '/' );
    define( 'SQUAREKIT_PLUGIN_DIR', dirname( __FILE__ ) . '/' );
}

// Include required files for testing
require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-default-attribute-handler.php';
require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-logger.php';

/**
 * Test the default attribute handler
 */
function test_default_attribute_handler() {
    echo "<h2>Testing Default Attribute Handler</h2>\n";
    
    $handler = new SquareKit_Default_Attribute_Handler();
    
    // Test variation name strategy detection
    $square_item_with_options = array(
        'item_data' => array(
            'variations' => array(
                array(
                    'item_variation_data' => array(
                        'name' => 'Small',
                        'item_option_values' => array(
                            array('option_id' => 'size_option', 'choice_id' => 'small')
                        )
                    )
                ),
                array(
                    'item_variation_data' => array(
                        'name' => 'Large',
                        'item_option_values' => array(
                            array('option_id' => 'size_option', 'choice_id' => 'large')
                        )
                    )
                )
            )
        )
    );
    
    $square_item_without_options = array(
        'item_data' => array(
            'variations' => array(
                array(
                    'item_variation_data' => array(
                        'name' => 'Regular',
                        'item_option_values' => array()
                    )
                ),
                array(
                    'item_variation_data' => array(
                        'name' => 'Big',
                        'item_option_values' => array()
                    )
                ),
                array(
                    'item_variation_data' => array(
                        'name' => 'Extra Large'
                        // No item_option_values at all
                    )
                )
            )
        )
    );
    
    echo "Item with options uses variation name strategy: " . 
         ($handler->uses_variation_name_strategy($square_item_with_options) ? 'YES' : 'NO') . "<br>\n";
    
    echo "Item without options uses variation name strategy: " . 
         ($handler->uses_variation_name_strategy($square_item_without_options) ? 'YES' : 'NO') . "<br>\n";
    
    // Test attribute creation
    $attribute = $handler->create_default_product_options_attribute($square_item_without_options, 123);
    
    if ($attribute) {
        echo "Default attribute created successfully<br>\n";
        echo "Attribute name: " . $attribute->get_name() . "<br>\n";
        echo "Attribute options: " . implode(', ', $attribute->get_options()) . "<br>\n";
        echo "Attribute slug: " . $handler->get_default_attribute_slug() . "<br>\n";
    } else {
        echo "Failed to create default attribute<br>\n";
    }
    
    // Test variation attribute values
    echo "Variation attribute value for 'Regular': " . $handler->get_variation_attribute_value('Regular') . "<br>\n";
    echo "Variation attribute value for 'Extra Large': " . $handler->get_variation_attribute_value('Extra Large') . "<br>\n";
}

/**
 * Test the variation attribute building logic
 */
function test_variation_attribute_building() {
    echo "<h2>Testing Variation Attribute Building</h2>\n";
    
    // Simulate the variation data for Monster Energy product
    $monster_energy_variations = array(
        array(
            'id' => 'var_regular',
            'item_variation_data' => array(
                'name' => 'Regular',
                'sku' => '454536',
                'price_money' => array('amount' => 1499)
                // No item_option_values - this is the key!
            )
        ),
        array(
            'id' => 'var_big',
            'item_variation_data' => array(
                'name' => 'Big',
                'sku' => '576578',
                'price_money' => array('amount' => 20000)
                // No item_option_values
            )
        ),
        array(
            'id' => 'var_extra_large',
            'item_variation_data' => array(
                'name' => 'Extra Large',
                'sku' => '677868',
                'price_money' => array('amount' => 150000)
                // No item_option_values
            )
        )
    );
    
    $handler = new SquareKit_Default_Attribute_Handler();
    $attribute_slug = $handler->get_default_attribute_slug();
    
    // Simulate existing product attributes (what would be created by the attribute importer)
    $existing_attributes = array(
        $attribute_slug => (object) array(
            'name' => 'Product Options',
            'options' => array('Regular', 'Big', 'Extra Large')
        )
    );
    
    echo "Expected attribute slug: " . $attribute_slug . "<br>\n";
    echo "Existing attributes: " . print_r(array_keys($existing_attributes), true) . "<br>\n";
    
    // Test each variation
    foreach ($monster_energy_variations as $variation) {
        $variation_name = $variation['item_variation_data']['name'];
        $attribute_value = $handler->get_variation_attribute_value($variation_name);
        
        echo "Variation '{$variation_name}' should map to attribute value: '{$attribute_value}'<br>\n";
        
        // Test if the attribute would be found
        if (isset($existing_attributes[$attribute_slug])) {
            echo "✓ Attribute found for variation '{$variation_name}'<br>\n";
        } else {
            echo "✗ Attribute NOT found for variation '{$variation_name}'<br>\n";
        }
    }
}

/**
 * Run all tests
 */
function run_tests() {
    echo "<h1>Square Kit Variation Name Strategy Tests</h1>\n";
    echo "<p>Testing the fix for products like Monster Energy that have variations without Square Options.</p>\n";
    
    test_default_attribute_handler();
    echo "<hr>\n";
    test_variation_attribute_building();
    
    echo "<h2>Summary</h2>\n";
    echo "<p>If all tests pass, the fix should work for products like Monster Energy that have:</p>\n";
    echo "<ul>\n";
    echo "<li>Multiple variations (Regular, Big, Extra Large)</li>\n";
    echo "<li>No Square Options (empty or missing item_option_values)</li>\n";
    echo "<li>Variation names that should become attribute values</li>\n";
    echo "</ul>\n";
}

// Run tests if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    run_tests();
}
