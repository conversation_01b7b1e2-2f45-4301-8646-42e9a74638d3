<?php
/**
 * SquareKit Checkout UI Test Page
 * 
 * This file demonstrates the beautiful, modern checkout interface
 * with all the new styling and UX improvements.
 * 
 * @package SquareKit
 * @version 1.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SquareKit Modern Checkout UI - Test Page</title>
    
    <!-- Load our beautiful CSS -->
    <link rel="stylesheet" href="assets/css/payment-gateway.css">
    
    <!-- Load jQuery for interactions -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 40px 20px;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .test-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .test-content {
            padding: 40px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .demo-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .demo-section h3 {
            color: #374151;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 20px 0;
        }
        
        .demo-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .demo-btn {
            padding: 10px 20px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #ffffff;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .demo-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .demo-btn.primary {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .demo-btn.primary:hover {
            background: #5a67d8;
            border-color: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎨 SquareKit Modern Checkout</h1>
            <p>Beautiful, responsive payment experience with professional design</p>
        </div>
        
        <div class="test-content">
            <!-- Payment Description -->
            <div class="demo-section">
                <h3>💳 Payment Information</h3>
                <div class="squarekit-payment-description">
                    <p>Your payment is secured with industry-standard encryption and processed through Square's trusted payment platform.</p>
                </div>
            </div>
            
            <!-- Digital Wallets -->
            <div class="demo-section">
                <h3>📱 Digital Wallet Options</h3>
                <div class="squarekit-digital-wallets">
                    <div id="squarekit-google-pay-button" class="squarekit-wallet-button">
                        <span>Google Pay</span>
                    </div>
                    <div id="squarekit-apple-pay-button" class="squarekit-wallet-button">
                        <span>Apple Pay</span>
                    </div>
                    <div id="squarekit-afterpay-button" class="squarekit-wallet-button">
                        <span>Afterpay</span>
                    </div>
                </div>
                
                <div class="squarekit-payment-divider">
                    <span>or pay with card</span>
                </div>
            </div>
            
            <!-- Card Container -->
            <div class="demo-section">
                <h3>💳 Credit Card Information</h3>
                <div class="squarekit-card-container">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 8px;">Card Number</label>
                        <div style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 16px; background: #ffffff;">
                            <span style="color: #9ca3af;">•••• •••• •••• ••••</span>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 8px;">Expiry Date</label>
                            <div style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 16px; background: #ffffff;">
                                <span style="color: #9ca3af;">MM/YY</span>
                            </div>
                        </div>
                        <div>
                            <label style="display: block; font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 8px;">CVV</label>
                            <div style="border: 2px solid #e5e7eb; border-radius: 8px; padding: 16px; background: #ffffff;">
                                <span style="color: #9ca3af;">•••</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Demo Controls -->
            <div class="demo-section">
                <h3>🎮 Interactive Demo</h3>
                <p style="color: #6b7280; margin-bottom: 20px;">Test the different UI states and interactions:</p>
                
                <div class="demo-buttons">
                    <button class="demo-btn primary" onclick="showSuccess()">Show Success</button>
                    <button class="demo-btn" onclick="showError()">Show Error</button>
                    <button class="demo-btn" onclick="showLoading()">Show Loading</button>
                    <button class="demo-btn" onclick="hideAll()">Reset</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Error Display Container -->
    <div id="squarekit-payment-errors" class="squarekit-payment-errors" style="display: none; max-width: 600px; margin: 20px auto 0;" role="alert" aria-live="polite"></div>
    
    <!-- Loading Indicator -->
    <div id="squarekit-payment-loading" class="squarekit-payment-loading" style="display: none;" role="status" aria-live="polite">
        <div class="squarekit-spinner" aria-hidden="true"></div>
        <span>Processing payment...</span>
        <span class="sr-only">Please wait while we process your payment securely.</span>
    </div>
    
    <script>
        // Demo functions to showcase the UI states
        function showSuccess() {
            hideAll();
            const successHtml = `
                <div class="squarekit-payment-success" style="max-width: 600px; margin: 20px auto 0;">
                    <p>Payment processed successfully! Your order has been confirmed.</p>
                </div>
            `;
            $('body').append(successHtml);
            
            // Scroll to success message
            $('html, body').animate({
                scrollTop: $('.squarekit-payment-success').offset().top - 120
            }, 600);
        }
        
        function showError() {
            hideAll();
            const errorHtml = `<p>Payment failed: Invalid card number. Please check your card details and try again.</p>`;
            $('#squarekit-payment-errors').html(errorHtml).show();
            
            // Scroll to error
            $('html, body').animate({
                scrollTop: $('#squarekit-payment-errors').offset().top - 120
            }, 600);
        }
        
        function showLoading() {
            hideAll();
            $('#squarekit-payment-loading').fadeIn(300);
            $('body').addClass('squarekit-processing');
        }
        
        function hideAll() {
            $('#squarekit-payment-errors').hide();
            $('#squarekit-payment-loading').hide();
            $('.squarekit-payment-success').remove();
            $('body').removeClass('squarekit-processing');
        }
        
        // Add hover effects to wallet buttons
        $(document).ready(function() {
            $('.squarekit-wallet-button').on('click', function() {
                const walletType = $(this).find('span').text();
                alert(`${walletType} clicked! In a real checkout, this would initiate the ${walletType} payment flow.`);
            });
        });
    </script>
</body>
</html>
