<?php
/**
 * Emergency script to disable <PERSON><PERSON><PERSON><PERSON><PERSON>ce to Square sync
 * Run this if sync is accidentally enabled and causing issues
 */

// Load WordPress
require_once '../../../wp-config.php';
require_once '../../../wp-load.php';

// Load SquareKit settings
require_once 'includes/class-squarekit-settings.php';

$settings = new SquareKit_Settings();
$action = $_GET['action'] ?? 'check';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Emergency Sync Disable</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .button { 
            display: inline-block; 
            padding: 10px 20px; 
            margin: 10px 5px; 
            text-decoration: none; 
            border-radius: 5px; 
            font-weight: bold;
        }
        .danger { background-color: #dc3545; color: white; }
        .success { background-color: #28a745; color: white; }
        .warning { background-color: #ffc107; color: black; }
        .info { background-color: #17a2b8; color: white; }
        .alert { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .alert-danger { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <h1>🚨 Emergency Sync Control</h1>
    
    <?php if ($action === 'disable'): ?>
        <?php
        // Disable WooCommerce to Square sync
        $result = $settings->disable_woo_to_square_sync();
        ?>
        <div class="alert alert-success">
            <h3>✅ Sync Disabled Successfully</h3>
            <p>WooCommerce to Square sync has been disabled. Your WooCommerce products will no longer automatically sync to Square.</p>
        </div>
    <?php elseif ($action === 'enable'): ?>
        <?php
        // Enable WooCommerce to Square sync (use with caution!)
        $result = $settings->enable_woo_to_square_sync();
        ?>
        <div class="alert alert-warning">
            <h3>⚠️ Sync Enabled</h3>
            <p>WooCommerce to Square sync has been enabled. WooCommerce product changes will now automatically sync to Square.</p>
        </div>
    <?php endif; ?>
    
    <?php
    $sync_direction = $settings->get_sync_direction_settings();
    $current_status = $sync_direction['woo_to_square'];
    ?>
    
    <h2>Current Status</h2>
    <div class="alert <?php echo $current_status ? 'alert-danger' : 'alert-success'; ?>">
        <strong>WooCommerce → Square Sync:</strong> 
        <?php echo $current_status ? 'ENABLED' : 'DISABLED'; ?>
        
        <?php if ($current_status): ?>
            <br><br><strong>⚠️ WARNING:</strong> This means WooCommerce product changes will automatically sync to Square, including deletions!
        <?php else: ?>
            <br><br><strong>✅ SAFE:</strong> WooCommerce products will not automatically sync to Square.
        <?php endif; ?>
    </div>
    
    <h2>Actions</h2>
    
    <?php if ($current_status): ?>
        <a href="?action=disable" class="button danger" onclick="return confirm('Are you sure you want to disable WooCommerce to Square sync?')">
            🛑 DISABLE Sync (Recommended)
        </a>
    <?php else: ?>
        <a href="?action=enable" class="button warning" onclick="return confirm('⚠️ WARNING: This will enable automatic syncing from WooCommerce to Square. Are you absolutely sure?')">
            ⚠️ Enable Sync (Use with Caution)
        </a>
    <?php endif; ?>
    
    <a href="?" class="button info">🔄 Refresh Status</a>
    
    <h2>What This Does</h2>
    <ul>
        <li><strong>Disable Sync:</strong> Prevents WooCommerce product changes from automatically syncing to Square</li>
        <li><strong>Enable Sync:</strong> Allows WooCommerce product changes to automatically sync to Square (dangerous in production)</li>
    </ul>
    
    <h2>Safe Alternatives</h2>
    <p>Instead of automatic sync, consider:</p>
    <ul>
        <li>Manual sync from the SquareKit dashboard</li>
        <li>Scheduled sync at specific times</li>
        <li>One-way sync from Square to WooCommerce only</li>
    </ul>
    
    <div class="alert alert-warning">
        <h3>🔒 Production Safety</h3>
        <p>For production environments, it's recommended to:</p>
        <ul>
            <li>Keep WooCommerce → Square sync DISABLED</li>
            <li>Use manual sync operations when needed</li>
            <li>Test all sync operations in staging first</li>
        </ul>
    </div>
    
</body>
</html>
