(()=>{function e(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,l,r,o,c=[],i=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(a=r.call(n)).done)&&(c.push(a.value),c.length!==t);i=!0);}catch(e){s=!0,l=e}finally{try{if(!i&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(s)throw l}}return c}}(e,n)||function(e,n){if(e){if("string"==typeof e)return t(e,n);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?t(e,n):void 0}}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}!function(t,n){if(t&&t.plugins&&t.element&&t.data&&n&&n.blocksCheckout){var a=t.plugins.registerPlugin,l=n.blocksCheckout,r=l.ExperimentalDiscountsMeta,o=l.extensionCartUpdate,c=t.element,i=c.useState,s=c.useEffect,u=(t.data.dispatch,t.data.useSelect),m=function(){var n,a,l=e(i(!1),2),r=l[0],c=l[1],m=e(i(null),2),d=m[0],y=m[1],p=e(i(0),2),f=p[0],v=p[1],h=e(i([]),2),g=h[0],w=h[1],E=e(i(null),2),b=E[0],k=E[1],C=e(i(null),2),_=C[0],A=C[1],N=e(i(0),2),S=N[0],R=N[1],j=e(i(0),2),L=j[0],x=j[1],q=e(i({other:"points"}),2),D=q[0],I=q[1],P=e(i(loyalty_data.isLoggedIn),2),O=P[0],U=(P[1],u(function(e){var t;return null===(t=e("wc/store/cart").getCartData())||void 0===t||null===(t=t.billingAddress)||void 0===t?void 0:t.phone},[])),Y=u(function(e){var t;return null===(t=e("wc/store/cart").getCartData())||void 0===t||null===(t=t.shippingAddress)||void 0===t?void 0:t.phone},[]),M=function(){if(O){c(!0);var e=U||Y||"";console.log(e),t.ajax.post("fetch_loyalty_data",{nonce:loyalty_data.nonce,data:{phone:e}}).done(function(e){e.points?(v(e.points),I(e.terminology),w(e.rewards),x(e.pointsEarned),e.activeReward&&(A(e.activeReward.name),R(e.activeReward.points))):k("Failed to fetch loyalty data"),c(!1)}).fail(function(e){console.error("Error fetching loyalty data:",e),k("Loyalty program user not found"),c(!1)})}};return s(function(){var e=null;if(O){M();var n=t.data.subscribe(function(){var n=t.data.select("wc/store/cart").getCartTotals();n&&n!==e&&(e=n,M())});return function(){n()}}},[O]),t.element.createElement("div",{className:"loyalty-blocks--block-container"},r&&t.element.createElement("div",{id:"loyalty-loader"},"Loading..."),b&&t.element.createElement("div",{className:"error"},b),!b&&!r&&t.element.createElement("div",null,O?t.element.createElement("div",null,t.element.createElement("h4",null,"Loyalty Program"),t.element.createElement("p",null,"You currently have ",f," ",D.other,"!"),_?t.element.createElement("div",null,t.element.createElement("p",null,"Active reward: ",_),t.element.createElement("p",null,"You will redeem ",t.element.createElement("span",{className:"points-redeemed"},S)," ",t.element.createElement("span",{className:"terminology"},D.other),"."),t.element.createElement("button",{id:"blocks-remove-loyalty-reward",disabled:r,onClick:function(){c(!0),t.ajax.post("remove_loyalty_discount",{nonce:loyalty_data.nonce}).done(function(e){o({namespace:"squarewoosync"}),A(null),R(0),y(null),c(!1)}).fail(function(){alert("Error removing the reward."),c(!1)})}},"Remove Reward")):t.element.createElement("div",null,g.length>0?(null==(a=t.data.select("wc/store/cart").getCartData())||null===(n=a.fees)||void 0===n||n.some(function(e){return"gift-card"===e.key}),t.element.createElement("div",{className:"available-rewards"},t.element.createElement("p",null,"Select a reward:"),t.element.createElement("div",{className:"loyalty-blocks-rewards-container"},g.map(function(e){return t.element.createElement("label",{key:e.id},t.element.createElement("input",{type:"radio",name:"loyalty_reward",value:e.id,onChange:function(e){return y(e.target.value)}}),e.name," (",e.points," ",D.other,")")})),t.element.createElement("button",{id:"blocks-apply-loyalty-reward",disabled:r,onClick:function(){var e;(e=d)?(c(!0),o({namespace:"squarewoosync",data:{nonce:loyalty_data.nonce,selected_tier:e}}).then(function(){var t=g.find(function(t){return t.id===e});t&&(A(t.name),R(t.points)),c(!1)}).catch(function(e){console.error("Error updating the cart:",e),alert("Unable to apply loyalty reward, please contact support"),c(!1)})):alert("Please select a reward")}},"Apply Rewards"))):t.element.createElement("p",null,"No rewards available at this time."),L>0&&!_&&t.element.createElement("p",{className:"loyalty-blocks-points-earn"},"You will earn ",t.element.createElement("span",{className:"points-earned"},L)," ",t.element.createElement("span",{className:"terminology"},D.other)," from this purchase."))):t.element.createElement("div",null,t.element.createElement("p",null,"Login to take advantage of our loyalty program and earn points on this purchase and claim rewards!"),t.element.createElement("a",{href:"/my-account"},"Login here"))))};loyalty_data.loyalty_enabled&&a("squaresync-loyalty-plugin",{render:function(){return t.element.createElement(r,null,t.element.createElement(m,null))},scope:"woocommerce-checkout"})}else console.error("Required WordPress or WooCommerce scripts are not loaded.")}(window.wp,window.wc)})();