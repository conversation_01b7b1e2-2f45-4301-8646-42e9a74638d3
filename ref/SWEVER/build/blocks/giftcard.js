(()=>{function e(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,c,a,l,r=[],s=!0,i=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(r.push(o.value),r.length!==t);s=!0);}catch(e){i=!0,c=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(i)throw c}}return r}}(e,n)||function(e,n){if(e){if("string"==typeof e)return t(e,n);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?t(e,n):void 0}}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}!function(t,n){if(t&&t.plugins&&t.element&&t.i18n&&t.components&&n&&n.blocksCheckout&&window.giftcard_data){var o=t.plugins.registerPlugin,c=t.element,a=c.useState,l=c.useCallback,r=c.useEffect,s=t.data.useSelect,__=t.i18n.__,i=t.components.Button,m=n.blocksCheckout,u=m.ExperimentalDiscountsMeta,d=m.extensionCartUpdate,p=n.wcBlocksData.processErrorResponse;giftcard_data.giftcards_enabled&&o("squaresync-giftcard-panel-plugin",{render:function(){var n=e(a(!1),2),o=n[0],c=n[1],m=e(a(!1),2),f=m[0],w=m[1],b=e(a(null),2),g=b[0],v=b[1],h=giftcard_data.appliedGiftCard||"",k=e(a(h),2),E=k[0],_=k[1],y=e(a(h),2),C=y[0],x=y[1],N=e(a(!h),2),q=N[0],z=N[1],A=e(a(!1),2),B=A[0],S=A[1],G=!E||o,M=s(function(e){return e("wc/store/cart").getCartData()},[]);r(function(){var e,t;if((null==M||null===(e=M.fees)||void 0===e?void 0:e.length)>0&&M.fees.find(function(e){return"gift-card"===e.key})&&x(E),(null==M||null===(t=M.errors)||void 0===t?void 0:t.length)>0){var n=M.errors.find(function(e){return e.message.includes("Gift Card")});n&&v(n.message)}},[M]);var j=l(function(){z(function(e){return!e})},[]),D=l(function(e){e.preventDefault(),E&&(c(!0),v(null),d({namespace:"squarewoosync-giftcard",data:{nonce:giftcard_data.nonce,gift_card_code:E}}).then(function(){c(!1)}).catch(function(e){console.error("Error applying gift card:",e),v(__("Unable to apply gift card.","squarewoosync")),p(e),c(!1)}))},[E]),I=l(function(){C&&(w(!0),v(null),d({namespace:"squarewoosync-giftcard-remove",data:{nonce:giftcard_data.nonce}}).then(function(){x(""),_(""),w(!1),v(null),z(!0)}).catch(function(e){console.error("Error removing gift card:",e),v(__("Unable to remove gift card.","squarewoosync")),p(e),c(!1)}))},[C]);return t.element.createElement(u,null,t.element.createElement("div",{className:"wp-block-woocommerce-checkout-order-summary-coupon-form-block wc-block-components-totals-wrapper gift-card-wrapper",style:{marginTop:16,borderBottom:"1px solid hsla(0, 0%, 7%, .11)"}},t.element.createElement("div",{role:"heading","aria-level":"2",className:"wc-block-components-totals-coupon wc-block-components-panel ".concat(o?"wc-block-components-loading-mask_children":"")},C?t.element.createElement("div",{className:"wc-block-components-panel__content"},t.element.createElement("p",null,__("Gift Card Applied:","squarewoosync")," ",t.element.createElement("strong",null,C)),t.element.createElement(i,{className:"wc-block-components-button wp-element-button wc-block-components-totals-coupon__button contained ".concat(o?"wc-block-components-button--loading":""),id:"blocks-remove-giftcard",disabled:f,onClick:I},__("Remove Gift Card","squarewoosync")),g&&t.element.createElement("div",{class:"wc-block-components-validation-error",role:"alert"},t.element.createElement("p",{id:"validate-error-coupon"},t.element.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},t.element.createElement("path",{d:"M10 2c4.42 0 8 3.58 8 8s-3.58 8-8 8-8-3.58-8-8 3.58-8 8-8zm1.13 9.38l.35-6.46H8.52l.35 6.46h2.26zm-.09 3.36c.24-.23.37-.55.37-.96 0-.42-.12-.74-.36-.97s-.59-.35-1.06-.35-.82.12-1.07.35-.37.55-.37.97c0 .***********.***********.34 1.06.34s.8-.11 1.05-.34z"})),t.element.createElement("span",null," ",g)))):t.element.createElement("div",null,t.element.createElement("div",{className:"wc-block-components-panel__button",role:"button",tabIndex:"0","aria-expanded":q?"true":"false",onClick:j},t.element.createElement("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24","aria-hidden":"true",className:"wc-block-components-panel__button-icon",focusable:"false"},t.element.createElement("path",{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})),__("Redeem a gift card","squarewoosync")),q&&t.element.createElement("div",{className:"wc-block-components-panel__content"},t.element.createElement("div",{className:"".concat(o?"wc-block-components-loading-mask":"")},t.element.createElement("div",{className:"".concat(o?"wc-block-components-loading-mask__children":"")},t.element.createElement("div",{className:"wc-block-components-totals-coupon__content"},t.element.createElement("form",{className:"wc-block-components-totals-coupon__form",id:"wc-block-components-totals-coupon__form",onSubmit:D},t.element.createElement("div",{className:"wc-block-components-text-input wc-block-components-totals-coupon__input has-floating-label "+(E||B?"is-active":"")},t.element.createElement("input",{type:"text",id:"wc-block-components-totals-coupon__input-giftcard",value:E,onFocus:function(){return S(!0)},onBlur:function(){return S(!1)},onChange:function(e){return _(e.target.value)},autoCapitalize:"off",autoComplete:"off","aria-invalid":"false"}),t.element.createElement("label",{htmlFor:"wc-block-components-totals-coupon__input-giftcard"},__("Enter code","squarewoosync"))),t.element.createElement(i,{type:"submit",className:"wc-block-components-button wp-element-button wc-block-components-totals-coupon__button contained ".concat(o?"wc-block-components-button--loading":""),disabled:G||o,style:G?{pointerEvents:"none"}:{}},o&&t.element.createElement("span",{className:"wc-block-components-spinner","aria-hidden":"true"}),t.element.createElement("span",{className:"wc-block-components-button__text"},__("Apply","squarewoosync"))))))),g&&t.element.createElement("div",{class:"wc-block-components-validation-error",role:"alert"},t.element.createElement("p",{id:"validate-error-coupon"},t.element.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},t.element.createElement("path",{d:"M10 2c4.42 0 8 3.58 8 8s-3.58 8-8 8-8-3.58-8-8 3.58-8 8-8zm1.13 9.38l.35-6.46H8.52l.35 6.46h2.26zm-.09 3.36c.24-.23.37-.55.37-.96 0-.42-.12-.74-.36-.97s-.59-.35-1.06-.35-.82.12-1.07.35-.37.55-.37.97c0 .***********.***********.34 1.06.34s.8-.11 1.05-.34z"})),t.element.createElement("span",null," ",g))))))))},scope:"woocommerce-checkout"})}else console.error("Required scripts or global data for Gift Card Block are not available.")}(window.wp,window.wc)})();