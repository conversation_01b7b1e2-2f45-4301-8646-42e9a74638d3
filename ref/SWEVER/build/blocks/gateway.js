(()=>{"use strict";const e=window.wp.element;var t=window.wc.wcSettings.getSetting,n=function(){var e=t("squaresync_credit_data",null);if(!e)throw new Error("Square initialization data is not available");return{title:e.title||"",applicationId:e.applicationId||"",locationId:e.locationId||"",isSandbox:e.is_sandbox||!1,availableCardTypes:e.accepted_credit_cards||{},loggingEnabled:e.logging_enabled||!1,generalError:e.general_error||"",showSavedCards:e.show_saved_cards||!1,showSaveOption:e.show_save_option||!1,supports:e.supports||{},isTokenizationForced:e.is_tokenization_forced||!1,paymentTokenNonce:e.payment_token_nonce||"",isDigitalWalletsEnabled:"yes"===e.enable_apple_pay||"yes"===e.enable_google_pay||"yes"===e.enable_after_pay||!1,googlePay:e.enable_google_pay||"no",applePay:e.enable_apple_pay||"no",afterPay:e.enable_after_pay||"no",isPayForOrderPage:e.is_pay_for_order_page||!1,recalculateTotalNonce:e.recalculate_totals_nonce||!1,context:e.context||"",ajaxUrl:e.ajax_url||"",paymentRequestNonce:e.payment_request_nonce||"",googlePayColor:e.google_pay_color||"black",applePayColor:e.apple_pay_color||"black",applePayType:e.apple_pay_type||"buy",hideButtonOptions:e.hide_button_options||[],hasSubscription:e.hasSubscription||!1}};function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",a=n.toStringTag||"@@toStringTag";function c(n,r,o,a){var c=r&&r.prototype instanceof l?r:l,s=Object.create(c.prototype);return i(s,"_invoke",function(n,r,o){var i,a,c,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,a=0,c=e,p.n=n,u}};function d(n,r){for(a=n,c=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(a=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,a=0))}if(o||n>1)return u;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),a=s,c=y;(t=a<2?e:c)||!f;){i||(a?a<3?(a>1&&(p.n=-1),d(a,c)):p.n=c:p.v=c);try{if(l=2,i){if(a||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,a<2&&(a=0)}else 1===a&&(t=i.return)&&t.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=e}else if((t=(f=p.n<0)?c:n.call(r,p))!==u)break}catch(t){i=e,a=1,c=t}finally{l=1}}return{value:t,done:f}}}(n,o,a),!0),s}var u={};function l(){}function s(){}function f(){}t=Object.getPrototypeOf;var p=[][r]?t(t([][r]())):(i(t={},r,function(){return this}),t),d=f.prototype=l.prototype=Object.create(p);function y(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,i(e,a,"GeneratorFunction")),e.prototype=Object.create(d),e}return s.prototype=f,i(d,"constructor",f),i(f,"constructor",s),s.displayName="GeneratorFunction",i(f,a,"GeneratorFunction"),i(d),i(d,a,"Generator"),i(d,r,function(){return this}),i(d,"toString",function(){return"[object Generator]"}),(o=function(){return{w:c,m:y}})()}function i(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}i=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var a=function(t,n){i(e,t,function(e){return this._invoke(t,n,e)})};a("next",0),a("throw",1),a("return",2)}},i(e,t,n,r)}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach(function(t){u(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function u(e,t,n){return(t=function(e){var t=function(e){if("object"!=r(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==r(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}var s=(0,e.createContext)(!1),f=function(t){var r=t.checkoutFormHandler,i=t.eventRegistration,a=t.emitResponse,u=(0,e.useContext)(s),f=i.onPaymentSetup,p=i.onCheckoutAfterProcessingWithError,d=i.onCheckoutAfterProcessingWithSuccess;return function(t,r,i,a,u,s){var f=(0,e.useRef)(i);(0,e.useEffect)(function(){f.current=i},[i]),(0,e.useEffect)(function(){var e=function(){var e,t=(e=o().m(function e(){var t,i,l,p,d,y,v,m,b,h,g,w,C,S,_,O;return o().w(function(e){for(;;)switch(e.n){case 0:if(i={type:r.responseTypes.SUCCESS},l={nonce:"",notices:[],logs:[]},null===(t=f.current)||void 0===t||!t.token){e.n=6;break}return e.p=1,p=n(),d=p.paymentTokenNonce,e.n=2,fetch("".concat(wc.wcSettings.ADMIN_URL,"admin-ajax.php?action=squaresync_credit_card_get_token_by_id&token_id=").concat(f.current.token,"&nonce=").concat(d));case 2:return y=e.v,e.n=3,y.json();case 3:v=e.v,m=v.success,b=v.data,l.token=m?b:"",e.n=5;break;case 4:e.p=4,e.v;case 5:e.n=9;break;case 6:return e.p=6,e.n=7,u(f.current.card);case 7:w=e.v,l.nonce=w.token,null!=w&&null!==(h=w.details)&&void 0!==h&&h.card&&null!=w&&null!==(g=w.details)&&void 0!==g&&g.billing&&(l.cardData=c(c({},w.details.card),w.details.billing)),e.n=9;break;case 8:e.p=8,_=e.v,console.error("Error creating nonce:",_);case 9:if(!(C=l.token||l.nonce)){e.n=13;break}return e.p=10,e.n=11,s(f.current.payments,C);case 11:S=e.v,l.verificationToken=S.verificationToken||"",l.logs=l.logs.concat(S.log||[]),l.errors=l.notices.concat(S.errors||[]),e.n=13;break;case 12:e.p=12,O=e.v,console.error("Error during buyer verification:",O);case 13:return C||l.logs.length>0?i.meta={paymentMethodData:a(l)}:l.notices.length>0&&(console.log("Errors or notices found:",l.notices),i.type=r.responseTypes.ERROR,i.message=l.notices),e.a(2,i)}},e,null,[[10,12],[6,8],[1,4]])}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){l(i,r,o,a,c,"next",e)}function c(e){l(i,r,o,a,c,"throw",e)}a(void 0)})});return function(){return t.apply(this,arguments)}}();return t(e)},[t,r.responseTypes.SUCCESS,r.responseTypes.ERROR,u,s,a])}(f,a,u,r.getPaymentMethodData,r.createNonce,r.verifyBuyer),function(t,n,r){(0,e.useEffect)(function(){var e=function(e){var t={type:r.responseTypes.SUCCESS},n=e.processingResponse,o=n.paymentStatus,i=n.paymentDetails;return o===r.responseTypes.ERROR&&i.checkoutNotices&&(t={type:r.responseTypes.ERROR,message:JSON.parse(i.checkoutNotices),messageContext:r.noticeContexts.PAYMENTS,retry:!0}),t},o=t(e),i=n(e);return function(){o(),i()}},[t,n,r.noticeContexts.PAYMENTS,r.responseTypes.ERROR,r.responseTypes.SUCCESS])}(p,d,a),null};function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function d(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return y(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(y(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,y(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,y(f,"constructor",l),y(l,"constructor",u),u.displayName="GeneratorFunction",y(l,o,"GeneratorFunction"),y(f),y(f,o,"Generator"),y(f,r,function(){return this}),y(f,"toString",function(){return"[object Generator]"}),(d=function(){return{w:i,m:p}})()}function y(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}y=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){y(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},y(e,t,n,r)}function v(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function m(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){v(i,r,o,a,c,"next",e)}function c(e){v(i,r,o,a,c,"throw",e)}a(void 0)})}}function b(e,t,n){return(t=function(e){var t=function(e){if("object"!=p(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==p(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return g(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function w(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return C(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(C(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,C(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,C(f,"constructor",l),C(l,"constructor",u),u.displayName="GeneratorFunction",C(l,o,"GeneratorFunction"),C(f),C(f,o,"Generator"),C(f,r,function(){return this}),C(f,"toString",function(){return"[object Generator]"}),(w=function(){return{w:i,m:p}})()}function C(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}C=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){C(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},C(e,t,n,r)}function S(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function _(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return O(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?O(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var E=function(t){var r=t.children,o=t.token,i=void 0===o?null:o,a=t.defaults.postalCode,c=void 0===a?"":a,u=_((0,e.useState)(!1),2),l=u[0],f=u[1],p=_((0,e.useState)(!1),2),d=p[0],y=p[1],v=n(),m=v.applicationId,b=v.locationId;return(0,e.useEffect)(function(){!l&&window.Square&&f(Square.payments(m,b))},[m,b,l]),(0,e.useEffect)(function(){if(l&&!d&&!i){var e=function(){var e,t=(e=w().m(function e(){var t;return w().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,l.card({postalCode:c});case 1:t=e.v,y(t);case 2:return e.a(2)}},e)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){S(i,r,o,a,c,"next",e)}function c(e){S(i,r,o,a,c,"throw",e)}a(void 0)})});return function(){return t.apply(this,arguments)}}();e()}},[l,d,i,c]),l?wp.element.createElement(s.Provider,{value:{payments:l,card:d,token:i}},r):null};function j(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return k(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(k(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,k(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,k(f,"constructor",l),k(l,"constructor",u),u.displayName="GeneratorFunction",k(l,o,"GeneratorFunction"),k(f),k(f,o,"Generator"),k(f,r,function(){return this}),k(f,"toString",function(){return"[object Generator]"}),(j=function(){return{w:i,m:p}})()}function k(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}k=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){k(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},k(e,t,n,r)}function P(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}var T=function(){var t=(0,e.useContext)(s).card;console.log("test");var n=(0,e.useRef)(!1);return(0,e.useEffect)(function(){if(t){var e=function(){var e,r=(e=j().m(function e(){return j().w(function(e){for(;;)switch(e.n){case 0:t.attach(n.current);case 1:return e.a(2)}},e)}),function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){P(i,r,o,a,c,"next",e)}function c(e){P(i,r,o,a,c,"throw",e)}a(void 0)})});return function(){return r.apply(this,arguments)}}();e()}},[t]),wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{ref:n}))},x=function(t){var r=t.billing,o=t.eventRegistration,i=t.emitResponse,a=(t.shouldSavePayment,function(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=h((0,e.useState)(!1),2),a=i[0],c=i[1],u=h((0,e.useState)(""),2),l=u[0],s=u[1],f=(0,e.useMemo)(function(){var e=r&&!o?"STORE":"CHARGE",n={billingContact:{familyName:t.billingData.last_name||"",givenName:t.billingData.first_name||"",email:t.billingData.email||"",country:t.billingData.country||"",region:t.billingData.state||"",city:t.billingData.city||"",postalCode:t.billingData.postcode||"",phone:t.billingData.phone||"",addressLines:[t.billingData.address_1||"",t.billingData.address_2||""]},intent:e};return"CHARGE"===e&&(n.amount=(t.cartTotal.value/100).toString(),n.currencyCode=t.currency.code),n},[t.billingData,t.cartTotal.value,t.currency.code,r,o]),p=(0,e.useCallback)(function(e){var t,n,i,a=e.cardData,c=void 0===a?{}:a,u=e.nonce,l=e.verificationToken,s=e.notices,f=(e.logs,b(b(b(b(b(b(b(b(b(b(i={},"wc-squaresync_credit-card-type",(null==c?void 0:c.brand)||""),"wc-squaresync_credit-last-four",(null==c?void 0:c.last4)||""),"wc-squaresync_credit-exp-month",(null==c||null===(t=c.expMonth)||void 0===t?void 0:t.toString())||""),"wc-squaresync_credit-exp-year",(null==c||null===(n=c.expYear)||void 0===n?void 0:n.toString())||""),"wc-squaresync_credit-payment-postcode",(null==c?void 0:c.postalCode)||""),"wc-squaresync_credit-payment-nonce",u||""),"wc-squaresync_credit-payment-token",o||""),"wc-squaresync_credit-buyer-verification-token",l||""),"wc-squaresync_credit-tokenize-payment-method",r||!1),"log-data",""),b(i,"checkout-notices",s.length>0?JSON.stringify(s):""));return o&&(f.token=o),f},[l,r,o]),y=(0,e.useCallback)(function(){var e=m(d().m(function e(t){return d().w(function(e){for(;;)switch(e.n){case 0:if(o){e.n=2;break}return e.n=1,t.tokenize();case 1:return e.a(2,e.v);case 2:return e.a(2,o)}},e)}));return function(_x){return e.apply(this,arguments)}}(),[o]),v=(0,e.useCallback)(function(e){var t={notices:[],logs:[]};return e&&e.token?t.verificationToken=e.token:console.log("Verification token is missing from the Square response",t),t},[]),g=(0,e.useCallback)(function(){var e=m(d().m(function e(t,n){var r,o;return d().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,t.verifyBuyer(n,f);case 1:return r=e.v,e.a(2,v(r));case 2:return e.p=2,o=e.v,console.error("Error in verifyBuyer:",o),e.a(2,!1)}},e,null,[[0,2]])}));return function(t,n){return e.apply(this,arguments)}}(),[f,v]);return{handleInputReceived:(0,e.useCallback)(function(e){if("cardBrandChanged"===e.eventType){var t=e.cardBrand,r="plain";null!==t&&"unknown"!==t||(r=""),null!==n().availableCardTypes[t]&&(r=n().availableCardTypes[t]),s(r)}},[]),isLoaded:a,setLoaded:c,getPostalCode:(0,e.useCallback)(function(){return t.billingData.postcode||""},[t.billingData.postcode]),cardType:l,createNonce:y,verifyBuyer:g,getPaymentMethodData:p}}(r,!1));return wp.element.createElement(E,{defaults:{postalCode:a.getPostalCode()}},wp.element.createElement(T,null),wp.element.createElement(f,{checkoutFormHandler:a,eventRegistration:o,emitResponse:i}))},A=["RenderedComponent"],L=window.wc.wcBlocksRegistry,R=(L.registerPaymentMethod,L.registerExpressPaymentMethod,function(e){var t=e.RenderedComponent,n=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,A);return wp.element.createElement(t,n)});const H={name:"squaresync_credit",paymentMethodId:"squaresync_credit",label:wp.element.createElement(function(e){var t=e.components.PaymentMethodLabel,n=e.labelText;return wp.element.createElement(t,{text:n})},{labelText:"Credit Card"}),content:wp.element.createElement(R,{RenderedComponent:x}),edit:wp.element.createElement(R,{RenderedComponent:x}),ariaLabel:"Square",canMakePayment:function(){return!(!n().applicationId||!n().locationId)},supports:{features:n().supports,showSaveOption:!n().hasSubscription}},I=window.wp.data;function G(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return D(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(D(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,D(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,D(f,"constructor",l),D(l,"constructor",u),u.displayName="GeneratorFunction",D(l,o,"GeneratorFunction"),D(f),D(f,o,"Generator"),D(f,r,function(){return this}),D(f,"toString",function(){return"[object Generator]"}),(G=function(){return{w:i,m:p}})()}function D(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}D=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){D(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},D(e,t,n,r)}function N(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function M(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){N(i,r,o,a,c,"next",e)}function c(e){N(i,r,o,a,c,"throw",e)}a(void 0)})}}var F=function(e){return n().ajaxUrl.replace("%%endpoint%%","square_digital_wallet_".concat(e))},q=function(){return new Promise(function(e,t){var r={context:n().context,security:n().paymentRequestNonce,is_pay_for_order_page:!1};jQuery.post(F("get_payment_request"),r,function(n){return n.success?e(n.data):t(n.data)})})},V=function(){var e=M(G().m(function e(t){return G().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e,n){return jQuery.post(F("recalculate_totals"),t,function(t){return t.success?e(t.data):n(t.data)})}))},e)}));return function(_x){return e.apply(this,arguments)}}(),Z=function(){var e=M(G().m(function e(t){var r,o;return G().w(function(e){for(;;)switch(e.n){case 0:return r={context:n().context,shipping_option:t.id,security:n().recalculateTotalNonce,is_pay_for_order_page:n().isPayForOrderPage},e.n=1,V(r);case 1:return o=e.v,e.a(2,o)}},e)}));return function(t){return e.apply(this,arguments)}}(),U=function(){var e=M(G().m(function e(t){var r,o;return G().w(function(e){for(;;)switch(e.n){case 0:return r={context:n().context,shipping_contact:t,security:n().recalculateTotalNonce,is_pay_for_order_page:n().isPayForOrderPage},e.n=1,V(r);case 1:return o=e.v,e.a(2,o)}},e)}));return function(t){return e.apply(this,arguments)}}(),B=function(){var e=M(G().m(function e(t,n,r){var o,i;return G().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,t.verifyBuyer(n,r);case 1:return o=e.v,e.a(2,o);case 2:throw e.p=2,i=e.v,console.error("Error during buyer verification:",i),i;case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t,n,r){return e.apply(this,arguments)}}(),z=function(){var e=M(G().m(function e(t){var n;return G().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,t.tokenize();case 1:if("OK"!==(n=e.v).status){e.n=2;break}return e.a(2,n);case 2:e.n=4;break;case 3:return e.p=3,e.v,e.a(2,!1);case 4:return e.a(2,!1)}},e,null,[[0,3]])}));return function(t){return e.apply(this,arguments)}}();function $(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function W(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$(Object(n),!0).forEach(function(t){Y(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Y(e,t,n){return(t=function(e){var t=function(e){if("object"!=J(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=J(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==J(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e){return J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},J(e)}function K(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return Q(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(Q(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,Q(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,Q(f,"constructor",l),Q(l,"constructor",u),u.displayName="GeneratorFunction",Q(l,o,"GeneratorFunction"),Q(f),Q(f,o,"Generator"),Q(f,r,function(){return this}),Q(f,"toString",function(){return"[object Generator]"}),(K=function(){return{w:i,m:p}})()}function Q(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Q=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){Q(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},Q(e,t,n,r)}function X(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return ee(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ee(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ee(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function te(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function ne(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){te(i,r,o,a,c,"next",e)}function c(e){te(i,r,o,a,c,"throw",e)}a(void 0)})}}function re(e){var t=e.givenName,n=e.familyName,r=e.addressLines,o=void 0===r?[]:r,i=e.city,a=e.state,c=e.email,u=e.countryCode,l=e.postalCode;return!!(t&&n&&o[0]&&i&&a&&u&&l&&c)}function oe(e,t){var n,r,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e&&"object"===J(e)||(e={});var i=e,a=i.familyName,c=i.givenName,u=i.region,l=i.state,s=i.country,f=i.countryCode,p=i.city,d=i.addressLines,y=i.postalCode,v=i.phone,m=i.email,b=o.first_name,h=void 0===b?"":b,g=o.last_name,w=void 0===g?"":g,C=o.address_1,S=void 0===C?"":C,_=o.address_2,O=void 0===_?"":_,E=o.city,j=void 0===E?"":E,k=o.state,P=void 0===k?"":k,T=o.country,x=void 0===T?"":T,A=o.postcode,L=void 0===A?"":A,R=o.phone,H=void 0===R?"":R,I=o.email,G=null!=y?y:L,D=null!=m?m:void 0===I?"":I,N=null!=v?v:H;return{givenName:null!=c?c:h,familyName:null!=a?a:w,state:t?null!=u?u:P:null!==(n=null!=l?l:u)&&void 0!==n?n:P,countryCode:t?null!=s?s:x:null!==(r=null!=f?f:s)&&void 0!==r?r:x,city:null!=p?p:j,addressLines:Array.isArray(d)&&d.length>0?d:[S,O].filter(Boolean),postalCode:G,phone:N,email:D}}function ie(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return ae(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(ae(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,ae(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,ae(f,"constructor",l),ae(l,"constructor",u),u.displayName="GeneratorFunction",ae(l,o,"GeneratorFunction"),ae(f),ae(f,o,"Generator"),ae(f,r,function(){return this}),ae(f,"toString",function(){return"[object Generator]"}),(ie=function(){return{w:i,m:p}})()}function ae(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}ae=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){ae(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},ae(e,t,n,r)}function ce(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function ue(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return le(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?le(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function le(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var se=function(){return wp.element.createElement("svg",{width:"343",height:"50",viewBox:"0 0 343 50",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("rect",{width:"343",height:"50",rx:"4",fill:"black"}),wp.element.createElement("path",{d:"M97.6482 17.68H103.048C103.622 17.68 104.162 17.78 104.668 17.98C105.188 18.1667 105.642 18.4333 106.028 18.78C106.428 19.1133 106.742 19.5133 106.968 19.98C107.195 20.4467 107.308 20.9533 107.308 21.5C107.308 22.2333 107.122 22.86 106.748 23.38C106.388 23.8867 105.928 24.2667 105.368 24.52V24.64C106.088 24.8933 106.668 25.3067 107.108 25.88C107.562 26.4533 107.788 27.1467 107.788 27.96C107.788 28.5733 107.668 29.1267 107.428 29.62C107.188 30.1133 106.862 30.54 106.448 30.9C106.035 31.2467 105.555 31.52 105.008 31.72C104.475 31.9067 103.902 32 103.288 32H97.6482V17.68ZM102.968 23.66C103.302 23.66 103.602 23.6067 103.868 23.5C104.135 23.38 104.355 23.2333 104.528 23.06C104.715 22.8733 104.855 22.6667 104.948 22.44C105.042 22.2 105.088 21.96 105.088 21.72C105.088 21.48 105.042 21.2467 104.948 21.02C104.855 20.78 104.722 20.5733 104.548 20.4C104.375 20.2133 104.162 20.0667 103.908 19.96C103.655 19.84 103.368 19.78 103.048 19.78H99.9082V23.66H102.968ZM103.288 29.9C103.648 29.9 103.968 29.84 104.248 29.72C104.528 29.6 104.762 29.44 104.948 29.24C105.135 29.04 105.275 28.8133 105.368 28.56C105.475 28.3067 105.528 28.0467 105.528 27.78C105.528 27.5133 105.475 27.26 105.368 27.02C105.275 26.7667 105.128 26.5467 104.928 26.36C104.728 26.16 104.482 26 104.188 25.88C103.908 25.76 103.582 25.7 103.208 25.7H99.9082V29.9H103.288ZM116.875 30.68H116.755C116.461 31.1467 116.041 31.54 115.495 31.86C114.961 32.1667 114.355 32.32 113.675 32.32C112.435 32.32 111.508 31.9533 110.895 31.22C110.281 30.4733 109.975 29.4867 109.975 28.26V22.2H112.195V27.96C112.195 28.8133 112.388 29.4133 112.775 29.76C113.175 30.0933 113.695 30.26 114.335 30.26C114.708 30.26 115.041 30.18 115.335 30.02C115.641 29.86 115.901 29.6467 116.115 29.38C116.328 29.1 116.488 28.7867 116.595 28.44C116.701 28.08 116.755 27.7067 116.755 27.32V22.2H118.975V32H116.875V30.68ZM124.747 31.44L120.667 22.2H123.147L125.847 28.7H125.947L128.567 22.2H131.027L124.887 36.32H122.527L124.747 31.44ZM136.589 22.2H139.009L140.909 28.98H140.989L143.189 22.2H145.489L147.669 28.98H147.749L149.629 22.2H152.009L148.869 32H146.529L144.309 25.14H144.229L142.069 32H139.729L136.589 22.2ZM154.995 20.46C154.568 20.46 154.208 20.3133 153.915 20.02C153.635 19.7267 153.495 19.3733 153.495 18.96C153.495 18.5467 153.635 18.1933 153.915 17.9C154.208 17.6067 154.568 17.46 154.995 17.46C155.408 17.46 155.755 17.6067 156.035 17.9C156.328 18.1933 156.475 18.5467 156.475 18.96C156.475 19.3733 156.328 19.7267 156.035 20.02C155.755 20.3133 155.408 20.46 154.995 20.46ZM153.875 32V22.2H156.095V32H153.875ZM159.982 24.16H158.262V22.2H159.982V19.2H162.202V22.2H164.622V24.16H162.202V28.52C162.202 28.76 162.229 28.98 162.282 29.18C162.336 29.38 162.416 29.5467 162.522 29.68C162.749 29.9333 163.036 30.06 163.382 30.06C163.596 30.06 163.762 30.0467 163.882 30.02C164.002 29.98 164.129 29.9267 164.262 29.86L164.942 31.82C164.662 31.9267 164.369 32.0067 164.062 32.06C163.756 32.1267 163.409 32.16 163.022 32.16C162.556 32.16 162.142 32.0867 161.782 31.94C161.422 31.7933 161.109 31.5933 160.842 31.34C160.269 30.7667 159.982 29.9867 159.982 29V24.16ZM167.059 17.68H169.279V21.94L169.159 23.52H169.279C169.559 23.0533 169.972 22.6667 170.519 22.36C171.065 22.04 171.672 21.88 172.339 21.88C172.979 21.88 173.532 21.98 173.999 22.18C174.465 22.3667 174.852 22.64 175.159 23C175.465 23.36 175.692 23.7933 175.839 24.3C175.985 24.7933 176.059 25.34 176.059 25.94V32H173.839V26.24C173.839 25.4267 173.639 24.84 173.239 24.48C172.852 24.12 172.359 23.94 171.759 23.94C171.372 23.94 171.025 24.0267 170.719 24.2C170.425 24.36 170.165 24.58 169.939 24.86C169.725 25.14 169.559 25.46 169.439 25.82C169.332 26.18 169.279 26.5533 169.279 26.94V32H167.059V17.68Z",fill:"white"}),wp.element.createElement("path",{d:"M213.951 24.6725V31.7485H211.69V14.2748H217.684C219.203 14.2748 220.498 14.7778 221.558 15.7836C222.641 16.7895 223.183 18.0175 223.183 19.4678C223.183 20.9532 222.641 22.1813 221.558 23.1754C220.51 24.1696 219.215 24.6608 217.684 24.6608H213.951V24.6725ZM213.951 16.4269V22.5204H217.731C218.626 22.5204 219.379 22.2164 219.968 21.6199C220.569 21.0234 220.875 20.2982 220.875 19.4795C220.875 18.6725 220.569 17.9591 219.968 17.3626C219.379 16.7427 218.638 16.4386 217.731 16.4386H213.951V16.4269Z",fill:"white"}),wp.element.createElement("path",{d:"M229.094 19.3976C230.766 19.3976 232.085 19.8421 233.05 20.731C234.016 21.6199 234.499 22.8362 234.499 24.3801V31.7485H232.344V30.0877H232.25C231.319 31.4561 230.071 32.1345 228.517 32.1345C227.186 32.1345 226.079 31.7485 225.185 30.9649C224.29 30.1813 223.842 29.2105 223.842 28.0409C223.842 26.8011 224.313 25.8187 225.255 25.0935C226.197 24.3567 227.457 23.9941 229.023 23.9941C230.366 23.9941 231.472 24.2397 232.332 24.731V24.2164C232.332 23.4327 232.026 22.7778 231.402 22.228C230.778 21.6783 230.048 21.4093 229.212 21.4093C227.952 21.4093 226.951 21.9357 226.221 23L224.231 21.7602C225.326 20.1813 226.951 19.3976 229.094 19.3976ZM226.174 28.076C226.174 28.6608 226.421 29.152 226.927 29.538C227.422 29.9239 228.011 30.1228 228.682 30.1228C229.636 30.1228 230.483 29.7719 231.225 29.0701C231.967 28.3684 232.344 27.5497 232.344 26.6023C231.637 26.0526 230.66 25.7719 229.4 25.7719C228.482 25.7719 227.716 25.9941 227.104 26.4269C226.48 26.883 226.174 27.4327 226.174 28.076Z",fill:"white"}),wp.element.createElement("path",{d:"M246.792 19.7836L239.256 37H236.924L239.727 30.9766L234.758 19.7836H237.219L240.798 28.3684H240.845L244.331 19.7836H246.792Z",fill:"white"}),wp.element.createElement("path",{d:"M204.959 23.2456C204.959 22.5134 204.893 21.8128 204.77 21.1392H195.294V24.9988L200.751 25C200.53 26.2842 199.818 27.3789 198.726 28.1087V30.6128H201.975C203.872 28.869 204.959 26.2912 204.959 23.2456Z",fill:"#4285F4"}),wp.element.createElement("path",{d:"M198.727 28.1088C197.823 28.7146 196.658 29.069 195.296 29.069C192.664 29.069 190.432 27.3076 189.632 24.9333H186.281V27.5158C187.941 30.7883 191.354 33.0339 195.296 33.0339C198.021 33.0339 200.31 32.1439 201.976 30.6117L198.727 28.1088Z",fill:"#34A853"}),wp.element.createElement("path",{d:"M189.317 23.0175C189.317 22.3509 189.428 21.7064 189.632 21.1006V18.5181H186.281C185.594 19.8713 185.208 21.3988 185.208 23.0175C185.208 24.6362 185.596 26.1637 186.281 27.5169L189.632 24.9345C189.428 24.3286 189.317 23.6842 189.317 23.0175Z",fill:"#FABB05"}),wp.element.createElement("path",{d:"M195.296 16.9649C196.783 16.9649 198.115 17.4737 199.166 18.4678L202.045 15.6105C200.297 13.993 198.017 13 195.296 13C191.355 13 187.941 15.2456 186.281 18.5181L189.632 21.1006C190.432 18.7263 192.664 16.9649 195.296 16.9649Z",fill:"#E94235"}))},fe=function(){return wp.element.createElement("svg",{width:"343",height:"50",viewBox:"0 0 343 50",fill:"none",xmlns:"http://www.w3.org/2000/svg"},wp.element.createElement("rect",{width:"343",height:"50",rx:"8",fill:"black"}),wp.element.createElement("path",{d:"M155.748 19.8275C154.411 19.8275 153.333 20.636 152.637 20.636C151.907 20.636 150.93 19.8724 149.773 19.8724C147.572 19.8724 145.337 21.7029 145.337 25.1282C145.337 27.2733 146.168 29.5306 147.19 31.0018C148.055 32.2259 148.83 33.2366 149.93 33.2366C151.02 33.2366 151.503 32.5179 152.862 32.5179C154.232 32.5179 154.546 33.2142 155.748 33.2142C156.95 33.2142 157.747 32.1248 158.499 31.0467C159.33 29.8001 159.69 28.5872 159.701 28.5311C159.634 28.5086 157.343 27.5765 157.343 24.9485C157.343 22.68 159.139 21.6693 159.241 21.5906C158.061 19.8724 156.253 19.8275 155.748 19.8275ZM155.13 18.3787C155.669 17.7161 156.051 16.8177 156.051 15.908C156.051 15.7845 156.04 15.6609 156.017 15.5599C155.141 15.5936 154.063 16.1439 153.423 16.8963C152.929 17.4691 152.457 18.3787 152.457 19.2884C152.457 19.4232 152.48 19.5692 152.491 19.6141C152.547 19.6253 152.637 19.6365 152.727 19.6365C153.524 19.6365 154.535 19.0975 155.13 18.3787ZM164.115 16.8289V33.0345H167.013V27.7225H170.528C173.807 27.7225 176.098 25.5213 176.098 22.3094C176.098 19.0413 173.886 16.8289 170.652 16.8289H164.115ZM167.013 19.2547H169.888C171.977 19.2547 173.156 20.3216 173.156 22.3094C173.156 24.241 171.943 25.3192 169.877 25.3192H167.013V19.2547ZM181.535 31.0467C180.3 31.0467 179.412 30.429 179.412 29.3958C179.412 28.3963 180.142 27.8348 181.703 27.7337L184.477 27.554V28.5311C184.477 29.9573 183.219 31.0467 181.535 31.0467ZM180.715 33.2366C182.321 33.2366 183.669 32.5403 184.354 31.3499H184.545V33.0345H187.229V24.6453C187.229 22.0399 185.454 20.5013 182.299 20.5013C179.379 20.5013 177.346 21.8826 177.121 24.0501H179.749C180.008 23.2191 180.884 22.7698 182.164 22.7698C183.669 22.7698 184.477 23.4437 184.477 24.6453V25.6785L181.31 25.8694C178.323 26.0491 176.65 27.3294 176.65 29.553C176.65 31.7991 178.345 33.2366 180.715 33.2366ZM190.329 37.493C193.081 37.493 194.395 36.4822 195.439 33.4276L199.875 20.7484H196.933L194.069 30.3392H193.878L191.003 20.7484H187.948L192.34 33.0906L192.194 33.6297C191.834 34.764 191.172 35.2132 189.992 35.2132C189.801 35.2132 189.386 35.202 189.229 35.1683V37.4481C189.408 37.4818 190.161 37.493 190.329 37.493Z",fill:"white"}),wp.element.createElement("rect",{width:"343",height:"50",rx:"8",stroke:"black"}))};const pe={name:"squaresync_credit_wallet",paymentMethodId:"squaresync_credit",content:wp.element.createElement(function(t){var r=t.billing,o=t.shippingData,i=t.onClick,a=t.onClose,c=t.onSubmit,u=t.setExpressPaymentError,l=t.emitResponse,s=t.eventRegistration,f=s.onPaymentSetup,p=s.onCheckoutFail,d=ue((0,e.useState)(!1),2),y=d[0],v=d[1],m=o.needsShipping;(0,e.useEffect)(function(){var e=(0,I.subscribe)(function(){wp.data.select("wc/store/cart").isApplyingCoupon()||v(function(e){return!e})});return function(){e()}},[]);var b=function(){var t=X((0,e.useState)(null),2),r=t[0],o=t[1];return(0,e.useEffect)(function(){var e=n().applicationId,t=n().locationId;if(window.Square)try{var r=Square.payments(e,t);o(r)}catch(e){console.error(e)}},[]),r}(),h=function(t,n,r){var o=(0,e.useRef)(null),i=X((0,e.useState)(null),2),a=i[0],c=i[1];return(0,e.useEffect)(function(){if(t){var e=function(){var e=ne(K().m(function e(){var r,i,a,u,l,s;return K().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,q();case 1:r=e.v,(i=JSON.parse(r))&&(Array.isArray(i.lineItems)&&(-1!==(a=i.lineItems.findIndex(function(e){var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().includes("shipping")}))&&i.lineItems.splice(a,1),-1!==(u=i.lineItems.findIndex(function(e){var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().includes("discount")}))&&i.lineItems.splice(u,1)),n&&(i.requestShippingAddress=!0)),o.current?o.current.update(i):(l=t.paymentRequest(i),o.current=l,c(l)),e.n=3;break;case 2:e.p=2,s=e.v,console.error("Failed to create or update payment request:",s);case 3:return e.a(2)}},e,null,[[0,2]])}));return function(){return e.apply(this,arguments)}}();e()}},[t,n,r]),a}(b,m,y),g=function(t,r){var o=X((0,e.useState)(null),2),i=o[0],a=o[1],c=(0,e.useRef)(null);return(0,e.useEffect)(function(){t&&r&&ne(K().m(function e(){var o;return K().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,t.googlePay(r);case 1:return o=e.v,e.n=2,o.attach(c.current,{buttonColor:n().googlePayColor,buttonSizeMode:"fill",buttonType:"long"});case 2:a(o),e.n=4;break;case 3:e.p=3,e.v;case 4:return e.a(2)}},e,null,[[0,3]])}))()},[t,r]),[i,c]}(b,h),w=ue(g,2),C=w[0],S=w[1],_=function(t,r){var o=X((0,e.useState)(null),2),i=o[0],a=o[1],c=(0,e.useRef)(null);return(0,e.useEffect)(function(){t&&r&&ne(K().m(function e(){var n,o;return K().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,t.applePay(r);case 1:n=e.v,a(n),e.n=3;break;case 2:e.p=2,o=e.v,console.log(o);case 3:return e.a(2)}},e,null,[[0,2]])}))()},[t,r]),(0,e.useEffect)(function(){if(null!=c&&c.current&&i){var e=n().applePayColor,t=n().applePayType;"plain"!==t&&(c.current.querySelector(".text").innerText="".concat(t.charAt(0).toUpperCase()).concat(t.slice(1)," with"),c.current.classList.add("wc-square-wallet-button-with-text")),c.current.style.cssText+="-apple-pay-button-type: ".concat(t,";"),c.current.style.cssText+="-apple-pay-button-style: ".concat(e,";"),c.current.style.display="block",c.current.classList.add("wc-square-wallet-button-".concat(e))}},[i,c]),[i,c]}(b,h),O=ue(_,2),E=O[0],j=O[1],k=function(t,n){var r=X((0,e.useState)(null),2),o=r[0],i=r[1],a=(0,e.useRef)(null);return(0,e.useEffect)(function(){t&&n&&ne(K().m(function e(){var r;return K().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,t.afterpayClearpay(n);case 1:return r=e.v,e.n=2,r.attach(a.current,{buttonColor:"black",buttonSizeMode:"fill"});case 2:i(r),e.n=4;break;case 3:e.p=3,e.v;case 4:return e.a(2)}},e,null,[[0,3]])}))()},[t,n]),[o,a]}(b,h),P=ue(k,2),T=P[0],x=P[1],A=ue((0,e.useState)(!1),2),L=A[0],R=A[1];!function(t){(0,e.useEffect)(function(){null==t||t.addEventListener("shippingcontactchanged",function(e){return U(e)}),null==t||t.addEventListener("afterpay_shippingaddresschanged",function(e){return U(e)})},[t])}(h),function(t){(0,e.useEffect)(function(){null==t||t.addEventListener("shippingoptionchanged",function(e){return Z(e)}),null==t||t.addEventListener("afterpay_shippingoptionchanged",function(e){return Z(e)})},[t])}(h),function(t,n,r,o,i){var a=function(e){var t={familyName:e.billingData.last_name||"",givenName:e.billingData.first_name||"",email:e.billingData.email||"",country:e.billingData.country||"",region:e.billingData.state||"",city:e.billingData.city||"",postalCode:e.billingData.postcode||""};e.billingData.phone&&(t.phone=e.billingData.phone);var n=[e.billingData.address_1,e.billingData.address_2].filter(Boolean);return n.length&&(t.addressLines=n),{intent:"CHARGE",amount:(e.cartTotal.value/100).toString(),currencyCode:e.currency.code,billingContact:t}}(n);(0,e.useEffect)(function(){return i(function(){function e(){return(e=ne(K().m(function e(){var n,i,c,u,l,s,f,p,d,y,v,m,b,h,g,w,C,S,_,O,E,j,k,P,T,x,A,L,R,H,G,D,N,M,F,q,V,Z,U,z,$,J,Q,X,ee,te;return K().w(function(e){for(;;)switch(e.n){case 0:if(g={type:o.responseTypes.SUCCESS},r){e.n=1;break}return g={type:o.responseTypes.FAILURE},e.a(2,g);case 1:return C=(w=null!=r?r:{}).details,S=w.token,O=(_=null!=C?C:{}).method,E=void 0===O?"":O,j=_.card,k=void 0===j?{}:j,P=_.shipping,T=void 0===P?{}:P,_.billing,(x=null!=T?T:{}).contact,A=x.option,L=void 0===A?{}:A,R="AfterpayClearpay"===E,H=(0,I.select)("wc/store/cart").getCustomerData().shippingAddress,G=(0,I.select)("wc/store/cart").getCustomerData().billingAddress,D=oe(null==r||null===(n=r.details)||void 0===n||null===(n=n.shipping)||void 0===n?void 0:n.contact,R,H),re(N=oe(null==r||null===(i=r.details)||void 0===i?void 0:i.billing,R,G))||(q=oe(null==r||null===(M=r.details)||void 0===M||null===(M=M.shipping)||void 0===M?void 0:M.contact,R,H),N=W(W({},q),{},{email:N.email||q.email||(null==r||null===(F=r.details)||void 0===F||null===(F=F.shipping)||void 0===F?void 0:F.contact.email)})),V={token:""},e.p=2,e.n=3,B(t,S,a);case 3:V=e.v,e.n=5;break;case 4:e.p=4,ee=e.v,console.error("verifyBuyer error:",ee);case 5:if(Z=V.token,U=R?"AfterpayClearpay":null!==(c=null==k?void 0:k.cardType)&&void 0!==c?c:E,z=R?"":null!==(u=null==k?void 0:k.last4)&&void 0!==u?u:"",$=R?"":null!==(l=null==k||null===(s=k.expMonth)||void 0===s?void 0:s.toString())&&void 0!==l?l:"",J=R?"":null!==(f=null==k||null===(p=k.expYear)||void 0===p?void 0:p.toString())&&void 0!==f?f:"",Q=R?"":null!==(d=null==k?void 0:k.postalCode)&&void 0!==d?d:"",X="wc-squaresync_credit",g.meta={paymentMethodData:Y(Y(Y(Y(Y(Y(Y(Y({},"".concat(X,"-card-type"),U),"".concat(X,"-last-four"),z),"".concat(X,"-exp-month"),$),"".concat(X,"-exp-year"),J),"".concat(X,"-payment-postcode"),Q),"".concat(X,"-payment-nonce"),S),"".concat(X,"-buyer-verification-token"),Z||""),"shipping_method",null!==(y=null==L?void 0:L.id)&&void 0!==y&&y),billingAddress:{email:N.email,first_name:N.givenName,last_name:N.familyName,company:"",address_1:null!==(v=N.addressLines[0])&&void 0!==v?v:"",address_2:null!==(m=N.addressLines[1])&&void 0!==m?m:"",city:N.city,state:N.state,postcode:N.postalCode,country:N.countryCode,phone:N.phone},shippingAddress:{first_name:D.givenName,last_name:D.familyName,company:"",address_1:null!==(b=D.addressLines[0])&&void 0!==b?b:"",address_2:null!==(h=D.addressLines[1])&&void 0!==h?h:"",city:D.city,state:D.state,postcode:D.postalCode,country:D.countryCode,phone:D.phone}},e.p=6,wp.data.dispatch("wc/store/cart").setBillingAddress(g.meta.billingAddress),!wp.data.select("wc/store/cart").getNeedsShipping()){e.n=7;break}if(wp.data.dispatch("wc/store/cart").setShippingAddress(g.meta.shippingAddress),wp.data.select("wc/store/cart").getShippingRates().some(function(e){return e.shipping_rates.length})){e.n=7;break}return g.type=o.responseTypes.FAILURE,e.a(2,g);case 7:e.n=9;break;case 8:return e.p=8,te=e.v,console.error("WooCommerce address dispatch error:",te),g.type=o.responseTypes.FAILURE,e.a(2,g);case 9:return e.a(2,g)}},e,null,[[6,8],[2,4]])}))).apply(this,arguments)}return function(){return e.apply(this,arguments)}()})},[i,n.billingData,r])}(b,r,L,l,f),(0,e.useEffect)(function(){return p(function(){return a(),!0})},[p]);var H=n().googlePay.includes("no"),G=n().applePay.includes("no"),D=n().afterPay.includes("no");function N(e){var t;e?(u(""),i(),(t=ie().m(function t(){var n;return ie().w(function(t){for(;;)switch(t.n){case 0:return t.n=1,z(e);case 1:(n=t.v)?(R(n),c()):(console.error("Tokenization failed in onClickHandler"),a());case 2:return t.a(2)}},t)}),function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(e){ce(i,r,o,a,c,"next",e)}function c(e){ce(i,r,o,a,c,"throw",e)}a(void 0)})})()):console.error("Button instance is null or undefined")}var M=!H&&wp.element.createElement("div",{tabIndex:0,role:"button",ref:S,onClick:function(){return N(C)}}),F=!D&&wp.element.createElement("div",{tabIndex:0,role:"button",ref:x,onClick:function(){return N(T)}}),V=!G&&wp.element.createElement("div",{tabIndex:0,role:"button",ref:j,onClick:function(){return N(E)},className:"apple-pay-button wc-square-wallet-buttons"},wp.element.createElement("span",{className:"text"}),wp.element.createElement("span",{className:"logo"}));return wp.element.createElement(React.Fragment,null,V,M,F)},null),edit:wp.element.createElement(function(){return wp.element.createElement(React.Fragment,null,wp.element.createElement(se,null),wp.element.createElement(fe,null))},null),canMakePayment:function(){var e=!(!n().applicationId||!n().locationId),t=n().isDigitalWalletsEnabled;return e&&t},supports:{features:n().supports}};function de(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return ye(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(ye(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,ye(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,ye(f,"constructor",l),ye(l,"constructor",u),u.displayName="GeneratorFunction",ye(l,o,"GeneratorFunction"),ye(f),ye(f,o,"Generator"),ye(f,r,function(){return this}),ye(f,"toString",function(){return"[object Generator]"}),(de=function(){return{w:i,m:p}})()}function ye(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}ye=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){ye(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},ye(e,t,n,r)}function ve(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function me(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){ve(i,r,o,a,c,"next",e)}function c(e){ve(i,r,o,a,c,"throw",e)}a(void 0)})}}var be=function(){return new Promise(function(e,t){var r,o={context:n().context,security:n().paymentRequestNonce,is_pay_for_order_page:!1};jQuery.post((r="get_payment_request",n().ajaxUrl.replace("%%endpoint%%","square_digital_wallet_".concat(r))),o,function(n){n.success?e(n.data):t(n.data)})})},he=function(){var e=me(de().m(function e(t,n,r){var o,i;return de().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,t.verifyBuyer(n,r);case 1:return o=e.v,e.a(2,o);case 2:throw e.p=2,i=e.v,console.error("Error during buyer verification:",i),i;case 3:return e.a(2)}},e,null,[[0,2]])}));return function(t,n,r){return e.apply(this,arguments)}}(),ge=function(){var e=me(de().m(function e(t){var n,r;return de().w(function(e){for(;;)switch(e.n){case 0:return console.log("[utils.js] tokenize() called with button:",t),e.p=1,e.n=2,t.tokenize();case 2:if(n=e.v,console.log("[utils.js] tokenize() => tokenResult:",n),"OK"!==n.status){e.n=3;break}return console.log("[utils.js] tokenize() => status=OK => returning tokenResult"),e.a(2,n);case 3:n.errors&&n.errors.length&&n.errors.forEach(function(e){console.error("Afterpay tokenization error:",e)}),e.n=5;break;case 4:e.p=4,r=e.v,console.error("[utils.js] Exception during Afterpay tokenization:",r);case 5:return console.error("[utils.js] tokenize() => returning false (tokenization failed)"),e.a(2,!1)}},e,null,[[1,4]])}));return function(t){return e.apply(this,arguments)}}();function we(e){return we="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},we(e)}function Ce(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Pe(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var _n=0,r=function(){};return{s:r,n:function(){return _n>=e.length?{done:!0}:{done:!1,value:e[_n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(a)throw o}}}}function Se(e,t,n){return(t=function(e){var t=function(e){if("object"!=we(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=we(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==we(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _e(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return Oe(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(Oe(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,Oe(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,Oe(f,"constructor",l),Oe(l,"constructor",u),u.displayName="GeneratorFunction",Oe(l,o,"GeneratorFunction"),Oe(f),Oe(f,o,"Generator"),Oe(f,r,function(){return this}),Oe(f,"toString",function(){return"[object Generator]"}),(_e=function(){return{w:i,m:p}})()}function Oe(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Oe=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){Oe(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},Oe(e,t,n,r)}function Ee(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function je(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Ee(i,r,o,a,c,"next",e)}function c(e){Ee(i,r,o,a,c,"throw",e)}a(void 0)})}}function ke(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||Pe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pe(e,t){if(e){if("string"==typeof e)return Te(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Te(e,t):void 0}}function Te(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function xe(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return((parseInt(e,10)||0)/Math.pow(10,t)).toFixed(t)}function Ae(){return Le.apply(this,arguments)}function Le(){return(Le=je(_e().m(function e(){var t,n,r,o,i,a,c,u,l,s,f,p,d,y,v,m,b,h,g,w;return _e().w(function(e){for(;;)switch(e.n){case 0:if((r=(0,I.select)("wc/store/cart").getShippingRates()||[]).length){e.n=1;break}return e.a(2,[]);case 1:if(o=r[0],null!==(t=o.shipping_rates)&&void 0!==t&&t.length){e.n=2;break}return e.a(2,[]);case 2:i=(0,I.select)("wc/store/cart").getCartTotals()||{},a=parseInt(i.currency_minor_unit,10)||2,c=null===(n=o.shipping_rates.find(function(e){return e.is_chosen}))||void 0===n?void 0:n.rate_id,u=[],l=Ce(o.shipping_rates),e.p=3,l.s();case 4:if((s=l.n()).done){e.n=7;break}return v=s.value,(0,I.dispatch)("wc/store/cart").selectShippingRate(v.rate_id,0),e.n=5,new Promise(function(e){return setTimeout(e,300)});case 5:m=(0,I.select)("wc/store/cart").getCartTotals()||{},b=xe(null!==(f=m.total_price)&&void 0!==f?f:"0",a),h=xe(null!==(p=m.total_tax)&&void 0!==p?p:"0",a),g=xe(null!==(d=v.price)&&void 0!==d?d:"0",a),u.push({id:v.rate_id,label:v.name,amount:g,taxLineItems:[{id:"taxItem1",label:"Taxes",amount:h}],total:{label:null!==(y=m.total_label)&&void 0!==y?y:"Total",amount:b}});case 6:e.n=4;break;case 7:e.n=9;break;case 8:e.p=8,w=e.v,l.e(w);case 9:return e.p=9,l.f(),e.f(9);case 10:if(!c){e.n=11;break}return(0,I.dispatch)("wc/store/cart").selectShippingRate(c,0),e.n=11,new Promise(function(e){return setTimeout(e,300)});case 11:return e.a(2,u)}},e,null,[[3,8,9,10]])}))).apply(this,arguments)}function Re(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof c?r:c,l=Object.create(u.prototype);return He(l,"_invoke",function(n,r,o){var i,c,u,l=0,s=o||[],f=!1,p={p:0,n:0,v:e,a:d,f:d.bind(e,4),d:function(t,n){return i=t,c=0,u=e,p.n=n,a}};function d(n,r){for(c=n,u=r,t=0;!f&&l&&!o&&t<s.length;t++){var o,i=s[t],d=p.p,y=i[2];n>3?(o=y===r)&&(u=i[(c=i[4])?5:(c=3,3)],i[4]=i[5]=e):i[0]<=d&&((o=n<2&&d<i[1])?(c=0,p.v=r,p.n=i[1]):d<y&&(o=n<3||i[0]>r||r>y)&&(i[4]=n,i[5]=r,p.n=y,c=0))}if(o||n>1)return a;throw f=!0,r}return function(o,s,y){if(l>1)throw TypeError("Generator is already running");for(f&&1===s&&d(s,y),c=s,u=y;(t=c<2?e:u)||!f;){i||(c?c<3?(c>1&&(p.n=-1),d(c,u)):p.n=u:p.v=u);try{if(l=2,i){if(c||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,c<2&&(c=0)}else 1===c&&(t=i.return)&&t.call(i),c<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),c=1);i=e}else if((t=(f=p.n<0)?u:n.call(r,p))!==a)break}catch(t){i=e,c=1,u=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function c(){}function u(){}function l(){}t=Object.getPrototypeOf;var s=[][r]?t(t([][r]())):(He(t={},r,function(){return this}),t),f=l.prototype=c.prototype=Object.create(s);function p(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,He(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=l,He(f,"constructor",l),He(l,"constructor",u),u.displayName="GeneratorFunction",He(l,o,"GeneratorFunction"),He(f),He(f,o,"Generator"),He(f,r,function(){return this}),He(f,"toString",function(){return"[object Generator]"}),(Re=function(){return{w:i,m:p}})()}function He(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}He=function(e,t,n,r){if(t)o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n;else{var i=function(t,n){He(e,t,function(e){return this._invoke(t,n,e)})};i("next",0),i("throw",1),i("return",2)}},He(e,t,n,r)}function Ie(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function Ge(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return De(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?De(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function De(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}wp.element.createElement(function(t){var r,o=t.billing,i=t.shippingData,a=t.onClick,c=t.onClose,u=(t.onSubmit,t.setExpressPaymentError),l=t.emitResponse,s=t.eventRegistration,f=s.onPaymentSetup,p=s.onCheckoutFail,d=Ge((0,e.useState)(!1),2),y=d[0],v=d[1],m=i.needsShipping;(0,e.useEffect)(function(){var e=(0,I.subscribe)(function(){wp.data.select("wc/store/cart").isApplyingCoupon()||v(function(e){return!e})});return function(){e()}},[]);var b,h=function(){var t=ke((0,e.useState)(null),2),r=t[0],o=t[1];return(0,e.useEffect)(function(){var e=n(),t=e.applicationId,r=e.locationId;if(window.Square)try{var i=Square.payments(t,r);o(i)}catch(e){console.error("[useSquare] Error creating payments:",e)}else console.error("[useSquare] window.Square is not available!")},[]),r}(),g=function(t,n,r){var o=(0,e.useRef)(null),i=ke((0,e.useState)(null),2),a=i[0],c=i[1];return(0,e.useEffect)(function(){function e(){return(e=je(_e().m(function e(){var r,i,a,u,l,s;return _e().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,e.n=1,be();case 1:r=e.v,i=JSON.parse(r),Array.isArray(i.lineItems)&&(-1!==(a=i.lineItems.findIndex(function(e){var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().includes("shipping")}))&&i.lineItems.splice(a,1),-1!==(u=i.lineItems.findIndex(function(e){var t;return null===(t=e.label)||void 0===t?void 0:t.toLowerCase().includes("discount")}))&&i.lineItems.splice(u,1)),n&&(i.requestShippingAddress=!0),o.current?o.current.update(i):(l=t.paymentRequest(i),o.current=l,c(l)),e.n=3;break;case 2:e.p=2,s=e.v,console.error("Failed to create/update PaymentRequest:",s);case 3:return e.a(2)}},e,null,[[0,2]])}))).apply(this,arguments)}t&&function(){e.apply(this,arguments)}()},[t,n,r]),[a]}(h,m,y),w=Ge(g,1)[0],C=function(t,n){var r=ke((0,e.useState)(null),2),o=r[0],i=r[1],a=(0,e.useRef)(null);return(0,e.useEffect)(function(){t&&n?je(_e().m(function e(){var r;return _e().w(function(e){for(;;)switch(e.n){case 0:return console.log(n),e.p=1,e.n=2,t.afterpayClearpay(n);case 2:return r=e.v,console.log(r),e.n=3,r.attach(a.current,{buttonColor:"black",buttonSizeMode:"fill"});case 3:i(r),e.n=5;break;case 4:e.p=4,e.v;case 5:return e.a(2)}},e,null,[[1,4]])}))():console.log("no payment")},[t,n]),[o,a]}(h,w),S=Ge(C,2),_=S[0],O=S[1],E=Ge((0,e.useState)(!1),2),j=E[0],k=E[1];return b=w,(0,e.useRef)(!1),(0,e.useEffect)(function(){if(b&&"function"==typeof b.addEventListener){var e=function(){var e=je(_e().m(function e(t){var n,r,o,i;return _e().w(function(e){for(;;)switch(e.n){case 0:return e.p=0,t.givenName,t.familyName,null===(n=t.addressLines)||void 0===n||n[0],null===(r=t.addressLines)||void 0===r||r[1],t.city,t.state,t.postalCode,t.countryCode,t.phone,e.n=1,new Promise(function(e){return setTimeout(e,300)});case 1:return e.n=2,Ae();case 2:if((o=e.v).length){e.n=3;break}return e.a(2);case 3:return e.a(2,{shippingOptions:o});case 4:e.p=4,i=e.v,console.error("Error in onAfterpayShippingAddressChanged:",i);case 5:return e.a(2)}},e,null,[[0,4]])}));return function(_x){return e.apply(this,arguments)}}();return b.addEventListener("afterpay_shippingaddresschanged",e),function(){"function"==typeof b.removeEventListener&&b.removeEventListener("afterpay_shippingaddresschanged",e)}}},[b]),function(t,n,r,o,i){console.log(r);var a=function(e){var t={familyName:e.billingData.last_name||"",givenName:e.billingData.first_name||"",email:e.billingData.email||"",country:e.billingData.country||"",region:e.billingData.state||"",city:e.billingData.city||"",postalCode:e.billingData.postcode||""};e.billingData.phone&&(t.phone=e.billingData.phone);var n=[e.billingData.address_1,e.billingData.address_2].filter(Boolean);return n.length&&(t.addressLines=n),{intent:"CHARGE",amount:(e.cartTotal.value/100).toString(),currencyCode:e.currency.code,billingContact:t}}(n);(0,e.useEffect)(function(){if(r)return i(function(){function e(){return(e=je(_e().m(function e(){var n,i,c,u,l,s,f,p,d,y,v,m,b,h,g,w,C,S,_,O,E,j,k,P,T,x,A,L,R,H,I,G,D,N,M,F,q,V,Z,U;return _e().w(function(e){for(;;)switch(e.n){case 0:if(console.log(r.token),P={type:o.responseTypes.SUCCESS},r){e.n=1;break}return console.error("[usePaymentProcessing] tokenResult is null/undefined!"),P={type:o.responseTypes.FAILURE},e.a(2,P);case 1:return T=r.details,x=T.card,A=T.method,L=r.token,e.n=2,he(t,L,a);case 2:if(R=e.v,console.log(R),H=R.token,I="wc-squaresync_credit",G=(null==r||null===(n=r.details)||void 0===n?void 0:n.billing)||{},D=(null==r||null===(i=r.details)||void 0===i?void 0:i.shipping)||{},N=D.contact,M=void 0===N?{}:N,F=D.option,q=void 0===F?{}:F,V=null!==(c=null!==(u=null==G?void 0:G.email)&&void 0!==u?u:null==M?void 0:M.email)&&void 0!==c?c:"",Z=null!==(l=null!==(s=null==G?void 0:G.phone)&&void 0!==s?s:null==M?void 0:M.phone)&&void 0!==l?l:"",U=null!==(f=null!==(p=null==M?void 0:M.phone)&&void 0!==p?p:null==G?void 0:G.phone)&&void 0!==f?f:"",P.meta={paymentMethodData:Se(Se(Se(Se(Se(Se(Se(Se({},"".concat(I,"-card-type"),A||""),"".concat(I,"-last-four"),(null==x?void 0:x.last4)||""),"".concat(I,"-exp-month"),(null==x||null===(d=x.expMonth)||void 0===d?void 0:d.toString())||""),"".concat(I,"-exp-year"),(null==x||null===(y=x.expYear)||void 0===y?void 0:y.toString())||""),"".concat(I,"-payment-postcode"),(null==x?void 0:x.postalCode)||""),"".concat(I,"-payment-nonce"),L||""),"".concat(I,"-buyer-verification-token"),H||""),"shipping_method",null!==(v=q.id)&&void 0!==v&&v),billingAddress:{email:V,first_name:null!==(m=G.givenName)&&void 0!==m?m:"",last_name:null!==(b=G.familyName)&&void 0!==b?b:"",company:"",address_1:G.addressLines?G.addressLines[0]:"",address_2:G.addressLines?G.addressLines[1]:"",city:null!==(h=G.city)&&void 0!==h?h:"",state:null!==(g=G.state)&&void 0!==g?g:"",postcode:null!==(w=G.postalCode)&&void 0!==w?w:"",country:null!==(C=G.countryCode)&&void 0!==C?C:"",phone:Z},shippingAddress:{first_name:null!==(S=M.givenName)&&void 0!==S?S:"",last_name:null!==(_=M.familyName)&&void 0!==_?_:"",company:"",address_1:M.addressLines?M.addressLines[0]:"",address_2:M.addressLines?M.addressLines[1]:"",city:null!==(O=M.city)&&void 0!==O?O:"",state:null!==(E=M.state)&&void 0!==E?E:"",postcode:null!==(j=M.postalCode)&&void 0!==j?j:"",country:null!==(k=M.countryCode)&&void 0!==k?k:"",phone:U}},!wp.data.select("wc/store/cart").getNeedsShipping()){e.n=3;break}if(wp.data.select("wc/store/cart").getShippingRates().some(function(e){return e.shipping_rates.length})){e.n=3;break}return console.error("[usePaymentProcessing] No shipping rates available => FAILURE"),P.type=o.responseTypes.FAILURE,console.log(P),e.a(2,P);case 3:return console.log(P),e.a(2,P)}},e)}))).apply(this,arguments)}return function(){return e.apply(this,arguments)}()});console.log("[usePaymentProcessing] no tokenResult yet, doing nothing.")},[r])}(h,o,j,l,f),(0,e.useEffect)(function(){return p(function(){return c(),!0})},[p]),null===(r=n())||void 0===r||null===(r=r.afterpay)||void 0===r||r.includes("no"),wp.element.createElement(React.Fragment,null,wp.element.createElement("div",{ref:O,role:"button",tabIndex:0,className:"afterpay-button wc-square-wallet-buttons",onClick:function(){var e,t;(e=_)?(u(""),a(),(t=Re().m(function t(){var n;return Re().w(function(t){for(;;)switch(t.n){case 0:return t.n=1,ge(e);case 1:(n=t.v)?k(n):(console.error("Tokenization failed in onClickHandler"),c());case 2:return t.a(2)}},t)}),function(){var e=this,n=arguments;return new Promise(function(r,o){var i=t.apply(e,n);function a(e){Ie(i,r,o,a,c,"next",e)}function c(e){Ie(i,r,o,a,c,"throw",e)}a(void 0)})})()):console.error("Button instance is null or undefined")}}))},null),wp.element.createElement(function(){return wp.element.createElement(React.Fragment,null,wp.element.createElement("div",null,"afterpay"))},null),n().supports;var Ne=window.wc.wcBlocksRegistry,Me=Ne.registerPaymentMethod;(0,Ne.registerExpressPaymentMethod)(pe),Me(H)})();