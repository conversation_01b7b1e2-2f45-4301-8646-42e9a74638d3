<?php
/**
 * Square Gift Card Email (Plain text template)
 *
 * This template can be overridden by copying it to:
 * yourtheme/woocommerce/emails/plain/square-gift-card.php
 *
 * @var WC_Email $email
 * @var object   $data An object containing:
 *     - gift_card_gan (string) The actual code
 *     - personal_message (string) A personal note from the buyer
 *     - recipient_name (string) The recipient name
 *     - from (string) The buyer’s name (billing first name)
 *     - gift_card_amount (string) (Optional) If you want to show an initial value
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$email_heading = isset( $email_heading ) ? $email_heading : $email->get_heading();

$recipient_display_name = ! empty( $data->recipient_name ) ? $data->recipient_name : __( 'Friend', 'squarewoosync' );
$sender_name = ! empty( $data->from ) ? $data->from : __( 'Someone', 'squarewoosync' );

echo "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n";
echo esc_html( $email_heading ) . "\n";
echo "=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=\n\n";

printf(
    esc_html__( 'Hello %s,', 'squarewoosync' ),
    esc_html( $recipient_display_name )
);
echo "\n\n";

printf(
    esc_html__( '%s just sent you a gift card! Below are the details:', 'squarewoosync' ),
    esc_html( $sender_name )
);
echo "\n\n";

if ( ! empty( $data->gift_card_gan ) ) {
    echo esc_html__( 'Gift Card Number:', 'squarewoosync' ) . "\n";
    echo '  ' . esc_html( $data->gift_card_gan ) . "\n\n";
}

if ( ! empty( $data->gift_card_amount ) ) {
    echo esc_html__( 'Gift Card Balance:', 'squarewoosync' ) . "\n";
    echo '  ' . esc_html( $data->gift_card_amount ) . "\n\n";
}

if ( ! empty( $data->personal_message ) ) {
    echo esc_html__( 'A Message From the Sender:', 'squarewoosync' ) . "\n";
    echo wp_strip_all_tags( $data->personal_message ) . "\n\n";
}

echo "----------------------------------------\n";
echo esc_html__( 'HOW TO REDEEM YOUR GIFT CARD', 'squarewoosync' ) . "\n";
echo "----------------------------------------\n\n";
echo esc_html__( '1) Visit our store and add your desired products to the cart.', 'squarewoosync' ) . "\n";
echo esc_html__( '2) Proceed to checkout and find the Gift Card or Promo Code field.', 'squarewoosync' ) . "\n";
echo esc_html__( '3) Enter your Gift Card Number (GAN) and apply it to your order.', 'squarewoosync' ) . "\n\n";
echo esc_html__( 'Any remaining balance stays on the card for future use (per store policy).', 'squarewoosync' ) . "\n\n";

echo esc_html__( 'If you have any questions, please contact our support team.', 'squarewoosync' ) . "\n\n";

printf(
    esc_html__( 'Thank you for shopping with %s!', 'squarewoosync' ),
    esc_html( get_bloginfo( 'name' ) )
);
echo "\n\n";

// WooCommerce footer text
echo "----------------------------------------\n";
echo apply_filters( 'woocommerce_email_footer_text', get_option( 'woocommerce_email_footer_text' ) );
echo "\n";
