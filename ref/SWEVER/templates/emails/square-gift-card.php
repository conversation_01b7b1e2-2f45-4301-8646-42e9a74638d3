<?php

/**
 * Square Gift Card Email (HTML template)
 *
 * This template can be overridden by copying it to:
 * yourtheme/woocommerce/emails/square-gift-card.php
 *
 * @var WC_Email $email
 * @var object   $data  An object containing:
 *     - gift_card_gan (string)  The actual code
 *     - personal_message (string)   A personal note from the sender
 *     - recipient_name (string)     The recipient name
 *     - from (string)              The buyer’s name (billing first name)
 *     - gift_card_id (string)      (Optional) The internal Square gift card ID
 *     - gift_card_amount (string)  (Optional) If you want to show an initial value
 */

if (! defined('ABSPATH')) {
  exit;
}

$email_heading = isset($email_heading) ? $email_heading : $email->get_heading();

do_action('woocommerce_email_header', $email_heading, $email);

$recipient_display_name = ! empty($data->recipient_name) ? $data->recipient_name : __('Friend', 'squarewoosync');

$sender_name = ! empty($data->from) ? $data->from : __('Someone', 'squarewoosync');
?>

<p>
  <?php
  printf(
    esc_html__('Hello %s,', 'squarewoosync'),
    esc_html($recipient_display_name)
  );
  ?>
</p>


<?php if (! empty($data->from)) : ?>
  <p>
    <?php
    printf(
      esc_html__('%s just sent you a gift card! Below are the details:', 'squarewoosync'),
      esc_html($sender_name)
    );
    ?>
  </p>
<?php else : ?>

  <p>
    <?php
    esc_html__('Your new gift card details are below:', 'squarewoosync')
    ?>

  <?php endif; ?>

  <?php if (! empty($data->gift_card_gan)) : ?>
  <p>
    <strong><?php esc_html_e('Gift Card Number:', 'squarewoosync'); ?></strong>
    <br>
    <span style="font-size: 1.2em; font-weight: bold;">
      <?php echo esc_html($data->gift_card_gan); ?>
    </span>
  </p>
<?php endif; ?>

<?php if (! empty($data->gift_card_amount)) : ?>
  <p>
    <strong><?php esc_html_e('Gift card balance:', 'squarewoosync'); ?></strong>
    <br>
    <?php
    echo esc_html($data->gift_card_amount);
    ?>
  </p>
<?php endif; ?>

<?php if (! empty($data->personal_message)) : ?>
  <p>
    <strong><?php esc_html_e('A Message From the Sender:', 'squarewoosync'); ?></strong><br>
    <?php echo wp_kses_post(nl2br($data->personal_message)); ?>
  </p>
<?php endif; ?>

<hr>

<h2><?php esc_html_e('How to Redeem Your Gift Card', 'squarewoosync'); ?></h2>
<ol>
  <li>
    <?php esc_html_e('Visit our store and add your desired products to the cart.', 'squarewoosync'); ?>
  </li>
  <li>
    <?php esc_html_e('Proceed to checkout and locate the Gift Card field.', 'squarewoosync'); ?>
  </li>
  <li>
    <?php esc_html_e('Enter the Gift Card Number above and apply it to your order.', 'squarewoosync'); ?>
  </li>
</ol>
<p>
  <?php esc_html_e('The gift card balance will be applied to your total. Any remaining balance stays on the card for future use..', 'squarewoosync'); ?>
</p>

<p>
  <em>
    <?php esc_html_e('If you have any questions, feel free to contact our support team.', 'squarewoosync'); ?>
  </em>
</p>

<p>
  <?php
  printf(
    esc_html__('Thank you for shopping with %s!', 'squarewoosync'),
    esc_html(get_bloginfo('name'))
  );
  ?>
</p>

<?php
// Standard WooCommerce email footer
do_action('woocommerce_email_footer', $email);
