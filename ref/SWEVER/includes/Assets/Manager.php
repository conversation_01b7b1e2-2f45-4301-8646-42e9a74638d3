<?php

namespace Pixeldev\SquareWooSync\Assets;

if (!defined('ABSPATH')) {
  exit; // Exit if accessed directly
}

/**
 * Asset Manager class.
 *
 * Responsible for managing all of the assets (CSS, JS, Images, Locales).
 */
class Manager
{

  /**
   * Constructor.
   *
   * @since 0.2.0
   */
  public function __construct()
  {
    add_action('init', [$this, 'register_all_scripts']);
    add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
  }

  /**
   * Register all scripts and styles.
   *
   * @since 0.2.0
   *
   * @return void
   */
  public function register_all_scripts()
  {
    $this->register_styles($this->get_styles());
    $this->register_scripts($this->get_scripts());
  }

  /**
   * Get all styles.
   *
   * @since 0.2.0
   *
   * @return array
   */
  public function get_styles(): array
  {
    return [
      'sws-css' => [
        'src'     => SQUAREWOOSYNC_BUILD . '/index.css',
        'version' => SQUAREWOOSYNC_VERSION,
        'deps'    => [],
      ],
    ];
  }

  /**
   * Get all scripts.
   *
   * @since 0.2.0
   *
   * @return array
   */
  public function get_scripts(): array
  {
    $dependency = require_once SQUAREWOOSYNC_DIR . '/build/index.asset.php';


    $asset_path   = SQUAREWOOSYNC_URL . '/build/blocks/gift-card-admin.asset.php';
    $version      = SQUAREWOOSYNC_VERSION;
    $gift_card_dependency = array();

    if (file_exists($asset_path)) {
      $asset        = require $asset_path;
      $version      = is_array($asset) && isset($asset['version']) ? $asset['version'] : $version;
      $gift_card_dependency = is_array($asset) && isset($asset['dependencies']) ? $asset['dependencies'] : $dependencies;
    }

    return [
      'sws-app' => [
        'src'       => SQUAREWOOSYNC_BUILD . '/index.js',
        'version'   => $dependency['version'],
        'deps'      => $dependency['dependencies'],
        'in_footer' => true,
      ],
      // 'sws-gift-card' => [
      //   'src'       => SQUAREWOOSYNC_BUILD . '/blocks/gift-card-admin.js',
      //   'version'   => $version,
      //   'deps'      => $gift_card_dependency,
      //   'in_footer' => true,
      // ],
    ];
  }

  /**
   * Register styles.
   *
   * @since 0.2.0
   *
   * @return void
   */
  public function register_styles(array $styles)
  {
    foreach ($styles as $handle => $style) {
      wp_register_style($handle, $style['src'], $style['deps'], $style['version']);
    }
  }

  /**
   * Register scripts.
   *
   * @since 0.2.0
   *
   * @return void
   */
  public function register_scripts(array $scripts)
  {
    foreach ($scripts as $handle => $script) {
      wp_register_script($handle, $script['src'], $script['deps'], $script['version'], $script['in_footer']);
    }
  }


  /**
   * Enqueue admin styles and scripts.
   *
   * @since 0.2.0
   * @since 0.3.0 Loads the JS and CSS only on the Job Place admin page.
   *
   * @return void
   */
  public function enqueue_admin_assets()
  {
    if (!is_admin() || !isset($_GET['page']) || sanitize_text_field(wp_unslash($_GET['page'])) !== 'squarewoosync') {
      return;
    }

    wp_enqueue_style('sws-css');
    wp_enqueue_script('sws-app');
    wp_enqueue_script('sws-gift-card');

    // Prepare roles as [{id, label}, …]
    $roles_array = array();
    if (function_exists('wp_roles')) {
      /** @var WP_Roles $wp_roles */
      $wp_roles = wp_roles();
      foreach ($wp_roles->roles as $role_key => $role_info) {
        $roles_array[] = array(
          'id'    => $role_key,
          'label' => $role_info['name'],
        );
      }
    }

    // Localize data for sws-app
    wp_localize_script('sws-app', 'swsData', array(
      'nonce' => wp_create_nonce('sws_sec_nonce'),
      'roles' => $roles_array,
    ));
  }
}
