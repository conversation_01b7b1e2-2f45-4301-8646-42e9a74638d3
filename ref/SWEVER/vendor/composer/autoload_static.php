<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit21d8b426006b0e66bb19626536946587
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WooCommerce\\' => 12,
        ),
        'P' => 
        array (
            'Pixeldev\\SquareWooSync\\' => 23,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WooCommerce\\' => 
        array (
            0 => __DIR__ . '/../..' . '/wp-content/plugins/woocommerce/includes',
        ),
        'Pixeldev\\SquareWooSync\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit21d8b426006b0e66bb19626536946587::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit21d8b426006b0e66bb19626536946587::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit21d8b426006b0e66bb19626536946587::$classMap;

        }, null, ClassLoader::class);
    }
}
