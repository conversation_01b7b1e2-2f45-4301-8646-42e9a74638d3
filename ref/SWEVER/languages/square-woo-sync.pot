# Copyright (c) 2025 <PERSON>. All Rights Reserved.
msgid ""
msgstr ""
"Project-Id-Version: Square Sync for Woocommerce 6.0.1\n"
"Report-Msgid-Bugs-To: https://github.com/LiamHillier/square-woo-sync/issues\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-06-16T23:11:11+10:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: squarewoosync\n"

#. Plugin Name of the plugin
#: squarewoosync.php
msgid "Square Sync for Woocommerce"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: squarewoosync.php
msgid "https://squaresyncforwoo.com"
msgstr ""

#. Description of the plugin
#: squarewoosync.php
msgid "Easily Sync your WooCommerce Square data in real-time with the SquareSync for Woo. Stock, titles, descriptions, orders and more."
msgstr ""

#. Author of the plugin
#: squarewoosync.php
msgid "SquareSync for Woo"
msgstr ""

#: includes/Abstracts/RESTController.php:35
#: includes/Payments/WC_SquareSync_Gateway.php:1744
msgid "Nonce verification failed"
msgstr ""

#: includes/Admin/Menu.php:43
msgid "SquareSync"
msgstr ""

#: includes/Admin/Menu.php:46
msgid "Dashboard"
msgstr ""

#: includes/Admin/Menu.php:47
msgid "Products"
msgstr ""

#: includes/Admin/Menu.php:48
msgid "Customers"
msgstr ""

#: includes/Admin/Menu.php:49
msgid "Orders"
msgstr ""

#: includes/Admin/Menu.php:50
#: squarewoosync.php:278
msgid "Settings"
msgstr ""

#: includes/Logger/Logger.php:156
msgid "Error fetching logs from the database."
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:210
msgid "Subscription renewal of %1$s via Square succeeded. Transaction ID: %2$s"
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:217
msgid "Square renewal failed: %s"
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1461
msgid "Invalid payment method. Please try again."
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1576
#: includes/Payments/WC_SquareSync_Gateway.php:1795
msgid "There was a problem saving your payment method."
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1643
msgid "Payment error: "
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1723
msgid "Payment of %1$s via Square successfully completed (Square Transaction ID: %2$s)"
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1732
msgid "Payment error: An unexpected error occurred. Please try again."
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1752
msgid "Payment token is missing."
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1764
msgid "Could not retrieve or create Square customer."
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1773
msgid "There was a problem adding your payment method: Invalid card data."
msgstr ""

#: includes/Payments/WC_SquareSync_Gateway.php:1793
msgid "Payment method added successfully."
msgstr ""

#: includes/REST/LogController.php:89
msgid "Unable to retrieve logs"
msgstr ""

#: includes/REST/LogController.php:108
msgid "Invalid log parameters"
msgstr ""

#: includes/REST/LogController.php:110
msgid "Log parameters are missing or incomplete"
msgstr ""

#: includes/REST/LogController.php:116
msgid "success"
msgstr ""

#: includes/REST/LogController.php:118
msgid "Invalid argument: "
msgstr ""

#: includes/REST/LogController.php:124
msgid "Exception encountered: "
msgstr ""

#: includes/REST/OrdersController.php:86
#: includes/REST/OrdersController.php:88
msgid "Square Orders error: "
msgstr ""

#: includes/REST/OrdersController.php:97
msgid "Square Orders API error: "
msgstr ""

#: includes/REST/OrdersController.php:99
#: includes/REST/OrdersController.php:112
msgid "Square API error: "
msgstr ""

#: includes/REST/OrdersController.php:110
msgid "Square Payment API error: "
msgstr ""

#: includes/REST/OrdersController.php:128
msgid "Order and Transaction created in Square, receipt: #"
msgstr ""

#: includes/REST/OrdersController.php:148
msgid "Order created in Square"
msgstr ""

#: includes/REST/OrdersController.php:155
msgid "Failed to create order: "
msgstr ""

#: includes/REST/OrdersController.php:962
msgid "Woocommerce not installed or activated"
msgstr ""

#: includes/REST/SquareController.php:64
msgid "Search query for products (name, sku, or square product id)."
msgstr ""

#: includes/REST/SquareController.php:69
msgid "Category filter."
msgstr ""

#: includes/REST/SquareController.php:74
msgid "Status filter."
msgstr ""

#: includes/REST/SquareController.php:79
msgid "Page number."
msgstr ""

#: includes/REST/SquareController.php:85
msgid "Number of items per page."
msgstr ""

#: includes/Square/SquareImport.php:147
msgid "Deleted invalid product: "
msgstr ""

#: includes/Square/SquareImport.php:173
msgid "Successfully synced: "
msgstr ""

#: includes/Square/SquareImport.php:173
msgid " from Square to Woo"
msgstr ""

#: includes/Square/SquareInventory.php:303
msgid "Unknown Option"
msgstr ""

#: includes/Square/SquareInventory.php:304
msgid "Unknown Value"
msgstr ""

#: includes/Woo/CreateOrder.php:347
msgid "Discount"
msgstr ""

#: includes/Woo/CreateOrder.php:390
msgid "Order status updated based on Square order state and payment status."
msgstr ""

#: includes/Woo/CreateOrder.php:391
msgid "Order status set to %s based on Square state: %s"
msgstr ""

#: includes/Woo/CreateOrder.php:435
msgid "Square Shipping"
msgstr ""

#: includes/Woo/CreateOrder.php:477
msgid "Shipping method with ID %s not found."
msgstr ""

#. translators: %s: WP_Error message
#: includes/Woo/CreateProduct.php:856
msgid "Error creating category: %s"
msgstr ""

#: includes/Woo/CreateProduct.php:857
msgid "unknown error"
msgstr ""

#: includes/Woo/SyncProduct.php:1222
msgid "Unlock the full power of SquareSync for Woo with Pro:"
msgstr ""

#: includes/Woo/SyncProduct.php:1226
msgid "Real-time, two-way product & stock sync"
msgstr ""

#: includes/Woo/SyncProduct.php:1227
msgid "Location-aware inventory & taxes"
msgstr ""

#: includes/Woo/SyncProduct.php:1228
msgid "Square Loyalty earn & redeem at checkout"
msgstr ""

#: includes/Woo/SyncProduct.php:1229
msgid "Automatic POS ↔ WooCommerce order sync"
msgstr ""

#: includes/Woo/SyncProduct.php:1230
msgid "Priority support & self-healing scheduler"
msgstr ""

#: includes/Woo/SyncProduct.php:1236
msgid "Go Pro"
msgstr ""

#: includes/Woo/SyncProduct.php:1664
msgid "Sync to Square"
msgstr ""

#: includes/Woo/SyncProduct.php:1711
msgid "Sync to Square initiated."
msgstr ""

#: includes/Woo/SyncProduct.php:1716
msgid "A sync job is already running. Please wait until the current job finishes."
msgstr ""

#: includes/Woo/WooImport.php:119
msgid "Export to Square"
msgstr ""

#: includes/Woo/WooImport.php:122
msgid "Export to Square (All Pages)"
msgstr ""

#: includes/Woo/WooImport.php:212
msgid "Export/Sync job initiated."
msgstr ""

#: includes/Woo/WooImport.php:215
msgid "A job is already running. Please wait until it finishes."
msgstr ""

#: includes/Woo/WooImport.php:515
msgid "WooCommerce must be installed."
msgstr ""

#: includes/Woo/WooImport.php:578
msgid "Failed to import products: "
msgstr ""

#: includes/Woo/WooImport.php:662
msgid "Square batch-upsert failed: "
msgstr ""

#: includes/Woo/WooImport.php:676
msgid "Failed to process products: "
msgstr ""

#: squarewoosync.php:123
msgid "SquareWooSync requires WooCommerce to be installed and activated."
msgstr ""

#: squarewoosync.php:219
msgid "SquareSync for Woo is not yet connected to your Square account. Connect now to start syncing products and accepting payments"
msgstr ""

#: squarewoosync.php:224
msgid "Connect Square"
msgstr ""

#: squarewoosync.php:279
msgid "Documentation"
msgstr ""

#: squarewoosync.php:359
msgid "Additional Roles"
msgstr ""

#: squarewoosync.php:362
msgid "Roles"
msgstr ""

#: templates/emails/plain/square-gift-card.php:23
#: templates/emails/square-gift-card.php:27
msgid "Friend"
msgstr ""

#: templates/emails/plain/square-gift-card.php:24
#: templates/emails/square-gift-card.php:29
msgid "Someone"
msgstr ""

#: templates/emails/plain/square-gift-card.php:31
#: templates/emails/square-gift-card.php:35
msgid "Hello %s,"
msgstr ""

#: templates/emails/plain/square-gift-card.php:37
#: templates/emails/square-gift-card.php:46
msgid "%s just sent you a gift card! Below are the details:"
msgstr ""

#: templates/emails/plain/square-gift-card.php:43
#: templates/emails/square-gift-card.php:62
msgid "Gift Card Number:"
msgstr ""

#: templates/emails/plain/square-gift-card.php:48
msgid "Gift Card Balance:"
msgstr ""

#: templates/emails/plain/square-gift-card.php:53
#: templates/emails/square-gift-card.php:82
msgid "A Message From the Sender:"
msgstr ""

#: templates/emails/plain/square-gift-card.php:58
msgid "HOW TO REDEEM YOUR GIFT CARD"
msgstr ""

#: templates/emails/plain/square-gift-card.php:60
msgid "1) Visit our store and add your desired products to the cart."
msgstr ""

#: templates/emails/plain/square-gift-card.php:61
msgid "2) Proceed to checkout and find the Gift Card or Promo Code field."
msgstr ""

#: templates/emails/plain/square-gift-card.php:62
msgid "3) Enter your Gift Card Number (GAN) and apply it to your order."
msgstr ""

#: templates/emails/plain/square-gift-card.php:63
msgid "Any remaining balance stays on the card for future use (per store policy)."
msgstr ""

#: templates/emails/plain/square-gift-card.php:65
msgid "If you have any questions, please contact our support team."
msgstr ""

#: templates/emails/plain/square-gift-card.php:68
#: templates/emails/square-gift-card.php:114
msgid "Thank you for shopping with %s!"
msgstr ""

#: templates/emails/square-gift-card.php:55
msgid "Your new gift card details are below:"
msgstr ""

#: templates/emails/square-gift-card.php:72
msgid "Gift card balance:"
msgstr ""

#: templates/emails/square-gift-card.php:89
msgid "How to Redeem Your Gift Card"
msgstr ""

#: templates/emails/square-gift-card.php:92
msgid "Visit our store and add your desired products to the cart."
msgstr ""

#: templates/emails/square-gift-card.php:95
msgid "Proceed to checkout and locate the Gift Card field."
msgstr ""

#: templates/emails/square-gift-card.php:98
msgid "Enter the Gift Card Number above and apply it to your order."
msgstr ""

#: templates/emails/square-gift-card.php:102
msgid "The gift card balance will be applied to your total. Any remaining balance stays on the card for future use.."
msgstr ""

#: templates/emails/square-gift-card.php:107
msgid "If you have any questions, feel free to contact our support team."
msgstr ""

#: build/blocks/giftcard.js:1
msgid "Unable to apply gift card."
msgstr ""

#: build/blocks/giftcard.js:1
msgid "Unable to remove gift card."
msgstr ""

#: build/blocks/giftcard.js:1
msgid "Gift Card Applied:"
msgstr ""

#: build/blocks/giftcard.js:1
msgid "Remove Gift Card"
msgstr ""

#: build/blocks/giftcard.js:1
msgid "Redeem a gift card"
msgstr ""

#: build/blocks/giftcard.js:1
msgid "Enter code"
msgstr ""

#: build/blocks/giftcard.js:1
msgid "Apply"
msgstr ""
