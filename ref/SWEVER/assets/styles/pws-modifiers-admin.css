/* Main container for each modifier set in the admin panel */
#pws_modifier_sets_container {
  padding: 5px 9px;
}
.pws-modifier-set {
  background-color: #fafafa;
  border: 1px solid #ddd;
  margin-bottom: 15px;
  padding: 10px;
}

.pws-modifier-set-header {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 5px;
  flex-wrap: wrap;
}

/* The internal table of options */
.pws-options-table {
  margin-top: 10px;
}

.pws-options-table thead th {
  background-color: #efefef;
}

.pws-options-table tbody td {
  vertical-align: middle;
}

/* Buttons */
.pws-remove-set-btn {
  margin-top: 20px;
  color: #a00;
}

.pws-remove-option-btn {
  color: #a00;
}

#pws_add_set_btn {
    margin: 5px 9px !important;
    margin-bottom: 20px !important;
}

.pws-add-option-btn {
  margin-top: 10px !important;
}

/* A bit of spacing/padding around labels, etc. */
.pws-modifier-set input[type="text"],
.pws-modifier-set input[type="number"] {
  width: 100%;
  max-width: 150px;
}

.pws-modifier-set-header label {
  margin: 0px !important;
  float: none !important;
  width: auto !important;
  display: flex !important;
  align-items: center !important;
  flex-wrap: nowrap !important;
  gap: 5px !important;
}

.pws-modifier-set-header > div {
  min-width: calc(33% - 60px);
  display: flex;
  flex-wrap: no-wrap;
  align-items: center;
  gap: 6px;
}

.pws-modifier-set-header br {
  display: none;
}