.woocommerce-page .woocommerce-checkout .woocommerce-checkout-review-order-table #order-loyalty-points td {
    font-weight: 500;
}


#apply-loyalty-reward, #remove-loyalty-reward {
    padding: 10px 25px;
    cursor: pointer;
    background-color: #111111;
    border-color: #111111;
    border-width: 0;
    color: white;
    font-family: inherit;
    font-size: 0.9rem;
    font-style: normal;
    font-weight: 500;
    line-height: inherit;
    padding: .78rem 1.1rem;
    text-decoration: none;
}

#apply-loyalty-reward:hover, #remove-loyalty-reward:hover {
    background-color: #636363;
    border-color: #636363;
    border-width: 0;
}

.order-loyalty-points .program-title {
    margin: 10px 0px;
    display: flex;
    gap: 0.5rem;
    align-items: flex-start;
}

.order-loyalty-points .current-points {
    font-size: 20px; 
}
.order-loyalty-points td * {
    margin: 0px;
    font-weight: 500;
}
.order-loyalty-points td {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

}
.order-loyalty-points .reward-list {
    list-style-type: none;
    padding: 0px;
}

.order-loyalty-points .reward-list  li {
    position: relative;
 
    margin-bottom: 12px;
}
  


.order-loyalty-points .reward-list label {
    margin-left: 7px;
}

.loyalty-blocks--block-container {
    padding: 16px !important;
    border-top: 1px solid hsla(0, 0%, 7%, .11);
    border-bottom: 1px solid hsla(0, 0%, 7%, .11);
    margin-top: 16px;
}

.loyalty-blocks--block-container h4 {
    font-weight: 500;
    font-family: var(--wp--preset--font-family--body);
    font-size: var(--wp--preset--font-size--medium);
    margin: 0px;
    margin-bottom: 8px;
}

.loyalty-blocks-rewards-container {
    display: flex;
    flex-direction: column;
}

.loyalty-blocks--block-container p {
    margin: 0px;
}

#blocks-apply-loyalty-reward, #blocks-remove-loyalty-reward {
    background-color: var(--wp--preset--color--contrast);
    border-radius: .33rem;
    border-color: var(--wp--preset--color--contrast); 
    align-items: center;
    display: inline-flex;
    height: auto;
    justify-content: center;
    position: relative;
    text-align: center;
    box-shadow: none;   
    border: none;    
    outline: none;        
    transition: box-shadow .1s linear;
    width: 100%;
    padding: 1em;
    font-family: inherit;
    font-size: var(--wp--preset--font-size--small);
    font-style: normal;
    font-weight: 500;
    color: var(--wp--preset--color--base);
    cursor: pointer;
}

#blocks-apply-loyalty-reward:hover, #blocks-remove-loyalty-reward:hover {
    background-color: var(--wp--preset--color--contrast-2);
    border-color: var(--wp--preset--color--contrast-2);
    color: var(--wp--preset--color--base);
}

.available-rewards p {
    margin-bottom: 8px;
    margin-top: 8px;
}

#blocks-remove-loyalty-reward {
    margin-top: 8px !important;
}

.loyalty-blocks-points-earn {
    margin-top: 12px !important;
}

.points-earned, .terminology, .points-redeemed {
    font-weight: bold;
}