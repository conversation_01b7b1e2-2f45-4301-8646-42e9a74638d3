jQuery(document).ready(function ($) {
  // "Add Modifier" button
  $('#pws_add_modifier_btn').on('click', function () {
    var newRow = `
            <tr>
                <td>
                    <input type="text" name="pws_modifier_name[]" value="" placeholder="e.g. Extra Sauce" />
                </td>
                <td>
                    <input type="number" step="0.01" name="pws_modifier_price[]" value="" placeholder="e.g. 1.00" />
                </td>
                <td>
                    <button type="button" class="button link-delete pws_remove_modifier_btn">Remove</button>
                </td>
            </tr>
        `;
    $('#pws_modifiers_table tbody').append(newRow);
  });

  // Remove row
  $(document).on('click', '.pws_remove_modifier_btn', function () {
    $(this).closest('tr').remove();
  });

});
