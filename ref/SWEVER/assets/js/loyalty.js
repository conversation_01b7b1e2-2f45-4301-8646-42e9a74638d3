jQuery(document).ready(function ($) {
    // Function to show the loader
    function showLoader() {
        $('#apply-loyalty-reward, #remove-loyalty-reward').prop('disabled', true); // Disable buttons
        $('#loyalty-loader').show(); // Show loader (spinner)
    }

    // Function to hide the loader
    function hideLoader() {
        $('#apply-loyalty-reward, #remove-loyalty-reward').prop('disabled', false); // Enable buttons
        $('#loyalty-loader').hide(); // Hide loader (spinner)
    }

    // Apply reward when the "Apply Reward" button is clicked
    $(document).on('click', '#apply-loyalty-reward', function () {
        var selectedReward = $('input[name="loyalty_reward"]:checked').val();

        if (!selectedReward) {
            alert('Please select a reward.');
            return;
        }

        showLoader(); // Show loader while processing the request

        $.ajax({
            url: apply_loyalty_discount_vars.ajax_url,
            method: 'POST',
            data: {
                action: 'apply_loyalty_discount',
                nonce: apply_loyalty_discount_vars.nonce,
                selected_tier: selectedReward
            },
            success: function (response) {
                console.log(response)
                if (response.success) {
                    // Reload the order review section to reflect updated totals and reward display
                    $('body').trigger('update_checkout');
                    $('body').addClass('loyalty-reward-applied'); // Add class to indicate reward applied
                } else {
                    alert(response.data.message);
                }
                hideLoader(); // Hide loader after success
            },
            error: function () {
                alert('Error applying the reward.');
                hideLoader(); // Hide loader after error
            }
        });
    });

    // Remove reward when the "Remove Reward" button is clicked
    $(document).on('click', '#remove-loyalty-reward', function () {
        showLoader(); // Show loader while processing the request

        $.ajax({
            url: apply_loyalty_discount_vars.ajax_url,
            method: 'POST',
            data: {
                action: 'remove_loyalty_discount',
                nonce: apply_loyalty_discount_vars.nonce
            },
            success: function (response) {
                if (response.success) {
                    // Reload the order review section to reflect updated totals and options
                    $('body').trigger('update_checkout');
                    $('body').removeClass('loyalty-reward-applied'); // Remove class to indicate reward removed
                } else {
                    alert(response.data.message);
                }
                hideLoader(); // Hide loader after success
            },
            error: function () {
                alert('Error removing the reward.');
                hideLoader(); // Hide loader after error
            }
        });
    });


});
