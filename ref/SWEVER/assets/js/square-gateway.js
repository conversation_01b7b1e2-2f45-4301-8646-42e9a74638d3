(function ($) {
  let payments = null;
  let walletPaymentRequest = null;
  let paymentInProgress = false;

  let alreadyInitialized = false; // Guard for initial load

  // Debounce timer
  let squareInitTimeout = null;
  const DEBOUNCE_DELAY = 20;

  // We remove lastKnownTotal logic & the booleans, allowing each partial refresh to re-init

  /**
   * Debounces the Square init calls so multiple near-simultaneous triggers
   * only cause a single actual initialization attempt.
   */
  function scheduleSquareInit() {
    if (squareInitTimeout) {
      clearTimeout(squareInitTimeout);
    }
    squareInitTimeout = setTimeout(handleSquareInit, DEBOUNCE_DELAY);
  }

  /**
   * Main initialization if squaresync_credit is chosen.
   * Called after the debounce timer.
   */
  async function handleSquareInit() {
    const selectedMethod = $('#payment input[name="payment_method"]:checked').val();
    if (selectedMethod !== 'squaresync_credit') {
      return;
    }


    clearStoredTokens();

    if (typeof Square === 'undefined') {
      console.error('Square Web Payments SDK not loaded');
      console.log('return 2')
      return;
    }


    try {
      // Create 'payments' once if not done yet
      if (!payments) {
        const applicationId = SquareConfig.applicationId;
        const locationId = SquareConfig.locationId;
        payments = await Square.payments(applicationId, locationId);
        console.log(payments)
        if (!payments) {
          throw new Error('Square payments init failed');
        }
      }

      const needsShipping = await checkNeedsShipping();
      const couponApplied = false;
      // Build or refresh the PaymentRequest object
      walletPaymentRequest = await initPaymentRequest(payments, needsShipping, couponApplied);

      // Show/hide card fields
      toggleCardFields();

      // Force-clean the container and re-mount the card
      forceCleanCardContainer();
      initCreditCardPayment(payments, walletPaymentRequest);

      // Also forcibly remove leftover wallet buttons, then re-init
      if (walletPaymentRequest) {
        forceCleanWalletButtons();
        initWalletPayments(payments, walletPaymentRequest, needsShipping, couponApplied);
      }

      alreadyInitialized = true;

    } catch (err) {
      console.error('Square init error:', err);
    }
  }

  /**
   * Remove any leftover .sq-card-wrapper from partial refresh
   */
  function forceCleanCardContainer() {
    const $container = $('#payment #card-container');
    if ($container.length) {
      $container.find('.sq-card-wrapper').remove();
    }
  }

  /**
   * Remove leftover wallet button elements
   */
  function forceCleanWalletButtons() {
    jQuery("#apple-pay-button").empty();
    jQuery("#google-pay-button").empty();
    jQuery("#afterpay-button").empty();

    // Also re-allow initWalletPayments() if you use a guard inside it
    // e.g. `walletPaymentsInitialized = false;`
    window.walletPaymentsInitialized = false;
    walletPaymentsInitialized = false;
  }

  /**
   * Intercept place_order if squaresync_credit is chosen, so we can tokenize.
   */
  $(document).on('click', '#place_order', function (e) {
    const method = $('#payment input[name="payment_method"]:checked').val();
    if (method !== 'squaresync_credit' || paymentInProgress) return;

    e.preventDefault();
    paymentInProgress = true;
    $(document.body).trigger('checkout_place_order_squaresync_credit');
  });

  /**
   * Actually tokenize the card, store nonce, then re-submit.
   */
  $(document.body).on('checkout_place_order_squaresync_credit', async function () {
    const success = await processCreditCardPayment(payments, walletPaymentRequest);
    paymentInProgress = false;
    if (success) {
      if (SquareConfig.context === 'account') {
        $('#add_payment_method').trigger('submit');
      } else {
        $('form.checkout').trigger('submit');
      }
    } else {
      clearStoredTokens();
    }
  });

  $( document.body ).on( 'wc_payment_method_selected', scheduleSquareInit );

  /**
   * Show/hide the new card fields vs. saved tokens
   */
  function toggleCardFields() {
    const token = $('#payment input[name="wc-squaresync_credit-payment-token"]:checked').val();
    if (token && token !== 'new') {
      $('#payment-form').hide();
      $('.woocommerce-SavedPaymentMethods-saveNew').hide();
    } else {
      $('#payment-form').show();
      $('.woocommerce-SavedPaymentMethods-saveNew').show();
    }
  }

  // Debounced triggers from WooCommerce
  $(document.body).on('updated_checkout', scheduleSquareInit);
  $(document.body).on('change', '#payment input[name="payment_method"]', scheduleSquareInit);

  $(document).on('click', '#wc-squaresync_credit-payment-token-new', function (e) {
    $('#payment-form').show();
  });
  $(document).on('click', '.woocommerce-SavedPaymentMethods-token input', function (e) {
    $('#payment-form').hide();
  });

  // If user picks "Use a new card," re-check
  // $(document).on('change', '#payment input[name="wc-squaresync_credit-payment-token"]', function () {
  //   toggleCardFields();
  //   const method = $('#payment input[name="payment_method"]:checked').val();
  //   if ($(this).val() === 'new' && method === 'squaresync_credit') {
  //     forceCleanCardContainer()
  //     forceCleanWalletButtons()
  //     scheduleSquareInit();
  //   }
  // });

  // On account page "Add Payment Method", do an init once
  $(document).ready(function () {
    if (SquareConfig.context === 'account') {
      $(document.body).trigger('wc_payment_method_selected');
    }
  });

  // On checkout_error, clear tokens
  $(document.body).on('checkout_error', function () {
    clearStoredTokens();
  });

})(jQuery);
