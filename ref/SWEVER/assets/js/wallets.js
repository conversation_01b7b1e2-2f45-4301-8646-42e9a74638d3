let walletPaymentsInitialized = false;

async function initWalletPayments(
  payments,
  walletPaymentRequest,
  needsShipping,
  couponApplied
) {
  if (walletPaymentsInitialized) {
    return; // Don't run again
  }
  walletPaymentsInitialized = true;

  if (!payments) {
    console.error("Payments object is required for wallet payments.");
    return;
  }

  // Initialize or reinitialize wallet buttons with updated payment request
  async function reinitializeWalletButtons() {
    const updatedWalletPaymentRequest = await initPaymentRequest(
      payments,
      needsShipping,
      couponApplied
    );

    if (updatedWalletPaymentRequest) {
      await attachWalletButtons(
        updatedWalletPaymentRequest,
        payments,
        needsShipping,
        couponApplied
      );
    }
  }

  // Attach initial wallet buttons on page load
  if (walletPaymentRequest) {
    await attachWalletButtons(
      walletPaymentRequest,
      payments,
      needsShipping,
      couponApplied
    );
  }
  // reinitializeWalletButtons();
  // // Reinitialize wallet buttons whenever cart totals update
  // jQuery(document.body).on(
  //     "wc_cart_totals_updated updated_shipping_method applied_coupon removed_coupon updated_checkout",
  //     async function () {
  //         await reinitializeWalletButtons();  // Reinitialize when any cart updates occur
  //     }
  // );
}

/**********************************************************/
/** 1) Helper: transformContact                          **/
/**********************************************************/
function transformContact(contact, isAfterpay, fallback = {}) {
  // If there's no contact at all, we immediately fallback.
  if (!contact || typeof contact !== "object") {
    contact = {};
  }

  const {
    familyName,
    givenName,
    region, // Afterpay uses `region`
    state, // Apple/Google Pay uses `state`
    country, // Afterpay uses `country`
    countryCode, // Apple/Google uses `countryCode`
    city,
    addressLines,
    postalCode,
    phone,
    email,
  } = contact;

  const {
    first_name: fallbackGivenName = "",
    last_name: fallbackFamilyName = "",
    address_1: fallbackAddress1 = "",
    address_2: fallbackAddress2 = "",
    city: fallbackCity = "",
    state: fallbackState = "",
    country: fallbackCountry = "",
    postcode: fallbackPostcode = "",
    phone: fallbackPhone = "",
    email: fallbackEmail = "",
  } = fallback;

  const mergedGivenName = givenName ?? fallbackGivenName;
  const mergedFamilyName = familyName ?? fallbackFamilyName;
  const mergedCity = city ?? fallbackCity;
  const mergedPostalCode = postalCode ?? fallbackPostcode;
  const mergedEmail = email ?? fallbackEmail;
  const mergedPhone = phone ?? fallbackPhone;

  // For region/state
  const mergedState = isAfterpay
    ? region ?? fallbackState
    : state ?? region ?? fallbackState;

  // For country / countryCode
  const mergedCountry = isAfterpay
    ? country ?? fallbackCountry
    : countryCode ?? country ?? fallbackCountry;

  // For address lines
  let mergedAddressLines = [];
  if (Array.isArray(addressLines) && addressLines.length > 0) {
    mergedAddressLines = addressLines;
  } else {
    // Merge fallback address_1 and address_2 if present
    mergedAddressLines = [fallbackAddress1, fallbackAddress2].filter(Boolean);
  }

  return {
    givenName: mergedGivenName,
    familyName: mergedFamilyName,
    state: mergedState,
    countryCode: mergedCountry,
    city: mergedCity,
    addressLines: mergedAddressLines,
    postalCode: mergedPostalCode,
    phone: mergedPhone,
    email: mergedEmail,
  };
}

/**********************************************************/
/** 2) Helper: isBillingComplete                         **/
/**********************************************************/
function isBillingComplete(billingContact) {
  // Define whatever fields you consider "required"
  // Adjust as needed for your store’s requirements
  return (
    billingContact &&
    billingContact.givenName &&
    billingContact.familyName &&
    billingContact.addressLines &&
    billingContact.addressLines.length > 0 &&
    billingContact.city &&
    billingContact.state &&
    billingContact.postalCode &&
    billingContact.countryCode
  );
}

async function attachWalletButtons(
  paymentRequest,
  payments,
  needsShipping,
  couponApplied
) {
  // Common function to handle wallet tokenization and verification
  /**********************************************************/
  /** 1) Helper: transformContact                          **/
  /**********************************************************/
  function transformContact(contact, isAfterpay, fallback = {}) {
    // If there's no contact at all, we immediately fallback.
    if (!contact || typeof contact !== "object") {
      contact = {};
    }

    const {
      familyName,
      givenName,
      region, // Afterpay uses `region`
      state, // Apple/Google Pay uses `state`
      country, // Afterpay uses `country`
      countryCode, // Apple/Google uses `countryCode`
      city,
      addressLines,
      postalCode,
      phone,
      email,
    } = contact;

    const {
      first_name: fallbackGivenName = "",
      last_name: fallbackFamilyName = "",
      address_1: fallbackAddress1 = "",
      address_2: fallbackAddress2 = "",
      city: fallbackCity = "",
      state: fallbackState = "",
      country: fallbackCountry = "",
      postcode: fallbackPostcode = "",
      phone: fallbackPhone = "",
      email: fallbackEmail = "",
    } = fallback;

    const mergedGivenName = givenName ?? fallbackGivenName;
    const mergedFamilyName = familyName ?? fallbackFamilyName;
    const mergedCity = city ?? fallbackCity;
    const mergedPostalCode = postalCode ?? fallbackPostcode;
    const mergedEmail = email ?? fallbackEmail;
    const mergedPhone = phone ?? fallbackPhone;

    // For region/state
    const mergedState = isAfterpay
      ? region ?? fallbackState
      : state ?? region ?? fallbackState;

    // For country / countryCode
    const mergedCountry = isAfterpay
      ? country ?? fallbackCountry
      : countryCode ?? country ?? fallbackCountry;

    // For address lines
    let mergedAddressLines = [];
    if (Array.isArray(addressLines) && addressLines.length > 0) {
      mergedAddressLines = addressLines;
    } else {
      // Merge fallback address_1 and address_2 if present
      mergedAddressLines = [fallbackAddress1, fallbackAddress2].filter(Boolean);
    }

    return {
      givenName: mergedGivenName,
      familyName: mergedFamilyName,
      state: mergedState,
      countryCode: mergedCountry,
      city: mergedCity,
      addressLines: mergedAddressLines,
      postalCode: mergedPostalCode,
      phone: mergedPhone,
      email: mergedEmail,
    };
  }

  /**********************************************************/
  /** 2) Helper: isBillingComplete                         **/
  /**********************************************************/
  function isBillingComplete(billingContact) {
    // Define whatever fields you consider "required"
    // Adjust as needed for your store’s requirements
    return (
      billingContact &&
      billingContact.givenName &&
      billingContact.familyName &&
      billingContact.addressLines &&
      billingContact.addressLines.length > 0 &&
      billingContact.city &&
      billingContact.state &&
      billingContact.postalCode &&
      billingContact.countryCode
    );
  }

  /**********************************************************/
  /** 3) handleTokenizationAndVerification (UPDATED)       **/
  /**********************************************************/
  async function handleTokenizationAndVerification(walletInstance, walletType) {
    try {
      const tokenResult = await walletInstance.tokenize();

      if (tokenResult.status === "OK") {
        // Attach raw nonce/token to a hidden field
        attachTokenToForm(tokenResult.token);

        // We assume `walletType === "AfterpayClearpay"` for Afterpay logic
        const isAfterpay = walletType === "AfterpayClearpay";

        // Ensure "Ship to a different address" is checked if we have shipping details
        const shipToDifferentAddressCheckbox = jQuery(
          "#ship-to-different-address-checkbox"
        );
        if (!shipToDifferentAddressCheckbox.is(":checked")) {
          shipToDifferentAddressCheckbox.prop("checked", true);
          jQuery("div.shipping_address").show(); // Make sure shipping fields are visible
        }

        /*******************************************************/
        /** 3a) Extract shipping & billing from tokenResult   **/
        /*******************************************************/
        const shippingDetails = tokenResult?.details?.shipping?.contact || {};
        const billingDetails = tokenResult?.details?.billing || {};

        // In case your legacy checkout still needs to fallback to
        // current form values if the contact is missing data:
        const fallbackShipping = {
          first_name: jQuery("#shipping_first_name").val(),
          last_name: jQuery("#shipping_last_name").val(),
          address_1: jQuery("#shipping_address_1").val(),
          address_2: jQuery("#shipping_address_2").val(),
          city: jQuery("#shipping_city").val(),
          state: jQuery("#shipping_state").val(),
          postcode: jQuery("#shipping_postcode").val(),
          country: jQuery("#shipping_country").val(),
          phone: jQuery("#shipping_phone").val(),
          email: jQuery("#billing_email").val(), // Sometimes shipping doesn't have its own email
        };
        const fallbackBilling = {
          first_name: jQuery("#billing_first_name").val(),
          last_name: jQuery("#billing_last_name").val(),
          address_1: jQuery("#billing_address_1").val(),
          address_2: jQuery("#billing_address_2").val(),
          city: jQuery("#billing_city").val(),
          state: jQuery("#billing_state").val(),
          postcode: jQuery("#billing_postcode").val(),
          country: jQuery("#billing_country").val(),
          phone: jQuery("#billing_phone").val(),
          email: jQuery("#billing_email").val(),
        };

        // Transform shipping contact
        const shippingContact = transformContact(
          shippingDetails,
          isAfterpay,
          fallbackShipping
        );

        // Transform billing contact
        let billingContact = transformContact(
          billingDetails,
          isAfterpay,
          fallbackBilling
        );

        // If billing is incomplete, fallback to shipping
        if (!isBillingComplete(billingContact)) {
          const shippingAsBilling = transformContact(
            shippingDetails,
            isAfterpay,
            fallbackShipping
          );
          // Preserve billingContact.email if it exists, otherwise use shipping email
          billingContact = {
            ...shippingAsBilling,
            email:
              billingContact.email ||
              shippingAsBilling.email ||
              shippingDetails.email,
          };
        }


        // Shipping:
        // Only update shipping if your store "needs shipping"
        if (typeof needsShipping !== "undefined" && needsShipping) {
          jQuery("#shipping_first_name").val(shippingContact.givenName || "");
          jQuery("#shipping_last_name").val(shippingContact.familyName || "");
          jQuery("#shipping_company").val("");
          jQuery("#shipping_address_1").val(
            shippingContact.addressLines[0] || ""
          );
          jQuery("#shipping_address_2").val(
            shippingContact.addressLines[1] || ""
          );
          jQuery("#shipping_city").val(shippingContact.city || "");
          jQuery("#shipping_state").val(shippingContact.state || "");
          jQuery("#shipping_postcode").val(shippingContact.postalCode || "");
          jQuery("#shipping_country").val(shippingContact.countryCode || "");
          jQuery("#shipping_phone").val(shippingContact.phone || "");
        }

        /*********************************************************/
        /** 3b) Update Legacy Billing & Shipping form fields    **/
        /*********************************************************/
        // — now country/state —
        jQuery("#billing_country")
          .val(billingContact.countryCode || "")
          .trigger("change");  // ⬅️ rebuild states




        // give WooCommerce a moment to repopulate the state <select>
        setTimeout(function () {
          jQuery("#billing_state")
            .val(billingContact.state || "")
            .trigger("change");

          // Billing:
          jQuery("#billing_first_name").val(billingContact.givenName || "");
          jQuery("#billing_last_name").val(billingContact.familyName || "");
          jQuery("#billing_company").val(""); // no company in token result
          jQuery("#billing_address_1").val(billingContact.addressLines[0] || "");
          jQuery("#billing_address_2").val(billingContact.addressLines[1] || "");
          // … your other field‐fills …
          jQuery("#billing_city").val(billingContact.city || "");
          jQuery("#billing_postcode").val(billingContact.postalCode || "");

          // … then the rest …
          jQuery("#billing_phone").val(billingContact.phone || "");
          jQuery("#billing_email").val(billingContact.email || "");

          jQuery("form.checkout").trigger("submit");
        }, 100);


      } else {
        // Clear stored tokens or error-handling
        clearStoredTokens();
        if (tokenResult.status !== "Cancel") {
          logPaymentError(
            `${walletType} tokenization failed: ${JSON.stringify(
              tokenResult.errors
            )}`
          );
        }
      }
    } catch (error) {
      clearStoredTokens();
      logPaymentError(
        `${walletType} tokenization or verification error: ${error.message}`
      );
    }
  }

  // Apple Pay Initialization or Update
  if (SquareConfig.applePayEnabled === "yes") {
    try {
      const applePayButtonContainer =
        document.querySelector("#apple-pay-button");
      if (applePayButtonContainer) {
        applePayButtonContainer.style.display = "block";

        const removedShippingOptions = paymentRequest;
        removedShippingOptions["_shippingOptions"] = [];

        const applePayInstance = await payments.applePay(
          removedShippingOptions
        );


        jQuery("#apple-pay-button")
          .off("click")
          .on("click", async function (e) {
            e.preventDefault();
            await handleTokenizationAndVerification(
              applePayInstance,
              "Apple Pay"
            );
          });
      }
    } catch (error) {
      console.error("Failed to initialize Apple Pay:", error);
    }
  }

  // Google Pay Initialization or Update
  if (SquareConfig.googlePayEnabled === "yes") {
    try {
      const googlePayButtonContainer =
        document.querySelector("#google-pay-button");

      // Check if the Google Pay button is already present by checking the element 'gpay-button-online-api-id'
      const googlePayButtonExists = document.querySelector(
        "#gpay-button-online-api-id"
      );

      if (googlePayButtonContainer && !googlePayButtonExists) {
        const removedShippingOptions = paymentRequest;
        removedShippingOptions["_shippingOptions"] = [];

        // Initialize the Google Pay instance
        const googlePayInstance = await payments.googlePay(
          removedShippingOptions
        );
        // Remove any existing click handler before attaching the new one
        jQuery("#google-pay-button")
          .off("click")
          .on("click", async function (e) {
            e.preventDefault();
            await handleTokenizationAndVerification(
              googlePayInstance,
              "Google Pay"
            );
          });

        // Attach the Google Pay button
        await googlePayInstance.attach("#google-pay-button");
        googlePayButtonContainer.style.display = "block"; // Ensure the button is visible
      }
    } catch (error) {
      console.error("Failed to initialize Google Pay:", error);
    }
  }

  if (SquareConfig.afterPayEnabled === "yes") {
    try {
      const afterPayButtonContainer =
        document.querySelector("#afterpay-button");
      // Check for an existing Afterpay button *inside* the container
      const afterPayButtonExists = afterPayButtonContainer
        ? afterPayButtonContainer.querySelector(".sq-ap__button")
        : null;

      if (afterPayButtonContainer && !afterPayButtonExists) {
        const afterPayInstance =
          await payments.afterpayClearpay(paymentRequest);

        jQuery("#afterpay-button")
          .off("click")
          .on("click", async function (e) {
            e.preventDefault();
            await handleTokenizationAndVerification(
              afterPayInstance,
              "Afterpay"
            );
          });

        await afterPayInstance.attach("#afterpay-button");

        // Set a minimum width to prevent it from being cut off
        afterPayButtonContainer.style.minWidth = "240px";

        paymentRequest?.addEventListener(
          "afterpay_shippingaddresschanged",
          (shippingContact) => handleShippingAddressChanged(shippingContact)
        );
        paymentRequest?.addEventListener(
          "afterpay_shippingoptionchanged",
          (option) => handleShippingOptionChanged(option)
        );
      }
    } catch (error) {
      console.error("Failed to initialize Afterpay:", error);
    }
  }
}

function updateWooCommerceShippingFields(shipping) {
  // Bail out if shipping or addressLines is missing
  if (
    !shipping ||
    !Array.isArray(shipping.addressLines) ||
    shipping.addressLines.length === 0
  ) {
    return;
  }

  jQuery("#shipping_first_name").val(shipping.givenName);
  jQuery("#shipping_last_name").val(shipping.familyName);
  jQuery("#shipping_address_1").val(shipping.addressLines[0]);
  jQuery("#shipping_city").val(shipping.city);
  jQuery("#shipping_postcode").val(shipping.postalCode);
  jQuery("#shipping_state").val(shipping.state);
  jQuery("#shipping_country").val(shipping.countryCode);
  jQuery("#shipping_phone").val(shipping.phone);
}
