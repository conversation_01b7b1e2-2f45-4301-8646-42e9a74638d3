<?php
/**
 * Test the fixed import functionality
 */

// Load WordPress
require_once '../../../wp-config.php';
require_once '../../../wp-load.php';

// Load SquareKit classes
require_once 'includes/class-squarekit-settings.php';

$settings = new SquareKit_Settings();
$is_connected = $settings->is_connected();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Fixed Import Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-card { 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 15px 0; 
            background: #f9f9f9;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .info { border-color: #17a2b8; background: #d1ecf1; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        .result { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success { background: #d4edda; color: #155724; }
        .result.error { background: #f8d7da; color: #721c24; }
        .result.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔧 Test Fixed Import Functionality</h1>
    <p><strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <?php if (!$is_connected): ?>
        <div class="test-card error">
            <h2>❌ Not Connected to Square</h2>
            <p>Please connect to Square first before testing imports.</p>
        </div>
    <?php else: ?>
    
    <div class="test-card info">
        <h2>🧪 Test Individual Import (Fixed)</h2>
        <p>Test the fixed individual product import using SWEVER-style architecture:</p>
        
        <button onclick="testIndividualImport()">Test Individual Import AJAX</button>
        <button onclick="testBulkImport()">Test Bulk Import AJAX</button>
        
        <div id="test-results"></div>
    </div>
    
    <div class="test-card info">
        <h2>📋 Compare Methods</h2>
        <p>Compare the working SWEVER method vs the fixed AJAX method:</p>
        
        <button onclick="testSWEVERDirect()">Test SWEVER Direct</button>
        <button onclick="testAJAXMethod()">Test AJAX Method</button>
        
        <div id="comparison-results"></div>
    </div>
    
    <div class="test-card info">
        <h2>🛒 Live Products Page Test</h2>
        <p>Test the actual Products page:</p>
        
        <button onclick="window.open('/wp-admin/admin.php?page=squarekit-products', '_blank')">Open Products Page</button>
        
        <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 5px;">
            <h4>Manual Testing Steps:</h4>
            <ol>
                <li>Open the Products page</li>
                <li>Click an individual "Import" button</li>
                <li>Check if the progress modal appears</li>
                <li>Check if the product is created correctly</li>
                <li>Try the "Import All" button</li>
            </ol>
        </div>
    </div>
    
    <?php endif; ?>
    
    <script>
    function testIndividualImport() {
        const resultsDiv = document.getElementById('test-results');
        resultsDiv.innerHTML += '<div class="result info">Testing individual import with first available Square product...</div>';
        
        // First get a Square product ID
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'squarekit_get_square_inventory',
                nonce: '<?php echo wp_create_nonce('squarekit-admin'); ?>',
                per_page: 1
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success && result.data.products && result.data.products.length > 0) {
                const productId = result.data.products[0].id;
                const productName = result.data.products[0].name;
                
                resultsDiv.innerHTML += `<div class="result info">Found test product: ${productName} (${productId})</div>`;
                
                // Now test the import
                return fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'squarekit_import_product',
                        product_id: productId,
                        nonce: '<?php echo wp_create_nonce('squarekit-admin'); ?>'
                    })
                });
            } else {
                throw new Error('No Square products found');
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                resultsDiv.innerHTML += `<div class="result success">✅ Individual import SUCCESS: ${result.data.message}</div>`;
                if (result.data.wc_product_id) {
                    resultsDiv.innerHTML += `<div class="result success">Created WooCommerce Product ID: ${result.data.wc_product_id}</div>`;
                }
            } else {
                resultsDiv.innerHTML += `<div class="result error">❌ Individual import FAILED: ${result.data.message}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML += `<div class="result error">❌ ERROR: ${error.message}</div>`;
        });
    }
    
    function testBulkImport() {
        const resultsDiv = document.getElementById('test-results');
        resultsDiv.innerHTML += '<div class="result info">Testing bulk import...</div>';
        
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'squarekit_import_all_products',
                nonce: '<?php echo wp_create_nonce('squarekit-admin'); ?>'
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                resultsDiv.innerHTML += `<div class="result success">✅ Bulk import started: ${result.data.message}</div>`;
                resultsDiv.innerHTML += `<div class="result info">Operation ID: ${result.data.operation_id}</div>`;
                
                // Start polling for progress
                pollBulkProgress(result.data.operation_id, resultsDiv);
            } else {
                resultsDiv.innerHTML += `<div class="result error">❌ Bulk import FAILED: ${result.data.message}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML += `<div class="result error">❌ ERROR: ${error.message}</div>`;
        });
    }
    
    function pollBulkProgress(operationId, resultsDiv) {
        let pollCount = 0;
        const maxPolls = 60; // 1 minute max
        
        const pollInterval = setInterval(() => {
            pollCount++;
            
            if (pollCount > maxPolls) {
                clearInterval(pollInterval);
                resultsDiv.innerHTML += '<div class="result error">❌ Polling timeout</div>';
                return;
            }
            
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'squarekit_get_bulk_operation_progress',
                    operation_id: operationId,
                    nonce: '<?php echo wp_create_nonce('squarekit-admin'); ?>'
                })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    const progress = result.data;
                    resultsDiv.innerHTML += `<div class="result info">Progress: ${progress.progress_percentage}% (${progress.processed_items}/${progress.total_items})</div>`;
                    
                    if (progress.is_completed) {
                        clearInterval(pollInterval);
                        resultsDiv.innerHTML += `<div class="result success">✅ Bulk import completed! Status: ${progress.status}</div>`;
                        resultsDiv.innerHTML += `<div class="result success">Results: ${progress.successful_items} successful, ${progress.failed_items} failed</div>`;
                    }
                }
            })
            .catch(error => {
                clearInterval(pollInterval);
                resultsDiv.innerHTML += `<div class="result error">❌ Progress polling error: ${error.message}</div>`;
            });
        }, 1000);
    }
    
    function testSWEVERDirect() {
        const resultsDiv = document.getElementById('comparison-results');
        resultsDiv.innerHTML += '<div class="result info">Testing direct SWEVER method (same as working test)...</div>';
        
        // This would need to be implemented in PHP since we can\'t call PHP classes directly from JavaScript
        resultsDiv.innerHTML += '<div class="result info">Direct SWEVER test would need PHP implementation. Check test-live-import.php for working example.</div>';
    }
    
    function testAJAXMethod() {
        const resultsDiv = document.getElementById('comparison-results');
        resultsDiv.innerHTML += '<div class="result info">Testing AJAX method (should now use SWEVER internally)...</div>';
        
        // Same as individual import test
        testIndividualImport();
    }
    </script>
    
</body>
</html>
