<?php
/**
 * Test Monster Energy Import Specifically
 * 
 * This file tests the import of the Monster Energy product that was failing
 */

// Ensure WordPress environment
if ( ! defined( 'ABSPATH' ) ) {
    // Try to load WordPress
    $wp_load_paths = array(
        __DIR__ . '/../../../wp-load.php',
        __DIR__ . '/../../../../wp-load.php',
        __DIR__ . '/../../../../../wp-load.php'
    );
    
    $wp_loaded = false;
    foreach ( $wp_load_paths as $path ) {
        if ( file_exists( $path ) ) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }
    
    if ( ! $wp_loaded ) {
        die( 'WordPress environment not found. Please run this from within WordPress.' );
    }
}

// Ensure WooCommerce is active
if ( ! class_exists( 'WooCommerce' ) ) {
    die( 'WooCommerce is not active. Please activate WooCommerce before running this test.' );
}

echo "<h1>Monster Energy Import Test</h1>\n";
echo "<p>Testing the specific Monster Energy product that was failing validation...</p>\n";

// Monster Energy Square ID from the logs
$monster_energy_square_id = 'KK3Q37655RZJKODXUBCNX7I2';

try {
    echo "<h2>Step 1: Initialize Import System</h2>\n";
    
    // Load required classes
    if ( ! class_exists( 'SquareKit_Product_Importer' ) ) {
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/importers/class-squarekit-product-importer.php';
    }
    
    $importer = new SquareKit_Product_Importer();
    
    echo "<p>✅ Import system initialized</p>\n";
    
    echo "<h2>Step 2: Import Monster Energy Product</h2>\n";
    echo "<p>Square ID: <code>{$monster_energy_square_id}</code></p>\n";
    
    // Configure import settings
    $import_config = array(
        'name' => true,
        'description' => true,
        'images' => true,
        'categories' => true,
        'variations' => true,
        'modifiers' => true,
        'attributesDisabled' => false
    );
    
    // Import using SWEVER-style method
    $result = $importer->import_product_swever_style(
        $monster_energy_square_id,
        $import_config,
        false // Not update-only
    );
    
    if ( is_wp_error( $result ) ) {
        echo "<p style='color: red;'>❌ Import failed: " . $result->get_error_message() . "</p>\n";
        die();
    }
    
    echo "<p>✅ Import completed successfully</p>\n";
    
    $product_id = $result['product_id'] ?? null;
    if ( ! $product_id ) {
        echo "<p style='color: red;'>❌ No product ID returned</p>\n";
        die();
    }
    
    echo "<p><strong>Product ID:</strong> {$product_id}</p>\n";
    
    echo "<h2>Step 3: Validate Imported Product</h2>\n";
    
    $product = wc_get_product( $product_id );
    if ( ! $product ) {
        echo "<p style='color: red;'>❌ Product not found</p>\n";
        die();
    }
    
    echo "<p><strong>Product Name:</strong> " . $product->get_name() . "</p>\n";
    echo "<p><strong>Product Type:</strong> " . $product->get_type() . "</p>\n";
    echo "<p><strong>Product Status:</strong> " . $product->get_status() . "</p>\n";
    
    // Check attributes
    $attributes = $product->get_attributes();
    echo "<h3>Product Attributes:</h3>\n";
    if ( empty( $attributes ) ) {
        echo "<p style='color: red;'>❌ No attributes found</p>\n";
    } else {
        echo "<ul>\n";
        foreach ( $attributes as $attribute ) {
            $name = $attribute->get_name();
            $options = $attribute->get_options();
            echo "<li><strong>{$name}:</strong> " . implode( ', ', $options ) . "</li>\n";
        }
        echo "</ul>\n";
        echo "<p style='color: green;'>✅ Product has attributes</p>\n";
    }
    
    // Check variations
    if ( $product->is_type( 'variable' ) ) {
        $variations = $product->get_children();
        echo "<h3>Product Variations:</h3>\n";
        echo "<p><strong>Variation Count:</strong> " . count( $variations ) . "</p>\n";
        
        $all_variations_have_attributes = true;
        foreach ( $variations as $variation_id ) {
            $variation = wc_get_product( $variation_id );
            if ( $variation ) {
                $variation_attributes = $variation->get_attributes();
                $variation_name = $variation->get_name();
                $variation_price = $variation->get_price();
                
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>\n";
                echo "<p><strong>Variation:</strong> {$variation_name}</p>\n";
                echo "<p><strong>Price:</strong> $" . number_format( $variation_price, 2 ) . "</p>\n";
                echo "<p><strong>Attributes:</strong></p>\n";
                
                if ( empty( $variation_attributes ) ) {
                    echo "<p style='color: red;'>❌ No attributes</p>\n";
                    $all_variations_have_attributes = false;
                } else {
                    echo "<ul>\n";
                    foreach ( $variation_attributes as $attr_name => $attr_value ) {
                        echo "<li>{$attr_name} = {$attr_value}</li>\n";
                    }
                    echo "</ul>\n";
                    echo "<p style='color: green;'>✅ Has attributes</p>\n";
                }
                echo "</div>\n";
            }
        }
        
        if ( $all_variations_have_attributes ) {
            echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 SUCCESS: All variations have attributes!</p>\n";
        } else {
            echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ FAILURE: Some variations have no attributes</p>\n";
        }
    } else {
        echo "<p>Product is not variable (simple product)</p>\n";
    }
    
    echo "<h2>Step 4: Check Import Logs</h2>\n";
    echo "<p><a href='sk-logs.php' target='_blank'>View Import Logs</a></p>\n";
    
} catch ( Exception $e ) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
