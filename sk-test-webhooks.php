<?php
/**
 * SquareKit Webhook Test
 * 
 * Test and monitor Square webhook integration
 * Access via: https://teapot.local/wp-content/plugins/squarekit/sk-test-webhooks.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once(dirname(__FILE__) . '/../../../wp-load.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>SquareKit - Webhook Test</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; 
            margin: 20px; 
            background: #f1f1f1; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .header { 
            border-bottom: 2px solid #0073aa; 
            padding-bottom: 15px; 
            margin-bottom: 20px; 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .btn { 
            background: #0073aa; 
            color: white; 
            padding: 8px 16px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover { background: #005a87; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .webhook-url { 
            background: #f8f9fa; 
            padding: 10px; 
            border: 1px solid #dee2e6; 
            border-radius: 4px; 
            font-family: monospace; 
            word-break: break-all; 
        }
        .stats-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 15px; 
            margin: 20px 0; 
        }
        .stat-card { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            border: 1px solid #dee2e6; 
            text-align: center; 
        }
        .stat-number { 
            font-size: 24px; 
            font-weight: bold; 
            color: #0073aa; 
        }
        .stat-label { 
            font-size: 12px; 
            color: #666; 
            text-transform: uppercase; 
        }
        .webhook-log { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
            font-size: 12px; 
        }
        .log-timestamp { 
            color: #666; 
            font-weight: bold; 
        }
        .log-event { 
            color: #0073aa; 
            font-weight: bold; 
        }
        pre { 
            background: #f4f4f4; 
            padding: 10px; 
            border-radius: 4px; 
            overflow-x: auto; 
            font-size: 11px; 
        }
        .copy-btn { 
            background: #6c757d; 
            color: white; 
            border: none; 
            padding: 5px 10px; 
            border-radius: 3px; 
            cursor: pointer; 
            font-size: 12px; 
        }
        .copy-btn:hover { background: #5a6268; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 SquareKit Webhook Test</h1>
            <p>Test and monitor Square webhook integration for real-time synchronization</p>
        </div>

        <?php
        // Initialize webhook handler
        try {
            $webhook_handler = new SquareKit_Webhook_Handler();
            echo '<div class="test-section success"><strong>✅ Webhook Handler Initialized Successfully</strong></div>';
        } catch (Exception $e) {
            echo '<div class="test-section error"><strong>❌ Failed to Initialize Webhook Handler:</strong> ' . esc_html($e->getMessage()) . '</div>';
            exit;
        }

        // Handle actions
        if (isset($_POST['action'])) {
            $action = sanitize_text_field($_POST['action']);
            
            switch ($action) {
                case 'test_webhook':
                    echo '<div class="test-section info"><h3>🧪 Testing Webhook Endpoint...</h3>';
                    test_webhook_endpoint($webhook_handler);
                    echo '</div>';
                    break;
                    
                case 'simulate_webhook':
                    $event_type = sanitize_text_field($_POST['event_type']);
                    echo '<div class="test-section info"><h3>🎭 Simulating Webhook Event...</h3>';
                    simulate_webhook_event($webhook_handler, $event_type);
                    echo '</div>';
                    break;
            }
        }

        // Display webhook URL and configuration
        echo '<div class="test-section">';
        echo '<h3>🔗 Webhook Configuration</h3>';
        echo '<p><strong>Webhook URL:</strong></p>';
        echo '<div class="webhook-url">';
        echo esc_html($webhook_handler->get_webhook_url());
        echo ' <button class="copy-btn" onclick="copyToClipboard(\'' . esc_js($webhook_handler->get_webhook_url()) . '\')">Copy</button>';
        echo '</div>';
        echo '<p><em>Configure this URL in your Square Developer Dashboard under Webhooks.</em></p>';
        
        // Check if webhook URL is accessible
        $webhook_url = $webhook_handler->get_webhook_url();
        $response = wp_remote_get($webhook_url);
        if (is_wp_error($response)) {
            echo '<div class="warning"><strong>⚠️ Warning:</strong> Webhook URL may not be accessible. Error: ' . esc_html($response->get_error_message()) . '</div>';
        } else {
            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code === 405) {
                echo '<div class="success"><strong>✅ Webhook endpoint is accessible</strong> (returns 405 for GET requests, which is expected)</div>';
            } else {
                echo '<div class="warning"><strong>⚠️ Unexpected response:</strong> HTTP ' . esc_html($status_code) . '</div>';
            }
        }
        echo '</div>';

        // Display webhook statistics
        echo '<div class="test-section">';
        echo '<h3>📊 Webhook Statistics (Last 30 Days)</h3>';
        
        $webhook_stats = $webhook_handler->get_webhook_stats();
        
        if (empty($webhook_stats)) {
            echo '<p class="info">No webhook events recorded in the last 30 days.</p>';
        } else {
            echo '<div class="stats-grid">';
            foreach ($webhook_stats as $event_type => $count) {
                $clean_event = ucwords(str_replace(['webhook_', '_'], ['', ' '], $event_type));
                echo '<div class="stat-card">';
                echo '<div class="stat-number">' . esc_html($count) . '</div>';
                echo '<div class="stat-label">' . esc_html($clean_event) . '</div>';
                echo '</div>';
            }
            echo '</div>';
        }
        echo '</div>';

        // Display recent webhook logs
        echo '<div class="test-section">';
        echo '<h3>📋 Recent Webhook Events</h3>';
        
        $recent_webhooks = get_recent_webhook_logs();
        
        if (empty($recent_webhooks)) {
            echo '<p class="info">No recent webhook events found.</p>';
        } else {
            foreach ($recent_webhooks as $log) {
                display_webhook_log($log);
            }
        }
        echo '</div>';

        // Test actions
        echo '<div class="test-section">';
        echo '<h3>🧪 Test Actions</h3>';
        
        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="test_webhook">';
        echo '<button type="submit" class="btn btn-success">Test Webhook Endpoint</button>';
        echo '</form>';
        
        echo '<h4>Simulate Webhook Events:</h4>';
        $webhook_events = array(
            'payment.created' => 'Payment Created',
            'payment.updated' => 'Payment Updated',
            'refund.created' => 'Refund Created',
            'order.created' => 'Order Created',
            'inventory.count.updated' => 'Inventory Updated'
        );
        
        foreach ($webhook_events as $event_type => $event_label) {
            echo '<form method="post" style="display: inline; margin: 5px;">';
            echo '<input type="hidden" name="action" value="simulate_webhook">';
            echo '<input type="hidden" name="event_type" value="' . esc_attr($event_type) . '">';
            echo '<button type="submit" class="btn">' . esc_html($event_label) . '</button>';
            echo '</form>';
        }
        echo '</div>';

        // System information
        echo '<div class="test-section">';
        echo '<h3>ℹ️ System Information</h3>';
        echo '<pre>';
        echo 'Webhook Handler Class: ' . (class_exists('SquareKit_Webhook_Handler') ? 'Available' : 'Missing') . "\n";
        echo 'Webhook URL: ' . $webhook_handler->get_webhook_url() . "\n";
        echo 'WordPress Version: ' . get_bloginfo('version') . "\n";
        echo 'SquareKit Version: ' . (defined('SQUAREKIT_VERSION') ? SQUAREKIT_VERSION : 'Unknown') . "\n";
        echo 'PHP Version: ' . PHP_VERSION . "\n";
        echo 'Server Time: ' . current_time('mysql') . "\n";
        echo 'Timezone: ' . wp_timezone_string() . "\n";
        echo '</pre>';
        echo '</div>';

        /**
         * Test webhook endpoint accessibility
         */
        function test_webhook_endpoint($webhook_handler) {
            $webhook_url = $webhook_handler->get_webhook_url();
            
            // Test GET request (should return 405)
            echo '<p>Testing GET request to webhook URL...</p>';
            $response = wp_remote_get($webhook_url);
            
            if (is_wp_error($response)) {
                echo '<p>❌ GET request failed: ' . esc_html($response->get_error_message()) . '</p>';
            } else {
                $status_code = wp_remote_retrieve_response_code($response);
                if ($status_code === 405) {
                    echo '<p>✅ GET request correctly returns 405 (Method Not Allowed)</p>';
                } else {
                    echo '<p>⚠️ GET request returned unexpected status: ' . esc_html($status_code) . '</p>';
                }
            }
            
            // Test POST request with invalid data (should return 400)
            echo '<p>Testing POST request with invalid data...</p>';
            $response = wp_remote_post($webhook_url, array(
                'body' => 'invalid json',
                'headers' => array(
                    'Content-Type' => 'application/json'
                )
            ));
            
            if (is_wp_error($response)) {
                echo '<p>❌ POST request failed: ' . esc_html($response->get_error_message()) . '</p>';
            } else {
                $status_code = wp_remote_retrieve_response_code($response);
                if ($status_code === 400) {
                    echo '<p>✅ POST request with invalid data correctly returns 400</p>';
                } else {
                    echo '<p>⚠️ POST request returned unexpected status: ' . esc_html($status_code) . '</p>';
                }
            }
        }

        /**
         * Simulate a webhook event
         */
        function simulate_webhook_event($webhook_handler, $event_type) {
            $sample_data = get_sample_webhook_data($event_type);
            
            echo '<p>Simulating webhook event: <strong>' . esc_html($event_type) . '</strong></p>';
            echo '<p>Sample data:</p>';
            echo '<pre>' . esc_html(wp_json_encode($sample_data, JSON_PRETTY_PRINT)) . '</pre>';
            
            // Note: In a real implementation, you would need to properly simulate the webhook
            // For now, just show what would be sent
            echo '<p>✅ Sample webhook data generated. In production, this would be sent to the webhook endpoint.</p>';
        }

        /**
         * Get sample webhook data for testing
         */
        function get_sample_webhook_data($event_type) {
            $base_data = array(
                'merchant_id' => 'MERCHANT_ID',
                'location_id' => 'LOCATION_ID',
                'event_id' => 'test_' . wp_generate_uuid4(),
                'created_at' => current_time('c'),
                'type' => $event_type
            );

            switch ($event_type) {
                case 'payment.created':
                case 'payment.updated':
                    $base_data['data'] = array(
                        'id' => 'PAYMENT_ID_' . wp_generate_uuid4(),
                        'reference_id' => '1001',
                        'status' => 'COMPLETED',
                        'amount_money' => array(
                            'amount' => 9999,
                            'currency' => 'USD'
                        )
                    );
                    break;

                case 'refund.created':
                    $base_data['data'] = array(
                        'id' => 'REFUND_ID_' . wp_generate_uuid4(),
                        'payment_id' => 'PAYMENT_ID_123',
                        'status' => 'COMPLETED',
                        'amount_money' => array(
                            'amount' => 5000,
                            'currency' => 'USD'
                        )
                    );
                    break;

                case 'order.created':
                    $base_data['data'] = array(
                        'id' => 'ORDER_ID_' . wp_generate_uuid4(),
                        'reference_id' => '1002',
                        'state' => 'OPEN'
                    );
                    break;

                case 'inventory.count.updated':
                    $base_data['data'] = array(
                        'catalog_object_id' => 'CATALOG_OBJECT_ID_123',
                        'location_id' => 'LOCATION_ID',
                        'quantity' => '25'
                    );
                    break;

                default:
                    $base_data['data'] = array('test' => true);
            }

            return $base_data;
        }

        /**
         * Get recent webhook logs
         */
        function get_recent_webhook_logs() {
            if (!class_exists('SquareKit_Logger')) {
                return array();
            }

            $logger = new SquareKit_Logger();
            return $logger->get_logs(array(
                'limit' => 10,
                'event_type' => 'webhook_',
                'order_by' => 'created_at',
                'order' => 'DESC'
            ));
        }

        /**
         * Display a webhook log entry
         */
        function display_webhook_log($log) {
            echo '<div class="webhook-log">';
            echo '<div>';
            echo '<span class="log-timestamp">' . esc_html($log->created_at) . '</span> ';
            echo '<span class="log-event">' . esc_html($log->event_type) . '</span>';
            echo '</div>';
            echo '<div>' . esc_html($log->message) . '</div>';
            if ($log->context && $log->context !== '[]') {
                echo '<details><summary>Context Data</summary>';
                echo '<pre>' . esc_html(wp_json_encode(json_decode($log->context), JSON_PRETTY_PRINT)) . '</pre>';
                echo '</details>';
            }
            echo '</div>';
        }
        ?>

    </div>

    <script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            alert('Webhook URL copied to clipboard!');
        }, function(err) {
            console.error('Could not copy text: ', err);
        });
    }
    </script>
</body>
</html>
