# Hierarchical Category Import System Documentation

## Overview

The Square Kit plugin's hierarchical category import system ensures that products imported from Square maintain their complete category hierarchy in WooCommerce. This fixes the issue where only leaf categories were imported, missing parent categories in the hierarchy.

## Problem Statement

**Before the fix:**
- Square product assigned to "<PERSON> Tais" (child of "Drinks")
- WooCommerce import only created "Mai Tais" category
- Missing "Drinks" parent category
- Product only appeared in "Mai Tais" listings

**After the fix:**
- Square product assigned to "Mai Tais" (child of "Drinks")
- WooCommerce import creates both "Drinks" and "Mai Tais" categories
- Proper parent-child relationship: Drinks → Mai Tais
- Product appears in both category listings
- Breadcrumbs show: Drinks > Mai Tais

## Architecture

### Components Modified

1. **`class-squarekit-square-import.php`** - Enhanced category mapping
2. **`class-squarekit-create-product.php`** - Hierarchical category processing
3. **`class-squarekit-category-sync.php`** - Public hierarchy methods
4. **`class-squarekit-woocommerce.php`** - Integration method
5. **`class-squarekit-square-api.php`** - Search catalog objects method

### Data Flow

```
Square Product → Get Direct Category → Fetch Parent Hierarchy → Create WC Categories → Assign All to Product
     ↓                    ↓                      ↓                       ↓                    ↓
"Mai Tais Item"    "Mai Tais" ID        ["Drinks", "Mai Tais"]    Create both categories   Assign both IDs
```

## Implementation Details

### 1. Enhanced Category Mapping (`class-squarekit-square-import.php`)

**Location:** `includes/importers/class-squarekit-square-import.php` lines 302-321

**Changes:**
- Modified `map_square_product_to_woocommerce()` method
- Added `get_category_hierarchy()` method
- Fetches complete parent chain for each assigned category

**Code Structure:**
```php
// Map categories with full hierarchy
if ( isset( $related_objects['categories'] ) ) {
    $all_category_hierarchies = array();

    foreach ( $related_objects['categories'] as $category ) {
        // Get complete hierarchy for this category (including parents)
        $hierarchy = $this->get_category_hierarchy( $category['id'] );

        // Merge hierarchies, avoiding duplicates
        foreach ( $hierarchy as $hierarchy_category ) {
            $category_id = $hierarchy_category['square_id'];
            if ( ! isset( $all_category_hierarchies[$category_id] ) ) {
                $all_category_hierarchies[$category_id] = $hierarchy_category;
            }
        }
    }

    // Convert to indexed array for product creation
    $wc_product_data['categories'] = array_values( $all_category_hierarchies );
}
```

### 2. Hierarchy Fetching Method

**Method:** `get_category_hierarchy( $category_id )`

**Purpose:** Traces the complete parent chain for a given Square category

**Algorithm:**
1. Start with the assigned category ID
2. Fetch category data from Square API
3. Extract parent category ID if exists
4. Repeat until reaching root category
5. Reverse array to get parent-to-child order

**Data Structure Returned:**
```php
array(
    array(
        'square_id' => 'PARENT_ID',
        'name' => 'Drinks',
        'description' => 'All beverages',
        'parent_square_id' => null
    ),
    array(
        'square_id' => 'CHILD_ID',
        'name' => 'Mai Tais',
        'description' => 'Tropical cocktails',
        'parent_square_id' => 'PARENT_ID'
    )
)
```

### 3. Hierarchical Category Processing (`class-squarekit-create-product.php`)

**Location:** `includes/importers/class-squarekit-create-product.php` lines 424-473

**Changes:**
- Modified `import_product_categories()` method
- Integrated with `SquareKit_Category_Sync` class
- Processes categories in hierarchy order (parents first)

**Process:**
1. Load category sync module
2. Iterate through categories in hierarchy order
3. Use `import_single_category_with_parent()` for each category
4. Assign all created categories to the product

### 4. Category Sync Integration

**Method:** `import_single_category_with_parent()`

**Visibility:** Changed from `protected` to `public`

**Purpose:** Creates WooCommerce categories with proper parent relationships

**Features:**
- Checks for existing categories
- Creates parent categories first
- Maintains Square ID mapping
- Handles recursive parent creation

### 5. WooCommerce Integration Method

**Location:** `includes/integrations/class-squarekit-woocommerce.php` lines 340-393

**Method:** `import_category_hierarchy_and_assign()`

**Purpose:** Bridge between product importer and category sync module

**Fallback:** Includes simple category creation if sync module unavailable

## Square API Structure

### Category Data Format

```json
{
  "id": "CATEGORY_ID",
  "type": "CATEGORY",
  "category_data": {
    "name": "Category Name",
    "description": "Category Description",
    "category_type": "REGULAR_CATEGORY",
    "is_top_level": true|false,
    "online_visibility": true|false,
    "parent_category": {
      "id": "PARENT_CATEGORY_ID"
    }
  }
}
```

### Key Fields

- **`is_top_level`**: Boolean indicating if category has no parent
- **`parent_category.id`**: Square ID of parent category (only present for child categories)
- **`category_type`**: Usually "REGULAR_CATEGORY" for standard categories

## WooCommerce Integration

### Category Taxonomy

- **Taxonomy Name:** `product_cat`
- **Hierarchical:** Yes (supports parent-child relationships)
- **Meta Field:** `_square_category_id` stores Square category ID

### Category Creation

```php
$term_result = wp_insert_term( $category_name, 'product_cat', array(
    'description' => $category_description,
    'parent' => $parent_wc_id  // WooCommerce parent category ID
) );
```

### Product Assignment

```php
wp_set_object_terms( $product_id, $category_ids, 'product_cat', false );
```

**Note:** `$category_ids` contains ALL categories in the hierarchy