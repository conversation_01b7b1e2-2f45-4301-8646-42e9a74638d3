<?php
/**
 * Plugin Name: Square Kit
 * Plugin URI: https://chimastudios.com/
 * Description: Seamlessly synchronize WooCommerce and Square, providing real-time integration for products, inventory, orders, customers, and loyalty rewards with advanced security and logging.
 * Version: 1.1.0
 * Author: Chima Studios
 * Author URI: https://chimastudios.com/
 * Text Domain: squarekit
 * Domain Path: /languages
 * Requires at least: 5.6
 * Requires PHP: 7.2
 * WC requires at least: 4.0
 * WC tested up to: 8.0
 *
 * @package SquareKit
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'SQUAREKIT_VERSION', '1.1.0' );
define( 'SQUAREKIT_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'SQUAREKIT_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'SQUAREKIT_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

/**
 * Main SquareKit Class
 */
final class SquareKit {

    /**
     * Singleton instance
     *
     * @var SquareKit
     */
    private static $instance = null;

    /**
     * Get the singleton instance
     *
     * @return SquareKit
     */
    public static function instance() {
        if ( is_null( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->load_dependencies();
        $this->init_hooks();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * @since 1.0.0
     * @access private
     */
    private function load_dependencies() {
        // Core classes
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-activator.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-deactivator.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-loader.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-settings.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-db.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-logger.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-payment-logger.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-version-manager.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-secure-storage.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-bulk-operations.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-conflict-manager.php';

        // Exception classes
        require_once plugin_dir_path( __FILE__ ) . 'includes/exceptions/class-squarekit-payment-exception.php';

        // API classes (load first as dependencies)
        require_once plugin_dir_path( __FILE__ ) . 'includes/api/class-squarekit-api.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/api/class-squarekit-square-api.php';

        // Enhanced attribute mapping classes (load before importers)
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-squarekit-attribute-mapper.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/database/class-squarekit-attribute-mappings-table.php';

        // Utility classes (load before importers)
        require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-squarekit-price-calculator.php';

        // Import handler classes (load after dependencies)
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-attribute-importer.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-variation-importer.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-modifier-importer.php';

        // SWEVER-based import architecture (load after basic importers)
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-option-resolver.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-import-validator.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-create-product.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-square-import.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/importers/class-squarekit-product-importer.php';

        // Sync modules (refactored from monolithic WooCommerce integration)
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-product-sync.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-inventory-sync.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-image-handler.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-variation-handler.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-order-sync.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-customer-sync.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-category-sync.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-webhook-handler.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/sync/class-squarekit-sync-coordinator.php';

        // Integration classes
        require_once plugin_dir_path( __FILE__ ) . 'includes/integrations/class-squarekit-woocommerce.php';

        // Import bridge classes (SWEVER conflict resolution)
        require_once plugin_dir_path( __FILE__ ) . 'includes/integrations/class-squarekit-import-bridge.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/integrations/class-squarekit-import-manager.php';

        // Gateway class will be loaded when WooCommerce is ready

        // Admin classes
        require_once plugin_dir_path( __FILE__ ) . 'admin/class-squarekit-admin.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/admin/class-squarekit-changelog-page.php';
        require_once plugin_dir_path( __FILE__ ) . 'includes/admin/class-squarekit-webhook-settings.php';
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        register_activation_hook( __FILE__, array( $this, 'activate' ) );
        register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
        
        // Initialize the plugin
        add_action( 'plugins_loaded', array( $this, 'init' ), 0 );
        
        // Add plugin action links
        add_filter( 'plugin_action_links_' . SQUAREKIT_PLUGIN_BASENAME, array( $this, 'add_plugin_action_links' ) );
        
        // Handle plugin reset
        add_action( 'admin_post_squarekit_reset_plugin', array( $this, 'handle_plugin_reset' ) );
        
        // Add admin notices
        add_action( 'admin_notices', array( $this, 'admin_notices' ) );
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create custom database tables
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-activator.php';
        SquareKit_Activator::activate();

        // Initialize attribute mappings table
        if ( ! class_exists( 'SquareKit_Attribute_Mappings_Table' ) ) {
            require_once SQUAREKIT_PLUGIN_DIR . 'includes/database/class-squarekit-attribute-mappings-table.php';
        }
        $mappings_table = new SquareKit_Attribute_Mappings_Table();
        $mappings_table->init();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        require_once SQUAREKIT_PLUGIN_DIR . 'includes/class-squarekit-deactivator.php';
        SquareKit_Deactivator::deactivate();
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if ( ! class_exists( 'WooCommerce' ) ) {
            add_action( 'admin_notices', array( $this, 'woocommerce_missing_notice' ) );
            return;
        }

        // Load plugin text domain
        load_plugin_textdomain( 'squarekit', false, dirname( plugin_basename( __FILE__ ) ) . '/languages' );

        // Load WooCommerce-dependent classes
        require_once plugin_dir_path( __FILE__ ) . 'includes/integrations/class-squarekit-gateway.php';

        // Initialize version manager
        new SquareKit_Version_Manager();

        // Initialize webhook handler
        new SquareKit_Webhook_Handler();

        // Initialize admin pages (admin only)
        if ( is_admin() ) {
            new SquareKit_Changelog_Page();
            new SquareKit_Webhook_Settings();
        }

        // Initialize the loader
        $loader = new SquareKit_Loader();
        $loader->init();

        add_action( 'before_woocommerce_init', function() {
            if ( class_exists( \Automattic\WooCommerce\Utilities\FeaturesUtil::class ) ) {
                \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
            }
        } );
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="error">
            <p><?php esc_html_e( 'Square Kit requires WooCommerce to be installed and active.', 'squarekit' ); ?></p>
        </div>
        <?php
    }

    /**
     * Add plugin action links
     *
     * @param array $links Plugin action links
     * @return array Modified plugin action links
     */
    public function add_plugin_action_links( $links ) {
        // Add settings link
        $settings_link = '<a href="' . admin_url( 'admin.php?page=squarekit-settings' ) . '">' . __( 'Settings', 'squarekit' ) . '</a>';
        array_unshift( $links, $settings_link );
        
        // Add reset link
        $reset_url = wp_nonce_url( admin_url( 'admin-post.php?action=squarekit_reset_plugin' ), 'squarekit_reset_plugin', 'squarekit_nonce' );
        $reset_link = '<a href="' . esc_url( $reset_url ) . '" style="color: #dc3232;" onclick="return confirm(\'' . esc_js( __( 'Are you sure you want to reset Square Kit? This will delete all plugin data and settings. This action cannot be undone.', 'squarekit' ) ) . '\');">' . __( 'Reset DB & Deactivate', 'squarekit' ) . '</a>';
        $links[] = $reset_link;
        
        return $links;
    }

    /**
     * Handle plugin reset
     */
    public function handle_plugin_reset() {
        // Verify nonce
        if ( ! isset( $_GET['squarekit_nonce'] ) || ! wp_verify_nonce( $_GET['squarekit_nonce'], 'squarekit_reset_plugin' ) ) {
            wp_die( __( 'Security check failed.', 'squarekit' ) );
        }
        
        // Check user capabilities
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( __( 'You do not have sufficient permissions to perform this action.', 'squarekit' ) );
        }
        
        // Reset plugin data
        $this->reset_plugin_data();
        
        // Deactivate plugin
        deactivate_plugins( SQUAREKIT_PLUGIN_BASENAME );
        
        // Redirect to plugins page with success message
        wp_redirect( admin_url( 'plugins.php?deactivated=true&squarekit_reset=true' ) );
        exit;
    }

    /**
     * Reset plugin data
     */
    private function reset_plugin_data() {
        global $wpdb;
        
        // Drop custom tables
        $tables = array(
            $wpdb->prefix . 'squarekit_sync_logs',
            $wpdb->prefix . 'squarekit_inventory',
            $wpdb->prefix . 'squarekit_customers',
            $wpdb->prefix . 'squarekit_locations',
            $wpdb->prefix . 'squarekit_bulk_operations',
            $wpdb->prefix . 'squarekit_fetched_products',
        );
        
        foreach ( $tables as $table ) {
            $wpdb->query( "DROP TABLE IF EXISTS {$table}" );
        }
        
        // Delete plugin options
        $options = array(
            'squarekit_settings',
            'squarekit_oauth_data',
            'squarekit_webhook_data',
            'squarekit_sync_settings',
            'squarekit_payment_settings',
            'squarekit_customer_mapping',
            'squarekit_order_mapping',
            'squarekit_inventory_settings',
            'squarekit_product_mapping',
            'squarekit_fulfillment_mapping',
            'squarekit_sku_mapping',
            'squarekit_option_sets_mapping',
        );
        
        foreach ( $options as $option ) {
            delete_option( $option );
        }
        
        // Clear any transients
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_squarekit_%'" );
        $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_squarekit_%'" );
        
        // Log the reset action
        error_log( 'Square Kit plugin reset performed by user: ' . get_current_user_id() );
    }

    /**
     * Display admin notices
     */
    public function admin_notices() {
        // Check if plugin was reset
        if ( isset( $_GET['squarekit_reset'] ) && $_GET['squarekit_reset'] === 'true' ) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><?php esc_html_e( 'Square Kit has been reset and deactivated. All plugin data has been removed.', 'squarekit' ); ?></p>
            </div>
            <?php
        }
    }
}

/**
 * Returns the main instance of SquareKit
 *
 * @return SquareKit
 */
function SquareKit() {
    return SquareKit::instance();
}

// Initialize the plugin
SquareKit(); 