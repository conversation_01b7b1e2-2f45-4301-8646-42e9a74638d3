<!DOCTYPE html>
<!-- saved from url=(0067)https://new.teapot.name.ng/wp-content/plugins/squarekit/sk-logs.php -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Square Kit Debug Logs</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .filters {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 500;
            color: #555;
        }

        select, input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .logs-container {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .log-entry {
            border-bottom: 1px solid #eee;
            padding: 15px 20px;
            transition: background-color 0.2s;
        }

        .log-entry:hover {
            background: #f8f9fa;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .log-level {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .level-debug {
            background: #e8f4fd;
            color: #1976d2;
        }

        .level-info {
            background: #e3f2fd;
            color: #0277bd;
        }

        .level-warning {
            background: #fff3e0;
            color: #f57c00;
        }

        .level-error {
            background: #ffebee;
            color: #d32f2f;
        }

        .level-critical {
            background: #fce4ec;
            color: #c2185b;
        }

        .log-category {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            color: #666;
        }

        .log-timestamp {
            color: #888;
            font-size: 12px;
        }

        .log-message {
            font-weight: 500;
            margin-bottom: 8px;
        }

        .log-context {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        .log-meta {
            display: flex;
            gap: 15px;
            font-size: 11px;
            color: #888;
            margin-top: 8px;
        }

        .no-logs {
            text-align: center;
            padding: 40px;
            color: #888;
        }

        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .log-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .log-meta {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body cz-shortcut-listen="true">
    <div class="container">
        <div class="header">
            <h1>🔍 Square Kit Debug Logs</h1>
            <p>Real-time debugging and monitoring for Square Kit plugin operations</p>
        </div>

        
        
        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">260</div>
                <div class="stat-label">Total Log Entries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0.07 MB</div>
                <div class="stat-label">Log File Size</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">Categories</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1</div>
                <div class="stat-label">Errors</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters">
            <form method="get" action="https://new.teapot.name.ng/wp-content/plugins/squarekit/sk-logs.php">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="level">Log Level:</label>
                        <select name="level" id="level">
                            <option value="">All Levels</option>
                                                            <option value="info">
                                    Info                                </option>
                                                            <option value="success">
                                    Success                                </option>
                                                            <option value="error">
                                    Error                                </option>
                                                    </select>
                    </div>

                    <div class="filter-group">
                        <label for="category">Category:</label>
                        <select name="category" id="category">
                            <option value="">All Categories</option>
                                                            <option value="bulk_operation">
                                    Bulk_operation                                </option>
                                                            <option value="import">
                                    Import                                </option>
                                                            <option value="validation">
                                    Validation                                </option>
                                                            <option value="image_handler">
                                    Image_handler                                </option>
                                                            <option value="api">
                                    Api                                </option>
                                                            <option value="category_sync">
                                    Category_sync                                </option>
                                                            <option value="option_resolver">
                                    Option_resolver                                </option>
                                                            <option value="ajax">
                                    Ajax                                </option>
                                                    </select>
                    </div>

                    <div class="filter-group">
                        <label for="limit">Limit:</label>
                        <select name="limit" id="limit">
                            <option value="50">50</option>
                            <option value="100" selected="selected">100</option>
                            <option value="250">250</option>
                            <option value="500">500</option>
                        </select>
                    </div>

                    <button type="submit" class="btn btn-primary">Filter</button>
                    
                                        <a href="https://new.teapot.name.ng/wp-content/plugins/squarekit/sk-logs.php?action=clear" class="btn btn-danger" onclick="return confirm(&#39;Are you sure you want to clear all logs?&#39;)">
                        Clear Logs
                    </a>
                                    </div>
            </form>
        </div>

        <!-- Logs -->
        <div class="logs-container">
                                                <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">bulk_operation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Completed bulk operation: import_products (ID: 3, Status: completed, Processed: 7/7)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.75 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Product validation passed: 0 successes, 0 warnings                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.94 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Successfully created product: 2977 for Square item: KK3Q37655RZJKODXUBCNX7I2                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.94 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation completed for product 2977                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.97 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_in_stock]: Product is in stock                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.97 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_purchasable]: Product is purchasable                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.97 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [meta_correct]: Meta _square_item_id is correct                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.97 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [simple_pricing_valid]: Simple product has valid price: $14.99                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.97 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [square_id_match]: Square ID correctly stored                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.97 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_published]: Product is published                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.93 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [name_match]: Product name matches expected value                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.93 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Starting validation for product 2977 (Square ID: KK3Q37655RZJKODXUBCNX7I2)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validating imported product: 2977                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">image_handler</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Image already exists: 2966                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">image_handler</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Importing image from URL: https://items-images-production.s3.us-west-2.amazonaws.com/files/43682f599df954e6603ce614cd758118b7d0453f/original.png                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.91 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Assigned hierarchical categories to product 2977: 102                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Creating/updating WooCommerce product                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.86 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            API request successful: GET /catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2",
    "method": "GET",
    "status_code": 200,
    "response_size": 271
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Starting API request: GET /catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2",
    "method": "GET",
    "data_size": 0
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Mapping Square product to WooCommerce format                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Transforming Square product data (resolving option IDs)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validating Square product data for: KK3Q37655RZJKODXUBCNX7I2                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Successfully fetched Square data for: KK3Q37655RZJKODXUBCNX7I2                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Item options/values missing from related objects, fetching separately for item: KK3Q37655RZJKODXUBCNX7I2                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            API request successful: GET /catalog/object/KK3Q37655RZJKODXUBCNX7I2?include_related_objects=true                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/KK3Q37655RZJKODXUBCNX7I2?include_related_objects=true",
    "method": "GET",
    "status_code": 200,
    "response_size": 6437
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Starting API request: GET /catalog/object/KK3Q37655RZJKODXUBCNX7I2?include_related_objects=true                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/KK3Q37655RZJKODXUBCNX7I2?include_related_objects=true",
    "method": "GET",
    "data_size": 0
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.82 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Fetching Square product data for: KK3Q37655RZJKODXUBCNX7I2                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.82 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Starting import for Square item: KK3Q37655RZJKODXUBCNX7I2                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.82 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Product validation passed: 0 successes, 0 warnings                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.89 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Successfully created product: 2976 for Square item: EXYIY4RKJG7BQGTXE2YXQ35L                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.89 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation completed for product 2976                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_in_stock]: Product is in stock                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_purchasable]: Product is purchasable                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [meta_correct]: Meta _square_item_id is correct                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [simple_pricing_valid]: Simple product has valid price: $90                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [square_id_match]: Square ID correctly stored                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.92 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_published]: Product is published                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [name_match]: Product name matches expected value                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Starting validation for product 2976 (Square ID: EXYIY4RKJG7BQGTXE2YXQ35L)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Validating imported product: 2976                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">image_handler</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Image already exists: 2964                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">image_handler</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Importing image from URL: https://items-images-production.s3.us-west-2.amazonaws.com/files/0c2832114297acdf40866e8872be6e931a2d2bad/original.jpeg                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:38</div>
                        </div>
                        
                        <div class="log-message">
                            Assigned hierarchical categories to product 2976: 102                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Creating/updating WooCommerce product                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.81 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            API request successful: GET /catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2",
    "method": "GET",
    "status_code": 200,
    "response_size": 271
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.83 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Starting API request: GET /catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/UC6MTOG6F2SOUICA7DCWGHZ2",
    "method": "GET",
    "data_size": 0
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.8 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Mapping Square product to WooCommerce format                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.8 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Transforming Square product data (resolving option IDs)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.8 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validating Square product data for: EXYIY4RKJG7BQGTXE2YXQ35L                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.8 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Successfully fetched Square data for: EXYIY4RKJG7BQGTXE2YXQ35L                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.8 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Item options/values missing from related objects, fetching separately for item: EXYIY4RKJG7BQGTXE2YXQ35L                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.8 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            API request successful: GET /catalog/object/EXYIY4RKJG7BQGTXE2YXQ35L?include_related_objects=true                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/EXYIY4RKJG7BQGTXE2YXQ35L?include_related_objects=true",
    "method": "GET",
    "status_code": 200,
    "response_size": 6777
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.83 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Starting API request: GET /catalog/object/EXYIY4RKJG7BQGTXE2YXQ35L?include_related_objects=true                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/EXYIY4RKJG7BQGTXE2YXQ35L?include_related_objects=true",
    "method": "GET",
    "data_size": 0
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.77 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Fetching Square product data for: EXYIY4RKJG7BQGTXE2YXQ35L                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.77 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Starting import for Square item: EXYIY4RKJG7BQGTXE2YXQ35L                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.77 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Product validation passed: 0 successes, 0 warnings                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Successfully created product: 2972 for Square item: TQSLHLAL3L3R32RYFMDUUN3V                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.85 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation completed for product 2972                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_in_stock]: Product is in stock                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_purchasable]: Product is purchasable                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [meta_correct]: Meta _square_item_id is correct                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variable_pricing_valid]: Variable product has valid price range: $19.95 - $45.00                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.88 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_price]: Variation 2975 has valid price: $45                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.91 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_attributes]: Variation 2975 has attributes                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.91 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_square_id]: Variation 2975 has Square ID                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.9 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_price]: Variation 2974 has valid price: $35                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.9 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_attributes]: Variation 2974 has attributes                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.9 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_square_id]: Variation 2974 has Square ID                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.9 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_price]: Variation 2973 has valid price: $19.95                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.9 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_attributes]: Variation 2973 has attributes                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.9 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_has_square_id]: Variation 2973 has Square ID                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.9 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [variation_count_match]: Correct number of variations (3)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.86 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [attributes_exist]: Product has attributes                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-error">
                                    ERROR                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation error [attribute_values_missing]: Attribute 'Size' missing values: Small, Medium, Large                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [square_id_match]: Square ID correctly stored                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.87 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_published]: Product is published                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.83 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [name_match]: Product name matches expected value                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.83 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Starting validation for product 2972 (Square ID: TQSLHLAL3L3R32RYFMDUUN3V)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.82 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Validating imported product: 2972                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.82 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">image_handler</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Image already exists: 2962                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.82 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">image_handler</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Importing image from URL: https://items-images-production.s3.us-west-2.amazonaws.com/files/f3165a54d57ca6cc7837f1337f27a2de43a34bcd/original.jpeg                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.81 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:37</div>
                        </div>
                        
                        <div class="log-message">
                            Assigned hierarchical categories to product 2972: 101                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.81 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Creating/updating WooCommerce product                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.6 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            API request successful: GET /catalog/object/COJHSIZ63P2JAPXHM7Z34D4Q                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/COJHSIZ63P2JAPXHM7Z34D4Q",
    "method": "GET",
    "status_code": 200,
    "response_size": 314
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.62 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Starting API request: GET /catalog/object/COJHSIZ63P2JAPXHM7Z34D4Q                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/COJHSIZ63P2JAPXHM7Z34D4Q",
    "method": "GET",
    "data_size": 0
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.6 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Mapping Square product to WooCommerce format                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.59 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Transforming Square product data (resolving option IDs)                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.59 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Validating Square product data for: TQSLHLAL3L3R32RYFMDUUN3V                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.59 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Successfully fetched Square data for: TQSLHLAL3L3R32RYFMDUUN3V                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.59 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            API request successful: GET /catalog/object/XM4O2EDCOMCPSD3NIND4YLOX                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/XM4O2EDCOMCPSD3NIND4YLOX",
    "method": "GET",
    "status_code": 200,
    "response_size": 1245
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.61 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Starting API request: GET /catalog/object/XM4O2EDCOMCPSD3NIND4YLOX                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/XM4O2EDCOMCPSD3NIND4YLOX",
    "method": "GET",
    "data_size": 0
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.58 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Item options/values missing from related objects, fetching separately for item: TQSLHLAL3L3R32RYFMDUUN3V                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.58 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            API request successful: GET /catalog/object/TQSLHLAL3L3R32RYFMDUUN3V?include_related_objects=true                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/TQSLHLAL3L3R32RYFMDUUN3V?include_related_objects=true",
    "method": "GET",
    "status_code": 200,
    "response_size": 7788
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.62 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">api</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Starting API request: GET /catalog/object/TQSLHLAL3L3R32RYFMDUUN3V?include_related_objects=true                        </div>

                                                    <div class="log-context">
                                {
    "endpoint": "/catalog/object/TQSLHLAL3L3R32RYFMDUUN3V?include_related_objects=true",
    "method": "GET",
    "data_size": 0
}                            </div>
                        
                        <div class="log-meta">
                            <span>Memory: 18.54 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Fetching Square product data for: TQSLHLAL3L3R32RYFMDUUN3V                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.54 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Starting import for Square item: TQSLHLAL3L3R32RYFMDUUN3V                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.54 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Product validation passed: 0 successes, 0 warnings                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.58 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-success">
                                    SUCCESS                                </span>
                                <span class="log-category">import</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Successfully created product: 2971 for Square item: IPXAXRBGFJYSU2GT2SSCIFFI                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.58 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Validation completed for product 2971                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.6 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    <div class="log-entry">
                        <div class="log-header">
                            <div>
                                <span class="log-level level-info">
                                    INFO                                </span>
                                <span class="log-category">validation</span>
                            </div>
                            <div class="log-timestamp">2025-07-14 19:08:36</div>
                        </div>
                        
                        <div class="log-message">
                            Validation success [product_in_stock]: Product is in stock                        </div>

                        
                        <div class="log-meta">
                            <span>Memory: 18.6 MB</span>
                            <span>User: Guest</span>
                            <span>URI: /wp-cron.php?doing_wp_cron=1752520112.4386940002441406250000</span>
                        </div>
                    </div>
                                    </div>
    </div>

    <script>
        // Auto-refresh every 30 seconds if no filters are applied
                setTimeout(function() {
            window.location.reload();
        }, 30000);
            </script>




</body><div><template shadowrootmode="closed"><div><main><div class="sc-1uwbbvd-0 cTwtgP"></div></main><style data-styled="active" data-styled-version="6.1.12"></style></div></template></div></html>