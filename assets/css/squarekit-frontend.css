/* SquareKit Frontend Styles - Modern Clean Design */

/* Modifiers Wrapper - Clean Container */
.squarekit-modifiers-wrapper {
    margin: 24px 0 32px 0;
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0;
    width: 100%;
}

.squarekit-modifiers-wrapper h4 {
    margin: 0 0 24px 0;
    color: #1a202c;
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

/* Modifier Sets - Modern Card Design */
.squarekit-modifier-set-frontend {
    margin-bottom: 20px;
    padding: 0;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.squarekit-modifier-set-frontend:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.squarekit-modifier-set-frontend:last-child {
    margin-bottom: 0;
}

.squarekit-modifier-set-frontend h5 {
    margin: 0;
    color: #ffffff !important;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #083624 0%, #0f4c2c 100%);
    padding: 16px 20px;
    border: none;
    letter-spacing: 0.025em;
    text-shadow: none;
}

/* Modifier Options - Clean List Design */
.squarekit-modifier-option-label {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 16px 20px;
    background: #ffffff;
    border: none;
    border-bottom: 1px solid #f7fafc;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.15s ease;
    position: relative;
    font-size: 15px;
    line-height: 1.5;
}

.squarekit-modifier-option-label:last-child {
    border-bottom: none;
}

.squarekit-modifier-option-label:hover {
    background: #f7fafc;
}

.squarekit-modifier-option-label input[type="radio"],
.squarekit-modifier-option-label input[type="checkbox"] {
    margin: 0 12px 0 0;
    width: 18px;
    height: 18px;
    accent-color: #083624;
    cursor: pointer;
    flex-shrink: 0;
}

.squarekit-modifier-option-label:has(input:checked) {
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    border-left: 3px solid #083624;
    padding-left: 17px;
}

.option-name {
    flex: 1;
    font-weight: 500;
    color: #2d3748 !important;
    margin: 0;
}

.squarekit-modifier-option-label:has(input:checked) .option-name {
    color: #083624 !important;
    font-weight: 600;
}

.option-price {
    color: #083624 !important;
    font-weight: 600;
    font-size: 14px;
    background: #f0fdf4;
    padding: 6px 10px;
    border-radius: 6px;
    margin-left: 12px;
    flex-shrink: 0;
    min-width: 60px;
    text-align: center;
}

.squarekit-modifier-option-label:has(input:checked) .option-price {
    background: #083624 !important;
    color: #ffffff !important;
}

.squarekit-modifier-option-label:has(input:checked) .option-price * {
    color: #ffffff !important;
}

.squarekit-modifier-option-label:has(input:checked) .option-price .woocommerce-Price-amount,
.squarekit-modifier-option-label:has(input:checked) .option-price .amount,
.squarekit-modifier-option-label:has(input:checked) .option-price .woocommerce-Price-currencySymbol {
    color: #ffffff !important;
}

.stock-info {
    color: #718096;
    font-style: italic;
    font-size: 13px;
    margin-left: 8px;
}

.squarekit-modifier-option-label[disabled],
.squarekit-modifier-option-label:has(input:disabled) {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

/* Price Breakdown - Modern Clean Design */
.squarekit-price-breakdown {
    margin: 24px 0 0 0;
    padding: 20px;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.squarekit-price-breakdown h4 {
    margin: 0 0 16px 0;
    color: #1a202c;
    font-size: 18px;
    font-weight: 600;
    text-align: left;
    border: none;
    padding: 0;
}

.squarekit-price-breakdown-content {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Price Lines - Clean Layout */
.squarekit-base-price-line,
.squarekit-selected-options-line,
.squarekit-modifier-price-line,
.squarekit-price-total-line,
.squarekit-total-price-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f7fafc;
    font-size: 15px;
}

.squarekit-base-price-line {
    font-weight: 500;
    color: #2d3748;
    border-bottom: 1px solid #e2e8f0;
}

.squarekit-selected-options-line {
    font-weight: 500;
    color: #2d3748;
    background: #f8fffe;
    margin: 0 -20px;
    padding: 12px 20px;
    border-bottom: 1px solid #e2e8f0;
}

.squarekit-selected-summary {
    color: #083624 !important;
    font-weight: 600;
    font-style: italic;
    max-width: 60%;
    text-align: right;
    line-height: 1.4;
}

.squarekit-modifier-price-line {
    padding-left: 0;
    font-size: 14px;
    color: #4a5568;
    position: relative;
}

.squarekit-modifiers-price-list {
    margin: 8px 0;
}

.squarekit-price-total-line,
.squarekit-total-price-line {
    border-bottom: none;
    border-top: 2px solid #083624;
    margin-top: 16px;
    padding-top: 16px;
    font-weight: 700;
    font-size: 18px;
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    margin-left: -20px;
    margin-right: -20px;
    margin-bottom: -20px;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 20px;
    border-radius: 0 0 12px 12px;
    color: #083624;
}

.squarekit-price-label {
    color: #2d3748 !important;
    font-weight: 500;
}

.squarekit-price-value {
    font-weight: 600;
    color: #083624 !important;
    font-size: 15px;
}

.squarekit-base-price-display {
    color: #2d3748 !important;
    font-weight: 600;
}

.squarekit-total-price-display {
    font-size: 20px !important;
    color: #083624 !important;
    font-weight: 700;
}

/* Additional contrast fixes */
.squarekit-modifier-option-label * {
    color: inherit;
}

.squarekit-price-breakdown * {
    color: inherit;
}

/* Modifiers List */
.squarekit-modifiers-list {
    margin: 5px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .squarekit-modifiers-wrapper {
        padding: 15px;
        margin: 15px 0;
    }
    
    .squarekit-modifier-option {
        padding: 10px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .squarekit-modifier-option input {
        margin-bottom: 5px;
    }
    
    .squarekit-price-breakdown {
        padding: 15px;
    }
    
    .squarekit-price-total-line {
        font-size: 15px;
    }
}

/* Animation for price breakdown appearance */
.squarekit-price-breakdown {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus states for accessibility */
.squarekit-modifier-option input:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

.squarekit-modifier-option:focus-within {
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

/* Sold Out Modifier Styles */
.squarekit-modifier-sold-out {
    opacity: 0.6;
    position: relative;
}

.squarekit-modifier-sold-out input {
    cursor: not-allowed !important;
}

.squarekit-modifier-sold-out .option-name {
    text-decoration: line-through;
    color: #d32f2f !important;
}

.squarekit-modifier-sold-out::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    pointer-events: none;
    border-radius: 8px;
}

.squarekit-modifier-out-of-stock {
    opacity: 0.7;
}

.squarekit-modifier-out-of-stock input {
    cursor: not-allowed !important;
}

.squarekit-modifier-out-of-stock .option-name {
    color: #ff9800 !important;
    font-style: italic;
}
