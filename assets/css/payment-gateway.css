/**
 * SquareKit Payment Gateway Styles
 * Modern, responsive styling for Square payment forms
 */

/* Payment Form Container */
.squarekit-payment-form {
    margin: 20px 0;
    padding: 0;
}

.squarekit-payment-description {
    margin-bottom: 15px;
}

.squarekit-payment-description p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* Payment Error Styling */
.squarekit-payment-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px 15px;
    margin: 15px 0;
    color: #721c24;
}

.squarekit-payment-error p {
    margin: 0;
    font-size: 14px;
}

/* Saved Payment Methods */
.squarekit-saved-payment-methods {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.squarekit-saved-payment-methods h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.squarekit-saved-card,
.squarekit-new-card {
    margin-bottom: 10px;
}

.squarekit-saved-card:last-of-type {
    margin-bottom: 15px;
}

.squarekit-saved-card label,
.squarekit-new-card label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    font-size: 14px;
}

.squarekit-saved-card input[type="radio"],
.squarekit-new-card input[type="radio"] {
    margin-right: 10px;
}

.card-details {
    color: #555;
}

/* Digital Wallets */
.squarekit-digital-wallets {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.squarekit-wallet-button {
    min-height: 40px;
    border-radius: 6px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.squarekit-wallet-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.squarekit-wallet-button:active {
    transform: translateY(0);
}

/* Payment Divider */
.squarekit-payment-divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
    text-align: center;
}

.squarekit-payment-divider::before,
.squarekit-payment-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e0e0e0;
}

.squarekit-payment-divider span {
    padding: 0 15px;
    color: #666;
    font-size: 14px;
    background: #fff;
}

/* Card Container */
.squarekit-card-container {
    margin-bottom: 20px;
}

/* Square SDK Card Styling Overrides */
#squarekit-card-container .sq-input {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 12px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

#squarekit-card-container .sq-input:focus {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

#squarekit-card-container .sq-input.sq-input--error {
    border-color: #e74c3c;
}

#squarekit-card-container .sq-input-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

/* Error Display */
.squarekit-payment-errors {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px 15px;
    margin: 15px 0;
    color: #721c24;
}

.squarekit-payment-errors p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

/* Loading Indicator */
.squarekit-payment-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin: 15px 0;
}

.squarekit-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4a90e2;
    border-radius: 50%;
    animation: squarekit-spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes squarekit-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.squarekit-payment-loading span {
    color: #666;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .squarekit-digital-wallets {
        flex-direction: column;
    }
    
    .squarekit-wallet-button {
        width: 100%;
        min-height: 44px; /* Better touch target on mobile */
    }
    
    .squarekit-payment-divider {
        margin: 15px 0;
    }
    
    .squarekit-saved-payment-methods {
        padding: 12px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .squarekit-wallet-button {
        /* Ensure crisp rendering on retina displays */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Focus Management for Accessibility */
.squarekit-payment-form *:focus {
    outline: 2px solid #4a90e2;
    outline-offset: 2px;
}

.squarekit-wallet-button:focus {
    outline: 2px solid #4a90e2;
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .squarekit-saved-payment-methods {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .squarekit-saved-payment-methods h4 {
        color: #e2e8f0;
    }
    
    .card-details {
        color: #cbd5e0;
    }
    
    .squarekit-payment-divider::before,
    .squarekit-payment-divider::after {
        background: #4a5568;
    }
    
    .squarekit-payment-divider span {
        color: #a0aec0;
        background: #1a202c;
    }
    
    .squarekit-payment-loading {
        background: rgba(26, 32, 44, 0.95);
        border-color: #4a5568;
    }
    
    .squarekit-payment-loading span {
        color: #a0aec0;
    }
}

/* Print Styles */
@media print {
    .squarekit-payment-form,
    .squarekit-digital-wallets,
    .squarekit-payment-loading {
        display: none !important;
    }
}
