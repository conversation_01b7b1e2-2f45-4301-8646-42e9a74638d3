/**
 * SquareKit Payment Gateway Styles
 * Modern, beautiful checkout experience matching admin design standards
 *
 * @package SquareKit
 * @version 1.1.0
 */

/* CSS Variables for Consistent Design */
:root {
    --squarekit-primary: #667eea;
    --squarekit-primary-dark: #5a67d8;
    --squarekit-primary-light: #7c3aed;
    --squarekit-success: #10b981;
    --squarekit-error: #ef4444;
    --squarekit-warning: #f59e0b;
    --squarekit-gray-50: #f9fafb;
    --squarekit-gray-100: #f3f4f6;
    --squarekit-gray-200: #e5e7eb;
    --squarekit-gray-300: #d1d5db;
    --squarekit-gray-400: #9ca3af;
    --squarekit-gray-500: #6b7280;
    --squarekit-gray-600: #4b5563;
    --squarekit-gray-700: #374151;
    --squarekit-gray-800: #1f2937;
    --squarekit-gray-900: #111827;
    --squarekit-border-radius: 8px;
    --squarekit-border-radius-lg: 12px;
    --squarekit-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --squarekit-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --squarekit-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --squarekit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --squarekit-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Payment Form Container */
.squarekit-payment-form {
    margin: 24px 0;
    padding: 0;
    font-family: var(--squarekit-font-family);
    position: relative;
}

.squarekit-payment-description {
    margin-bottom: 20px;
    padding: 16px 20px;
    background: var(--squarekit-gray-50);
    border: 1px solid var(--squarekit-gray-200);
    border-radius: var(--squarekit-border-radius);
    border-left: 4px solid var(--squarekit-primary);
}

.squarekit-payment-description p {
    margin: 0;
    color: var(--squarekit-gray-700);
    font-size: 14px;
    line-height: 1.6;
    font-weight: 500;
}

/* Payment Error Styling */
.squarekit-payment-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: var(--squarekit-border-radius);
    padding: 16px 20px;
    margin: 20px 0;
    color: #991b1b;
    border-left: 4px solid var(--squarekit-error);
    box-shadow: var(--squarekit-shadow-sm);
    position: relative;
    animation: slideInFromTop 0.3s ease-out;
}

.squarekit-payment-error::before {
    content: '⚠️';
    position: absolute;
    left: 16px;
    top: 16px;
    font-size: 16px;
}

.squarekit-payment-error p {
    margin: 0 0 0 24px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
}

.squarekit-payment-error .error-details {
    margin-top: 8px;
    font-size: 13px;
    color: #7f1d1d;
    opacity: 0.8;
}

/* Saved Payment Methods */
.squarekit-saved-payment-methods {
    margin-bottom: 24px;
    padding: 0;
    background: #ffffff;
    border: 1px solid var(--squarekit-gray-200);
    border-radius: var(--squarekit-border-radius-lg);
    box-shadow: var(--squarekit-shadow-sm);
    overflow: hidden;
}

.squarekit-saved-payment-methods h4 {
    margin: 0;
    padding: 20px 24px 16px;
    color: var(--squarekit-gray-800);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid var(--squarekit-gray-100);
    background: var(--squarekit-gray-50);
}

.squarekit-saved-payment-methods .payment-methods-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.squarekit-saved-payment-methods .payment-method-item {
    padding: 20px 24px;
    border-bottom: 1px solid var(--squarekit-gray-100);
    transition: var(--squarekit-transition);
    cursor: pointer;
    position: relative;
}

.squarekit-saved-payment-methods .payment-method-item:last-child {
    border-bottom: none;
}

.squarekit-saved-payment-methods .payment-method-item:hover {
    background: var(--squarekit-gray-50);
}

.squarekit-saved-payment-methods .payment-method-item.selected {
    background: #f0f9ff;
    border-left: 4px solid var(--squarekit-primary);
}

.squarekit-saved-payment-methods .payment-method-radio {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.squarekit-saved-payment-methods .payment-method-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding-left: 32px;
    position: relative;
}

.squarekit-saved-payment-methods .payment-method-content::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--squarekit-gray-300);
    border-radius: 50%;
    background: #ffffff;
    transition: var(--squarekit-transition);
}

.squarekit-saved-payment-methods .payment-method-item.selected .payment-method-content::before {
    border-color: var(--squarekit-primary);
    background: var(--squarekit-primary);
    box-shadow: inset 0 0 0 4px #ffffff;
}

.squarekit-saved-payment-methods .card-icon {
    width: 32px;
    height: 20px;
    background: var(--squarekit-gray-200);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: var(--squarekit-gray-600);
    flex-shrink: 0;
}

.squarekit-saved-payment-methods .card-details {
    flex: 1;
}

.squarekit-saved-payment-methods .card-number {
    font-size: 14px;
    font-weight: 600;
    color: var(--squarekit-gray-800);
    margin-bottom: 4px;
}

.squarekit-saved-payment-methods .card-expiry {
    font-size: 13px;
    color: var(--squarekit-gray-500);
}

.squarekit-saved-payment-methods h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.squarekit-saved-card,
.squarekit-new-card {
    margin-bottom: 10px;
}

.squarekit-saved-card:last-of-type {
    margin-bottom: 15px;
}

.squarekit-saved-card label,
.squarekit-new-card label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 0;
    font-size: 14px;
}

.squarekit-saved-card input[type="radio"],
.squarekit-new-card input[type="radio"] {
    margin-right: 10px;
}

.card-details {
    color: #555;
}

/* Digital Wallets */
.squarekit-digital-wallets {
    margin-bottom: 32px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    padding: 0;
}

.squarekit-wallet-button {
    height: 48px;
    border-radius: var(--squarekit-border-radius);
    border: 1px solid var(--squarekit-gray-200);
    background: #ffffff;
    cursor: pointer;
    transition: var(--squarekit-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    box-shadow: var(--squarekit-shadow-sm);
    position: relative;
    overflow: hidden;
}

.squarekit-wallet-button:hover {
    transform: translateY(-1px);
    box-shadow: var(--squarekit-shadow);
    border-color: var(--squarekit-gray-300);
}

.squarekit-wallet-button:active {
    transform: translateY(0);
    box-shadow: var(--squarekit-shadow-sm);
}

/* Google Pay Button */
#squarekit-google-pay-button {
    background: #ffffff;
    color: #3c4043;
    border-color: #dadce0;
}

#squarekit-google-pay-button:hover {
    background: #f8f9fa;
    border-color: #dadce0;
}

#squarekit-google-pay-button::before {
    content: 'G';
    width: 20px;
    height: 20px;
    background: #4285f4;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 8px;
    font-size: 12px;
}

/* Apple Pay Button */
#squarekit-apple-pay-button {
    background: #000000;
    color: #ffffff;
    border-color: #000000;
}

#squarekit-apple-pay-button:hover {
    background: #1a1a1a;
    border-color: #1a1a1a;
}

#squarekit-apple-pay-button::before {
    content: '';
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 4px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

/* Afterpay Button */
#squarekit-afterpay-button {
    background: #b2fce4;
    color: #047857;
    border-color: #6ee7b7;
}

#squarekit-afterpay-button:hover {
    background: #a7f3d0;
    border-color: #34d399;
}

/* Payment Divider */
.squarekit-payment-divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
    text-align: center;
}

.squarekit-payment-divider::before,
.squarekit-payment-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e0e0e0;
}

.squarekit-payment-divider span {
    padding: 8px 24px;
    color: var(--squarekit-gray-500);
    font-size: 13px;
    font-weight: 500;
    background: #ffffff;
    border: 1px solid var(--squarekit-gray-200);
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Card Container */
.squarekit-card-container {
    margin-bottom: 24px;
    padding: 24px;
    background: #ffffff;
    border: 1px solid var(--squarekit-gray-200);
    border-radius: var(--squarekit-border-radius-lg);
    box-shadow: var(--squarekit-shadow-sm);
    position: relative;
}

.squarekit-card-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--squarekit-primary), var(--squarekit-primary-light));
    border-radius: var(--squarekit-border-radius-lg) var(--squarekit-border-radius-lg) 0 0;
}

/* Square SDK Card Styling Overrides */
#squarekit-card-container .sq-input {
    border: 2px solid var(--squarekit-gray-200);
    border-radius: var(--squarekit-border-radius);
    padding: 16px;
    font-size: 16px;
    font-family: var(--squarekit-font-family);
    transition: var(--squarekit-transition);
    background: #ffffff;
    width: 100%;
    box-sizing: border-box;
}

#squarekit-card-container .sq-input:focus {
    border-color: var(--squarekit-primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

#squarekit-card-container .sq-input.sq-input--error {
    border-color: var(--squarekit-error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

#squarekit-card-container .sq-input-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--squarekit-gray-700);
    margin-bottom: 8px;
    display: block;
    font-family: var(--squarekit-font-family);
}

/* Card field layout */
#squarekit-card-container .sq-card-iframe-container {
    margin-bottom: 20px;
}

#squarekit-card-container .sq-card-iframe-container:last-child {
    margin-bottom: 0;
}

/* Error Display */
.squarekit-payment-errors {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: var(--squarekit-border-radius);
    padding: 16px 20px;
    margin: 20px 0;
    color: #991b1b;
    border-left: 4px solid var(--squarekit-error);
    box-shadow: var(--squarekit-shadow-sm);
    position: relative;
    animation: slideInFromTop 0.3s ease-out;
}

.squarekit-payment-errors::before {
    content: '⚠️';
    position: absolute;
    left: 16px;
    top: 16px;
    font-size: 16px;
}

.squarekit-payment-errors p {
    margin: 0 0 0 24px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
}

/* Loading Indicator */
.squarekit-payment-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.squarekit-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--squarekit-gray-200);
    border-top: 4px solid var(--squarekit-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.squarekit-payment-loading span {
    color: var(--squarekit-gray-700);
    font-size: 16px;
    font-weight: 600;
    font-family: var(--squarekit-font-family);
}

/* Responsive Design */
@media (max-width: 768px) {
    .squarekit-digital-wallets {
        flex-direction: column;
    }
    
    .squarekit-wallet-button {
        width: 100%;
        min-height: 44px; /* Better touch target on mobile */
    }
    
    .squarekit-payment-divider {
        margin: 15px 0;
    }
    
    .squarekit-saved-payment-methods {
        padding: 12px;
    }
}

/* Success States */
.squarekit-payment-success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: var(--squarekit-border-radius);
    padding: 16px 20px;
    margin: 20px 0;
    color: #166534;
    border-left: 4px solid var(--squarekit-success);
    box-shadow: var(--squarekit-shadow-sm);
    position: relative;
    animation: slideInFromTop 0.3s ease-out;
}

.squarekit-payment-success::before {
    content: '✅';
    position: absolute;
    left: 16px;
    top: 16px;
    font-size: 16px;
}

.squarekit-payment-success p {
    margin: 0 0 0 24px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
}

/* Focus States */
.squarekit-payment-form *:focus {
    outline: 2px solid var(--squarekit-primary);
    outline-offset: 2px;
}

/* Accessibility Improvements */
.squarekit-payment-form [aria-hidden="true"] {
    display: none !important;
}

.squarekit-payment-form .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Enhanced Mobile Responsive */
@media (max-width: 480px) {
    .squarekit-payment-form {
        margin: 12px 0;
    }

    .squarekit-card-container {
        padding: 16px 12px;
    }

    .squarekit-saved-payment-methods h4 {
        padding: 12px 16px 8px;
        font-size: 14px;
    }

    .squarekit-saved-payment-methods .payment-method-item {
        padding: 12px 16px;
    }

    .squarekit-payment-divider span {
        padding: 4px 12px;
        font-size: 11px;
    }
}

/* Processing State */
body.squarekit-processing {
    overflow: hidden;
}

body.squarekit-processing .squarekit-payment-form {
    pointer-events: none;
    opacity: 0.7;
    transition: var(--squarekit-transition);
}

body.squarekit-processing .squarekit-wallet-button,
body.squarekit-processing .squarekit-card-container {
    filter: grayscale(0.5);
}

/* Print Styles */
@media print {
    .squarekit-payment-form,
    .squarekit-digital-wallets,
    .squarekit-payment-loading {
        display: none !important;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .squarekit-wallet-button {
        /* Ensure crisp rendering on retina displays */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Focus Management for Accessibility */
.squarekit-payment-form *:focus {
    outline: 2px solid #4a90e2;
    outline-offset: 2px;
}

.squarekit-wallet-button:focus {
    outline: 2px solid #4a90e2;
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .squarekit-saved-payment-methods {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .squarekit-saved-payment-methods h4 {
        color: #e2e8f0;
    }
    
    .card-details {
        color: #cbd5e0;
    }
    
    .squarekit-payment-divider::before,
    .squarekit-payment-divider::after {
        background: #4a5568;
    }
    
    .squarekit-payment-divider span {
        color: #a0aec0;
        background: #1a202c;
    }
    
    .squarekit-payment-loading {
        background: rgba(26, 32, 44, 0.95);
        border-color: #4a5568;
    }
    
    .squarekit-payment-loading span {
        color: #a0aec0;
    }
}

/* Print Styles */
@media print {
    .squarekit-payment-form,
    .squarekit-digital-wallets,
    .squarekit-payment-loading {
        display: none !important;
    }
}
