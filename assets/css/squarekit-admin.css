/* Square Kit Admin Styles */

/* Global Admin Styling */
.squarekit-admin-wrap {
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.squarekit-admin-header {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    padding: 2.5em 2em;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    margin-bottom: 2em;
    border: 1px solid rgba(226, 232, 240, 0.8);
    position: relative;
    overflow: hidden;
}

.squarekit-admin-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.squarekit-admin-header h1 {
    margin: 0 0 0.5em 0;
    color: #1e293b;
    font-size: 2.2rem;
    font-weight: 800;
    letter-spacing: -1px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.squarekit-admin-header p {
    margin: 0;
    color: #64748b;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Status Indicators */
.squarekit-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.8em 1.2em;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
}

.squarekit-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.squarekit-status.connected {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.squarekit-status.connected::before {
    background: #16a34a;
}

.squarekit-status.disconnected {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    border: 1px solid #fecaca;
}

.squarekit-status.disconnected::before {
    background: #dc2626;
}

.squarekit-status.syncing {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #d97706;
    border: 1px solid #fed7aa;
}

.squarekit-status.syncing::before {
    background: #d97706;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Unified Button System */
.squarekit-action-btn,
.squarekit-admin-btn,
.squarekit-admin-btn-primary,
.squarekit-btn-primary,
.squarekit-btn-secondary,
button[class*="squarekit"] {
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.75em 1.5em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
    font-family: inherit;
    line-height: 1.4;
}

.squarekit-action-btn:hover,
.squarekit-admin-btn:hover,
.squarekit-admin-btn-primary:hover,
.squarekit-btn-primary:hover,
button[class*="squarekit"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.35);
    color: #fff;
    text-decoration: none;
}

.squarekit-admin-btn,
.squarekit-btn-secondary {
    background: #fff;
    color: #374151;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.squarekit-admin-btn:hover,
.squarekit-btn-secondary:hover {
    background: #f8fafc;
    border-color: #667eea;
    color: #667eea;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.15);
}

.squarekit-action-btn.secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    box-shadow: 0 2px 8px rgba(100, 116, 139, 0.25);
}

.squarekit-action-btn.secondary:hover {
    box-shadow: 0 4px 16px rgba(100, 116, 139, 0.35);
}

.squarekit-action-btn.danger,
.squarekit-btn-remove {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.25);
}

.squarekit-action-btn.danger:hover,
.squarekit-btn-remove:hover {
    box-shadow: 0 4px 16px rgba(220, 38, 38, 0.35);
}

/* Loading States */
.squarekit-loading {
    opacity: 0.6;
    pointer-events: none;
}

.squarekit-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: squarekit-spin 1s linear infinite;
}

@keyframes squarekit-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modern Card Styling */
.squarekit-card {
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    border: 1px solid rgba(226, 232, 240, 0.8);
    padding: 2em;
    margin-bottom: 2em;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    position: relative;
    overflow: hidden;
}

.squarekit-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.12);
}

.squarekit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.squarekit-card h2,
.squarekit-card h3 {
    color: #1e293b;
    font-weight: 700;
    margin-bottom: 1em;
}

.squarekit-card h2 {
    font-size: 1.5rem;
    letter-spacing: -0.5px;
}

.squarekit-card h3 {
    font-size: 1.2rem;
}

/* Enhanced Table Styling */
.squarekit-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
}

.squarekit-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #374151;
    font-weight: 700;
    padding: 1.2em 1.5em;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.squarekit-table td {
    padding: 1.2em 1.5em;
    border-bottom: 1px solid #f1f5f9;
    color: #64748b;
    font-size: 0.95rem;
}

.squarekit-table tr:hover {
    background: rgba(102, 126, 234, 0.02);
}

.squarekit-table tr:last-child td {
    border-bottom: none;
}

/* Square Modifiers Admin Interface */

/* Main Panel Styling */
#squarekit_modifiers_panel {
    background: #ffffff !important;
    padding: 0 !important;
    border: none !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

#squarekit_modifiers_panel .options_group {
    margin: 0 !important;
    padding: 0 !important;
}

#squarekit_modifiers_panel .options_group > p:first-child {
    background: linear-gradient(135deg, #083624 0%, #0f4c2c 100%) !important;
    color: #ffffff !important;
    margin: 0 !important;
    padding: 20px 25px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    border-radius: 0 !important;
    border: none !important;
}

#squarekit_modifiers_panel .options_group > p:nth-child(2) {
    background: #f8fafc !important;
    color: #64748b !important;
    margin: 0 !important;
    padding: 15px 25px !important;
    font-size: 14px !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

/* Action Buttons */
.squarekit-modifier-controls {
    padding: 20px 25px !important;
    background: #ffffff !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

#squarekit_reimport_modifiers,
#squarekit_add_modifier_set {
    background: linear-gradient(135deg, #083624 0%, #0f4c2c 100%) !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 10px 16px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin-right: 10px !important;
    margin-bottom: 0 !important;
}

#squarekit_reimport_modifiers:hover,
#squarekit_add_modifier_set:hover {
    background: linear-gradient(135deg, #0a4a2e 0%, #083624 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(8, 54, 36, 0.3) !important;
}

#squarekit_reimport_modifiers {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
}

#squarekit_reimport_modifiers:hover {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%) !important;
}

/* Modifier Set Container */
#squarekit_modifier_sets_container {
    padding: 20px 25px !important;
    background: #f8fafc !important;
}

/* Individual Modifier Set */
.squarekit-modifier-set {
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
}

.squarekit-modifier-set:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* Modifier Set Header */
.squarekit-modifier-set-header {
    display: flex !important;
    gap: 20px !important;
    align-items: flex-end !important;
    margin-bottom: 20px !important;
    padding-bottom: 15px !important;
    border-bottom: 2px solid #f7fafc !important;
}

.squarekit-modifier-set-header > div {
    flex-shrink: 0 !important;
}

.squarekit-modifier-set-header label {
    display: block !important;
    font-weight: 600 !important;
    color: #2d3748 !important;
    margin-bottom: 5px !important;
    font-size: 13px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.squarekit-modifier-set-header input[type="text"] {
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    transition: all 0.2s ease !important;
    background: #ffffff !important;
}

.squarekit-modifier-set-header input[type="text"]:focus {
    border-color: #083624 !important;
    box-shadow: 0 0 0 3px rgba(8, 54, 36, 0.1) !important;
    outline: none !important;
}

.squarekit-modifier-set-header input[type="text"][readonly] {
    background: #f8f9fa !important;
    color: #6c757d !important;
}

.squarekit-modifier-set-header input[type="checkbox"] {
    width: 18px !important;
    height: 18px !important;
    accent-color: #083624 !important;
    margin-top: 5px !important;
}

/* Remove Set Button */
.remove-set-btn {
    background: #dc3545 !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    font-size: 13px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.remove-set-btn:hover {
    background: #c82333 !important;
    transform: translateY(-1px) !important;
}

/* Options Table */
.squarekit-options-table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin-top: 15px !important;
    background: #ffffff !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.squarekit-options-table thead {
    background: linear-gradient(135deg, #083624 0%, #0f4c2c 100%) !important;
}

.squarekit-options-table thead th {
    color: #ffffff !important;
    font-weight: 600 !important;
    padding: 12px 15px !important;
    text-align: left !important;
    font-size: 13px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border: none !important;
}

.squarekit-options-table tbody tr {
    border-bottom: 1px solid #f1f3f4 !important;
    transition: background-color 0.2s ease !important;
}

.squarekit-options-table tbody tr:hover {
    background: #f8fffe !important;
}

.squarekit-options-table tbody tr:last-child {
    border-bottom: none !important;
}

.squarekit-options-table tbody td {
    padding: 12px 15px !important;
    vertical-align: middle !important;
}

.squarekit-options-table input[type="text"],
.squarekit-options-table input[type="number"] {
    width: 100% !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 4px !important;
    padding: 6px 10px !important;
    font-size: 13px !important;
    transition: all 0.2s ease !important;
}

.squarekit-options-table input[type="text"]:focus,
.squarekit-options-table input[type="number"]:focus {
    border-color: #083624 !important;
    box-shadow: 0 0 0 2px rgba(8, 54, 36, 0.1) !important;
    outline: none !important;
}

.squarekit-options-table input[readonly] {
    background: #f8f9fa !important;
    color: #6c757d !important;
}

/* Remove Option Button */
.remove-option-btn {
    background: #dc3545 !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 4px !important;
    padding: 6px 10px !important;
    font-size: 12px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.remove-option-btn:hover:not(:disabled) {
    background: #c82333 !important;
}

.remove-option-btn:disabled {
    background: #e9ecef !important;
    color: #6c757d !important;
    cursor: not-allowed !important;
}

/* Add Option Button */
.add-option-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin-top: 15px !important;
}

.add-option-btn:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%) !important;
    transform: translateY(-1px) !important;
}

/* Frontend Modifiers Styles */
.squarekit-modifiers-wrapper {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.squarekit-modifiers-wrapper h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
}

.squarekit-modifier-set {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.squarekit-modifier-set h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

.squarekit-modifier-option {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.squarekit-modifier-option:hover {
    background-color: #f8f9fa;
}

.squarekit-modifier-option input[type="radio"],
.squarekit-modifier-option input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.1);
}

.squarekit-modifier-label {
    flex: 1;
    color: #495057;
    font-size: 14px;
}

.squarekit-modifier-option input:checked + .squarekit-modifier-label {
    font-weight: 600;
    color: #0073aa;
}

.squarekit-modifier-option input:checked ~ .squarekit-modifier-price {
    color: #0073aa;
}

/* Price display for modifiers */
.squarekit-modifier-price {
    color: #28a745;
    font-weight: 600;
    margin-left: 5px;
    white-space: nowrap;
}

.squarekit-modifier-price .woocommerce-Price-amount {
    color: inherit;
    font-weight: inherit;
}

/* OAuth Settings Styles */
.squarekit-environment-selector {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}

.squarekit-radio-option {
    flex: 1;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    background: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.squarekit-radio-option:hover {
    border-color: #667eea;
    background: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.squarekit-radio-option.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.squarekit-radio-option input[type="radio"] {
    margin: 0;
    margin-top: 2px;
    transform: scale(1.2);
    accent-color: #667eea;
}

.squarekit-radio-option .radio-content {
    flex: 1;
}

.squarekit-radio-option .radio-content strong {
    display: block;
    color: #1e293b;
    margin-bottom: 4px;
    font-size: 1rem;
    font-weight: 600;
}

.squarekit-radio-option .radio-content small {
    color: #64748b;
    font-size: 13px;
    line-height: 1.4;
}

.squarekit-credentials-section {
    margin: 30px 0;
    padding: 30px;
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    border: 1px solid rgba(226, 232, 240, 0.8);
    position: relative;
    overflow: hidden;
}

.squarekit-credentials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.squarekit-credentials-section h3 {
    margin-top: 0;
    margin-bottom: 25px;
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.squarekit-credentials-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.credential-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.credential-field input[type="text"],
.credential-field input[type="password"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #fff;
    color: #1e293b;
    max-width: none;
}

.credential-field input[type="text"]:focus,
.credential-field input[type="password"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: #fefefe;
}

.credential-field input[type="text"]:not(:placeholder-shown),
.credential-field input[type="password"]:not(:placeholder-shown) {
    border-color: #10b981;
    background: #f0fdf4;
}

/* Status Grid Styling */
.squarekit-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.squarekit-status-item {
    display: flex;
    justify-content: center;
}

/* Dashboard Specific Styles */
.squarekit-connection-banner {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 2em 2.5em;
    margin-bottom: 2em;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.squarekit-connection-banner.connected {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-color: #10b981;
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.15);
}

.squarekit-connection-banner.disconnected {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border-color: #ef4444;
    box-shadow: 0 4px 20px rgba(239, 68, 68, 0.15);
}

.squarekit-connection-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2em;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 1.25em;
    font-size: 1.1rem;
    flex: 1;
}

.status-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.status-icon.success {
    background: #10b981;
    color: #fff;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.status-icon.error {
    background: #ef4444;
    color: #fff;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.status-text {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.status-text strong {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.2;
}

.status-subtitle {
    font-size: 0.95rem;
    color: #64748b;
    font-weight: 500;
    line-height: 1.4;
}

/* Responsive Connection Banner */
@media (max-width: 768px) {
    .squarekit-connection-banner {
        padding: 1.5em;
    }

    .squarekit-connection-status {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5em;
    }

    .status-indicator {
        gap: 1em;
    }

    .status-icon {
        width: 40px;
        height: 40px;
    }

    .status-text strong {
        font-size: 1.1rem;
    }

    .status-subtitle {
        font-size: 0.9rem;
    }
}

/* Statistics Grid */
.squarekit-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5em;
    margin-bottom: 2em;
}

.squarekit-stat-card {
    background: #fff;
    border-radius: 16px;
    padding: 2em;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    position: relative;
    overflow: hidden;
}

.squarekit-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.squarekit-stat-card.primary::before {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.squarekit-stat-card.success::before {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.squarekit-stat-card.warning::before {
    background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.squarekit-stat-card.info::before {
    background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
}

.squarekit-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1em;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #667eea;
}

.squarekit-stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
}

.squarekit-stat-card.success .stat-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #fff;
}

.squarekit-stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: #fff;
}

.squarekit-stat-card.info .stat-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: #fff;
}

.stat-content h3 {
    margin: 0 0 0.5em 0;
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1;
    margin-bottom: 0.25em;
}

.stat-label {
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 1em;
}

.stat-progress {
    margin-top: 1em;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5em;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 600;
}

/* Dashboard Grid */
.squarekit-dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2em;
    margin-bottom: 2em;
}

/* Status List */
.squarekit-status-list {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75em 0;
    border-bottom: 1px solid #f1f5f9;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 600;
    color: #374151;
}

.status-value {
    color: #64748b;
    font-weight: 500;
}

/* Action Grid */
.squarekit-action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
}

.squarekit-action-btn {
    display: flex;
    align-items: center;
    gap: 1em;
    padding: 1.25em;
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    text-decoration: none;
    color: #374151;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    position: relative;
    overflow: hidden;
}

.squarekit-action-btn:hover {
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    color: #374151;
    text-decoration: none;
}

.squarekit-action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: #fff;
}

.squarekit-action-btn.primary:hover {
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.action-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    color: #667eea;
    flex-shrink: 0;
}

.squarekit-action-btn.primary .action-icon {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
}

/* Green gradient styling for Import All button */
.squarekit-action-btn.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #10b981;
    color: #fff;
}

.squarekit-action-btn.success:hover {
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
}

.squarekit-action-btn.success .action-icon {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
}

/* Main Actions Layout */
.squarekit-main-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 20px;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.main-actions-left {
    display: flex;
    gap: 16px;
}

.main-actions-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.products-summary {
    display: flex;
    gap: 8px;
    margin-right: 16px;
}

.count-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.count-badge.fetched {
    background: #dbeafe;
    color: #1e40af;
}

.count-badge.imported {
    background: #d1fae5;
    color: #065f46;
}

.count-badge.selected {
    background: #fef3c7;
    color: #92400e;
}

/* Progress Modal Styling */
.squarekit-progress-steps {
    margin-bottom: 20px;
}

.progress-step {
    display: flex;
    align-items: center;
    padding: 8px 0;
    color: #6b7280;
    transition: all 0.3s ease;
}

.progress-step.active {
    color: #1f2937;
    font-weight: 500;
}

.progress-step.complete {
    color: #059669;
}

.step-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.progress-step.active .step-icon {
    background: #3b82f6;
    color: #fff;
    animation: pulse 1.5s infinite;
}

.progress-step.complete .step-icon {
    background: #059669;
    color: #fff;
}

.step-text {
    flex: 1;
}

.squarekit-progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.squarekit-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.squarekit-progress-percentage {
    text-align: center;
    font-weight: 600;
    color: #374151;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.action-content {
    display: flex;
    flex-direction: column;
    gap: 0.25em;
}

.action-title {
    font-weight: 700;
    font-size: 1rem;
}

.action-subtitle {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* Card Header */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5em;
    padding-bottom: 1em;
    border-bottom: 2px solid #f1f5f9;
}

.card-header h3 {
    margin: 0;
}

/* Activity Log */
.squarekit-activity-log {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.activity-item {
    display: flex;
    gap: 1em;
    padding: 1em;
    background: #f8fafc;
    border-radius: 12px;
    border-left: 4px solid #e2e8f0;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #f1f5f9;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: #fff;
}

.activity-icon.error {
    background: #ef4444;
}

.activity-icon.success {
    background: #10b981;
}

.activity-icon.info,
.activity-icon.debug {
    background: #3b82f6;
}

.activity-content {
    flex: 1;
}

.activity-message {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5em;
    line-height: 1.4;
}

.activity-meta {
    display: flex;
    gap: 1em;
    font-size: 0.85rem;
    color: #64748b;
}

.activity-type {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Empty State */
.squarekit-empty-state {
    text-align: center;
    padding: 3em 2em;
    color: #64748b;
}

.empty-icon {
    margin-bottom: 1em;
    opacity: 0.5;
}

.squarekit-empty-state h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.2rem;
}

.squarekit-empty-state p {
    margin: 0;
    font-size: 0.95rem;
}

/* Sync Section */
.squarekit-sync-section {
    margin-bottom: 2em;
    padding-bottom: 2em;
}

.sync-header {
    margin-bottom: 1.5em;
}

.sync-header h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 700;
}

.sync-header p {
    margin: 0;
    color: #64748b;
    font-size: 0.9rem;
    line-height: 1.4;
}

.sync-actions {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.sync-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75em;
    padding: 1em 2em;
    font-size: 1rem;
    font-weight: 700;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    position: relative;
    overflow: hidden;
}

.sync-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.sync-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-icon svg {
    transition: transform 0.3s ease;
}

.sync-btn.loading .btn-icon svg {
    animation: spin 1s linear infinite;
}

.btn-loading svg {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.sync-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
}

.sync-option-btn {
    display: flex;
    align-items: center;
    gap: 1em;
    padding: 1.25em;
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    position: relative;
    overflow: hidden;
}

.sync-option-btn:hover {
    border-color: #667eea;
    background: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.sync-option-btn.selected {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.sync-option-btn.selected .option-check {
    opacity: 1;
    transform: scale(1);
}

.option-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    color: #64748b;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.sync-option-btn:hover .option-icon {
    background: #667eea;
    color: #fff;
}

.sync-option-btn.selected .option-icon {
    background: #10b981;
    color: #fff;
}

.option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25em;
}

.option-title {
    font-weight: 700;
    color: #374151;
    font-size: 1rem;
}

.option-subtitle {
    font-size: 0.85rem;
    color: #64748b;
}

.option-check {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #10b981;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
}

.sync-progress {
    margin-top: 1.5em;
    padding: 1.5em;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px solid #e2e8f0;
}

.sync-progress .progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1em;
}

.sync-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.progress-status {
    color: #374151;
    font-weight: 600;
}

.progress-percentage {
    color: #64748b;
    font-weight: 700;
}

.action-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
    margin: 2em 0;
}

/* Products Page Bulk Actions */
.squarekit-bulk-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1em;
    margin-bottom: 2em;
    padding: 1.5em;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
}

.bulk-actions-left,
.bulk-actions-right {
    display: flex;
    align-items: center;
    gap: 1em;
}

.bulk-actions-left {
    flex: 1;
}

.bulk-actions-right {
    flex-shrink: 0;
}

.squarekit-selected-count {
    color: #64748b;
    font-weight: 600;
    font-size: 0.9rem;
    margin-left: 0.5em;
}

/* Import All Button Styling */
#squarekit-import-all {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #fff;
    border: none;
    padding: 0.75em 1.5em;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5em;
}

#squarekit-import-all:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    color: #fff;
}

#squarekit-import-all:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

#squarekit-refresh-inventory {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    color: #374151;
    padding: 0.75em 1.5em;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5em;
}

#squarekit-refresh-inventory:hover {
    border-color: #667eea;
    background: #fff;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

/* Pagination Styling */
.squarekit-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1em;
    margin: 2em 0;
    padding: 1em;
}

.squarekit-pagination button {
    padding: 0.75em 1.5em;
    border: 2px solid #e2e8f0;
    background: #fff;
    color: #374151;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.squarekit-pagination button:hover:not(:disabled) {
    border-color: #667eea;
    background: #f8fafc;
    color: #667eea;
    transform: translateY(-1px);
}

.squarekit-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.pagination-info {
    padding: 0.75em 1.5em;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    color: #64748b;
    font-weight: 600;
}

/* Responsive design */
@media (max-width: 768px) {
    .squarekit-modifiers-wrapper {
        padding: 15px;
    }

    .squarekit-modifier-set {
        padding: 10px;
    }

    .squarekit-modifier-option {
        padding: 6px;
    }

    .squarekit-environment-selector {
        flex-direction: column;
    }

    .squarekit-credentials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .squarekit-credentials-section {
        padding: 20px;
    }

    .squarekit-status-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .squarekit-stats-grid {
        grid-template-columns: 1fr;
    }

    .squarekit-dashboard-grid {
        grid-template-columns: 1fr;
    }

    .squarekit-action-grid {
        grid-template-columns: 1fr;
    }

    .squarekit-connection-status {
        flex-direction: column;
        gap: 1em;
        align-items: flex-start;
    }

    .card-header {
        flex-direction: column;
        gap: 1em;
        align-items: flex-start;
    }

    .squarekit-bulk-actions {
        flex-direction: column;
        gap: 1.5em;
    }

    .bulk-actions-left,
    .bulk-actions-right {
        flex-direction: column;
        width: 100%;
        gap: 1em;
    }

    .bulk-actions-right {
        align-items: stretch;
    }

    .sync-actions {
        gap: 1.5em;
    }

    .sync-options {
        grid-template-columns: 1fr;
    }

    .sync-option-btn {
        padding: 1em;
    }

    .option-icon {
        width: 32px;
        height: 32px;
    }

    .option-title {
        font-size: 0.9rem;
    }

    .option-subtitle {
        font-size: 0.8rem;
    }
}

/* Settings Page Tabs */
.squarekit-tabs-nav {
    display: flex;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
    margin-bottom: 2em;
    overflow: hidden;
}

.squarekit-tab-link {
    display: flex;
    align-items: center;
    gap: 0.75em;
    padding: 1.25em 1.5em;
    text-decoration: none;
    color: #64748b;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    border-right: 1px solid #e2e8f0;
    position: relative;
    flex: 1;
    justify-content: center;
    min-width: 0;
}

.squarekit-tab-link:last-child {
    border-right: none;
}

.squarekit-tab-link:hover {
    background: #f8fafc;
    color: #374151;
    text-decoration: none;
}

.squarekit-tab-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    position: relative;
}

.squarekit-tab-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.tab-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.squarekit-tab-link span {
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Tab Content */
.squarekit-tab-content {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

/* Settings Form Styling */
.squarekit-settings-form {
    padding: 2.5em;
}

.squarekit-settings-section {
    margin-bottom: 3em;
    padding-left: 0;
    padding-right: 0;
}

.squarekit-settings-section:last-child {
    margin-bottom: 0;
}

.squarekit-settings-section h3 {
    margin: 0 0 1.5em 0;
    color: #374151;
    font-size: 1.3rem;
    font-weight: 700;
    padding-bottom: 0.75em;
    border-bottom: 2px solid #f1f5f9;
}

/* Ensure proper spacing for all mapping sections */
.squarekit-settings-section .squarekit-mapping-container,
.squarekit-settings-section .squarekit-payment-mapping-container {
    margin-left: 0;
    margin-right: 0;
}

.squarekit-form-group {
    margin-bottom: 2em;
}

.squarekit-form-group label {
    display: block;
    margin-bottom: 0.5em;
    color: #374151;
    font-weight: 600;
    font-size: 0.95rem;
}

.squarekit-form-group input[type="text"],
.squarekit-form-group input[type="number"],
.squarekit-form-group input[type="email"],
.squarekit-form-group select,
.squarekit-form-group textarea {
    width: 100%;
    max-width: 400px;
    padding: 0.75em;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fff;
}

.squarekit-form-group input:focus,
.squarekit-form-group select:focus,
.squarekit-form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.squarekit-form-group .description {
    margin-top: 0.5em;
    color: #64748b;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Radio and Checkbox Styling */
.squarekit-radio-group,
.squarekit-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.75em;
}

.squarekit-radio,
.squarekit-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75em;
    padding: 0.75em;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.squarekit-radio:hover,
.squarekit-checkbox:hover {
    border-color: #667eea;
    background: #f8fafc;
}

.squarekit-radio input[type="radio"],
.squarekit-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
}

.squarekit-radio-label,
.squarekit-checkbox-label {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

/* Status Indicators */
.squarekit-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
    padding: 0.5em 1em;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.squarekit-status-indicator.connected {
    background: #d1fae5;
    color: #065f46;
}

.squarekit-status-indicator.disconnected {
    background: #fee2e2;
    color: #991b1b;
}

.squarekit-status-indicator.enabled {
    background: #dbeafe;
    color: #1e40af;
}

.squarekit-status-indicator.disabled {
    background: #f3f4f6;
    color: #6b7280;
}

/* Connection Cards */
.squarekit-connection-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5em;
    margin-bottom: 2em;
}

.squarekit-connection-card {
    padding: 1.5em;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: #f8fafc;
    transition: all 0.3s ease;
}

.squarekit-connection-card.active {
    border-color: #10b981;
    background: #ecfdf5;
}

.squarekit-connection-card h4 {
    margin: 0 0 1em 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 700;
}

.squarekit-connection-info {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.squarekit-connection-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5em 0;
    border-bottom: 1px solid #e2e8f0;
}

.squarekit-connection-info-item:last-child {
    border-bottom: none;
}

.squarekit-connection-info-label {
    font-weight: 600;
    color: #374151;
}

.squarekit-connection-info-value {
    color: #64748b;
    font-family: monospace;
    font-size: 0.9rem;
}

/* Sync Direction Cards */
.squarekit-sync-direction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2em;
    margin-bottom: 2em;
}

.squarekit-sync-direction-card {
    padding: 2em;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    background: #fff;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    position: relative;
    overflow: hidden;
}

.squarekit-sync-direction-card.enabled {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

.squarekit-sync-direction-card.woo-to-square.enabled {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.sync-card-header {
    display: flex;
    align-items: center;
    gap: 1em;
    margin-bottom: 1.5em;
}

.sync-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    color: #64748b;
    transition: all 0.3s ease;
}

.squarekit-sync-direction-card.enabled .sync-card-icon {
    background: #10b981;
    color: #fff;
}

.squarekit-sync-direction-card.woo-to-square.enabled .sync-card-icon {
    background: #3b82f6;
    color: #fff;
}

.sync-card-header h4 {
    margin: 0;
    color: #374151;
    font-size: 1.25rem;
    font-weight: 700;
}

.sync-card-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5em;
}

.sync-card-toggle {
    margin-bottom: 1.5em;
}

.squarekit-toggle {
    display: flex;
    align-items: flex-start;
    gap: 1em;
    cursor: pointer;
    padding: 0.5em 0;
    margin-bottom: 0.5em;
}

.squarekit-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    width: 52px;
    height: 28px;
    background: #e2e8f0;
    border-radius: 14px;
    position: relative;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    flex-shrink: 0;
    margin-top: 2px;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: #fff;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.squarekit-toggle:hover .toggle-slider {
    background: #cbd5e1;
}

.squarekit-toggle input:checked + .toggle-slider {
    background: #10b981;
}

.squarekit-toggle input:checked + .toggle-slider::before {
    transform: translateX(24px);
}

.squarekit-toggle:hover input:checked + .toggle-slider {
    background: #059669;
}

.toggle-label {
    font-weight: 600;
    color: #374151;
    line-height: 1.5;
    flex: 1;
}

/* Form group styling for toggles */
.squarekit-form-group .squarekit-toggle {
    margin-bottom: 0;
}

.squarekit-form-group .squarekit-toggle + .description {
    margin-top: 0.75em;
    margin-left: 4.25em;
}

.sync-card-features {
    margin-bottom: 1.5em;
}

.sync-card-features h5 {
    margin: 0 0 0.75em 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
}

.sync-card-features ul {
    margin: 0;
    padding-left: 1.5em;
    list-style: none;
}

.sync-card-features li {
    position: relative;
    color: #64748b;
    margin-bottom: 0.5em;
    padding-left: 1.5em;
}

.sync-card-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.sync-card-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1em;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
    border: 1px solid rgba(0,0,0,0.05);
}

.status-label {
    font-weight: 600;
    color: #374151;
}

.status-indicator {
    padding: 0.5em 1em;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-indicator.enabled {
    background: #d1fae5;
    color: #065f46;
    
}

.status-indicator.disabled {
    background: #f3f4f6;
    color: #6b7280;
}

/* Sync Notes */
.squarekit-sync-notes {
    margin-top: 2em;
}

.sync-notes-card {
    padding: 1.5em;
    background: #fffbeb;
    border: 2px solid #fbbf24;
    border-radius: 12px;
}

.sync-notes-card h4 {
    margin: 0 0 1em 0;
    color: #92400e;
    font-size: 1.1rem;
    font-weight: 700;
}

.sync-notes-card ul {
    margin: 0;
    padding-left: 1.5em;
    color: #92400e;
}

.sync-notes-card li {
    margin-bottom: 0.5em;
    line-height: 1.5;
}

/* Form Actions */
.squarekit-form-actions {
    margin-top: 3em;
    padding-top: 2em;
    border-top: 2px solid #f1f5f9;
    display: flex;
    gap: 1em;
    justify-content: flex-start;
}

/* Coming Soon Placeholders */
.squarekit-coming-soon {
    text-align: center;
    padding: 4em 2em;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    border: 2px dashed #cbd5e1;
}

.coming-soon-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

.squarekit-coming-soon h4 {
    margin: 0 0 1em 0;
    color: #374151;
    font-size: 1.5rem;
    font-weight: 700;
}

.squarekit-coming-soon p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5em;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.squarekit-coming-soon ul {
    text-align: left;
    max-width: 400px;
    margin: 0 auto 1.5em;
    color: #64748b;
}

.squarekit-coming-soon li {
    margin-bottom: 0.5em;
    position: relative;
    padding-left: 1.5em;
}

.squarekit-coming-soon li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.squarekit-coming-soon em {
    color: #9ca3af;
    font-style: italic;
}

/* Enhanced Progress Modal Styles */
.squarekit-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.squarekit-modal-content {
    background: white;
    border-radius: 8px;
    padding: 0;
    max-width: 700px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.squarekit-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.squarekit-modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.squarekit-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.squarekit-modal-close:hover {
    color: #333;
}

.squarekit-modal-body {
    padding: 24px;
}

.squarekit-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* Enhanced Progress Styles */
.squarekit-progress-steps {
    margin-bottom: 20px;
}

.progress-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    padding: 12px;
    border-radius: 6px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.progress-step.active {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.progress-step.complete {
    background: #e8f5e8;
    border-left: 4px solid #4caf50;
}

.step-icon {
    margin-right: 12px;
    font-weight: bold;
    font-size: 16px;
    min-width: 20px;
    text-align: center;
}

.progress-step.active .step-icon {
    color: #2196f3;
    animation: spin 2s linear infinite;
}

.progress-step.complete .step-icon {
    color: #4caf50;
}

.step-text {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.squarekit-progress-bar {
    background: #e0e0e0;
    border-radius: 10px;
    height: 8px;
    margin-bottom: 12px;
    overflow: hidden;
}

.squarekit-progress-fill {
    background: linear-gradient(90deg, #4caf50, #66bb6a);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.squarekit-progress-percentage {
    text-align: center;
    font-weight: 600;
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
}

/* Import Details Section */
.squarekit-import-details {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.squarekit-import-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.squarekit-summary-item {
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.squarekit-summary-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    text-transform: uppercase;
    font-weight: 500;
}

.squarekit-summary-value {
    display: block;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.squarekit-summary-value.success {
    color: #4caf50;
}

.squarekit-summary-value.error {
    color: #f44336;
}

/* Import Log Styles */
.squarekit-import-log .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.squarekit-import-log h4 {
    margin: 0;
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.toggle-log-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-log-btn:hover {
    background: #e9ecef;
    border-color: #ccc;
}

.toggle-icon {
    font-size: 10px;
    transition: transform 0.2s ease;
}

.squarekit-log-content {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    transition: all 0.3s ease;
}

.squarekit-log-content.collapsed {
    max-height: 150px;
    overflow: hidden;
}

.squarekit-log-content.expanded {
    max-height: 400px;
    overflow-y: auto;
}

.log-entry {
    margin-bottom: 8px;
    padding: 6px 8px;
    border-radius: 4px;
    background: white;
    border-left: 3px solid #ddd;
    transition: all 0.2s ease;
}

.log-entry.info {
    border-left-color: #2196f3;
    background: #f3f8ff;
}

.log-entry.success {
    border-left-color: #4caf50;
    background: #f1f8e9;
}

.log-entry.error {
    border-left-color: #f44336;
    background: #fff3f3;
}

.log-time {
    color: #666;
    margin-right: 8px;
    font-weight: 500;
}

.log-summary {
    margin-top: 8px;
    text-align: center;
}

.hidden-entries-count {
    color: #999;
    font-style: italic;
    font-size: 11px;
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
    .squarekit-tabs-nav {
        flex-direction: column;
    }

    .squarekit-tab-link {
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
        justify-content: flex-start;
        padding: 1em 1.5em;
    }

    .squarekit-tab-link:last-child {
        border-bottom: none;
    }

    .squarekit-sync-direction-grid {
        grid-template-columns: 1fr;
    }

    .squarekit-connection-cards {
        grid-template-columns: 1fr;
    }

    .squarekit-form-actions {
        flex-direction: column;
    }

    .squarekit-coming-soon {
        padding: 2em 1em;
    }

    .coming-soon-icon {
        width: 60px;
        height: 60px;
    }

    .squarekit-modal-content {
        width: 95%;
        max-height: 90vh;
    }

    .squarekit-import-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mapping Interface */
.squarekit-mapping-container {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 2em;
}

.mapping-header {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) 100px;
    gap: 1em;
    padding: 1.5em 2em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    font-weight: 700;
    font-size: 0.9rem;
}

.mapping-header.customer-role {
    grid-template-columns: 1fr 1fr 1fr 1fr 80px 100px;
}

.mapping-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) 100px;
    gap: 1em;
    padding: 1.5em 2em;
    border-bottom: 1px solid #e2e8f0;
    background: #fff;
    align-items: center;
}

.mapping-row.customer-role {
    grid-template-columns: 1fr 1fr 1fr 1fr 80px 100px;
}

.mapping-row:last-child {
    border-bottom: none;
}

.mapping-row:hover {
    background: #f8fafc;
}

.mapping-col {
    display: flex;
    flex-direction: column;
}

.mapping-col input,
.mapping-col select {
    width: 100%;
    padding: 0.75em;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.mapping-col input:focus,
.mapping-col select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.mapping-actions {
    display: flex;
    justify-content: center;
    align-items: center;
}

.mapping-footer {
    padding: 1.5em 2em;
    background: #f1f5f9;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1em;
    justify-content: flex-start;
}

/* Responsive Mapping */
@media (max-width: 768px) {
    .mapping-header,
    .mapping-row {
        grid-template-columns: 1fr;
        gap: 0.5em;
    }

    .mapping-header.customer-role,
    .mapping-row.customer-role {
        grid-template-columns: 1fr;
    }

    .mapping-col {
        margin-bottom: 0.5em;
    }

    .mapping-col::before {
        content: attr(data-label);
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.25em;
        font-size: 0.85rem;
    }

    .mapping-footer {
        flex-direction: column;
    }
}

/* Advanced Settings Cards */
.squarekit-webhook-status-card,
.squarekit-cron-status-card {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2em;
    margin-top: 1.5em;
}

.webhook-info-grid,
.cron-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5em;
    margin-bottom: 2em;
}

.webhook-info-item,
.cron-status-item {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.info-label,
.status-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.info-value,
.status-value {
    font-family: monospace;
    background: #fff;
    padding: 0.5em;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    font-size: 0.85rem;
    color: #64748b;
}

.webhook-events {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5em;
}

.webhook-event-tag {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.25em 0.75em;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.webhook-actions,
.cron-actions {
    display: flex;
    gap: 1em;
    flex-wrap: wrap;
}

/* Performance Settings Grid */
.squarekit-performance-grid,
.squarekit-image-optimization-grid,
.squarekit-sku-validation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2em;
    margin-bottom: 2em;
}

.performance-card,
.optimization-card,
.sku-validation-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2em;
    transition: all 0.3s ease;
}

.performance-card:hover,
.optimization-card:hover,
.sku-validation-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.performance-card h4,
.optimization-card h4,
.sku-validation-card h4 {
    margin: 0 0 1.5em 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 700;
    padding-bottom: 0.75em;
    border-bottom: 2px solid #f1f5f9;
}

/* Responsive Advanced Settings */
@media (max-width: 768px) {
    .squarekit-performance-grid,
    .squarekit-image-optimization-grid,
    .squarekit-sku-validation-grid {
        grid-template-columns: 1fr;
    }

    .webhook-info-grid,
    .cron-status-grid {
        grid-template-columns: 1fr;
    }

    .webhook-actions,
    .cron-actions {
        flex-direction: column;
    }

    .performance-card,
    .optimization-card,
    .sku-validation-card {
        padding: 1.5em;
    }
}

/* Payment Settings */
.squarekit-payment-gateway-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 2em;
}

.gateway-header {
    display: flex;
    align-items: center;
    gap: 1.5em;
    padding: 2em;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

.gateway-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    flex-shrink: 0;
}

.gateway-info {
    flex: 1;
}

.gateway-info h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 700;
}

.gateway-info p {
    margin: 0;
    color: #64748b;
    line-height: 1.5;
}

.gateway-toggle {
    flex-shrink: 0;
}

.gateway-settings {
    padding: 2em;
}

.squarekit-payment-features-grid,
.squarekit-transaction-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2em;
    margin-bottom: 2em;
}

.payment-feature-card,
.transaction-setting-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2em;
    transition: all 0.3s ease;
}

.payment-feature-card:hover,
.transaction-setting-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.feature-header {
    display: flex;
    align-items: center;
    gap: 1em;
    margin-bottom: 1.5em;
}

.feature-icon {
    width: 40px;
    height: 40px;
    background: #f1f5f9;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    flex-shrink: 0;
}

.feature-header h4,
.transaction-setting-card h4 {
    margin: 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 700;
}

.feature-content {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

/* Digital Wallet Logos */
.digital-wallet-logos {
    display: flex;
    gap: 1em;
    align-items: center;
    margin-top: 1em;
    padding: 1em;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.wallet-logo {
    height: 32px;
    width: auto;
    border-radius: 6px;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.wallet-logo:hover {
    opacity: 1;
    transform: scale(1.05);
}

.squarekit-payment-mapping-container {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 2em;
}

.payment-mapping-header {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    gap: 1em;
    padding: 1.5em;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    font-weight: 700;
    font-size: 0.9rem;
}

.payment-mapping-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr 1fr;
    gap: 1em;
    padding: 1.5em;
    border-bottom: 1px solid #e2e8f0;
    background: #fff;
    align-items: center;
}

.payment-mapping-row:last-child {
    border-bottom: none;
}

.payment-mapping-row:hover {
    background: #f8fafc;
}

.payment-method-info {
    display: flex;
    flex-direction: column;
    gap: 0.25em;
}

.method-name {
    font-weight: 600;
    color: #374151;
}

.method-type {
    font-size: 0.85rem;
    color: #64748b;
}

.payment-mapping-empty {
    text-align: center;
    padding: 4em 2em;
    background: #fff;
}

.payment-mapping-empty .empty-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1em;
    color: #9ca3af;
}

.payment-mapping-empty h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 700;
}

.payment-mapping-empty p {
    margin: 0;
    color: #64748b;
}

.squarekit-security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2em;
}

.security-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2em;
}

.security-header {
    display: flex;
    align-items: center;
    gap: 1em;
    margin-bottom: 1.5em;
}

.security-icon {
    width: 40px;
    height: 40px;
    background: #10b981;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    flex-shrink: 0;
}

.security-header h4 {
    margin: 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 700;
}

.security-content p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1em;
}

.compliance-status {
    display: flex;
    align-items: center;
    gap: 0.5em;
}

/* Tools Page */
.squarekit-system-status-grid,
.squarekit-diagnostic-tools-grid,
.squarekit-maintenance-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2em;
    margin-bottom: 2em;
}

.system-status-card,
.diagnostic-tool-card,
.maintenance-tool-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2em;
    transition: all 0.3s ease;
}

.system-status-card:hover,
.diagnostic-tool-card:hover,
.maintenance-tool-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.system-status-card h4,
.diagnostic-tool-card h4,
.maintenance-tool-card h4 {
    margin: 0 0 1.5em 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 700;
    padding-bottom: 0.75em;
    border-bottom: 2px solid #f1f5f9;
}

.status-items {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75em;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.status-label {
    font-weight: 600;
    color: #374151;
    flex: 1;
}

.status-value {
    font-family: monospace;
    color: #64748b;
    margin-right: 1em;
}

.status-indicator {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

.status-indicator.enabled {
    background: #10b981;
    color: #fff;
    width: auto;
    border-radius: 9px;
}

.status-indicator.disabled {
    background: #ef4444;
    color: #fff;
    width: auto;
    border-radius: 9px;
}

.tool-header {
    display: flex;
    align-items: center;
    gap: 1em;
    margin-bottom: 1em;
}

.tool-icon {
    width: 40px;
    height: 40px;
    background: #f1f5f9;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    flex-shrink: 0;
}

.log-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5em;
}

.log-file-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5em;
    transition: all 0.3s ease;
}

.log-file-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.log-file-header {
    display: flex;
    align-items: center;
    gap: 1em;
    margin-bottom: 1.5em;
}

.log-file-icon {
    width: 32px;
    height: 32px;
    color: #64748b;
    flex-shrink: 0;
}

.log-file-info h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
}

.log-file-meta {
    display: flex;
    gap: 1em;
    font-size: 0.85rem;
    color: #64748b;
}

.log-file-actions {
    display: flex;
    gap: 0.75em;
    flex-wrap: wrap;
}

.log-files-empty {
    text-align: center;
    padding: 4em 2em;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px dashed #cbd5e1;
}

.log-files-empty .empty-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1em;
    color: #9ca3af;
}

.log-files-empty h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 700;
}

.log-files-empty p {
    margin: 0;
    color: #64748b;
}

.import-config-container {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

/* Responsive Tools */
@media (max-width: 768px) {
    .squarekit-system-status-grid,
    .squarekit-diagnostic-tools-grid,
    .squarekit-maintenance-tools-grid,
    .squarekit-payment-features-grid,
    .squarekit-transaction-settings-grid,
    .squarekit-security-grid {
        grid-template-columns: 1fr;
    }

    .payment-mapping-header,
    .payment-mapping-row {
        grid-template-columns: 1fr;
        gap: 0.5em;
    }

    .gateway-header {
        flex-direction: column;
        text-align: center;
    }

    .log-files-grid {
        grid-template-columns: 1fr;
    }

    .log-file-actions {
        flex-direction: column;
    }
}

/* Orders Page Styles */
.squarekit-orders {
    max-width: 100%;
}

.squarekit-orders-table-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-top: 20px;
}

.squarekit-orders-table-container table {
    margin: 0;
    border: none;
}

.squarekit-orders-table-container th,
.squarekit-orders-table-container td {
    padding: 12px 15px;
    border-bottom: 1px solid #e2e8f0;
}

.squarekit-orders-table-container th {
    background: #f8fafc;
    font-weight: 600;
    color: #475569;
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 0.5px;
}

.squarekit-orders-table-container tbody tr:hover {
    background: #f8fafc;
}

/* Order status styling */
.order-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-status.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.order-status.status-processing {
    background: #dbeafe;
    color: #1e40af;
}

.order-status.status-completed {
    background: #d1fae5;
    color: #065f46;
}

.order-status.status-cancelled {
    background: #fee2e2;
    color: #991b1b;
}

.order-status.status-refunded {
    background: #f3e8ff;
    color: #6b21a8;
}

/* Sync status styling */
.squarekit-sync-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.squarekit-sync-status.synced {
    background: #d1fae5;
    color: #065f46;
}

.squarekit-sync-status.not-synced {
    background: #fee2e2;
    color: #991b1b;
}

/* Pagination */
.squarekit-orders-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.pagination-info {
    color: #64748b;
    font-size: 14px;
}

.pagination-links {
    display: flex;
    gap: 5px;
}

.pagination-links .button {
    min-width: 40px;
    text-align: center;
}

/* Order details modal */
#squarekit-order-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.squarekit-modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
}

.squarekit-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #64748b;
    z-index: 1;
}

.squarekit-modal-close:hover {
    color: #1e293b;
}

.squarekit-order-details {
    padding: 30px;
}

.squarekit-order-details h3 {
    margin: 0 0 20px 0;
    color: #1e293b;
    font-size: 24px;
    font-weight: 700;
}

.squarekit-order-details h4 {
    margin: 25px 0 15px 0;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
}

.squarekit-order-details .order-header {
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.squarekit-order-details .order-header p {
    margin: 5px 0;
    color: #64748b;
}

.squarekit-order-details table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.squarekit-order-details table th,
.squarekit-order-details table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.squarekit-order-details table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.squarekit-order-details .order-totals table {
    max-width: 300px;
    margin-left: auto;
}

.squarekit-order-details .order-totals td:last-child {
    text-align: right;
    font-weight: 600;
}

/* Sync Automation Grid */
.squarekit-sync-automation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.sync-automation-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.sync-automation-card h4 {
    margin: 0 0 15px 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
}

.squarekit-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: #fff;
    font-size: 14px;
    color: #374151;
    transition: border-color 0.2s ease;
}

.squarekit-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.squarekit-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.squarekit-radio {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.squarekit-radio:hover {
    background: #f8fafc;
}

.squarekit-radio input[type="radio"] {
    margin: 0;
    width: 16px;
    height: 16px;
    accent-color: #667eea;
}

.squarekit-radio .radio-label {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.squarekit-form-group {
    margin-bottom: 20px;
}

.squarekit-form-group:last-child {
    margin-bottom: 0;
}

.squarekit-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.squarekit-form-group .description {
    margin-top: 6px;
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
}

/* Granular Sync Controls */
.squarekit-granular-sync-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-top: 20px;
}

.granular-sync-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.granular-sync-card.woo-to-square {
    border-left: 4px solid #f59e0b;
}

.granular-sync-card.square-to-woo {
    border-left: 4px solid #10b981;
}

.granular-sync-card .sync-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.granular-sync-card .sync-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

.granular-sync-card.woo-to-square .sync-card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.granular-sync-card.square-to-woo .sync-card-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.granular-sync-card h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.granular-sync-card .sync-card-description {
    color: #64748b;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.sync-master-toggle {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.sync-data-types {
    margin-top: 16px;
}

.sync-data-types h5 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-type-toggles {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.data-type-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: #fff;
}

.data-type-item:hover {
    background: #f8fafc;
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.data-type-item.recommended {
    border-color: #10b981;
    background: #f0fdf4;
}

.data-type-item.recommended:hover {
    background: #ecfdf5;
}

/* Beautiful Toggle Switch */
.squarekit-toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
    cursor: pointer;
    flex-shrink: 0;
}

.squarekit-toggle-switch input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.squarekit-toggle-switch .toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #cbd5e1;
    border-radius: 24px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.squarekit-toggle-switch .toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.squarekit-toggle-switch input:checked + .toggle-slider {
    background: #10b981;
}

.squarekit-toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(24px) translateY(-50%);
}

.squarekit-toggle-switch input:focus + .toggle-slider {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.data-type-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.data-type-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 15px;
    font-weight: 600;
    color: #374151;
    line-height: 1.4;
}

.recommended-badge {
    background: #10b981;
    color: white;
    font-size: 10px;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.data-type-description {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.5;
    margin: 0;
}

/* ===== ENHANCED CONFLICTS TAB STYLES ===== */

/* Conflicts Wrapper - Proper Padding */
.squarekit-conflicts-wrapper {
    padding: 2em;
    max-width: 1200px;
    margin: 0 auto;
}

/* Modern Notice Styles */
.squarekit-notice {
    display: flex;
    align-items: flex-start;
    gap: 1em;
    padding: 1.5em;
    margin: 1.5em 0;
    border-radius: 12px;
    border: 1px solid;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.squarekit-notice-success {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
}

.squarekit-notice-error {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fef2f2 100%);
}

.squarekit-notice-icon {
    color: inherit;
    flex-shrink: 0;
}

.squarekit-notice-success .squarekit-notice-icon {
    color: #10b981;
}

.squarekit-notice-error .squarekit-notice-icon {
    color: #ef4444;
}

.squarekit-notice-content h4 {
    margin: 0 0 0.5em 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.squarekit-notice-content p {
    margin: 0;
    color: #6b7280;
    font-size: 0.9rem;
}

/* Stats Grid */
.squarekit-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2em;
    margin-bottom: 3em;
}

/* Empty State */
.squarekit-empty-state {
    text-align: center;
    padding: 4em 2em;
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    margin: 2em 0;
}

.squarekit-empty-state-icon {
    color: #10b981;
    margin-bottom: 1.5em;
}

.squarekit-empty-state-content h3 {
    color: #374151;
    margin: 0 0 1em 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.squarekit-empty-state-content p {
    color: #6b7280;
    margin: 0 0 2em 0;
    font-size: 1rem;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.squarekit-empty-state-actions {
    display: flex;
    justify-content: center;
    gap: 1em;
}

/* Conflicts Container */
.squarekit-conflicts-container {
    margin-top: 2em;
}

.squarekit-conflicts-header {
    margin-bottom: 2em;
}

.squarekit-conflicts-header h3 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.5rem;
    font-weight: 700;
}

.squarekit-conflicts-description {
    color: #6b7280;
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
}

/* Conflicts List */
.squarekit-conflicts-list {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
}

/* Conflict Cards */
.squarekit-conflict-card {
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.squarekit-conflict-card:hover,
.squarekit-conflict-card.hover {
    border-color: #667eea;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.squarekit-conflict-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5em 2em;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
}

.squarekit-conflict-info {
    display: flex;
    align-items: center;
    gap: 1em;
}

.squarekit-conflict-type {
    flex-shrink: 0;
}

.squarekit-conflict-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5em 1em;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.squarekit-conflict-badge-price {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
}

.squarekit-conflict-badge-inventory {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
}

.squarekit-conflict-badge-name {
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    color: #3730a3;
}

.squarekit-conflict-title h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.1rem;
    font-weight: 700;
}

.squarekit-conflict-meta {
    display: flex;
    align-items: center;
    gap: 1em;
    font-size: 0.85rem;
    color: #6b7280;
}

.squarekit-conflict-id {
    font-weight: 600;
}

.squarekit-conflict-time {
    display: flex;
    align-items: center;
    gap: 0.25em;
}

.squarekit-conflict-details {
    padding: 2em;
}

/* Value Comparison */
.squarekit-value-comparison {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2em;
    align-items: stretch;
    margin-bottom: 2em;
}

.squarekit-value-item {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5em;
    transition: all 0.3s ease;
}

.squarekit-value-item:hover {
    border-color: #cbd5e1;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.squarekit-value-square {
    border-left: 4px solid #667eea;
}

.squarekit-value-woocommerce {
    border-left: 4px solid #10b981;
}

.squarekit-value-header {
    margin-bottom: 1em;
}

.squarekit-value-platform {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-bottom: 0.75em;
    color: #374151;
    font-weight: 600;
}

.squarekit-value-timestamp {
    display: flex;
    align-items: center;
    gap: 0.25em;
    font-size: 0.8rem;
    color: #6b7280;
}

.squarekit-value-content {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1em;
}

.squarekit-value-display {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    word-break: break-all;
}

.squarekit-vs-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
    color: #6b7280;
    font-weight: 700;
    font-size: 0.9rem;
}

.squarekit-vs-icon {
    background: #f1f5f9;
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    padding: 0.5em;
    color: #667eea;
}

/* Resolution Form */
.squarekit-resolution-form {
    margin-top: 2em;
    padding: 2em;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    border-top: 4px solid #667eea;
}

.squarekit-resolution-header {
    margin-bottom: 2em;
}

.squarekit-resolution-header h4 {
    margin: 0 0 0.5em 0;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 700;
}

.squarekit-resolution-header p {
    margin: 0;
    color: #6b7280;
    font-size: 0.95rem;
}

.squarekit-resolution-options {
    margin-bottom: 2em;
}

.squarekit-resolution-option {
    display: block;
    margin-bottom: 1em;
    cursor: pointer;
}

.squarekit-resolution-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.squarekit-option-card {
    display: flex;
    align-items: center;
    gap: 1em;
    padding: 1.5em;
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
    position: relative;
}

.squarekit-option-card::before {
    content: '';
    position: absolute;
    top: 1em;
    right: 1em;
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    background: #fff;
    transition: all 0.3s ease;
}

.squarekit-resolution-option input[type="radio"]:checked + .squarekit-option-card {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
}

.squarekit-resolution-option input[type="radio"]:checked + .squarekit-option-card::before {
    border-color: #667eea;
    background: #667eea;
    box-shadow: inset 0 0 0 4px #fff;
}

.squarekit-option-card:hover {
    border-color: #cbd5e1;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.squarekit-option-icon {
    flex-shrink: 0;
    color: #667eea;
    background: #f0f4ff;
    padding: 0.75em;
    border-radius: 10px;
}

.squarekit-option-content {
    flex: 1;
}

.squarekit-option-content strong {
    display: block;
    color: #374151;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25em;
}

.squarekit-option-description {
    color: #6b7280;
    font-size: 0.9rem;
    line-height: 1.4;
}

.squarekit-custom-value-input {
    margin-top: 1em;
    padding: 1.5em;
    background: #fff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
}

.squarekit-form-group {
    margin-bottom: 1.5em;
}

.squarekit-form-group:last-child {
    margin-bottom: 0;
}

.squarekit-form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5em;
    font-size: 0.95rem;
}

.squarekit-form-optional {
    font-weight: 400;
    color: #6b7280;
    font-size: 0.85rem;
}

.squarekit-form-input,
.squarekit-form-textarea {
    width: 100%;
    padding: 0.75em 1em;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.squarekit-form-input:focus,
.squarekit-form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.squarekit-form-textarea {
    resize: vertical;
    min-height: 100px;
}

.squarekit-resolution-notes {
    margin-bottom: 2em;
}

.squarekit-resolution-actions {
    display: flex;
    gap: 1em;
    align-items: center;
    justify-content: flex-start;
}

.resolve-conflict-btn.active {
    background: #ef4444;
    border-color: #ef4444;
}

.resolve-conflict-btn.active:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.value-item {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 16px;
}

.value-item.square {
    border-left: 4px solid #3b82f6;
}

.value-item.woocommerce {
    border-left: 4px solid #8b5cf6;
}

.value-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.value-header strong {
    color: #374151;
    font-size: 14px;
}

.timestamp {
    font-size: 12px;
    color: #6b7280;
}

.value-content {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.vs-divider {
    background: #ef4444;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 12px;
    text-align: center;
    white-space: nowrap;
}

.resolution-form {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.resolution-form h4 {
    margin: 0 0 16px 0;
    color: #374151;
    font-size: 15px;
    font-weight: 600;
}

.resolution-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.resolution-option {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.resolution-option:hover {
    background: #f8fafc;
    border-color: #3b82f6;
}

.resolution-option input[type="radio"] {
    margin: 0;
    margin-top: 2px;
}

.option-content {
    flex: 1;
}

.option-content strong {
    display: block;
    color: #374151;
    font-size: 14px;
    margin-bottom: 2px;
}

.option-description {
    font-size: 13px;
    color: #6b7280;
}

.custom-value-input {
    margin-top: 12px;
    padding: 12px;
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
}

.custom-value-input label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.custom-value-input input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
}

.resolution-notes {
    margin-bottom: 20px;
}

.resolution-notes label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.resolution-notes textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
}

.resolution-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.resolution-actions .button {
    padding: 8px 16px;
    font-size: 14px;
}

.resolve-conflict-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.resolve-conflict-btn:hover {
    background: #2563eb;
    color: white;
}

/* Responsive design for conflicts */
@media (max-width: 768px) {
    .squarekit-conflicts-wrapper {
        padding: 1em;
    }

    .squarekit-stats-grid {
        grid-template-columns: 1fr;
        gap: 1em;
    }

    .squarekit-value-comparison {
        grid-template-columns: 1fr;
        gap: 1em;
    }

    .squarekit-vs-divider {
        order: 2;
        flex-direction: row;
        justify-content: center;
    }

    .squarekit-conflict-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1em;
        padding: 1em 1.5em;
    }

    .squarekit-conflict-details {
        padding: 1.5em;
    }

    .squarekit-resolution-form {
        padding: 1.5em;
    }

    .squarekit-option-card {
        padding: 1em;
    }

    .squarekit-resolution-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .squarekit-resolution-actions .squarekit-btn-primary,
    .squarekit-resolution-actions .squarekit-btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .squarekit-empty-state {
        padding: 3em 1.5em;
    }

    .squarekit-empty-state-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Conflict Count Badge */
.conflict-count-badge {
    background: #ef4444;
    color: white;
    font-size: 11px;
    font-weight: 700;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 8px;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.squarekit-tab-link .conflict-count-badge {
    margin-left: auto;
    margin-right: 0;
}

.sync-card-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 16px;
    padding: 12px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    color: #92400e;
    font-size: 13px;
    font-weight: 500;
}

.sync-card-recommendation {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 16px;
    padding: 12px;
    background: #f0fdf4;
    border: 1px solid #10b981;
    border-radius: 6px;
    color: #065f46;
    font-size: 13px;
    font-weight: 500;
}

.sync-card-warning svg,
.sync-card-recommendation svg {
    flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .squarekit-sync-automation-grid {
        grid-template-columns: 1fr;
    }

    .squarekit-granular-sync-grid {
        grid-template-columns: 1fr;
    }

    .squarekit-radio-group {
        gap: 6px;
    }

    .sync-automation-card,
    .granular-sync-card {
        padding: 15px;
    }
}