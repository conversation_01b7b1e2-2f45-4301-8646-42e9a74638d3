// Square Kit Frontend Scripts
jQuery(function($) {
    // Only run on product pages
    if ($('.squarekit-modifiers-wrapper').length) {
        let $modifiersWrapper = $('.squarekit-modifiers-wrapper');
        let $priceContainer = $('.price');

        // Get base price from localized data instead of parsing HTML
        let basePrice = parseFloat(squarekit_frontend.base_price) || 0;
        let originalPriceHTML = $priceContainer.html(); // Store original price HTML
        let selectedModifiers = {};

        // Initialize modifier selection
        function initModifiers() {
            $modifiersWrapper.find('input[type="radio"], input[type="checkbox"]').on('change', function() {
                updateModifierSelection();
                updatePrice();
            });

            // Listen for variation changes to update modifier pricing and base price label
            $('form.variations_form').on('found_variation', function() {
                updatePrice();
                updateBasePriceLabel();
            }).on('reset_data', function() {
                updatePrice();
                updateBasePriceLabel();
            });

            // Also listen for direct variation select changes
            $('.variations select, .variations input').on('change', function() {
                setTimeout(function() {
                    updateBasePriceLabel();
                }, 100);
            });

            // Initial setup
            updateBasePriceLabel();
        }

        // Update selected modifiers
        function updateModifierSelection() {
            selectedModifiers = {};
            $modifiersWrapper.find('input:checked').each(function() {
                let $input = $(this);
                let setIndex = $input.data('set-index');
                let optionName = $input.val();
                let price = parseFloat($input.data('price')) || 0;
                let label = $input.closest('label').find('.option-name').text() || optionName;

                if (!selectedModifiers[setIndex]) {
                    selectedModifiers[setIndex] = [];
                }
                selectedModifiers[setIndex].push({
                    name: optionName,
                    label: label,
                    price: price
                });
            });
        }

        // Update product price based on selected modifiers
        function updatePrice() {
            // Get current variation price if available, otherwise use base price
            let currentPrice = getCurrentVariationPrice() || basePrice;
            let modifierTotal = 0;
            let selectedModifiersList = [];

            $modifiersWrapper.find('input:checked').each(function() {
                let price = parseFloat($(this).data('price')) || 0;
                let label = $(this).closest('label').find('.option-name').text() || $(this).val();
                modifierTotal += price;
                selectedModifiersList.push({
                    label: label,
                    price: price
                });
            });

            let totalPrice = currentPrice + modifierTotal;

            // Format prices using WooCommerce settings
            let formattedTotalPrice = formatPrice(totalPrice);
            let formattedCurrentPrice = formatPrice(currentPrice);

            // Update main price display
            if ($priceContainer.length) {
                if (modifierTotal > 0) {
                    // Show updated price with modifier total
                    $priceContainer.html('<span class="woocommerce-Price-amount amount"><bdi>' + formattedTotalPrice + '</bdi></span>');
                } else {
                    // Restore original price HTML when no modifiers selected
                    $priceContainer.html(originalPriceHTML);
                }
            }

            // Update price breakdown
            updatePriceBreakdown(formattedCurrentPrice, selectedModifiersList, formattedTotalPrice);
        }

        // Get current variation price from WooCommerce variation form
        function getCurrentVariationPrice() {
            let variationForm = $('form.variations_form');
            if (variationForm.length) {
                let variationData = variationForm.data('product_variations');
                let selectedAttributes = {};

                // Get selected variation attributes
                variationForm.find('select[name^="attribute_"]').each(function() {
                    let attributeName = $(this).attr('name');
                    let attributeValue = $(this).val();
                    if (attributeValue) {
                        selectedAttributes[attributeName] = attributeValue;
                    }
                });

                // Find matching variation
                if (variationData && Object.keys(selectedAttributes).length > 0) {
                    for (let i = 0; i < variationData.length; i++) {
                        let variation = variationData[i];
                        let matches = true;

                        for (let attr in selectedAttributes) {
                            if (variation.attributes[attr] && variation.attributes[attr] !== selectedAttributes[attr] && variation.attributes[attr] !== '') {
                                matches = false;
                                break;
                            }
                        }

                        if (matches) {
                            return parseFloat(variation.display_price) || basePrice;
                        }
                    }
                }
            }

            return basePrice;
        }

        // Update the price breakdown display
        function updatePriceBreakdown(formattedBasePrice, selectedModifiers, formattedTotalPrice) {
            let $breakdown = $('.squarekit-price-breakdown');
            let $modifiersList = $('.squarekit-modifiers-price-list');
            let $selectedSummary = $('.squarekit-selected-summary');

            if (selectedModifiers.length > 0) {
                // Show breakdown
                $breakdown.show();

                // Update base price and label
                $('.squarekit-base-price-display').text(formattedBasePrice);
                updateBasePriceLabel();

                // Update selected options summary
                let summaryText = selectedModifiers.map(function(modifier) {
                    return toSentenceCase(modifier.label);
                }).join(', ');
                $selectedSummary.text(summaryText);

                // Clear and rebuild modifiers list
                $modifiersList.empty();
                selectedModifiers.forEach(function(modifier) {
                    let formattedModifierPrice = formatPrice(modifier.price);
                    let formattedModifierLabel = toSentenceCase(modifier.label);
                    $modifiersList.append(
                        '<div class="squarekit-modifier-price-line">' +
                        '<span class="squarekit-price-label">+ ' + formattedModifierLabel + ':</span>' +
                        '<span class="squarekit-price-value">' + formattedModifierPrice + '</span>' +
                        '</div>'
                    );
                });

                // Update total
                $('.squarekit-total-price-display').text(formattedTotalPrice);
            } else {
                // Hide breakdown when no modifiers selected
                $breakdown.hide();
            }
        }

        // Update base price label to show selected variation
        function updateBasePriceLabel() {
            let basePriceLabel = 'Product Price:';
            let selectedVariations = [];

            // Check for selected variations/attributes
            $('.variations select, .variations input:checked').each(function() {
                let $this = $(this);
                let value = $this.val();

                if (value && value !== '') {
                    let attributeName = '';
                    let attributeValue = '';

                    if ($this.is('select')) {
                        attributeName = $this.closest('tr').find('label').text().replace(':', '').trim();
                        attributeValue = $this.find('option:selected').text();
                    } else if ($this.is('input')) {
                        attributeName = $this.closest('tr').find('label').text().replace(':', '').trim();
                        attributeValue = $this.next('label').text() || value;
                    }

                    if (attributeName && attributeValue) {
                        // Convert attribute name to sentence case and format with value
                        let formattedAttributeName = toSentenceCase(attributeName);
                        let formattedAttributeValue = toSentenceCase(attributeValue);
                        selectedVariations.push(formattedAttributeName + ': ' + formattedAttributeValue);
                    }
                }
            });

            // Update label based on selected variations
            if (selectedVariations.length > 0) {
                basePriceLabel = selectedVariations.join(', ') + ':';
            }

            $('.squarekit-base-price-line .squarekit-price-label').text(basePriceLabel);
        }

        // Helper function to convert text to sentence case
        function toSentenceCase(text) {
            if (!text) return '';
            return text.toLowerCase().replace(/^\w/, function(c) {
                return c.toUpperCase();
            });
        }

        // Format price according to WooCommerce settings
        function formatPrice(price) {
            let decimals = parseInt(squarekit_frontend.price_decimals) || 2;
            let decimalSep = squarekit_frontend.price_decimal_sep || '.';
            let thousandSep = squarekit_frontend.price_thousand_sep || ',';
            let currencySymbol = squarekit_frontend.currency_symbol || '$';
            let currencyPosition = squarekit_frontend.currency_position || 'left';

            // Format the number
            let formattedNumber = price.toFixed(decimals);

            // Add thousand separators if needed
            if (thousandSep && price >= 1000) {
                let parts = formattedNumber.split(decimalSep);
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandSep);
                formattedNumber = parts.join(decimalSep);
            }

            // Add currency symbol based on position
            switch (currencyPosition) {
                case 'left':
                    return currencySymbol + formattedNumber;
                case 'right':
                    return formattedNumber + currencySymbol;
                case 'left_space':
                    return currencySymbol + ' ' + formattedNumber;
                case 'right_space':
                    return formattedNumber + ' ' + currencySymbol;
                default:
                    return currencySymbol + formattedNumber;
            }
        }

        // Handle add to cart form submission
        $('form.cart').on('submit', function(e) {
            // Ensure modifier data is included
            let hasModifiers = false;
            
            $modifiersWrapper.find('input:checked').each(function() {
                hasModifiers = true;
                return false; // Break after first checked input
            });
            
            if (!hasModifiers) {
                // Check if any modifier sets are required (single choice sets)
                let requiredSets = $modifiersWrapper.find('.squarekit-modifier-set').filter(function() {
                    return $(this).find('input[type="radio"]').length > 0;
                });
                
                if (requiredSets.length > 0) {
                    let missingRequired = false;
                    requiredSets.each(function() {
                        if ($(this).find('input:checked').length === 0) {
                            missingRequired = true;
                            return false;
                        }
                    });
                    
                    if (missingRequired) {
                        e.preventDefault();
                        alert('Please select required options before adding to cart.');
                        return false;
                    }
                }
            }
        });

        // Debug function for troubleshooting
        function debugPricing() {
            console.log('SquareKit Debug Info:', {
                basePrice: basePrice,
                modifierTotal: getModifierTotal(),
                totalPrice: basePrice + getModifierTotal(),
                selectedModifiers: selectedModifiers,
                frontendData: squarekit_frontend
            });
        }

        function getModifierTotal() {
            let total = 0;
            $modifiersWrapper.find('input:checked').each(function() {
                total += parseFloat($(this).data('price')) || 0;
            });
            return total;
        }

        // Expose debug function globally for testing
        window.squareKitDebug = debugPricing;

        // Initialize
        initModifiers();

        // Log initial state for debugging
        console.log('SquareKit initialized with base price:', basePrice);
    }
}); 