// Square Kit Admin Scripts
jQuery(function($){
  // AJAX helper for admin actions
  window.squarekitAdminAjax = function(action, data, callback) {
    data = data || {};
    data.action = action;
    data.nonce = window.squarekit_admin ? window.squarekit_admin.nonce : '';
    $.post(window.squarekit_admin.ajax_url, data, function(resp){
      if (typeof callback === 'function') callback(resp);
    }).fail(function(){
      if (typeof callback === 'function') callback({success: false, data: {message: 'AJAX error.'}});
    });
  };

  // Accessibility: focus first error or notice
  var $notice = $('.squarekit-admin-notice, .notice-error, .notice-success').first();
  if ($notice.length) {
    $notice.attr('tabindex', '-1').focus();
  }

  // Button loading state
  $(document).on('click', '.squarekit-admin-btn, .squarekit-admin-btn-primary', function(){
    var $btn = $(this);
    if (!$btn.is(':disabled')) {
      $btn.addClass('loading');
      setTimeout(function(){ $btn.removeClass('loading'); }, 2000);
    }
  });

  // Dashboard Sync Option Selection
  $(document).on('click', '.sync-option-btn', function(e) {
    e.preventDefault();

    var $btn = $(this);
    var syncType = $btn.data('sync-type');

    // Toggle selection
    if ($btn.hasClass('selected')) {
      $btn.removeClass('selected');
    } else {
      $btn.addClass('selected');
    }

    // Update main sync button text based on selections
    updateSyncButtonText();
  });

  // Update sync button text based on selected options
  function updateSyncButtonText() {
    var selectedOptions = [];
    $('.sync-option-btn.selected').each(function() {
      var type = $(this).data('sync-type');
      selectedOptions.push(type);
    });

    var $syncBtn = $('#squarekit-sync-now');
    var $btnText = $syncBtn.find('.btn-text');

    if (selectedOptions.length === 0) {
      $btnText.text('Select sync options');
      $syncBtn.prop('disabled', true);
    } else if (selectedOptions.length === 3) {
      $btnText.text('Sync Everything');
      $syncBtn.prop('disabled', false);
    } else {
      var typeNames = {
        'products': 'Products',
        'customers': 'Customers',
        'inventory': 'Inventory'
      };
      var selectedNames = selectedOptions.map(function(type) {
        return typeNames[type];
      });
      $btnText.text('Sync ' + selectedNames.join(' + '));
      $syncBtn.prop('disabled', false);
    }
  }

  // Dashboard Sync Functionality
  $(document).on('click', '#squarekit-sync-now', function(e) {
    e.preventDefault();

    var $btn = $(this);

    // Get selected sync types
    var selectedTypes = [];
    $('.sync-option-btn.selected').each(function() {
      selectedTypes.push($(this).data('sync-type'));
    });

    if (selectedTypes.length === 0) {
      alert('Please select at least one sync option.');
      return;
    }

    var syncType = selectedTypes.length === 3 ? 'all' : selectedTypes;
    var $progress = $('#sync-progress');
    var $progressFill = $progress.find('.progress-fill');
    var $progressStatus = $progress.find('.progress-status');
    var $progressPercentage = $progress.find('.progress-percentage');

    // Disable all sync buttons
    $('.sync-btn, .sync-specific').prop('disabled', true);
    $btn.addClass('loading');

    // Show progress
    $progress.show();
    $progressFill.css('width', '0%');
    $progressStatus.text('Initializing sync...');
    $progressPercentage.text('0%');

    // Start sync operation
    startSyncOperation(syncType, function(progress) {
      // Update progress
      $progressFill.css('width', progress.percentage + '%');
      $progressStatus.text(progress.status);
      $progressPercentage.text(progress.percentage + '%');

      if (progress.completed) {
        // Sync completed
        setTimeout(function() {
          $progress.hide();
          $('.sync-btn, .sync-specific').prop('disabled', false);
          $btn.removeClass('loading');

          // Show success message
          showSyncNotification(progress.success, progress.message);

          // Refresh page stats if successful
          if (progress.success) {
            location.reload();
          }
        }, 1000);
      }
    });
  });

  // Sync operation handler
  function startSyncOperation(syncType, progressCallback) {
    var operations = [];

    // Determine what to sync based on type and settings
    if (Array.isArray(syncType)) {
      operations = syncType;
    } else {
      switch(syncType) {
        case 'all':
          operations = ['products', 'customers', 'inventory'];
          break;
        case 'products':
          operations = ['products'];
          break;
        case 'customers':
          operations = ['customers'];
          break;
        case 'inventory':
          operations = ['inventory'];
          break;
      }
    }

    var totalOperations = operations.length;
    var completedOperations = 0;
    var overallSuccess = true;
    var messages = [];

    function runNextOperation() {
      if (completedOperations >= totalOperations) {
        // All operations completed
        progressCallback({
          completed: true,
          success: overallSuccess,
          percentage: 100,
          status: overallSuccess ? 'Sync completed successfully!' : 'Sync completed with errors',
          message: messages.join(', ')
        });
        return;
      }

      var currentOperation = operations[completedOperations];
      var basePercentage = (completedOperations / totalOperations) * 100;

      progressCallback({
        completed: false,
        percentage: basePercentage,
        status: 'Syncing ' + currentOperation + '...'
      });

      // Execute sync operation
      $.ajax({
        url: squarekit_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'squarekit_manual_sync',
          nonce: squarekit_admin.nonce,
          sync_type: currentOperation
        },
        success: function(response) {
          completedOperations++;
          var operationPercentage = (completedOperations / totalOperations) * 100;

          if (response.success) {
            messages.push(currentOperation + ' synced successfully');
          } else {
            overallSuccess = false;
            messages.push(currentOperation + ' sync failed: ' + (response.data.message || 'Unknown error'));
          }

          progressCallback({
            completed: false,
            percentage: operationPercentage,
            status: response.success ?
              currentOperation + ' synced successfully' :
              currentOperation + ' sync failed'
          });

          // Continue with next operation after a short delay
          setTimeout(runNextOperation, 500);
        },
        error: function() {
          completedOperations++;
          overallSuccess = false;
          messages.push(currentOperation + ' sync failed: Network error');

          progressCallback({
            completed: false,
            percentage: (completedOperations / totalOperations) * 100,
            status: currentOperation + ' sync failed'
          });

          setTimeout(runNextOperation, 500);
        }
      });
    }

    // Start the first operation
    runNextOperation();
  }

  // Show sync notification
  function showSyncNotification(success, message) {
    var noticeClass = success ? 'notice-success' : 'notice-error';
    var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');

    $('.squarekit-admin-wrap').prepend($notice);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
      $notice.fadeOut(function() {
        $(this).remove();
      });
    }, 5000);
  }

  // Initialize dashboard sync options
  if ($('.sync-option-btn').length > 0) {
    // Select all options by default if none are selected
    if ($('.sync-option-btn.selected').length === 0) {
      $('.sync-option-btn').addClass('selected');
    }
    updateSyncButtonText();
  }

  // Granular Sync Controls
  $(document).on('change', '.master-toggle', function() {
    var direction = $(this).data('direction');
    var isEnabled = $(this).is(':checked');
    var dataTypesContainer = $('.sync-data-types[data-direction="' + direction + '"]');
    var dataTypeCheckboxes = dataTypesContainer.find('input[type="checkbox"]');

    if (isEnabled) {
      dataTypesContainer.show();
      dataTypeCheckboxes.prop('disabled', false);
    } else {
      dataTypesContainer.hide();
      dataTypeCheckboxes.prop('disabled', true);
      dataTypeCheckboxes.prop('checked', false);
    }
  });

  // Initialize granular sync controls on page load
  $('.master-toggle').each(function() {
    var direction = $(this).data('direction');
    var isEnabled = $(this).is(':checked');
    var dataTypesContainer = $('.sync-data-types[data-direction="' + direction + '"]');
    var dataTypeCheckboxes = dataTypesContainer.find('input[type="checkbox"]');

    if (!isEnabled) {
      dataTypesContainer.hide();
      dataTypeCheckboxes.prop('disabled', true);
    }
  });

  // Ready for extension: custom admin UI logic here
});

jQuery(document).ready(function($) {
    // Only run on the product edit screen with the Modifiers panel
    if ($('#squarekit-modifiers-admin-ui').length) {
        let $container = $('#squarekit-modifiers-admin-ui');
        let $dataInput = $('#squarekit_modifiers_data');
        let modifiers = [];
        try {
            modifiers = JSON.parse($dataInput.val()) || [];
        } catch (e) { modifiers = []; }

        // Render the UI
        function render() {
            let html = '';
            html += '<button type="button" class="button" id="squarekit-reimport-modifiers">Re-import from Square</button>';
            html += '<button type="button" class="button button-primary" id="squarekit-add-modset" style="margin-left:1em;">Add Modifier Set</button>';
            html += '<div style="margin-top:1em;">';
            if (modifiers.length === 0) {
                html += '<p>No modifier sets yet.</p>';
            }
            modifiers.forEach(function(set, setIdx) {
                let locked = set.source === 'square';
                html += '<div class="squarekit-modset" style="border:1px solid #ddd;padding:1em;margin-bottom:1em;background:' + (locked ? '#f9f9f9' : '#fff') + ';">';
                html += '<div style="display:flex;align-items:center;gap:1em;">';
                html += '<label>Set Name: <input type="text" class="modset-name" data-idx="' + setIdx + '" value="' + (set.name || '') + '" ' + (locked ? 'readonly' : '') + '></label>';
                html += '<label>Square Mod List ID: <input type="text" class="modset-listid" data-idx="' + setIdx + '" value="' + (set.square_list_id || '') + '" ' + (locked ? 'readonly' : '') + '></label>';
                html += '<label><input type="checkbox" class="modset-single" data-idx="' + setIdx + '" ' + (set.single ? 'checked' : '') + (locked ? ' disabled' : '') + '> Single Choice?</label>';
                if (locked) {
                    html += '<span style="color:#888;">(Square-managed)</span>';
                } else {
                    html += '<button type="button" class="button squarekit-remove-modset" data-idx="' + setIdx + '">Remove Set</button>';
                }
                html += '</div>';
                // Options table
                html += '<table class="widefat" style="margin-top:1em;">';
                html += '<thead><tr><th>Option Name</th><th>Price</th><th>Stock (optional)</th><th>Square Modifier ID</th><th>Actions</th></tr></thead>';
                html += '<tbody>';
                (set.options || []).forEach(function(opt, optIdx) {
                    html += '<tr>';
                    html += '<td><input type="text" class="modopt-name" data-set="' + setIdx + '" data-opt="' + optIdx + '" value="' + (opt.name || '') + '" ' + (locked ? 'readonly' : '') + '></td>';
                    html += '<td><input type="number" step="0.01" class="modopt-price" data-set="' + setIdx + '" data-opt="' + optIdx + '" value="' + (opt.price || '') + '" ' + (locked ? 'readonly' : '') + '></td>';
                    html += '<td><input type="text" class="modopt-stock" data-set="' + setIdx + '" data-opt="' + optIdx + '" value="' + (opt.stock || '') + '" ' + (locked ? 'readonly' : '') + '></td>';
                    html += '<td><input type="text" class="modopt-id" data-set="' + setIdx + '" data-opt="' + optIdx + '" value="' + (opt.square_id || '') + '" ' + (locked ? 'readonly' : '') + '></td>';
                    html += '<td>';
                    if (!locked) {
                        html += '<button type="button" class="button squarekit-remove-modopt" data-set="' + setIdx + '" data-opt="' + optIdx + '">Remove</button>';
                    }
                    html += '</td>';
                    html += '</tr>';
                });
                html += '</tbody>';
                html += '</table>';
                if (!locked) {
                    html += '<button type="button" class="button squarekit-add-modopt" data-idx="' + setIdx + '" style="margin-top:0.5em;">Add Option</button>';
                }
                html += '</div>';
            });
            html += '</div>';
            $container.html(html);
        }

        // Save to hidden input
        function save() {
            $dataInput.val(JSON.stringify(modifiers));
        }

        // Event handlers
        $container.on('click', '#squarekit-add-modset', function() {
            modifiers.push({
                name: '',
                square_list_id: '',
                single: false,
                source: 'wc',
                options: []
            });
            render(); save();
        });
        $container.on('click', '.squarekit-remove-modset', function() {
            let idx = $(this).data('idx');
            modifiers.splice(idx, 1);
            render(); save();
        });
        $container.on('click', '.squarekit-add-modopt', function() {
            let idx = $(this).data('idx');
            modifiers[idx].options = modifiers[idx].options || [];
            modifiers[idx].options.push({ name: '', price: '', stock: '', square_id: '' });
            render(); save();
        });
        $container.on('click', '.squarekit-remove-modopt', function() {
            let setIdx = $(this).data('set');
            let optIdx = $(this).data('opt');
            modifiers[setIdx].options.splice(optIdx, 1);
            render(); save();
        });
        $container.on('input change', '.modset-name, .modset-listid, .modset-single, .modopt-name, .modopt-price, .modopt-stock, .modopt-id', function() {
            let $el = $(this);
            if ($el.hasClass('modset-name')) modifiers[$el.data('idx')].name = $el.val();
            if ($el.hasClass('modset-listid')) modifiers[$el.data('idx')].square_list_id = $el.val();
            if ($el.hasClass('modset-single')) modifiers[$el.data('idx')].single = $el.is(':checked');
            if ($el.hasClass('modopt-name')) modifiers[$el.data('set')].options[$el.data('opt')].name = $el.val();
            if ($el.hasClass('modopt-price')) modifiers[$el.data('set')].options[$el.data('opt')].price = $el.val();
            if ($el.hasClass('modopt-stock')) modifiers[$el.data('set')].options[$el.data('opt')].stock = $el.val();
            if ($el.hasClass('modopt-id')) modifiers[$el.data('set')].options[$el.data('opt')].square_id = $el.val();
            save();
        });
        $container.on('click', '#squarekit-reimport-modifiers', function() {
            // AJAX call to re-import modifiers from Square
            let productId = $('input#post_ID').val();
            $(this).prop('disabled', true).text('Re-importing...');
            $.post(ajaxurl, {
                action: 'squarekit_reimport_modifiers',
                product_id: productId,
                nonce: squarekit_admin.nonce
            }, function(resp) {
                if (resp.success && Array.isArray(resp.data.modifiers)) {
                    // Mark all imported as source: 'square'
                    resp.data.modifiers.forEach(function(set) { set.source = 'square'; });
                    // Remove all existing Square-managed sets, keep WC-only
                    modifiers = modifiers.filter(set => set.source !== 'square').concat(resp.data.modifiers);
                    render(); save();
                } else {
                    alert('Failed to re-import modifiers from Square.');
                }
                $('#squarekit-reimport-modifiers').prop('disabled', false).text('Re-import from Square');
            }).fail(function() {
                alert('AJAX error.');
                $('#squarekit-reimport-modifiers').prop('disabled', false).text('Re-import from Square');
            });
        });

        // Initial render
        render();
    }
}); 