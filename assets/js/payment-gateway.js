/**
 * SquareKit Payment Gateway - Modern Square Web Payments SDK Integration
 * Production-ready implementation with SCA compliance
 */

(function($) {
    'use strict';

    let payments = null;
    let card = null;
    let digitalWallets = {};
    let isProcessing = false;

    const SquareKitPaymentGateway = {
        
        /**
         * Initialize the payment gateway
         */
        init: function() {
            if (typeof SquareKitPayment === 'undefined') {
                console.error('SquareKit Payment configuration not found');
                return;
            }

            // Wait for Square SDK to load
            this.waitForSquareSDK();
        },

        /**
         * Wait for Square Web Payments SDK to load
         */
        waitForSquareSDK: function() {
            const self = this;
            
            if (typeof Square !== 'undefined') {
                self.initializeSquarePayments();
            } else {
                // Retry after 100ms
                setTimeout(function() {
                    self.waitForSquareSDK();
                }, 100);
            }
        },

        /**
         * Initialize Square payments
         */
        initializeSquarePayments: async function() {
            try {
                // Initialize Square payments
                payments = Square.payments(SquareKitPayment.applicationId, SquareKitPayment.locationId);
                
                if (!payments) {
                    throw new Error('Failed to initialize Square payments');
                }

                // Initialize payment methods
                await this.initializeCard();
                await this.initializeDigitalWallets();
                
                // Bind checkout events
                this.bindCheckoutEvents();

                console.log('SquareKit Payment Gateway initialized successfully');

            } catch (error) {
                console.error('Square initialization error:', error);
                this.showError(SquareKitPayment.messages.sdkLoadError);
            }
        },

        /**
         * Initialize card payment method
         */
        initializeCard: async function() {
            try {
                card = await payments.card({
                    style: {
                        '.input-container': {
                            borderColor: '#E0E0E0',
                            borderRadius: '4px',
                        },
                        '.input-container.is-focus': {
                            borderColor: '#4A90E2',
                        },
                        '.input-container.is-error': {
                            borderColor: '#E74C3C',
                        },
                        '.message-text': {
                            color: '#E74C3C',
                        },
                        '.message-icon': {
                            color: '#E74C3C',
                        },
                    }
                });

                await card.attach('#squarekit-card-container');
                
            } catch (error) {
                console.error('Card initialization error:', error);
            }
        },

        /**
         * Initialize digital wallets
         */
        initializeDigitalWallets: async function() {
            const enabledWallets = SquareKitPayment.enabledWallets || [];
            
            for (const walletType of enabledWallets) {
                try {
                    await this.initializeWallet(walletType);
                } catch (error) {
                    console.error(`${walletType} initialization error:`, error);
                }
            }
        },

        /**
         * Initialize specific wallet
         */
        initializeWallet: async function(walletType) {
            const paymentRequest = await this.buildPaymentRequest();
            
            switch (walletType) {
                case 'googlePay':
                    digitalWallets.googlePay = await payments.googlePay(paymentRequest);
                    await digitalWallets.googlePay.attach('#squarekit-google-pay-button');
                    break;
                    
                case 'applePay':
                    digitalWallets.applePay = await payments.applePay(paymentRequest);
                    await digitalWallets.applePay.attach('#squarekit-apple-pay-button');
                    break;
                    
                case 'afterpay':
                    digitalWallets.afterpay = await payments.afterpayClearpay(paymentRequest);
                    await digitalWallets.afterpay.attach('#squarekit-afterpay-button');
                    break;
            }
        },

        /**
         * Build payment request for digital wallets
         */
        buildPaymentRequest: async function() {
            const total = this.getOrderTotal();
            
            return {
                countryCode: SquareKitPayment.countryCode,
                currencyCode: SquareKitPayment.currency,
                total: {
                    amount: total.toString(),
                    label: 'Total',
                },
            };
        },

        /**
         * Get order total from checkout
         */
        getOrderTotal: function() {
            const totalElement = $('.order-total .woocommerce-Price-amount');
            if (totalElement.length) {
                const totalText = totalElement.text().replace(/[^\d.,]/g, '');
                return parseFloat(totalText.replace(',', '.')) || 0;
            }
            return 0;
        },

        /**
         * Bind checkout events
         */
        bindCheckoutEvents: function() {
            const self = this;

            // Handle checkout form submission
            $('form.checkout').on('checkout_place_order_squarekit', function(e) {
                return self.handleCheckoutSubmission(e);
            });

            // Handle digital wallet clicks
            $('#squarekit-digital-wallets').on('click', '.squarekit-wallet-button', function(e) {
                e.preventDefault();
                const walletType = $(this).attr('id').replace('squarekit-', '').replace('-button', '');
                self.processDigitalWalletPayment(walletType);
            });

            // Handle saved payment method selection
            $('input[name="wc-squarekit-payment-token"]').on('change', function() {
                const isNewMethod = $(this).val() === 'new';
                $('#squarekit-card-container').toggle(isNewMethod);
                $('#squarekit-digital-wallets').toggle(isNewMethod);
            });
        },

        /**
         * Handle checkout form submission
         */
        handleCheckoutSubmission: function(e) {
            if (isProcessing) {
                return false;
            }

            // Check if using saved payment method
            const selectedToken = $('input[name="wc-squarekit-payment-token"]:checked').val();
            if (selectedToken && selectedToken !== 'new') {
                return true; // Allow form submission with saved method
            }

            e.preventDefault();
            this.processCardPayment();
            return false;
        },

        /**
         * Process card payment
         */
        processCardPayment: async function() {
            if (!card) {
                this.showError('Card payment method not initialized');
                return;
            }

            try {
                this.showLoading(SquareKitPayment.messages.processingPayment);
                isProcessing = true;

                // Build verification details for SCA compliance
                const verificationDetails = await this.buildVerificationDetails();

                // Tokenize the card with verification details
                const result = await card.tokenize(verificationDetails);

                if (result.status === 'OK') {
                    // Set payment token and submit form
                    $('#squarekit-payment-token').val(result.token);
                    $('#squarekit-payment-method').val('card');
                    
                    // Add nonce for security
                    if (!$('input[name="squarekit_payment_nonce"]').length) {
                        $('<input>').attr({
                            type: 'hidden',
                            name: 'squarekit_payment_nonce',
                            value: SquareKitPayment.nonce
                        }).appendTo('form.checkout');
                    }

                    this.hideLoading();
                    $('form.checkout').off('checkout_place_order_squarekit').submit();
                } else {
                    throw new Error(result.errors ? result.errors[0].message : SquareKitPayment.messages.tokenizeError);
                }

            } catch (error) {
                console.error('Card payment error:', error);
                this.showError(error.message || SquareKitPayment.messages.tokenizeError);
            } finally {
                isProcessing = false;
                this.hideLoading();
            }
        },

        /**
         * Process digital wallet payment
         */
        processDigitalWalletPayment: async function(walletType) {
            const wallet = digitalWallets[walletType];
            if (!wallet) {
                this.showError(`${walletType} not initialized`);
                return;
            }

            try {
                this.showLoading(SquareKitPayment.messages.processingPayment);
                isProcessing = true;

                const result = await wallet.tokenize();

                if (result.status === 'OK') {
                    $('#squarekit-payment-token').val(result.token);
                    $('#squarekit-payment-method').val(walletType);
                    
                    // Add nonce for security
                    if (!$('input[name="squarekit_payment_nonce"]').length) {
                        $('<input>').attr({
                            type: 'hidden',
                            name: 'squarekit_payment_nonce',
                            value: SquareKitPayment.nonce
                        }).appendTo('form.checkout');
                    }

                    this.hideLoading();
                    $('form.checkout').submit();
                } else {
                    throw new Error(result.errors ? result.errors[0].message : `${walletType} payment failed`);
                }

            } catch (error) {
                console.error(`${walletType} payment error:`, error);
                this.showError(error.message || `${walletType} payment failed`);
            } finally {
                isProcessing = false;
                this.hideLoading();
            }
        },

        /**
         * Build verification details for SCA compliance
         */
        buildVerificationDetails: async function() {
            const billingData = this.getBillingData();
            const total = this.getOrderTotal();

            return {
                amount: total.toFixed(2),
                currencyCode: SquareKitPayment.currency,
                intent: 'CHARGE',
                customerInitiated: true,
                sellerKeyedIn: false,
                billingContact: {
                    givenName: billingData.firstName || '',
                    familyName: billingData.lastName || '',
                    email: billingData.email || '',
                    phone: billingData.phone || '',
                    addressLines: [billingData.address1 || '', billingData.address2 || ''].filter(Boolean),
                    city: billingData.city || '',
                    state: billingData.state || '',
                    postalCode: billingData.postcode || '',
                    countryCode: billingData.country || SquareKitPayment.countryCode,
                },
            };
        },

        /**
         * Get billing data from checkout form
         */
        getBillingData: function() {
            return {
                firstName: $('#billing_first_name').val(),
                lastName: $('#billing_last_name').val(),
                email: $('#billing_email').val(),
                phone: $('#billing_phone').val(),
                address1: $('#billing_address_1').val(),
                address2: $('#billing_address_2').val(),
                city: $('#billing_city').val(),
                state: $('#billing_state').val(),
                postcode: $('#billing_postcode').val(),
                country: $('#billing_country').val(),
            };
        },

        /**
         * Show loading indicator
         */
        showLoading: function(message) {
            $('#squarekit-payment-loading').show().find('span').text(message || SquareKitPayment.messages.processingPayment);
            $('#squarekit-payment-errors').hide();
        },

        /**
         * Hide loading indicator
         */
        hideLoading: function() {
            $('#squarekit-payment-loading').hide();
        },

        /**
         * Show error message
         */
        showError: function(message) {
            this.hideLoading();
            $('#squarekit-payment-errors').html('<p>' + message + '</p>').show();
            
            // Scroll to error
            $('html, body').animate({
                scrollTop: $('#squarekit-payment-errors').offset().top - 100
            }, 500);
        },

        /**
         * Clear errors
         */
        clearErrors: function() {
            $('#squarekit-payment-errors').hide().empty();
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize on checkout page when SquareKit is selected
        if ($('#payment_method_squarekit').length) {
            SquareKitPaymentGateway.init();
        }
    });

})(jQuery);
